﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ToolCommon
{
    public class CacheItem
    {
        private string objKey = string.Empty;
        private object objValue = null;
        private double expireTimes = 300;
        private DateTime dtStart;

        /// <summary>
        /// 键
        /// </summary>
        public string ObjKey
        {
            get { return objKey; }
            set { objKey = value; }
        }

        /// <summary>
        /// 值
        /// </summary>
        public object ObjValue
        {
            get { return objValue; }
            set { objValue = value; }
        }

        /// <summary>
        /// 过期时间(小时)
        /// </summary>
        public double ExpireTimes
        {
            get { return expireTimes; }
            set { expireTimes = value; }
        }

        /// <summary>
        /// 开始生效时间
        /// </summary>
        public DateTime DtStart
        {
            get { return dtStart; }
            set { dtStart = value; }
        }
    }
}
