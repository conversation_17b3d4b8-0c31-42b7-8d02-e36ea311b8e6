﻿using CommonLib;
using System;
using System.Data;
using System.IO;

namespace Account.Web
{
    public partial class View : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (CommonLib.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            {
                Response.End();
                return;
            }
            if (BoxUtil.GetStringFromObject(Request.QueryString["op"]) == "del")
            {
                DelPic(BoxUtil.GetStringFromObject(Request.QueryString["app"]), BoxUtil.GetStringFromObject(Request.QueryString["file"]));
                Response.End();
            }
            else
            {
                //if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                //    Response.End();
            }
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            //if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            //    return;
            string strApp = txtApp.Text.Trim();
            GetPic(strApp);
        }

        protected void btnDel_Click(object sender, EventArgs e)
        {
            //if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            //    return;
            string strApp = txtApp.Text.Trim();
            if (string.IsNullOrEmpty(strApp))
                return;
            DelPic(strApp);
        }

        private void DelPic(string strApp, string strFile)
        {
            try
            {
                string filePath = System.Web.HttpContext.Current.Request.MapPath("Pic/" + strApp + "/" + strFile);//路径
                if (!File.Exists(filePath))
                {
                    if (Directory.Exists(filePath))
                    {
                        DelPic(strApp);
                    }
                    return;
                }
                File.Delete(filePath);
                if (new DirectoryInfo(System.Web.HttpContext.Current.Request.MapPath("Pic/" + strApp)).GetFiles("*.jpg").Length <= 0)
                {
                    DelPic(strApp);
                }
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
            Response.Write("删除成功！");
        }

        private void DelPic(string strApp)
        {
            try
            {
                string filePath = System.Web.HttpContext.Current.Request.MapPath("Pic/" + strApp);//路径
                if (!Directory.Exists(filePath))
                {
                    return;
                }
                DeleteDirectory(filePath);
                Response.Write("删除成功！");
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }

        /// <summary>
        /// 删除非空文件夹
        /// </summary>
        /// <param name="path">要删除的文件夹目录</param>
        void DeleteDirectory(string path)
        {
            DirectoryInfo dir = new DirectoryInfo(path);
            if (dir.Exists)
            {
                try
                {
                    DirectoryInfo[] childs = dir.GetDirectories();
                    foreach (DirectoryInfo child in childs)
                    {
                        try
                        {
                            child.Delete(true);
                        }
                        catch { }
                    }
                    dir.Delete(true);
                }
                catch { }
            }
        }

        private void GetPic(string strApp)
        {
            try
            {
                if (string.IsNullOrEmpty(strApp))
                {
                    GetAll();
                    return;
                }
                string filePath = System.Web.HttpContext.Current.Request.MapPath("Pic/" + strApp);//路径
                if (!Directory.Exists(filePath))
                {
                    lblCount.Text = "0";
                    return;
                }
                //string[] files = Directory.GetFiles(filePath, "*.jpg");

                FileInfo[] fileinfos = new DirectoryInfo(filePath).GetFiles("*.jpg");
                Array.Sort(fileinfos, new FileComparer());
                int index = 0;
                DataTable dtTmp = new DataTable();
                dtTmp.Columns.Add("序号");
                dtTmp.Columns.Add("用户");
                dtTmp.Columns.Add("时间");
                dtTmp.Columns.Add("名称");
                dtTmp.Columns.Add("图片");
                if (fileinfos != null)
                {
                    foreach (var item in fileinfos)
                    {
                        try
                        {
                            index++;
                            DataRow row = dtTmp.NewRow();
                            row[0] = index;
                            row[1] = strApp;
                            row[2] = item.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                            row[3] = item.Name;
                            row[4] = "Pic/" + strApp + "/" + item.FullName.Substring(item.FullName.LastIndexOf("\\") + 1);
                            dtTmp.Rows.Add(row);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                lblCount.Text = dtTmp.Rows.Count.ToString();
                vlightbox1.DataSource = null;
                vlightbox1.DataSource = dtTmp;
                vlightbox1.DataBind();
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }

        private void GetAll()
        {
            try
            {
                string filePath = System.Web.HttpContext.Current.Request.MapPath("Pic");//路径
                if (!Directory.Exists(filePath))
                {
                    lblCount.Text = "0";
                    return;
                }
                //string[] files = Directory.GetFiles(filePath, "*.jpg");

                DirectoryInfo[] fileinfos = new DirectoryInfo(filePath).GetDirectories();
                Array.Sort(fileinfos, new DirectoryComparer());

                DataTable dtTmp = new DataTable();
                dtTmp.Columns.Add("序号");
                dtTmp.Columns.Add("用户");
                dtTmp.Columns.Add("时间");
                dtTmp.Columns.Add("名称");
                dtTmp.Columns.Add("图片");
                int index = 0;
                if (fileinfos != null)
                {
                    foreach (var item in fileinfos)
                    {
                        try
                        {
                            index++;
                            DataRow row = dtTmp.NewRow();
                            row[0] = index;
                            row[1] = item.Name;
                            row[2] = item.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                            row[3] = "";
                            row[4] = "";
                            dtTmp.Rows.Add(row);
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                    }
                }
                //List<string> lstInt = new List<string>() { "名称", "图片" };
                //try
                //{
                //    foreach (var item in lstInt)
                //    {
                //        for (int i = 0; i < vlightbox1.Columns.Count; i++)
                //        {
                //            if (vlightbox1.Columns[i].HeaderText.Equals(item))
                //            {
                //                vlightbox1.Columns.RemoveAt(i);
                //                break;
                //            }
                //        }
                //    }
                //}
                //catch { }
                lblCount.Text = dtTmp.Rows.Count.ToString();
                vlightbox1.DataSource = dtTmp;
                vlightbox1.DataBind();
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }

        protected void btnDelAll_Click(object sender, EventArgs e)
        {
            try
            {
                string filePath = System.Web.HttpContext.Current.Request.MapPath("Pic");//路径
                if (!Directory.Exists(filePath))
                {
                    return;
                }
                DeleteDirectory(filePath);
                Response.Write("删除成功！");
            }
            catch (Exception oe)
            {
                Response.Write("服务器故障！");
            }
        }

        protected void btnAll_Click(object sender, EventArgs e)
        {
            txtApp.Text = "";
            btnOK_Click(sender, e);
        }
    }
}