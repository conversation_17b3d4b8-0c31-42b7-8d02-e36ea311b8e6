﻿using System;
using System.Collections.Generic;
using System.Web;

namespace Account.Web
{
    public class UpdateHelper
    {

        public static bool IsHasUpdate(DateTime dtLast, string strOldVersion)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(strOldVersion) && DateTime.Now.AddMonths(-2) < dtLast)
            {
                result = CommonHelper.DtNowVersion > dtLast;
            }
            return result;
        }
    }
}