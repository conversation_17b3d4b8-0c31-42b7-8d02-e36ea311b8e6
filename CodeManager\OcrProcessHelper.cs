﻿using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Account.Web
{
    public class OcrProcessHelper
    {
        public static string ProcessByWeb = "Web";
        public static string ProcessByWSS = "WSS";

        private static List<long> lstExpVersion = new List<long>() { DateTime.Parse("2025-03-19 15:00:00").Ticks, DateTime.Parse("2025-03-19 15:30:00").Ticks };

        static LocalWaitCache<string> ExecCache = new LocalWaitCache<string>("ExecCache", new TimeSpan(0, 1, 0), false);

        public static async Task<string> WaitOcr(string msgId, long version, string strServer, int timeOut, string type, CancellationTokenSource cts)
        {
            var result = string.Empty;
            if (lstExpVersion.Contains(version))
            {
                await Task.Delay((timeOut - 3) * 1000);
            }
            else
            {
                var content = await CodeProcessHelper.GetFromProcessPool(timeOut - 3, strServer, cts.Token);
                if (content != null)
                {
                    if (cts.IsCancellationRequested)
                    {
                        CodeProcessHelper.SendOcrRequestToRedis(content, false);
                        LogHelper.Log.ErrorFormat("{1}已取消请求,StrIndex:{0}"
                            , content.StrIndex
                            , version > 0 ? "【" + new DateTime(version).ToString("MM-dd HH:mm") + "】" : "");
                    }
                    else
                    {
                        content.OcrTime.ServerAlloted = ServerTime.DateTime.Ticks;
                        result = JsonConvert.SerializeObject(content);
                        LogHelper.Log.InfoFormat("{3}ID:{0},Received By:{1}{2}"
                            , content.StrIndex
                            , strServer
                            , content.RetryTimes > 0 ? ",RetryTime:" + content.RetryTimes : ""
                            , version > 0 ? "【" + new DateTime(version).ToString("MM-dd HH:mm") + "】" : "");
                        CodeProcessHelper.AddProcessInfo(content.StrIndex, "[" + strServer + "-" + type + "]节点，抢单成功！");
                        _ = Task.Factory.StartNew(async () =>
                        {
                            await CodeProcessHelper.WaitOcrServerState(content, strServer);
                        });
                    }
                }
            }
            return result;
        }

        public static async Task<string> WaitFile(string msgId, long version, string strServer, int timeOut, string type, CancellationTokenSource cts)
        {
            var result = string.Empty;
            if (lstExpVersion.Contains(version))
            {
                await Task.Delay((timeOut - 3) * 1000);
            }
            else
            {
                var content = await CodeProcessHelper.GetFileStatusFromProcessPool(timeOut - 1, strServer, cts.Token);
                if (content != null)
                {
                    if (cts.IsCancellationRequested)
                    {
                        //CodeProcessHelper.SendFileStatusToRedis(content);
                        LogHelper.Log.ErrorFormat("已取消请求,TaskId:{0}", content.TaskId);
                    }
                    else
                    {
                        var taskInfoCount = CodeProcessHelper.GetProcessInfo(content.TaskId).Count;
                        bool isValidateTask = taskInfoCount > 0 && taskInfoCount < 50;
                        if (isValidateTask)
                        {
                            CodeProcessHelper.AddProcessInfo(content.TaskId, "[" + strServer + "-" + type + "]查询处理进度，抢单成功！");
                            result = JsonConvert.SerializeObject(content);
                            //LogHelper.Log.InfoFormat("ID:{0},Received By:{1}", content.TaskId, context.Request.QueryString["server"]);
                        }
                        else
                        {
                            //超时任务，不处理，直接返回成功
                            var processState = new ProcessStateEntity()
                            {
                                taskId = content.TaskId,
                                desc = "处理完毕，可以下载了！",
                                state = OcrProcessState.处理成功
                            };
                            CodeProcessHelper.SetFileStatusResult(processState);
                        }
                    }
                }
            }
            return result;
        }

        public static void SaveOcrResult(string msgId, long version, string strServer, string type, string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return;
            }
            try
            {
                var ocrContent = JsonConvert.DeserializeObject<OcrContent>(content);
                if (ocrContent != null)
                {
                    var cacheKey = string.IsNullOrEmpty(msgId) ? "" : string.Format("{0}-{1}", "SaveOcrResult", msgId);
                    ExecCache.ExecWhenNotExsits(cacheKey, () =>
                    {
                        ocrContent.OcrTime = ocrContent.OcrTime ?? new OcrTimeEntity();
                        ocrContent.OcrTime.ServerReceivedOcrResult = ServerTime.DateTime.Ticks;
                        
                        // 记录服务端开始处理结果的时间
                        ocrContent.OcrTime.ServerProcessResultStart = ServerTime.DateTime.Ticks;
                        
                        // 使用直接计算OCR处理耗时，替代GetOcrProcessingTime方法调用
                        double ocrProcessingTime = 0;
                        var ocrTime = ocrContent.OcrTime;
                        
                        // 确保有必要的时间点数据
                        if (ocrTime.OcrServerEnd > 0)
                        {
                            // 情况1: 下载完成后才开始OCR处理（下载完成晚于引擎启动）
                            if (ocrTime.ClientDownloadEnd > ocrTime.ClientOcrEngineStart && ocrTime.ClientDownloadEnd > 0)
                            {
                                ocrProcessingTime = new TimeSpan(ocrTime.OcrServerEnd - ocrTime.ClientDownloadEnd).TotalMilliseconds;
                            }
                            // 情况2: 从OCR引擎启动就开始处理（下载已完成或无需下载）
                            else if (ocrTime.ClientOcrEngineStart > 0)
                            {
                                ocrProcessingTime = new TimeSpan(ocrTime.OcrServerEnd - ocrTime.ClientOcrEngineStart).TotalMilliseconds;
                            }
                            // 情况3: 缺少部分时间点，但有处理结束时间，尝试使用其他时间点估算
                            else if (ocrTime.ThreadPoolExecutionStartTime > 0)
                            {
                                ocrProcessingTime = new TimeSpan(ocrTime.OcrServerEnd - ocrTime.ThreadPoolExecutionStartTime).TotalMilliseconds;
                            }
                        }
                        
                        var resultTransferTime = new TimeSpan(ServerTime.DateTime.Ticks - ocrContent.OcrTime.OcrServerEnd).TotalMilliseconds;
                        var userWaitTime = ocrContent.OcrTime.UserStartRequest <= 0 ? "-" : 
                            new TimeSpan(ServerTime.DateTime.Ticks - ocrContent.OcrTime.UserStartRequest).TotalMilliseconds.ToString("F0");
                        
                        LogHelper.Log.InfoFormat("{6}ID:{0},Process By:{1}-{5} OCR耗时:{3}ms 结果传输耗时:{2} ms 客户已等待:{4}ms"
                            , ocrContent.id
                            , ocrContent.Server + "-" + type
                            , resultTransferTime.ToString("F0")
                            , ocrProcessingTime.ToString("F0")
                            , userWaitTime
                            , ocrContent.processName
                            , version > 0 ? "【" + new DateTime(version).ToString("MM-dd HH:mm") + "】" : "");
                        
                        // 记录服务端完成处理结果的时间
                        ocrContent.OcrTime.ServerProcessResultEnd = ServerTime.DateTime.Ticks;
                        
                        CodeProcessHelper.SetOcrResult(ocrContent, type);
                    });
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("SetOcrResult Error:" + content, oe);
            }
        }

        public static void SaveFileResult(string msgId, long version, string strServer, string type, string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return;
            }
            try
            {
                var ocrContent = JsonConvert.DeserializeObject<ProcessStateEntity>(HttpUtility.UrlDecode(content));
                if (ocrContent != null)
                {
                    var cacheKey = string.IsNullOrEmpty(msgId) ? "" : string.Format("{0}-{1}", "SaveFileResult", msgId);
                    ExecCache.ExecWhenNotExsits(cacheKey, () =>
                    {
                        _ = Task.Factory.StartNew(() =>
                        {
                            CodeProcessHelper.SetFileStatusResult(ocrContent);
                        });
                    });
                    //CodeProcessHelper.AddProcessInfo(ocrContent.taskId, "查询进度结果：" + ocrContent.desc);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("SaveFileResult Error:" + content, oe);
            }
        }

        public static void ReportToProcess(string msgId, string strId, string strServer, long dtReceived, long dtServerPush, long dtOcrStartTransfer, long dtAdd, long version, string type)
        {
            var cacheKey = string.IsNullOrEmpty(msgId) ? "" : string.Format("{0}-{1}", "SaveFileResult", msgId);
            ExecCache.ExecWhenNotExsits(cacheKey, () =>
            {
                LogHelper.Log.InfoFormat("{5}ID:{0},Report State:{1},抢单传输耗时:{2}ms,反馈传输耗时:{3}ms,服务端总耗时:{4}ms"
                            , strId
                            , strServer + "-" + type
                            //服务端分配完成时间 ~ OCR端收到请求开始处理
                            , dtReceived <= 0 ? "-" : new TimeSpan(Math.Abs(dtReceived - dtServerPush)).TotalMilliseconds.ToString("F0")
                            //Ocr端开始反馈 ~ 当前收到反馈
                            , dtOcrStartTransfer <= 0 ? "-" : new TimeSpan(Math.Abs(ServerTime.DateTime.Ticks - dtOcrStartTransfer)).TotalMilliseconds.ToString("F0")
                            // 服务端收到用户请求-现在
                            , dtAdd <= 0 ? "-" : new TimeSpan(Math.Abs(ServerTime.DateTime.Ticks - dtAdd)).TotalMilliseconds.ToString("F0")
                            , version > 0 ? "【" + new DateTime(version).ToString("MM-dd HH:mm") + "】" : "");
                CodeProcessHelper.ReportOcrServerState(strId, strServer);
                CodeProcessHelper.AddProcessInfo(strId, "[" + strServer + "-" + type + "]节点，正在处理，请稍候…");
            });
        }
    }
}