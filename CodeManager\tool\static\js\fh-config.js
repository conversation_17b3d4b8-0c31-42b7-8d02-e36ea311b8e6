window.FhConfig={toolMap:{"json-format":{name:"JSON美化工具",tips:"页面自动检测并格式化、手动格式化、乱码解码、排序、BigInt、编辑、下载、皮肤定制等",icon:"⒥"},"json-diff":{name:"JSON比对工具",tips:"支持两个JSON内容的自动键值比较，并高亮显示差异点，同时也能判断JSON是否合法",icon:"☷"},"qr-code":{name:"二维码/解码",tips:"支持自定义颜色和icon的二维码生成器，并且支持多种模式的二维码解码，包括截图后粘贴解码",icon:"▣"},"image-base64":{name:"图片转Base64",tips:"支持多种模式的图片转Base64格式，比如链接粘贴/截图粘贴等，也支持Base64数据逆转图片",icon:"▤"},"sticky-notes":{name:"我的便签笔记",tips:"方便快捷的浏览器便签笔记工具，支持创建目录对笔记进行分类管理，笔记支持一键导出/导入",icon:"✐"},"en-decode":{name:"信息编码转换",tips:"支持多格式的信息编解码，如Unicode、UTF-8、UTF-16、URL、Base64、MD5、Hex、Gzip等",icon:"♨"},"code-beautify":{name:"代码美化工具",tips:"支持多语言的代码美化，包括 Javascript、CSS、HTML、XML、SQL，且会陆续支持更多格式",icon:"✡"},"code-compress":{name:"代码压缩工具",tips:"Web开发用，提供简单的代码压缩功能，支持HTML、Javascript、CSS代码压缩",icon:"♯"},timestamp:{name:"时间(戳)转换",tips:"本地化时间与时间戳之间的相互转换，支持秒/毫秒、支持世界时区切换、各时区时钟展示等",icon:"♖"},password:{name:"随机密码生成",tips:"将各种字符进行随机组合生成密码，可以由数字、大小写字母、特殊符号组成，支持指定长度",icon:"♆"},html2markdown:{name:"Markdown工具",tips:"Markdown编辑器，支持在线编写、预览、下载等，并支持HTML内容到Markdown格式的转换",icon:"ⓜ"},postman:{name:"简易版Postman",tips:"开发过程中的接口调试工具，支持GET/POST/HEAD请求方式，且支持JSON内容自动格式化",icon:"☯"},regexp:{name:"JS正则表达式",tips:"正则校验工具，默认提供一些工作中常用的正则表达式，支持内容实时匹配并高亮显示结果",icon:"✙"},"trans-radix":{name:"进制转换工具",tips:"支持2进制到36进制数据之间的任意转换，比如：10进制转2进制，8进制转16进制，等等",icon:"❖"},"trans-color":{name:"颜色转换工具",tips:"支持HEX颜色到RGB格式的互转，比如HEX颜色「#43ad7f」转RGB后为「rgb(67, 173, 127)」",icon:"▶"},crontab:{name:"Crontab工具",tips:"一个简易的Crontab生成工具，支持随机生成Demo，编辑过程中，分时日月周会高亮提示",icon:"½"},"loan-rate":{name:"贷(还)款利率",tips:"贷款或还款利率的计算器，按月呈现还款计划；并支持按还款额反推贷款实际利率",icon:"$"},devtools:{name:"FH开发者工具",tips:"以开发平台的思想，FeHelper支持用户进行本地开发，将自己的插件功能集成进FH工具市场",icon:"㉿",extensionOnly:!0},"page-monkey":{name:"网页油猴工具",tips:"自行配置页面匹配规则、编写Hack脚本，实现网页Hack，如页面自动刷新、自动抢票等",icon:"♀",extensionOnly:!0},screenshot:{name:"网页截屏工具",tips:"可对任意网页进行截屏，支持可视区域截屏、全网页滚动截屏，最终结果可预览后再保存",icon:"✂",extensionOnly:!0},"color-picker":{name:"页面取色工具",tips:"可直接在网页上针对任意元素进行色值采集，将光标移动到需要取色的位置，单击确定即可",icon:"✑",extensionOnly:!0},naotu:{name:"便捷思维导图",tips:"轻量便捷，随想随用，支持自动保存、本地数据存储、批量数据导入导出、图片格式下载等",icon:"Ψ"},"grid-ruler":{name:"网页栅格标尺",tips:"Web开发用，横竖两把尺子，以10px为单位，用以检测&校准当前网页的栅格对齐率",icon:"Ⅲ",extensionOnly:!0},"page-timing":{name:"网页性能检测",tips:"检测网页加载性能，包括握手、响应、渲染等各阶段耗时，同时提供Response Headers以便分析",icon:"Σ",extensionOnly:!0},excel2json:{name:"Excel转JSON",tips:"将Excel或CVS中的数据，直接转换成为结构化数据，如JSON、XML、MySQL、PHP等（By @hpng）",icon:"Ⓗ"}},screenshots:["fh-market.png","fh-popup.png","fh-menu.png"]};