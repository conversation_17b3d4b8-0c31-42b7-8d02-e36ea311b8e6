﻿using Account.Web.pay._360;
using CommonLib;
using System;
using System.Linq;
using System.Web;

namespace Account.Web.pay
{
    public class NewPayUtil
    {
        private static Random RndOrderId = new Random();

        public static PayOrderEntity createOrder(string payId, string param, string remark, string price, OrderFrom from)
        {
            PayOrderEntity tmp = null;
            var priceD = Math.Round(BoxUtil.GetDoubleFromObject(price), 2);
            if (from == OrderFrom.Self && !string.IsNullOrEmpty(remark) && !string.IsNullOrEmpty(param))
            {
                var lstOrder = PayHelper.findOrderByRemarkAndParamAndPrice(remark, param, priceD);
                if (lstOrder?.Count > 0)
                {
                    tmp = lstOrder.FirstOrDefault();
                }
            }
            if (tmp == null)
            {
                //实际支付价格
                double reallyPrice = priceD;
                var orderDate = ServerTime.DateTime;

                while (from == OrderFrom.Self && true)
                {
                    if (PayHelper.findByReallyPriceAndType(reallyPrice) > 0)
                    {
                        if (ConfigHelper.IsPayPriceAdd)
                        {
                            reallyPrice += 0.01;
                        }
                        else
                        {
                            reallyPrice -= 0.01;
                        }
                    }
                    else
                    {
                        break;
                    }
                    if (reallyPrice <= 0 || (ConfigHelper.PayMaxRandom > 0 && Math.Abs(priceD - reallyPrice) > ConfigHelper.PayMaxRandom))
                    {
                        return tmp;
                    }
                }

                tmp = new PayOrderEntity
                {
                    payId = orderDate.ToString("yyyyMMddHHmmssfff") + RndOrderId.Next().ToString().PadLeft(5).Substring(0, 5),
                    orderId = orderDate.ToString("yyyyMMddHHmmssfff") + RndOrderId.Next().ToString().PadLeft(5).Substring(0, 5),
                    createDate = orderDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    closeDate = orderDate.AddMinutes(ConfigHelper.PayTimeOutMinute).ToString("yyyy-MM-dd HH:mm:ss"),
                    param = param,
                    type = 0,
                    price = priceD,
                    reallyPrice = reallyPrice,
                    returnUrl = ConfigHelper.PayReturnUrl,
                    state = 0,
                    isAuto = 1,
                    remark = remark,
                    from = from
                };
                switch (from)
                {
                    case OrderFrom.Self:
                        break;
                    case OrderFrom._360:
                        OderRequest request = new OderRequest()
                        {
                            wszOrderId = tmp.payId,
                            wszUserId = param,
                            dwAmount = (uint)(priceD * 100),
                            wszProductDescription = remark,
                            wszProductId = remark,
                            tOrderCreateTime = new TimeSpan(orderDate.Ticks).Seconds
                        };
                        var response = new OderResponse();
                        var orderId = Qh360SdkWrapper.SDK360_Pay(request, ref response, SDK360_PAYSTATUS_CALLBACK);
                        if (orderId > 0)
                        {
                            tmp.payUrl = response.wszQrCode;
                            tmp.orderId = orderId.ToString();
                        }
                        else
                        {
                            //获取支付失败
                        }
                        break;
                    case OrderFrom.Other:
                        break;
                    default:
                        break;
                }
                PayHelper.CreateOrder(tmp);
            }
            return tmp;
        }

        private static void SDK360_PAYSTATUS_CALLBACK(uint dwTicket, int iOderStatus, int iPayChanel)
        {
            var tmp = PayHelper.findByOrderId(dwTicket.ToString());
            switch ((Sdk360PayStatus)iPayChanel)
            {
                case Sdk360PayStatus.Success:
                case Sdk360PayStatus.PaidBeforeNotify:
                case Sdk360PayStatus.PaidAfterNotify:
                    //todo 更新订单状态
                    switch ((Sdk360PayChannel)iPayChanel)
                    {
                        case Sdk360PayChannel.Wx:
                            break;
                        case Sdk360PayChannel.Ali:
                            break;
                        default:
                            break;
                    }
                    PaySuccess(new PayOrderEntity()
                    {
                         orderId = dwTicket.ToString(),
                          payId = dwTicket.ToString(),
                           
                    }
                    , ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    break;
                case Sdk360PayStatus.Refund:
                    break;
            }
        }

        public static CheckOrderEntity<PayOrderEntity> GetPayOrderInfo(string orderId)
        {
            var payOrder = PayHelper.getByOrderId(orderId);
            var result = new CheckOrderEntity<PayOrderEntity>() { data = payOrder };
            if (payOrder == null)
            {
                result.msg = "订单编号不存在";
                result.code = -1;
                return result;
            }

            result.code = 1;
            if (payOrder.state >= 1)
            {
                string url = payOrder.returnUrl;
                if (string.IsNullOrEmpty(url))
                {
                    url = ConfigHelper.PayReturnUrl;
                }
                if (!string.IsNullOrEmpty(url))
                {
                    result.data.returnUrl = url + "?" + payOrder.getNotifyContent();
                }
            }
            else if (payOrder.state == 0)
            {
                var priceD = Math.Round(BoxUtil.GetDoubleFromObject(payOrder.reallyPrice), 2);
                //0|自动 1|微信 2|支付宝 3|360
                if (payOrder.type == 0 || payOrder.type == 1)
                {
                    //微信支付码
                    PayQrcode wxPayQrcode = PayQrcodeUtil.findByPriceAndType(priceD, 1);
                    if (wxPayQrcode != null)
                    {
                        result.data.wxPayUrl = wxPayQrcode.GetPayUrl();
                    }
                    if (string.IsNullOrEmpty(result.data.wxPayUrl))
                    {
                        result.data.needUserPay = true;
                        result.data.wxPayUrl = ConfigHelper.PayWXJTM;
                    }
                }

                //0|自动 1|微信 2|支付宝 3|360
                if (payOrder.type == 0 || payOrder.type == 2)
                {
                    //优先固定码
                    PayQrcode zfbPayQrcode = PayQrcodeUtil.findByPriceAndType(priceD, 2);
                    if (zfbPayQrcode != null)
                    {
                        result.data.zfbPayUrl = zfbPayQrcode.GetPayUrl();
                    }
                    //其次根据用户ID自动生成码
                    if (string.IsNullOrEmpty(result.data.zfbPayUrl))
                    {
                        if (!string.IsNullOrEmpty(ConfigHelper.PayZFBDTM))
                        {
                            result.data.zfbPayUrl = "alipays://platformapi/startapp?appId=20000067&url=alipays%3A%2F%2Fplatformapi%2Fstartapp%3FappId%3D20000123%26actionType%3Dscan%26biz_data%3D%7B%22s%22%3A%22money%22%2C%22u%22%3A%22"
                                    + ConfigHelper.PayZFBDTM + "%22%2C%22a%22%3A%22"
                                    + payOrder.reallyPrice + "%22%2C%22m%22%3A%22" + HttpUtility.UrlEncode(payOrder.remark) + "%22%7D";
                        }
                    }
                    //再次固定收款码
                    if (string.IsNullOrEmpty(result.data.zfbPayUrl))
                    {
                        result.data.needUserPay = true;
                        result.data.zfbPayUrl = ConfigHelper.PayZFBJTM;
                    }
                }
            }

            result.data.date = ConvertToUnixTimestamp(BoxUtil.GetDateTimeFromObject(result.data.createDate));
            result.data.timeOut = ConfigHelper.PayTimeOutMinute;
            result.data.qq = ConfigHelper.PayQQKeFu;

            return result;
        }

        private static DateTime UTCTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);
        public static long ConvertToUnixTimestamp(DateTime date)
        {
            TimeSpan diff = date.ToUniversalTime() - UTCTime;
            return (long)diff.TotalMilliseconds;
        }

        public static DateTime ConvertToDateTimeByTimestamp(long tick)
        {
            var ts = TimeSpan.FromMilliseconds(tick);
            return new DateTime(ts.Ticks + UTCTime.Ticks);
        }

        public static CheckOrderEntity<string> ChangePayType(string orderId, int payType)
        {
            PayHelper.UpdatePayType(orderId, payType);
            var result = new CheckOrderEntity<string>
            {
                msg = "成功",
                code = 1
            };
            return result;
        }

        public static CheckOrderEntity<string> CheckPayOrderState(string orderId)
        {
            var result = new CheckOrderEntity<string>();
            var payOrder = PayHelper.getByOrderId(orderId);
            if (payOrder == null)
            {
                result.msg = "订单编号不存在";
                result.code = -1;
                return result;
            }
            if (Equals(payOrder.state, -1))
            {
                result.msg = "订单已过期";
                result.code = -1;
                return result;
            }
            else if (Equals(payOrder.state, 0))
            {
                result.msg = "订单未支付";
                result.code = -1;
                result.payType = payOrder.type;
                result.date = (int)new TimeSpan(BoxUtil.GetDateTimeFromObject(payOrder.closeDate).Ticks - ServerTime.DateTime.Ticks).TotalSeconds;
                return result;
            }

            var url = payOrder.returnUrl;
            if (string.IsNullOrEmpty(url))
            {
                url = ConfigHelper.PayReturnUrl;
            }

            if (string.IsNullOrEmpty(url))
            {
                result.msg = "未配置通知URL";
                result.code = -1;
                return result;
            }

            var param = payOrder.getNotifyContent();

            result.data = url + "?" + param;
            result.msg = "成功";
            result.code = 1;

            return result;
        }

        public static CheckOrderEntity<string> appHeart(string t, string sign)
        {
            var result = new CheckOrderEntity<string>();
            var jssign = CommonEncryptHelper.MD5(t + ConfigHelper.PayToken);
            if (!Equals(jssign, sign))
            {
                result.msg = "签名校验错误";
                result.code = -1;
                return result;
            }

            result.msg = "成功";
            result.code = 1;
            return result;
        }

        public static CheckOrderEntity<string> appPush(int type, string price, string remark, string t, string sign)
        {
            //1、验证签名
            var result = new CheckOrderEntity<string>();
            var jssign = CommonEncryptHelper.MD5(type + price + t + ConfigHelper.PayToken);
            if (!Equals(jssign, sign))
            {
                result.msg = "签名校验错误";
                result.code = -1;
                return result;
            }

            //2、验证是否已经处理过(根据付款时间和付款方式)
            var payTime = ConvertToDateTimeByTimestamp(BoxUtil.GetInt64FromObject(t));
            var tmp = PayHelper.findByPayDate(payTime, type);
            if (tmp != null)
            {
                result.msg = "重复推送";
                result.code = -1;
                return result;
            }

            //3、根据金额查询未付款订单
            var pushPrice = Math.Round(BoxUtil.GetDoubleFromObject(price), 2);
            var payOrder = PayHelper.findByReallyPriceAndState(pushPrice, 0);
            //if (payOrder == null)
            //{
            //    //4、根据金额查询已过期订单
            //    var lstTmpOrder = PayHelper.findByReallyPriceAndStateAndTime(pushPrice, -1, payTime.AddMinutes(-10), payTime);
            //    if (lstTmpOrder.Count > 0)
            //    {
            //        LogHelper.Log.Error("共找到" + lstTmpOrder.Count + "个匹配条件的过期订单！" + JsonConvert.SerializeObject(lstTmpOrder));
            //        if (lstTmpOrder.Where(p => !string.IsNullOrEmpty(p.param)).Select(p => p.param).Distinct().Count() > 0)
            //        {
            //            LogHelper.Log.Error("存在多个用户同时支付的情况！" + JsonConvert.SerializeObject(lstTmpOrder));
            //            result.msg = "存在多个用户同时支付的情况";
            //            result.code = -1;
            //            return result;
            //        }
            //        //4.1、按创建时间倒序取第一个
            //        payOrder = lstTmpOrder.FirstOrDefault();
            //    }
            //}

            var nowDate = ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
            if (payOrder == null)
            {
                payOrder = new PayOrderEntity
                {
                    payId = "无订单转账",
                    orderId = "无订单转账",
                    remark = remark,
                    createDate = nowDate,
                    payDate = nowDate,
                    closeDate = nowDate,
                    param = "无订单转账",
                    type = type,
                    price = pushPrice,
                    reallyPrice = pushPrice,
                    state = 1
                };
                PayHelper.CreateOrder(payOrder);
            }
            else
            {
                payOrder.type = type;
                PaySuccess(payOrder, nowDate);
            }

            result.msg = "成功";
            result.code = 1;
            return result;
        }

        private static void PaySuccess(PayOrderEntity payOrder, string payDate)
        {
            payOrder.state = 1;
            payOrder.payDate = payDate;
            payOrder.closeDate = payDate;
            PayHelper.UpdateOrder(payOrder);

            PayUtil.PushPayMessage(payOrder.payId, payOrder.param, payOrder.price, payOrder.reallyPrice, payOrder.type, payOrder.remark);
        }

        public static CheckOrderEntity<string> DelByOrder(string orderId)
        {
            var result = new CheckOrderEntity<string>();
            if (PayHelper.DelByOrder(orderId))
            {
                result.msg = "成功";
                result.code = 1;
            }
            else
            {
                result.msg = "删除失败";
                result.code = 1;
            }
            return result;
        }

        public static CheckOrderEntity<string> BuDan(string orderId)
        {
            var result = new CheckOrderEntity<string>();
            var payOrder = PayHelper.getByOrderId(orderId);
            if (payOrder == null)
            {
                result.msg = "订单编号不存在";
                result.code = -1;
                return result;
            }

            if (!Equals(payOrder.state, 1))
            {
                var nowDate = ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss");
                //默认微信
                payOrder.type = payOrder.type == 0 ? 1 : payOrder.type;
                payOrder.state = 1;
                payOrder.payDate = nowDate;
                payOrder.closeDate = nowDate;
                PayHelper.UpdateOrder(payOrder);
            }

            PayUtil.PushPayMessage(payOrder.payId, payOrder.param, payOrder.price, payOrder.reallyPrice, payOrder.type, payOrder.remark);

            result.msg = "成功";
            result.code = 1;

            return result;
        }
    }

    public class CheckOrderEntity<T>
    {
        public int code { get; set; }

        public T data { get; set; }

        public string msg { get; set; }

        public int date { get; set; }

        public int payType { get; set; }
    }
}