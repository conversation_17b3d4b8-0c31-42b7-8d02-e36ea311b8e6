"use strict";var escapedChars={b:"\b",f:"\f",n:"\n",r:"\r",t:"\t",'"':'"',"/":"/","\\":"\\"},A_CODE="a".charCodeAt();function parse(e){var r={},n=0,a=0,t=0;return{data:o("",!0),pointers:r};function o(r,n){var a;i(),b(r,"value");var s=u();switch(s){case"t":f("rue"),a=!0;break;case"f":f("alse"),a=!1;break;case"n":f("ull"),a=null;break;case'"':a=c();break;case"[":a=function(e){i();var r=[],n=0;if("]"==u())return r;l();for(;;){var a=e+"/"+n;r.push(o(a)),i();var t=u();if("]"==t)break;","!=t&&y(),i(),n++}return r}(r);break;case"{":a=function(e){i();var r={};if("}"==u())return r;l();for(;;){var n=p();'"'!=u()&&y();var a=c(),t=e+"/"+escapeJsonPointer(a);d(t,"key",n),b(t,"keyEnd"),i(),":"!=u()&&y(),i(),r[a]=o(t),i();var f=u();if("}"==f)break;","!=f&&y(),i()}return r}(r);break;default:l(),"-0123456789".indexOf(s)>=0?a=function(){var r="";"-"==e[t]&&(r+=u());r+="0"==e[t]?u():v(),"."==e[t]&&(r+=u()+v());"e"!=e[t]&&"E"!=e[t]||(r+=u(),"+"!=e[t]&&"-"!=e[t]||(r+=u()),r+=v());return+r}():h()}return b(r,"valueEnd"),i(),n&&t<e.length&&h(),a}function i(){e:for(;t<e.length;){switch(e[t]){case" ":a++;break;case"\t":a+=4;break;case"\r":a=0;break;case"\n":a=0,n++;break;default:break e}t++}}function c(){for(var e,r="";'"'!=(e=u());)"\\"==e?(e=u())in escapedChars?r+=escapedChars[e]:"u"==e?r+=s():y():r+=e;return r}function f(e){for(var r=0;r<e.length;r++)u()!==e[r]&&y()}function u(){g();var r=e[t];return t++,a++,r}function l(){t--,a--}function s(){for(var e=4,r=0;e--;){r<<=4;var n=u().toLowerCase();n>="a"&&n<="f"?r+=n.charCodeAt()-A_CODE+10:n>="0"&&n<="9"?r+=+n:y()}return String.fromCharCode(r)}function v(){for(var r="";e[t]>="0"&&e[t]<="9";)r+=u();if(r.length)return r;g(),h()}function b(e,r){d(e,r,p())}function d(e,n,a){r[e]=r[e]||{},r[e][n]=a}function p(){return{line:n,column:a,pos:t}}function h(){throw new SyntaxError("Unexpected token "+e[t]+" in JSON at position "+t)}function y(){l(),h()}function g(){if(t>=e.length)throw new SyntaxError("Unexpected end of JSON input")}}function stringify(e,r,n){if(validType(e)){var a,t,o=0;switch(typeof n){case"number":var i=n>10?10:n<0?0:Math.floor(n);n=i&&y(i," "),a=i,t=i;break;case"string":n=n.slice(0,10),a=0,t=0;for(var c=0;c<n.length;c++){switch(n[c]){case" ":t++;break;case"\t":t+=4;break;case"\r":t=0;break;case"\n":t=0,o++;break;default:throw new Error("whitespace characters not allowed in JSON")}a++}break;default:n=void 0}var f="",u={},l=0,s=0,v=0;return function e(r,a,t){h(t,"value");switch(typeof r){case"number":case"boolean":d(""+r);break;case"string":d(quoted(r));break;case"object":null===r?d("null"):"function"==typeof r.toJSON?d(quoted(r.toJSON())):Array.isArray(r)?function o(){if(r.length){d("[");for(var e=a+1,o=0;o<r.length;o++){o&&d(","),p(e);var i=validType(r[o])?r[o]:null,c=t+"/"+o;!function e(r,a,t){h(t,"value");switch(typeof r){case"number":case"boolean":d(""+r);break;case"string":d(quoted(r));break;case"object":null===r?d("null"):"function"==typeof r.toJSON?d(quoted(r.toJSON())):Array.isArray(r)?o():function(){var o=Object.keys(r);if(o.length){d("{");for(var i=a+1,c=0;c<o.length;c++){var f=o[c],u=r[f];if(validType(u)){c&&d(",");var l=t+"/"+escapeJsonPointer(f);p(i),h(l,"key"),d(quoted(f)),h(l,"keyEnd"),d(":"),n&&d(" "),e(u,i,l)}}p(a),d("}")}else d("{}")}()}h(t,"valueEnd");function o(){if(r.length){d("[");for(var n=a+1,o=0;o<r.length;o++){o&&d(","),p(n);var i=validType(r[o])?r[o]:null,c=t+"/"+o;e(i,n,c)}p(a),d("]")}else d("[]")}}(i,e,c)}p(a),d("]")}else d("[]")}():function(){var o=Object.keys(r);if(o.length){d("{");for(var i=a+1,c=0;c<o.length;c++){var f=o[c],u=r[f];if(validType(u)){c&&d(",");var l=t+"/"+escapeJsonPointer(f);p(i),h(l,"key"),d(quoted(f)),h(l,"keyEnd"),d(":"),n&&d(" "),e(u,i,l)}}p(a),d("}")}else d("{}")}()}h(t,"valueEnd");function o(){if(r.length){d("[");for(var n=a+1,o=0;o<r.length;o++){o&&d(","),p(n);var i=validType(r[o])?r[o]:null,c=t+"/"+o;e(i,n,c)}p(a),d("]")}else d("[]")}}(e,0,""),{json:f,pointers:u}}function b(e,r,a){h(a,"value");switch(typeof e){case"number":case"boolean":d(""+e);break;case"string":d(quoted(e));break;case"object":if(e===null)d("null");else if(typeof e.toJSON=="function")d(quoted(e.toJSON()));else if(Array.isArray(e))t();else o()}h(a,"valueEnd");function t(){if(e.length){d("[");var n=r+1;for(var t=0;t<e.length;t++){if(t)d(",");p(n);var o=validType(e[t])?e[t]:null;var i=a+"/"+t;b(o,n,i)}p(r);d("]")}else{d("[]")}}function o(){var t=Object.keys(e);if(t.length){d("{");var o=r+1;for(var i=0;i<t.length;i++){var c=t[i];var f=e[c];if(validType(f)){if(i)d(",");var u=a+"/"+escapeJsonPointer(c);p(o);h(u,"key");d(quoted(c));h(u,"keyEnd");d(":");if(n)d(" ");b(f,o,u)}}p(r);d("}")}else{d("{}")}}}function d(e){s+=e.length,v+=e.length,f+=e}function p(e){if(n){for(f+="\n"+y(e,n),l++,s=0;e--;)o?(l+=o,s=t):s+=t,v+=a;v+=1}}function h(e,r){u[e]=u[e]||{},u[e][r]={line:l,column:s,pos:v}}function y(e,r){return Array(e+1).join(r)}}var VALID_TYPES=["number","boolean","string","object"];function validType(e){return VALID_TYPES.indexOf(typeof e)>=0}var ESC_QUOTE=/"|\\/g,ESC_B=/[\b]/g,ESC_F=/\f/g,ESC_N=/\n/g,ESC_R=/\r/g,ESC_T=/\t/g;function quoted(e){return'"'+(e=e.replace(ESC_QUOTE,"\\$&").replace(ESC_F,"\\f").replace(ESC_B,"\\b").replace(ESC_N,"\\n").replace(ESC_R,"\\r").replace(ESC_T,"\\t"))+'"'}var ESC_0=/~/g,ESC_1=/\//g;function escapeJsonPointer(e){return e.replace(ESC_0,"~0").replace(ESC_1,"~1")}