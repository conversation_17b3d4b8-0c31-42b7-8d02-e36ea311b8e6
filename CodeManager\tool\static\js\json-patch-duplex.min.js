var jsonpatch,__extends=this&&this.__extends||function(e,t){function r(){this.constructor=e}for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)},OriginalError=Error;!function(e){function t(e){return-1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function r(e){for(var t=0,r=v.length;r>t;t++)if(v[t].obj===e)return v[t]}function n(e){return"object"==typeof e?JSON.parse(JSON.stringify(e)):e}function o(e){for(var t,r=0,n=v.length;n>r;r++)if(v[r].obj===e.object){t=v[r];break}a(t.value,e.object,e.patches,""),e.patches.length&&s(t.value,e.patches);var o=e.patches;return o.length>0&&(e.patches=[],e.callback&&e.callback(o)),o}function a(e,r,o,i){for(var s=h(r),p=h(e),u=!1,c=p.length-1;c>=0;c--){var l=e[v=p[c]];if(r.hasOwnProperty(v)){var f=r[v];"object"==typeof l&&null!=l&&"object"==typeof f&&null!=f?a(l,f,o,i+"/"+t(v)):l!=f&&(!0,o.push({op:"replace",path:i+"/"+t(v),value:n(f)}))}else o.push({op:"remove",path:i+"/"+t(v)}),u=!0}if(u||s.length!=p.length)for(c=0;c<s.length;c++){var v=s[c];e.hasOwnProperty(v)||o.push({op:"add",path:i+"/"+t(v),value:n(r[v])})}}function i(e){for(var t,r=0,n=e.length;n>r;){if(!((t=e.charCodeAt(r))>=48&&57>=t))return!1;r++}return!0}function s(e,t,r){for(var n,o,a=!1,s=0,p=t.length;p>s;){n=t[s],s++;for(var h=(n.path||"").split("/"),v=e,d=1,O=h.length,m=void 0;;){if(o=h[d],r&&void 0===m&&(void 0===v[o]?m=h.slice(0,d).join("/"):d==O-1&&(m=n.path),void 0!==m&&this.validator(n,s-1,e,m)),d++,void 0===o&&d>=O){a=f[n.op].call(n,v,o,e);break}if(u(v)){if("-"===o)o=v.length;else{if(r&&!i(o))throw new g("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",s-1,n.path,n);o=parseInt(o,10)}if(d>=O){if(r&&"add"===n.op&&o>v.length)throw new g("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",s-1,n.path,n);a=l[n.op].call(n,v,o,e);break}}else if(o&&-1!=o.indexOf("~")&&(o=o.replace(/~1/g,"/").replace(/~0/g,"~")),d>=O){a=c[n.op].call(n,v,o,e);break}v=v[o]}}return a}function p(e){if(void 0===e)return!0;if("array"==typeof e||"object"==typeof e)for(var t in e)if(p(e[t]))return!0;return!1}if(!e.observe){var u,h=function(e){if(u(e)){for(var t=new Array(e.length),r=0;r<t.length;r++)t[r]=r.toString();return t}if(Object.keys)return Object.keys(e);t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t},c={add:function(e,t){return e[t]=this.value,!0},remove:function(e,t){return delete e[t],!0},replace:function(e,t){return e[t]=this.value,!0},move:function(e,t,r){var n={op:"_get",path:this.from};return s(r,[n]),s(r,[{op:"remove",path:this.from}]),s(r,[{op:"add",path:this.path,value:n.value}]),!0},copy:function(e,t,r){var n={op:"_get",path:this.from};return s(r,[n]),s(r,[{op:"add",path:this.path,value:n.value}]),!0},test:function(e,t){return function e(t,r){switch(typeof t){case"undefined":case"boolean":case"string":case"number":return t===r;case"object":if(null===t)return null===r;if(u(t)){if(!u(r)||t.length!==r.length)return!1;for(var n=0,o=t.length;o>n;n++)if(!e(t[n],r[n]))return!1;return!0}var a=h(r).length;if(h(t).length!==a)return!1;for(n=0;a>n;n++)if(!e(t[n],r[n]))return!1;return!0;default:return!1}}(e[t],this.value)},_get:function(e,t){this.value=e[t]}},l={add:function(e,t){return e.splice(t,0,this.value),!0},remove:function(e,t){return e.splice(t,1),!0},replace:function(e,t){return e[t]=this.value,!0},move:c.move,copy:c.copy,test:c.test,_get:c._get},f={add:function(e){for(var t in f.remove.call(this,e),this.value)this.value.hasOwnProperty(t)&&(e[t]=this.value[t]);return!0},remove:function(e){for(var t in e)e.hasOwnProperty(t)&&c.remove.call(this,e,t);return!0},replace:function(e){return s(e,[{op:"remove",path:this.path}]),s(e,[{op:"add",path:this.path,value:this.value}]),!0},move:c.move,copy:c.copy,test:function(e){return JSON.stringify(e)===JSON.stringify(this.value)},_get:function(e){this.value=e}},v=[],d=function(){return function(e){this.observers=[],this.obj=e}}(),O=function(){return function(e,t){this.callback=e,this.observer=t}}();e.unobserve=function(e,t){o(t),clearTimeout(t.next),function(e,t){for(var r=0,n=e.observers.length;n>r;r++)if(e.observers[r].observer===t)return void e.observers.splice(r,1)}(r(e),t)},e.observe=function(e,t){var a,i=r(e);if(i?a=function(e,t){for(var r=0,n=e.observers.length;n>r;r++)if(e.observers[r].callback===t)return e.observers[r].observer}(i,t):(i=new d(e),v.push(i)),a)return a;if(a={},i.value=n(e),t){a.callback=t,a.next=null;var s=this.intervals||[100,1e3,1e4,6e4];if(void 0===s.push)throw new OriginalError("jsonpatch.intervals must be an array");var p=0,u=function(){o(a)},h=function(){clearTimeout(a.next),a.next=setTimeout(function(){u(),p=0,a.next=setTimeout(c,s[p++])},0)},c=function(){u(),p==s.length&&(p=s.length-1),a.next=setTimeout(c,s[p++])};"undefined"!=typeof window&&(window.addEventListener?(window.addEventListener("mousedown",h),window.addEventListener("mouseup",h),window.addEventListener("keydown",h)):(document.documentElement.attachEvent("onmousedown",h),document.documentElement.attachEvent("onmouseup",h),document.documentElement.attachEvent("onkeydown",h))),a.next=setTimeout(c,s[p++])}return a.patches=[],a.object=e,i.observers.push(new O(t,a)),a},e.generate=o,u=Array.isArray?Array.isArray:function(e){return e.push&&"number"==typeof e.length},e.apply=s,e.compare=function(e,t){var r=[];return a(e,t,r,""),r};var g=function(e){function t(t,r,n,o,a){e.call(this,t),this.message=t,this.name=r,this.index=n,this.operation=o,this.tree=a}return __extends(t,e),t}(OriginalError);e.JsonPatchError=g,e.Error=g,e.hasUndefined=p,e.validator=function(t,r,n,o){if("object"!=typeof t||null===t||u(t))throw new g("Operation is not an object","OPERATION_NOT_AN_OBJECT",r,t,n);if(!c[t.op])throw new g("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",r,t,n);if("string"!=typeof t.path)throw new g("Operation `path` property is not a string","OPERATION_PATH_INVALID",r,t,n);if(("move"===t.op||"copy"===t.op)&&"string"!=typeof t.from)throw new g("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",r,t,n);if(("add"===t.op||"replace"===t.op||"test"===t.op)&&void 0===t.value)throw new g("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",r,t,n);if(("add"===t.op||"replace"===t.op||"test"===t.op)&&p(t.value))throw new g("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",r,t,n);if(n)if("add"==t.op){var a=t.path.split("/").length,i=o.split("/").length;if(a!==i+1&&a!==i)throw new g("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",r,t,n)}else if("replace"===t.op||"remove"===t.op||"_get"===t.op){if(t.path!==o)throw new g("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",r,t,n)}else if("move"===t.op||"copy"===t.op){var s={op:"_get",path:t.from,value:void 0},h=e.validate([s],n);if(h&&"OPERATION_PATH_UNRESOLVABLE"===h.name)throw new g("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",r,t,n)}},e.validate=function(e,t){try{if(!u(e))throw new g("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)t=JSON.parse(JSON.stringify(t)),s.call(this,t,e,!0);else for(var r=0;r<e.length;r++)this.validator(e[r],r)}catch(e){if(e instanceof g)return e;throw e}}}}(jsonpatch||(jsonpatch={})),"undefined"!=typeof exports&&(exports.apply=jsonpatch.apply,exports.observe=jsonpatch.observe,exports.unobserve=jsonpatch.unobserve,exports.generate=jsonpatch.generate,exports.compare=jsonpatch.compare,exports.validate=jsonpatch.validate,exports.validator=jsonpatch.validator,exports.JsonPatchError=jsonpatch.JsonPatchError,exports.Error=jsonpatch.Error);