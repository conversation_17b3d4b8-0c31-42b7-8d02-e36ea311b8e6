body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,pre,form,fieldset,legend,button,input,textarea,th,td{margin:0;padding:0}body{font-family:"Helvetica Neue",Helvetica,"Segoe UI",Aria<PERSON>,freesans,sans-serif;font-size:14px;line-height:1.8}ul,ol{list-style:none}a{text-decoration:none;color:#3fa7cb}sup{vertical-align:text-top}sub{vertical-align:text-bottom}fieldset,img{border:0}button,input,select,textarea{font-size:100%}table{border-collapse:collapse;border-spacing:0}img{-ms-interpolation-mode:bicubic}textarea{resize:vertical}article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary{display:block}.clearfix:after{content:'\20';display:block;height:0;clear:both}.clearfix{zoom:1}.clear{clear:both;height:0;line-height:0;font-size:0;visibility:hidden;overflow:hidden}.wordwrap{word-break:break-all;word-wrap:break-word}.left{float:left}.right{float:right}.hide{display:none}html,body{height:100%;width:100%}html,.fullscreen{background:#F9F9F7}.mod-page-body{color:#454545;height:auto;_height:100%;width:100%;min-height:100%;margin:0 auto}.mod-page-body .t-remark{color:#959595}.mod-page-body a.a-normal{color:#3fa7cb}.mod-page-body a.a-normal:hover{color:#3cbce7}.mod-page-body a.a-normal:visited{color:#3fa7cb}.mod-page-body a.a-incontent{color:#454545}.mod-page-body a.a-incontent:hover{color:#3fa7cb}.mod-page-body a.a-insist{color:#f46e6e}.mod-page-body a.a-insist:hover{color:#c55454}.mod-page-body .mod-page-main{padding-bottom:60px;margin:0 auto}.mod-footer{padding-top:23px;padding-bottom:15px;width:100%;position:relative;margin-top:-54px;clear:both}.mod-footer .footer-box{text-align:center;overflow:hidden;color:#666;font-size:12px}.mod-footer .footer-box .x-beian{color:#666}#alienfeBackToTop{display:block;width:38px;height:38px;background:url(../image/to-top.png) no-repeat;position:fixed;right:20px;bottom:20px;z-index:3;cursor:pointer;text-indent:-999em}#alienfeBackToTop:hover{background:url(../image/to-top-hover.png) no-repeat}.custom-scroll-bar{overflow-y:auto;overflow-x:hidden}.custom-scroll-bar::-webkit-scrollbar,.custom-scroll-bar::-webkit-scrollbar-thumb{overflow:visible}.custom-scroll-bar::-webkit-scrollbar{width:16px;background-color:#e2e2e2}.custom-scroll-bar::-webkit-scrollbar-thumb{background-color:#999;border-radius:16px;border:3px solid #e2e2e2}.custom-scroll-bar::-webkit-scrollbar-button{display:none}.mod-zoomdetect{width:100%;height:40px;background:#FEFFE5;line-height:40px;font-size:16px;text-align:center;border-bottom:1px solid #E7CF7B;user-select:none;clear:both}.mod-zoomdetect q{font-weight:bold;color:#f00}.mod-zoomdetect .wzd-btnclose{float:right;margin:12px 10px 0 0;line-height:20px;font-size:14px;text-decoration:none;display:block;height:17px;width:18px;text-indent:-999em;background:url(../image/yellowtipclose.png) center center no-repeat}.mod-zoomdetect .wzd-close:hover{text-decoration:none}.mod-zoomdetect .wzd-nevertip{text-decoration:underline;color:#2383C2;font-size:14px}.mod-zoomdetect .wzd-nevertip:hover{color:#23a3C2}.mod-preview h1,.item-content h1,.mod-preview h2,.item-content h2,.mod-preview h3,.item-content h3,.mod-preview h4,.item-content h4,.mod-preview h5,.item-content h5,.mod-preview h6,.item-content h6{font-weight:bold;color:#111;line-height:1em;margin:15px 0}.mod-preview h4,.item-content h4,.mod-preview h5,.item-content h5,.mod-preview h6,.item-content h6{font-weight:bold}.mod-preview h1,.item-content h1{font-size:1.5em}.mod-preview h2,.item-content h2{font-size:1.3em;border-bottom:1px solid #ddd;padding-bottom:8px}.mod-preview h3,.item-content h3{font-size:1.2em}.mod-preview h4,.item-content h4{font-size:1.1em}.mod-preview h5,.item-content h5{font-size:1em}.mod-preview h6,.item-content h6{font-size:0.9em}.mod-preview blockquote,.item-content blockquote{color:#666666;margin:10px;padding-left:1em;border-left:0.5em #EEE solid}.mod-preview hr,.item-content hr{display:block;height:2px;border:0;border-top:1px solid #aaa;border-bottom:1px solid #eee;margin:1em 0;padding:0}.mod-preview pre,.item-content pre,.mod-preview code,.item-content code{color:#df5000;border-radius:3px;border:1px solid #eee;padding:10px;overflow:auto;font-size:14px;line-height:1.45;background-color:#f7f7f7}.mod-preview pre,.item-content pre{white-space:pre;white-space:pre-wrap;word-wrap:break-word;margin:10px 0}.mod-preview code,.item-content code{padding:0 3px 0 3px}.mod-preview sub,.item-content sub,.mod-preview sup,.item-content sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.mod-preview sup,.item-content sup{top:-0.5em}.mod-preview sub,.item-content sub{bottom:-0.25em}.mod-preview ul,.item-content ul,.mod-preview ol,.item-content ol{margin:1em 0;padding:0 0 0 2em}.mod-preview li p:last-child,.item-content li p:last-child{margin:0}.mod-preview dd,.item-content dd{margin:0 0 0 2em}.mod-preview img,.item-content img{border:0;-ms-interpolation-mode:bicubic;vertical-align:middle}.mod-preview table,.item-content table{border-collapse:collapse;border-spacing:0}.mod-preview td,.item-content td,.mod-preview th,.item-content th{vertical-align:top;padding:4px 10px;border:1px solid #bbb}.mod-preview tr:nth-child(even) td,.item-content tr:nth-child(even) td,.mod-preview tr:nth-child(even) th,.item-content tr:nth-child(even) th{background:#eee}.mod-preview pre code,.item-content pre code{border:0px !important;display:block;color:#4d4d4c;padding:0.5em;background:transparent !important;line-height:1.3em}.mod-preview pre code,.item-content pre code,.mod-preview pre .subst,.item-content pre .subst,.mod-preview pre .tag .title,.item-content pre .tag .title,.mod-preview pre .lisp .title,.item-content pre .lisp .title,.mod-preview pre .clojure .built_in,.item-content pre .clojure .built_in,.mod-preview pre .nginx .title,.item-content pre .nginx .title{color:black}.mod-preview pre .string,.item-content pre .string,.mod-preview pre .title,.item-content pre .title,.mod-preview pre .constant,.item-content pre .constant,.mod-preview pre .parent,.item-content pre .parent,.mod-preview pre .tag .value,.item-content pre .tag .value,.mod-preview pre .rules .value,.item-content pre .rules .value,.mod-preview pre .rules .value .number,.item-content pre .rules .value .number,.mod-preview pre .preprocessor,.item-content pre .preprocessor,.mod-preview pre .ruby .symbol,.item-content pre .ruby .symbol,.mod-preview pre .ruby .symbol .string,.item-content pre .ruby .symbol .string,.mod-preview pre .aggregate,.item-content pre .aggregate,.mod-preview pre .template_tag,.item-content pre .template_tag,.mod-preview pre .django .variable,.item-content pre .django .variable,.mod-preview pre .smalltalk .class,.item-content pre .smalltalk .class,.mod-preview pre .addition,.item-content pre .addition,.mod-preview pre .flow,.item-content pre .flow,.mod-preview pre .stream,.item-content pre .stream,.mod-preview pre .bash .variable,.item-content pre .bash .variable,.mod-preview pre .apache .tag,.item-content pre .apache .tag,.mod-preview pre .apache .cbracket,.item-content pre .apache .cbracket,.mod-preview pre .tex .command,.item-content pre .tex .command,.mod-preview pre .tex .special,.item-content pre .tex .special,.mod-preview pre .erlang_repl .function_or_atom,.item-content pre .erlang_repl .function_or_atom,.mod-preview pre .markdown .header,.item-content pre .markdown .header{color:#df5000}.mod-preview pre .comment,.item-content pre .comment,.mod-preview pre .annotation,.item-content pre .annotation,.mod-preview pre .template_comment,.item-content pre .template_comment,.mod-preview pre .diff .header,.item-content pre .diff .header,.mod-preview pre .chunk,.item-content pre .chunk,.mod-preview pre .markdown .blockquote,.item-content pre .markdown .blockquote{color:#888}.mod-preview pre .number,.item-content pre .number,.mod-preview pre .date,.item-content pre .date,.mod-preview pre .regexp,.item-content pre .regexp,.mod-preview pre .literal,.item-content pre .literal,.mod-preview pre .smalltalk .symbol,.item-content pre .smalltalk .symbol,.mod-preview pre .smalltalk .char,.item-content pre .smalltalk .char,.mod-preview pre .go .constant,.item-content pre .go .constant,.mod-preview pre .change,.item-content pre .change,.mod-preview pre .markdown .bullet,.item-content pre .markdown .bullet,.mod-preview pre .markdown .link_url,.item-content pre .markdown .link_url{color:#080}.mod-preview pre .label,.item-content pre .label,.mod-preview pre .javadoc,.item-content pre .javadoc,.mod-preview pre .ruby .string,.item-content pre .ruby .string,.mod-preview pre .decorator,.item-content pre .decorator,.mod-preview pre .filter .argument,.item-content pre .filter .argument,.mod-preview pre .localvars,.item-content pre .localvars,.mod-preview pre .array,.item-content pre .array,.mod-preview pre .attr_selector,.item-content pre .attr_selector,.mod-preview pre .important,.item-content pre .important,.mod-preview pre .pseudo,.item-content pre .pseudo,.mod-preview pre .pi,.item-content pre .pi,.mod-preview pre .doctype,.item-content pre .doctype,.mod-preview pre .deletion,.item-content pre .deletion,.mod-preview pre .envvar,.item-content pre .envvar,.mod-preview pre .shebang,.item-content pre .shebang,.mod-preview pre .apache .sqbracket,.item-content pre .apache .sqbracket,.mod-preview pre .nginx .built_in,.item-content pre .nginx .built_in,.mod-preview pre .tex .formula,.item-content pre .tex .formula,.mod-preview pre .erlang_repl .reserved,.item-content pre .erlang_repl .reserved,.mod-preview pre .prompt,.item-content pre .prompt,.mod-preview pre .markdown .link_label,.item-content pre .markdown .link_label,.mod-preview pre .vhdl .attribute,.item-content pre .vhdl .attribute,.mod-preview pre .clojure .attribute,.item-content pre .clojure .attribute,.mod-preview pre .coffeescript .property,.item-content pre .coffeescript .property{color:#88f}.mod-preview pre .keyword,.item-content pre .keyword{color:#48b;font-weight:bold}.mod-preview pre .title,.item-content pre .title{color:#454545}.mod-preview pre .markdown .emphasis,.item-content pre .markdown .emphasis,.mod-preview pre .comment,.item-content pre .comment{font-style:italic}.mod-preview pre .nginx .built_in,.item-content pre .nginx .built_in{font-weight:normal}.mod-preview pre .coffeescript .javascript,.item-content pre .coffeescript .javascript,.mod-preview pre .javascript .xml,.item-content pre .javascript .xml,.mod-preview pre .tex .formula,.item-content pre .tex .formula,.mod-preview pre .xml .javascript,.item-content pre .xml .javascript,.mod-preview pre .xml .vbscript,.item-content pre .xml .vbscript,.mod-preview pre .xml .css,.item-content pre .xml .css,.mod-preview pre .xml .cdata,.item-content pre .xml .cdata{opacity:0.5}.mod-preview ul,.item-content ul{margin:0.5em 0;padding:0 0 0 2em}.mod-preview ul li,.item-content ul li{list-style:disc}.mod-page-body .mod-pagecontent{padding:20px 10px 0;min-width:980px;max-width:1300px;margin:0 auto}img.x-logo{width:30px;border-radius:15px;position:relative;top:8px}.mod-topbar ul li.x-searchitem{margin:0 15px}.q-serchform{box-sizing:border-box;margin:0;padding:0;color:#595959;font-size:14px;font-variant:tabular-nums;line-height:1.5;list-style:none;-webkit-font-feature-settings:"tnum";font-feature-settings:"tnum";position:relative;display:inline-block;width:100%;text-align:start}.q-serchform .ant-input-prefix{color:#8c8c8c;background-color:rgba(0,0,0,0);position:absolute;top:50%;left:12px;z-index:2;display:flex;align-items:center;line-height:0;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.q-serchform .ipt-text{background:#f5f5f5;border:none;outline:none;box-shadow:none;min-height:100%;position:relative;text-align:inherit;text-overflow:ellipsis;box-sizing:border-box;margin:0;font-variant:tabular-nums;list-style:none;-webkit-font-feature-settings:"tnum";font-feature-settings:"tnum";display:inline-block;width:100%;height:32px;padding:4px 11px 4px 34px;color:#595959;font-size:14px;line-height:1.5;background-image:none;border-radius:4px}.mod-topbar{overflow:hidden;position:relative;z-index:999;width:100%;height:60px;background:#fff;box-shadow:0 1px 2px 0 rgba(0,0,0,0.06);user-select:none}.mod-topbar .wrapper-box{min-width:980px;margin:0 auto ;height:100%}.mod-topbar .mainnav-box{float:left;margin-left:20px}.mod-topbar ul,.mod-topbar ul li{margin:0;padding:0}.mod-topbar .q-menubox{height:100%}.mod-topbar .q-menubox .q-menuitem{top:0;border-bottom:2px solid rgba(0,0,0,0);border-top:3px solid rgba(0,0,0,0);padding:0;position:relative;display:inline-block;vertical-align:bottom;line-height:56px}.mod-topbar .q-menubox .q-sp{float:left;font-size:14px;text-align:center;color:#ccc;margin-top:8px;display:none}.mod-topbar .q-menubox .q-menuitem a{margin:0 5px;border:1px solid transparent;display:block;color:#595959;position:relative;font-size:14px;line-height:56px;padding:0 5px}.mod-topbar .q-menubox .q-menuitem a:hover,.mod-topbar .q-menubox .q-menuitem a.x-focus{color:#6a0;border-bottom:1px solid #6a0}.mod-topbar .q-menuitem a img{position:relative;left:0;top:-4px}.mod-topbar .q-menuitem.q-widget a{padding-right:0}.mod-topbar .subnav-box{float:right;height:100%;margin-right:20px}.mod-topbar .q-navbox{height:100%}.mod-topbar .q-navbox .q-navitem{float:left;display:block;font-size:14px;text-align:center;padding:0px 2px;margin-top:2px;height:56px;line-height:36px}.mod-topbar .q-navbox .q-sp{float:left;font-size:12px;text-align:center;color:#ccc;margin-top:2px;line-height:56px}.mod-topbar .q-navbox .q-navitem a{padding:3px 5px;line-height:56px;color:#888}.mod-topbar .q-navbox .q-navitem a:hover{color:#6a0;border-bottom:1px solid #6a0}.mod-line{font-size:0}.mod-line img{height:2px;width:100%}.mod-xother{position:absolute;right:10px;top:20px;width:180px}.mod-xother a.x-xdld{color:#f00;font-weight:bold}.mod-xother a.x-xdld:hover{text-decoration:underline;color:#ff0}html{background:#f9f9f9}.mod-postdetail{min-width:900px;margin:0 auto;border:1px solid #ddd;background:#fff}.mod-postdetail.x-maxwidth{max-width:1024px}.mod-box-content{margin-bottom:15px}.module-title{font-size:20px;margin:10px ;font-weight:normal}.mod-box-content .item-content{padding:20px;border-bottom:1px dotted #ddd;overflow:hidden}.mod-box-content .item-content img{max-width:100%}.mod-box-content .item-footer{color:#aaa;padding:10px 20px;font-size:14px;cursor:default;overflow:hidden}.mod-box-content .item-footer .box-left{color:#aaa}.mod-box-content .item-footer .box-left .q-tag{margin-left:10px;color:#aaa}.mod-box-content .item-footer .box-left .q-tag:hover{color:#666}.mod-box-content .item-footer .box-right{float:right}.mod-box-content .item-footer .box-right .x-postdate{font-family:Georgia,Tahoma,Helvetica,arial}.mod-box-content .item-footer .box-right .b-item{margin-left:10px;color:#aaa}.mod-box-content .item-footer .box-right .b-item:hover{color:#888}.mod-postdetail .mod-box-cmt{padding:5px 0 20px;overflow:hidden}.mod-box-cmt .item-form{padding:15px 20px}.mod-box-cmt ul,.mod-box-cmt ul li{padding:0;margin:0}.mod-box-cmt .box-input li{position:relative;margin:10px 0}.mod-box-cmt .box-input li label{position:absolute;top:0px;left:5px;padding-top:4px;border-right:1px dotted #aaa;padding-right:5px;font-size:14px;color:#b4b4b4}.mod-box-cmt .box-input li .lbl-comment{width:14px;top:0px;padding:7px 4px 3px 2px}.mod-box-cmt .box-input li input{height:27px;line-height:18px;padding-left:40px;border:1px solid #ccc;-webkit-box-shadow:2px 2px 2px #eee inset;-moz-box-shadow:2px 2px 2px #eee inset;box-shadow:2px 2px 2px #eee inset;font-size:14px}.mod-box-cmt .box-input li input:hover,#content:hover{border:1px solid #999}#author_name,#author_email,#author_url{width:300px}#content{border:1px solid #ccc;-webkit-box-shadow:2px 2px 2px #eee inset;-moz-box-shadow:2px 2px 2px #eee inset;box-shadow:2px 2px 2px #eee inset;width:570px;height:98px;line-height:18px;padding:5px 10px 5px 30px;resize:none;font-size:14px}.mod-box-cmt .box-input li .invalid{border:1px dotted #f00}#lblErrorMsg{height:18px;border-right:none;position:static;font-size:14px;color:red}#btnSubmitCmt{color:#777;border:solid 1px #ccc;font-size:12px;height:27px;padding:0 15px;cursor:pointer;background:-webkit-gradient(linear, left top, left bottom, from(#fff), to(#eee));background:-moz-linear-gradient(top, #fff, #eee)}#btnSubmitCmt:hover{background:-webkit-gradient(linear, left top, left bottom, from(#eee), to(#ddd));background:-moz-linear-gradient(top, #eee, #ddd);color:#b00}.mod-box-cmt .item-cmtlist{padding:15px 20px;font-size:14px}.mod-box-cmt .q-cmtitem{padding:10px 0;border-top:1px dotted #ccc}.mod-box-cmt .q-cmtitem .q-content{white-space:pre-line}.mod-box-cmt .q-cmtmore{margin:15px auto 0;background:#f1f1f1;text-align:center;padding:5px;color:#777}.mod-box-cmt .q-cmtmore .x-highlight{padding:0 5px;color:#f00;font-size:1.2em}.mod-box-cmt .q-act{float:right}.mod-box-cmt .q-act .a-reply{margin-left:10px;padding:1px 3px}.mod-box-cmt .q-act .a-reply:hover{background:#3FA7CB;color:#fff}.mod-box-cmt .q-parent-content{padding:1px 10px 5px;margin:10px 20px;font-style:italic;background:#fff;color:#777;border:#ccc solid 1px}.mod-box-cmt .q-parent-content legend{padding:5px}.mod-related-articles{padding:5px 0 15px;overflow:hidden}.mod-related-articles .box-articles{padding:0;margin:15px 0 0}.mod-related-articles .box-articles .ra-item{padding:0;margin:0 0 0 20px;float:left;width:470px;line-height:24px}.mod-related-articles .box-articles .ra-item a{color:#666;text-decoration:underline;font-size:14px}.mod-related-articles .box-articles .ra-item a:hover{color:#b00}.mod-related-articles .box-articles .ra-item span{color:#0066cc}.mod-related-articles .mod-line,.mod-box-cmt .mod-line{padding:0 10px}.mod-related-articles .module-title,.mod-box-cmt .module-title{margin:10px 10px 0px }.mod-adv{padding:10px;border:1px solid #ddd;margin:10px 20px;background:#fcfcfc}.mod-adv a{color:#f90;text-decoration:underline}.mod-adv a:hover{color:#f00}.mod-adv .x-title{font-size:18px}.fullscreen{width:100%;height:100%;position:fixed;top:0;left:0;overflow-y:auto;margin:0 auto}.btn-request-fullscreen{font-size:14px;float:right;margin:5px 10px 0 0;cursor:pointer;color:#f00}.btn-request-fullscreen:hover{color:#000}.fullscreen-yes{position:fixed;top:2px;right:5px;z-index:5000;background:#fff;border:1px solid #aaa;border-radius:4px;line-height:28px;padding:0 10px}.fullscreen-yes:hover{color:#000}.mod-pay-encourage{margin:4px 10px 0 0}.mod-pay-encourage:hover img{opacity:0.7}