﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="FreeSql" version="3.2.680" targetFramework="net472" />
  <package id="FreeSql.Provider.MySql" version="3.2.680" targetFramework="net472" />
  <package id="FreeSql.Provider.Sqlite" version="3.2.680" targetFramework="net472" />
  <package id="Google.Protobuf" version="3.21.7" targetFramework="net472" />
  <package id="jQuery" version="1.6.4" targetFramework="net472" />
  <package id="K4os.Compression.LZ4" version="1.2.16" targetFramework="net472" />
  <package id="K4os.Compression.LZ4.Streams" version="1.2.16" targetFramework="net472" />
  <package id="K4os.Hash.xxHash" version="1.0.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.JS" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.SignalR.SystemWeb" version="2.4.3" targetFramework="net472" />
  <package id="Microsoft.Owin" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="2.1.0" targetFramework="net472" />
  <package id="MySql.Data" version="8.0.31" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net45" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net472" />
  <package id="Qiniu.SDK" version="8.0.0" targetFramework="net45" requireReinstallation="true" />
  <package id="StackExchange.Redis.StrongName" version="1.1.608" targetFramework="net472" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="*********" targetFramework="net45" requireReinstallation="true" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Data.SQLite.Core" version="*********" targetFramework="net45" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
</packages>