﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Data.SQLite;
using System.Data;
using System.Collections;

namespace ToolCommon
{
    public class SQLiteHelper : DataPool
    {
        /// <summary>
        /// ConnectionString样例：Data Source=Test.db3;Pooling=true;FailIfMissing=false
        /// </summary>
        public string ConnectionString { get; set; }

        public SQLiteHelper(string strCon)
        {
            ConnectionString = strCon;
        }

        /// <summary>     
        /// 判断数据库表是否存在    
        /// </summary>     
        /// <param name="tableName">表明</param>     
        public override bool IsTableExist(string tableName)
        {
            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (SQLiteCommand command = new SQLiteCommand(connection))
                {
                    /*select * from sysobjects where id = object_id('BookHotelInfo') and type = 'u'
                      select * from sys.tables where name='BookHotelInfo' and type = 'u'*/
                    command.CommandText = "select count(0) from sqlite_master where type='table' AND name='" + tableName + "'";
                    int iaaa = Convert.ToInt32(command.ExecuteScalar());
                    if (Convert.ToInt32(command.ExecuteScalar()) == 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
        }

        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>        
        public override bool ExecuteSqlTran(ArrayList SQLStringList)
        {
            DateTime dtStart = DateTime.Now;
            bool result = false;
            using (SQLiteConnection conn = new SQLiteConnection(ConnectionString))
            {
                conn.Open();
                SQLiteCommand cmd = new SQLiteCommand();
                cmd.Connection = conn;
                SQLiteTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    for (int n = 0; n < SQLStringList.Count; n++)
                    {
                        string strsql = SQLStringList[n].ToString();
                        if (strsql.Trim().Length > 1)
                        {
                            cmd.CommandText = strsql;
                            cmd.ExecuteNonQuery();
                        }
                    }
                    tx.Commit();
                    result = true;
                }
                catch (System.Data.SQLite.SQLiteException E)
                {
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
                catch (Exception E)
                {
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
            }
            if (IsWriteLog)
            {
                WriteLog("ExecuteSqlTran", dtStart);
            }
            return result;
        }

        private void PrepareCommand(SQLiteCommand cmd, SQLiteConnection conn, string cmdText, params object[] p)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            cmd.Parameters.Clear();
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            cmd.CommandType = CommandType.Text;
            cmd.CommandTimeout = 30;
            if (p != null)
            {
                foreach (object parm in p)
                    cmd.Parameters.AddWithValue(string.Empty, parm);
            }
        }

        public override DataSet GetDataSet(string strSql)
        {
            if (!CheckSQL(strSql, true))
            {
                return null;
            }
            DateTime dtStart = DateTime.Now;
            using (SQLiteConnection conn = new SQLiteConnection(ConnectionString))
            {
                using (SQLiteCommand command = new SQLiteCommand())
                {
                    DataSet ds = new DataSet();
                    try
                    {
                        PrepareCommand(command, conn, strSql, null);
                        SQLiteDataAdapter da = new SQLiteDataAdapter(command);
                        da.Fill(ds);
                    }
                    catch { }
                    //if (IsWriteLog)
                    //{
                    //    WriteLog("GetDataSet", dtStart);
                    //}
                    return ds;
                }
            }
        }

        #region 写日志

        private string GetSpanTime(DateTime dtStart)
        {
            StringBuilder strTemp = new StringBuilder();
            TimeSpan ts = new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks);
            strTemp.Append(ts.Hours > 0 ? ts.Hours + "时" : "");
            strTemp.Append(ts.Minutes > 0 ? ts.Minutes + "分" : "");
            strTemp.Append(ts.Seconds > 0 ? ts.Seconds + "秒" : "");
            strTemp.Append(ts.Milliseconds > 0 ? ts.Milliseconds + "毫秒" : "");
            return strTemp.ToString();
        }

        private void WriteLog(string action, string strSQL)
        {
            AppLog.WriteAction(action, string.Format("SQL:{0}", strSQL));
        }

        private void WriteLog(string action, string strSQL, DateTime dtStart)
        {
            AppLog.WriteAction(action, string.Format("SpanTime:{0}     SQL:{1}", GetSpanTime(dtStart), strSQL));
        }

        private void WriteLog(string action, DateTime dtStart)
        {
            AppLog.WriteAction(action, string.Format("SpanTime:{0}", GetSpanTime(dtStart)));
        }

        #endregion

        #region 获得DataTable对象
        /// <summary>
        /// 获得DataTable对象
        /// </summary>
        /// <param name="strSql">SQL语句</param>
        /// <returns></returns>
        public override DataTable GetTable(string strSql)
        {
            if (!CheckSQL(strSql, true))
            {
                return null;
            }
            DataSet ds = GetDataSet(strSql);
            if (ds != null && ds.Tables.Count > 0)
                return ds.Tables[0];
            else
                return new DataTable();
        }
        #endregion

        private bool CheckSQL(string strSql, bool isSelect = false)
        {
            bool result = true;
            try
            {
                strSql = strSql.ToLower().Trim();
                if (isSelect)
                {
                    if (strSql.Contains("insert ") || strSql.Contains("update ") || strSql.Contains("delete "))
                    {
                        WriteLog("Bad Upset SQL", strSql);
                        result = false;
                    }
                }
                else
                {
                    if (strSql.Contains("select "))
                    {
                        WriteLog("Bad Select SQL", strSql);
                        result = false;
                    }
                }
                if (result && (strSql.Contains("this_is_a_test_string") || strSql.Contains("md5(") || strSql.Contains("die(")
                    || strSql.Contains("print(") || strSql.Contains("sleep(") || strSql.Contains("{") || strSql.Contains("}")
                    || strSql.Contains("$") || strSql.Contains("/*") || strSql.Contains("()")))
                {
                    WriteLog("BadSQL", strSql);
                    result = false;
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
            return result;
        }

        public override int ExecuteCommand(string strSql)
        {
            if (!CheckSQL(strSql))
            {
                return 1;
            }
            DateTime dtStart = DateTime.Now;
            using (SQLiteConnection conn = new SQLiteConnection(ConnectionString))
            {
                using (SQLiteCommand command = new SQLiteCommand())
                {
                    PrepareCommand(command, conn, strSql, null);
                    int result = command.ExecuteNonQuery();
                    //if (IsWriteLog)
                    //{
                    //    WriteLog("ExecuteCommand", dtStart);
                    //}
                    return result;
                }
            }
        }

        public SQLiteDataReader ExecuteReader(string cmdText, params object[] p)
        {
            DateTime dtStart = DateTime.Now;
            using (SQLiteConnection conn = new SQLiteConnection(ConnectionString))
            {
                using (SQLiteCommand command = new SQLiteCommand())
                {
                    PrepareCommand(command, conn, cmdText, p);
                    SQLiteDataReader reader = command.ExecuteReader(CommandBehavior.CloseConnection);
                    return reader;
                }
            }
        }

        public override object ExecuScalar(string strSql)
        {
            if (!CheckSQL(strSql, true))
            {
                return "";
            }
            DateTime dtStart = DateTime.Now;
            using (SQLiteConnection conn = new SQLiteConnection(ConnectionString))
            {
                using (SQLiteCommand command = new SQLiteCommand())
                {
                    PrepareCommand(command, conn, strSql, null);
                    object obj = command.ExecuteScalar();
                    if (IsWriteLog)
                    {
                        WriteLog("ExecuScalar", dtStart);
                    }
                    return obj;
                }
            }
        }

        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="recordCount"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="cmdText"></param>
        /// <param name="countText"></param>
        /// <param name="p"></param>
        /// <returns></returns>
        public DataSet ExecutePager(ref int recordCount, int pageIndex, int pageSize, string cmdText, string countText)
        {
            DateTime dtStart = DateTime.Now;
            if (recordCount < 0)
                recordCount = int.Parse(ExecuScalar(countText).ToString());

            DataSet ds = new DataSet();

            SQLiteCommand command = new SQLiteCommand();
            using (SQLiteConnection connection = new SQLiteConnection(ConnectionString))
            {
                PrepareCommand(command, connection, cmdText);
                SQLiteDataAdapter da = new SQLiteDataAdapter(command);
                da.Fill(ds, (pageIndex - 1) * pageSize, pageSize, "result");
            }
            if (IsWriteLog)
            {
                WriteLog("ExecutePager", dtStart);
            }
            return ds;
        }

    }
}
