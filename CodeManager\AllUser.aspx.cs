﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Account.Web
{
    public partial class AllUser : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            {
                Response.End();
                return;
            }
        }

        protected void btnAllUser_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            hidType.Value = "1";
            DtOnLine = ServerTime.DateTime.AddDays(-1);
            DataTable dtTmp = CodeHelper.GetAllCode(txtApp.Text.Trim(), txtType.Text.Trim());
            BindDataTable(dtTmp);
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        private DateTime DtOnLine;
        protected void btnbtnOnLineUser_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            hidType.Value = "1";
            DtOnLine = ServerTime.DateTime.AddHours(-1);
            DataTable dtTmp = CodeHelper.GetOnLineCode(txtApp.Text.Trim(), txtType.Text.Trim(), DtOnLine.ToString("yyyy-MM-dd HH:mm:ss"));
            BindDataTable(dtTmp);
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        private void BindDataTable(DataTable dtTmp)
        {
            var strTmp = "详细：";
            if (dtTmp != null && dtTmp.Rows.Count > 0)
            {
                long totalOcrCount = 0;
                dtTmp.Columns.Add("总数");
                dtTmp.Columns.Add("本机");
                foreach (DataRow item in dtTmp.Rows)
                {
                    var strAccount = item["账号"]?.ToString();
                    if (!string.IsNullOrEmpty(strAccount))
                    {
                        var strToken = dtTmp.Columns.Contains("Token") ? item["Token"]?.ToString() : "";
                        var account = RdsCacheHelper.CodeRecordCache.GetUserCodeInfo(strAccount);
                        if (account != null)
                        {
                            if (!string.IsNullOrEmpty(strToken) && account.Tokens != null && account.Tokens.Any(p => Equals(p.Token, strToken)))
                            {
                                item["本机"] = account.Tokens.FirstOrDefault(p => Equals(p.Token, strToken)).TodayCount.ToString();
                                totalOcrCount += account.Tokens.FirstOrDefault(p => Equals(p.Token, strToken)).TodayCount;
                            }
                            item["总数"] = account.TodayCount.ToString();
                        }
                    }
                }
                if (dtTmp.Columns.Contains("Token"))
                    dtTmp.Columns.Remove("Token");
                var lstUserType = dtTmp.AsEnumerable().Select(p => p["类别"]?.ToString()).Distinct().ToList();
                Dictionary<string, int> dicCount = new Dictionary<string, int>();
                Dictionary<string, int> dicOnLineCount = new Dictionary<string, int>();
                lstUserType.ForEach(userType =>
                {
                    dicCount.Add(userType, dtTmp.AsEnumerable().Where(p => Equals(p["类别"]?.ToString(), userType)).Select(p => p["账号"]?.ToString()).Distinct().Count());
                    dicOnLineCount.Add(userType, dtTmp.AsEnumerable()
                        .Where(p => Equals(p["类别"]?.ToString(), userType) && DateTime.TryParse(p["活动时间"]?.ToString(), out DateTime dtLast) && dtLast > ServerTime.LocalTime.AddDays(-1))
                        .Select(p => p["账号"]?.ToString()).Distinct().Count());
                });
                if (dicCount.Count > 0)
                {
                    int allCount = 0;
                    int allOnLineCount = 0;
                    foreach (var item in dicCount)
                    {
                        allCount += item.Value;
                        if (dicOnLineCount.ContainsKey(item.Key))
                        {
                            allOnLineCount += dicOnLineCount[item.Key];
                        }
                        strTmp += string.Format("{0}:{1}{2} "
                            , item.Key, item.Value
                            , dicOnLineCount.ContainsKey(item.Key) ? string.Format("(在线{0})", dicOnLineCount[item.Key]) : ""
                            );
                    }
                    int allUserCount = RdsCacheHelper.LstAccountCache.GetLoginUserCount(DtOnLine);
                    strTmp += string.Format("<br />共计：{0}个，Token数：{1}，会员数：{2}，会员次数：{3}，{4}"
                        , allCount
                        , allUserCount
                        , allOnLineCount
                        , totalOcrCount
                        , GetAvgSpeed());
                }
            }
            lblCount.Text = strTmp;
        }

        private string GetAvgSpeed()
        {
            var num = ApiCountCacheHelper.GetCount("OcrCounts", ServerTime.DateTime);
            string[] units = { "天", "小时", "分钟", "秒", "毫秒" };  // 单位数组
            double[] divs = { 1, 24, 1440, 86400, ******** };  // 每个单位对应的秒数

            // 自动调整计算单位，最大单位为每毫秒，最小单位为天
            int i;
            for (i = 0; i < units.Length - 1; i++)
            {
                if (num / divs[i + 1] < 1) break;
            }

            // 按照当前计算单位计算平均访问量，并输出结果
            double avg = num / divs[i];
            string unit = units[i];
            if (avg < 1)
            {
                // 如果平均访问量小于 1，则依次向更小的单位转化，直到大于等于 1 为止
                for (int j = i + 1; j < units.Length; j++)
                {
                    avg *= divs[j - 1] / divs[j];
                    unit = units[j];
                    if (avg >= 1)
                    {
                        break;
                    }
                }
            }
            return string.Format("总次数：{0}，访问频次：{1}次/{2}", num, Math.Ceiling(avg), unit);
        }

        protected void btnVacuum_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            CodeHelper.CleanExpiredAccount();
            CodeHelper.Vacuum();
        }
    }
}