﻿using CommonLib;
using System.Web;

public static class RequestExtensions
{
    public static string GetValue(this HttpRequest request, string key)
    {
        string value = BoxUtil.GetStringFromObject(request.Headers[key]);
        if (string.IsNullOrEmpty(value))
        {
            value = BoxUtil.GetStringFromObject(request.QueryString[key]);
            if (string.IsNullOrEmpty(value))
                value = BoxUtil.GetStringFromObject(request[key]);
            if (string.IsNullOrEmpty(value) && request.RawUrl.Contains("&amp;"))
            {
                var queryParams = HttpUtility.ParseQueryString(HttpUtility.HtmlDecode(request.Url.Query));
                value = BoxUtil.GetStringFromObject(queryParams["type"]);
            }
        }
        return string.IsNullOrEmpty(value) ? value : HttpUtility.UrlDecode(value);
    }
}