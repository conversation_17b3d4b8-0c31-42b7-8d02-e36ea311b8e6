﻿<%@ Page Title="OCR助手服务状态 | 实时监控与系统运行情况" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Status.aspx.cs" Inherits="Account.Web.Status" %>
<%@ Import Namespace="CommonLib" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR助手服务状态页面提供实时系统运行状态监控，包括各项服务的可用性、性能指标和历史事件记录，确保您随时了解我们的服务运行情况。" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />

    <style>
        .status-container {
            margin-top: 30px;
            max-width: 850px;
            margin-left: auto;
            margin-right: auto;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(40, 167, 69, 0.1);
            font-size: 14px;
            line-height: 1.5;
            color: #333;
        }

        .status-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 14px 18px;
            font-size: 19px;
            font-weight: 600;
            margin-bottom: 0;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
        }

        .status-tabs {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            margin: 0;
            padding: 0;
        }

        .status-tab {
            padding: 8px 14px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
            flex: 1;
        }

            .status-tab.active {
                color: #2c3e50;
                border-bottom-color: #28a745;
                font-weight: 600;
            }

            .status-tab:hover {
                color: #28a745;
                background: rgba(40, 167, 69, 0.05);
            }

        .status-content {
            border: none;
            min-height: 500px;
            border-radius: 0;
        }

        .tab-panel {
            display: none;
            padding: 14px 14px 14px 14px;
        }

            .tab-panel.active {
                display: block;
            }

        .service-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 6px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            position: relative;
        }

            .service-card:first-child {
                margin-top: 0;
            }

            .service-card:hover {
                box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                transform: translateY(-2px);
                border-color: rgba(40, 167, 69, 0.2);
            }

            .service-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #28a745, #20c997);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .service-card:hover::before {
                opacity: 1;
            }

        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: none;
        }

        .service-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .service-help {
            margin-left: 10px;
            width: 18px;
            height: 18px;
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: help;
            position: relative;
            transition: all 0.2s ease;
        }

            .service-help:hover {
                background: linear-gradient(135deg, #5a6268, #495057);
                transform: scale(1.1);
            }

        .service-status {
            color: #28a745;
            font-weight: 500;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

            .service-status::before {
                content: '';
                width: 8px;
                height: 8px;
                background: #28a745;
                border-radius: 50%;
                display: inline-block;
            }

        .uptime-chart {
            padding: 0px 16px 10px 16px;
            /* 使用母版页的 .bg-white 类 */
        }

        .chart-container {
            position: relative;
            margin: 0;
            background: transparent;
            padding: 0;
        }

        .availability-time-line-graphic {
            width: 100%;
            height: 34px;
            margin: 2px 0;
            pointer-events: auto;
        }

        .uptime-day {
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 2px;
            pointer-events: auto;
        }

            .uptime-day:hover {
                opacity: 0.8;
            }

        .chart-legend {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            color: #6c757d;
            margin-top: 4px;
            font-weight: 400;
        }

        .uptime-percentage {
            font-weight: 600;
            color: #28a745;
            font-size: 14px;
        }

        .status-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            padding: 24px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-top: 1px solid #dee2e6;
            justify-content: center;
            border-radius: 0 0 8px 8px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            padding: 8px 12px;
            /* 使用母版页的 .bg-white 类 */
            border-radius: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }

            .legend-item:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }

        .legend-icon {
            width: 14px;
            height: 14px;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

            .legend-icon.normal {
                background: #28a745;
            }

            .legend-icon.high-load {
                background: #ffc107;
            }

            .legend-icon.network-issue {
                background: #fd7e14;
            }

            .legend-icon.outage {
                background: #dc3545;
            }

            .legend-icon.maintenance {
                background: #007bff;
            }

        .server-list {
            padding: 14px;
        }

        .server-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 6px;
            /* 使用母版页的 .bg-white 类 */
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
            position: relative;
        }

            .server-item:hover {
                box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                transform: translateY(-2px);
                border-color: #28a745;
            }

        .server-info {
            flex: 1;
        }

        .server-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 6px;
            font-size: 14px;
            line-height: 1.4;
        }

        .server-details {
            font-size: 13px;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 400;
        }

        .server-status-info {
            text-align: right;
            font-size: 13px;
            color: #6c757d;
            line-height: 1.5;
        }

        .server-indicator {
            width: 14px;
            height: 14px;
            background: #28a745;
            border-radius: 50%;
            margin-left: 16px;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
        }

        .custom-tooltip {
            position: fixed;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 350px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            line-height: 1.5;
            white-space: pre-line;
        }

            .custom-tooltip.show {
                opacity: 1;
                transform: translateY(-4px);
            }

        .uptime-tooltip {
            position: fixed;
            background: #ffffff;
            color: #333;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 350px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid #ddd;
            line-height: 1.5;
            white-space: pre-line;
        }

            .uptime-tooltip.show {
                opacity: 1;
                transform: translateY(-4px);
            }

        @media (max-width: 768px) {
            .status-container {
                padding: 0 10px;
                margin-top: 20px;
            }

            .status-header {
                font-size: 22px;
                padding: 20px 15px;
            }

            .status-tabs {
                flex-direction: column;
            }

            .status-tab {
                padding: 12px 20px;
                border-bottom: 1px solid #e0e0e0;
                border-right: none;
            }

                .status-tab.active {
                    border-bottom-color: #28a745;
                    border-right: none;
                }

            .tab-panel {
                padding: 20px 15px;
            }

            .service-header {
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
            }

            .server-item {
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
                padding: 12px 16px;
            }

            .server-status-info {
                text-align: right;
                flex-shrink: 0;
            }

            .server-indicator {
                margin-left: 16px;
                align-self: center;
            }

            .legend-item {
                font-size: 12px;
                padding: 6px 10px;
            }

            .custom-tooltip {
                max-width: 280px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .status-header {
                font-size: 18px;
                padding: 12px 8px;
            }

            .service-name {
                font-size: 15px;
            }

            .server-name {
                font-size: 13px;
            }

            .server-details {
                font-size: 11px;
            }

            .service-header {
                padding: 10px 12px;
            }

            .server-item {
                padding: 10px 12px;
            }

            .tab-panel {
                padding: 12px;
            }

            .status-legend {
                gap: 12px;
                padding: 16px;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="status-container" style="margin-top: 60px;">
        <div class="status-header text-center">
            OCR助手服务状态监控中心
        </div>

        <div class="status-tabs d-flex">
            <button class="status-tab active text-center bg-white" onclick="return switchTab('overview', this, event)">状态总览</button>
            <button class="status-tab text-center" onclick="return switchTab('servers', this, event)">OCR服务节点</button>
        </div>

        <div class="status-content bg-white">
            <div id="overview-panel" class="tab-panel active">
                <div class="service-card bg-white">
                    <div class="service-header bg-white">
                        <div class="service-name">
                            账户及其他
                            <span class="service-help" data-tooltip="提供用户登录注册及消息通知等相关的服务">?</span>
                        </div>
                        <div class="service-status">正常运行</div>
                    </div>
                    <div class="uptime-chart bg-white">
                        <div class="chart-container">
                            <svg class="availability-time-line-graphic" preserveaspectratio="none" height="30" viewbox="0 0 358 30">
                                <%for (int i = 0; i < 60; i++)
                                    { %>
                                <rect height="30" width="4" x="<%=i*6 %>" y="0" fill="#3ba55c" class="uptime-day day-<%=i %>" data-html="true"></rect>
                                <%} %>
                            </svg>
                            <div class="chart-legend">
                                <span>60 天前</span>
                                <span class="uptime-percentage">100.0 % 正常运行</span>
                                <span>今天</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="status-legend">
                    <div class="legend-item bg-white">
                        <div class="legend-icon normal"></div>
                        <span>正常</span>
                    </div>
                    <div class="legend-item bg-white">
                        <div class="legend-icon high-load"></div>
                        <span>高负载</span>
                    </div>
                    <div class="legend-item bg-white">
                        <div class="legend-icon network-issue"></div>
                        <span>网络抖动</span>
                    </div>
                    <div class="legend-item bg-white">
                        <div class="legend-icon outage"></div>
                        <span>中断</span>
                    </div>
                    <div class="legend-item bg-white">
                        <div class="legend-icon maintenance"></div>
                        <span>维护</span>
                    </div>
                </div>
            </div>

            <div id="servers-panel" class="tab-panel">
                <div class="service-card bg-white">
                    <div class="service-header bg-white">
                        <div class="service-name">
                            OCR识别-服务节点
                            <span class="service-help" data-tooltip="OCR文字识别相关服务节点状态监控">?</span>
                        </div>
                        <div class="service-status" id="server-count">加载中...</div>
                    </div>
                    <div class="server-list" id="server-list">
                        <div id="server-loading" class="text-center" style="padding: 60px 20px; color: #6c757d;">
                            <div style="font-size: 32px; margin-bottom: 16px; animation: spin 1s linear infinite;">⚙️</div>
                            <div style="font-size: 16px; font-weight: 500;">正在加载服务器信息...</div>
                        </div>
                    </div>

                    <div class="status-legend">
                        <div class="legend-item bg-white">
                            <div class="legend-icon normal"></div>
                            <span>正常运行</span>
                        </div>
                        <div class="legend-item bg-white">
                            <div class="legend-icon high-load"></div>
                            <span>高负载</span>
                        </div>
                        <div class="legend-item bg-white">
                            <div class="legend-icon network-issue"></div>
                            <span>网络抖动</span>
                        </div>
                        <div class="legend-item bg-white">
                            <div class="legend-icon outage"></div>
                            <span>服务中断</span>
                        </div>
                        <div class="legend-item bg-white">
                            <div class="legend-icon maintenance"></div>
                            <span>维护中</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tabName, element, event) {
            event?.preventDefault();
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
            document.querySelectorAll('.status-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(tabName + '-panel').classList.add('active');
            element.classList.add('active');
            if (tabName === 'servers' && !window.serversLoaded) {
                loadServerData();
                window.serversLoaded = true;
            }
            return false;
        }

        document.addEventListener('DOMContentLoaded', () => {
            createServiceCards();
            initHelpTooltips();
            initServerStatsTooltips();
            initUptimeTooltips();
        });

        function createServiceCards() {
            const overviewPanel = document.getElementById('overview-panel');
            const existingCards = overviewPanel.querySelectorAll('.service-card');
            if (existingCards.length >= 4) return;

            [['网关服务', '高可用网关服务'], ['OCR识别服务', 'OCR文字识别相关服务'], ['监测服务', '可用性监控服务']].forEach(([name, tooltip]) => {
                const newCard = existingCards[0].cloneNode(true);
                newCard.querySelector('.service-name').firstChild.textContent = name;
                newCard.querySelector('.service-help').setAttribute('data-tooltip', tooltip);
                overviewPanel.insertBefore(newCard, overviewPanel.querySelector('.status-legend'));
            });
        }

        function loadServerData() {
            if (window.loadingServers) return;
            window.loadingServers = true;
            fetch('code.ashx?op=getServers')
                .then(response => response.json())
                .then(servers => {
                    document.getElementById('server-count').textContent = `共 ${servers.length} 个`;
                    document.getElementById('server-list').innerHTML = servers.length === 0
                        ? `<div class="text-center" style="padding:80px 20px;color:#6c757d;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:12px;margin:20px"><div style="font-size:48px;margin-bottom:16px;opacity:0.6">📊</div><div style="font-size:18px;font-weight:500;margin-bottom:8px;color:#333">暂无服务节点</div><div style="font-size:14px;line-height:1.5;max-width:400px;margin:0 auto">当前没有可用的OCR服务节点，请检查服务配置或稍后再试。</div></div>`
                        : servers.map(server => {
                            const logo = {'Web':'🌍','Console':'🖥️','Service':'🚀'}[server.mode] || '';
                            const osInfo = server.os ? `${logo}${server.os.replace('Microsoft ', '')} ${server.is64Bit ? '64' : '32'}位` : '';
                            const statsSpan = server.statsSummary ? `<span style="font-size:16px;cursor:help;transition:transform 0.2s ease;position:relative" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'" data-tooltip="${server.statsSummary}">🐢</span>` : '';
                            return `<div class="server-item bg-white"><div class="server-info"><div class="server-name">${server.desc}&nbsp;<b>${server.createTime || ''}</b>&nbsp;${server.maskedIp}${server.hasHost ? '🌐' : ''}</div><div class="server-details">${osInfo}${statsSpan}</div></div><div class="server-status-info"><div>心跳: ${server.lastUpdate}</div><div>版本: ${server.version}</div></div><div class="server-indicator"></div></div>`;
                        }).join('');
                    if (servers.length > 0) initServerStatsTooltips();
                })
                .catch(error => {
                    console.error('加载失败:', error);
                    document.getElementById('server-count').textContent = '加载失败';
                    document.getElementById('server-list').innerHTML = `<div class="text-center" style="padding:60px 20px;color:#dc3545"><div style="font-size:32px;margin-bottom:16px">❌</div><div style="font-size:16px;font-weight:500;margin-bottom:8px">加载服务器信息失败</div><div style="font-size:14px">请检查网络连接或稍后重试</div><button onclick="retryLoadServerData()" style="margin-top:16px;padding:8px 16px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer">重新加载</button></div>`;
                    window.serversLoaded = false;
                })
                .finally(() => window.loadingServers = false);
        }

        function initHelpTooltips() {
            const tooltip = document.createElement('div');
            tooltip.id = 'help-tooltip';
            tooltip.style.cssText = 'position:absolute;background:rgba(0,0,0,0.9);color:white;padding:8px 12px;border-radius:4px;font-size:12px;z-index:1001;white-space:nowrap;opacity:0;transition:opacity 0.2s;pointer-events:none';
            document.body.appendChild(tooltip);

            document.querySelectorAll('.service-help').forEach(help => {
                help.addEventListener('mouseenter', function() {
                    tooltip.textContent = this.getAttribute('data-tooltip');
                    tooltip.style.opacity = '1';
                    const rect = this.getBoundingClientRect();
                    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
                });
                help.addEventListener('mouseleave', () => tooltip.style.opacity = '0');
            });
        }

        function initTooltips(selector, tooltipClass, contentHandler) {
            const elements = document.querySelectorAll(selector);
            let tooltip = null;
            elements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    if (!tooltip) {
                        tooltip = document.createElement('div');
                        tooltip.className = tooltipClass;
                        document.body.appendChild(tooltip);
                    }
                    const content = contentHandler(this);
                    if (!content) return;
                    tooltip.textContent = content;
                    tooltip.classList.add('show');
                    const rect = this.getBoundingClientRect();
                    const tooltipRect = tooltip.getBoundingClientRect();
                    const left = Math.max(10, Math.min(rect.left + rect.width / 2 - tooltipRect.width / 2, window.innerWidth - tooltipRect.width - 10));
                    const top = rect.top - tooltipRect.height - 15 < 10 ? rect.bottom + 15 : rect.top - tooltipRect.height - 15;
                    tooltip.style.left = left + 'px';
                    tooltip.style.top = top + 'px';
                });
                element.addEventListener('mouseleave', () => tooltip?.classList.remove('show'));
            });
            document.addEventListener('click', () => tooltip?.classList.remove('show'));
        }

        function initServerStatsTooltips() {
            initTooltips('span[data-tooltip]:not(.service-help)', 'custom-tooltip', element => {
                const title = element.getAttribute('data-tooltip');
                return title?.replace(/ \| /g, '\n') || null;
            });
        }

        function initUptimeTooltips() {
            initTooltips('.uptime-day', 'uptime-tooltip', element => {
                const dayIndex = parseInt(element.getAttribute('class').match(/day-(\d+)/)?.[1] || 0);
                const targetDate = new Date();
                targetDate.setDate(targetDate.getDate() - (59 - dayIndex));
                return `${targetDate.getFullYear()}-${targetDate.getMonth() + 1}-${targetDate.getDate()}\n全天运行正常`;
            });
        }

        function retryLoadServerData() {
            window.serversLoaded = window.loadingServers = false;
            document.getElementById('server-count').textContent = '加载中...';
            document.getElementById('server-list').innerHTML = `<div class="text-center" style="padding:60px 20px;color:#6c757d"><div style="font-size:32px;margin-bottom:16px;animation:spin 1s linear infinite">⚙️</div><div style="font-size:16px;font-weight:500">正在加载服务器信息...</div></div>`;
            loadServerData();
        }
    </script>
</asp:Content>
