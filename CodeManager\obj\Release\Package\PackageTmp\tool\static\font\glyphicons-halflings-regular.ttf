<!DOCTYPE HTML><html lang="zh-CN"><head><title>FeHelper - Awesome</title><meta name="Robots" content="INDEX,FOLLOW"><meta name="keywords" content="<PERSON><PERSON><PERSON><PERSON>,WEB前端助手,JSON格式化,JSON对比,信息编解码,代码美化,代码压缩,二维码生成,二维码解码,图片Base64转换,Markdown,随机密码生成器,正则表达式,时间戳转换,便签笔记,进制转换,贷款计算器"><meta name="description" content="WEB前端助手：FeHelper，浏览器插件，包含一些前端实用的工具，如JSON格式化,JSON对比,信息编解码,代码美化,代码压缩,二维码生成,二维码解码,图片Base64转换,Markdown,随机密码生成器,正则表达式,时间戳转换,便签笔记,进制转换,贷款计算器等"><link rel="chrome-webstore-item" href="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad"><meta name="renderer" content="webkit"><meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"><meta charset="UTF-8"><link rel="shortcut icon" href="../static/img/favicon.ico"><link rel="stylesheet" href="index.css?v=b6da8fdc"><script type="text/javascript" src="../static/vendor/vue/vue.js?v=b6ac71fa"></script></head><body><div class="wrapper" id="pageContainer"><div class="mod-pageheader"><div class="mod-sitedesc mod-fehelper"><div class="logo-box"><span class="q-logo"><img src="https://static.baidufe.com/fehelper/static/img/fe-128.png" title="WEB前端助手（FeHelper）" alt="WEB前端助手（FeHelper）"></span></div><div class="mod-btitle"><div class="x-name"><a href="/fehelper/index/index.html">FeHelper ( 浏览器插件 )</a><button id="btnInstallExtension" style="display: none"></button></div><div class="x-desc">2011年，FeHelper作为开发者专用的Chrome浏览器扩展在Google Chrome Webstore发布1.0版本，截至目前持续更新中，欢迎大家安装使用！</div></div></div><div class="mod-topbar" id="modTopbar"><div class="wrapper-box clearfix"><div class="mainnav-box"><ul class="q-menubox"><li class="q-menuitem"><a href="/fehelper/index/index.html">首页</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/json-format/index.html">JSON美化</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/json-diff/index.html">JSON比对</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/en-decode/index.html">编码转换</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/qr-code/index.html">二维码/解码</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/image-base64/index.html">图片Base64</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/code-beautify/index.html">代码美化</a></li><li class="q-sp">|</li><li class="q-menuitem"><a href="/fehelper/more/index.html">更多FH工具&gt;&gt;</a></li></ul></div><div class="subnav-box"><ul class="q-navbox"><li class="q-navitem"><a href="/fehelper/feedback.html" class="x-fbk"><span>意见反馈&gt;&gt;</span></a></li></ul></div></div></div></div><div class="panel-body mod-code"><section class="new-banner"><div class="group bg"><h1>Web开发者助手 FeHelper</h1><div class="desc">本插件支持<b style="color:red">Chrome、Firefox、MS-Edge</b>浏览器，内部工具集持续增加，目前包括 JSON自动/手动格式化、JSON内容比对、代码美化与压缩、信息编解码转换、二维码生成与解码、图片Base64编解码转换、Markdown、 网页油猴、网页取色器、脑图(Xmind)等贴心工具，甚至在目前新版本的FeHelper中，还集成了<b style="color:red;">FH开发者工具</b>， 如果你也想自己搞一个工具集成到FeHelper中，那这一定能满足到你。另外，本站也提供部分工具的在线版本，欢迎使用，欢迎反馈！</div><div class="x-crx"><div class="go-install"><span class="x-tips">点击按钮快速安装</span> <a href="#" class="btn-fh-install" @click="install($event)"><span>Chrome版</span></a> <a href="#" class="btn-fh-install" @click="install($event)"><span>Firefox版</span></a> <a href="#" class="btn-fh-install" @click="install($event)"><span>Microsoft Edge版</span></a></div><a href="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad"><img alt="FeHelper-version" src="https://img.shields.io/chrome-web-store/v/pkgccpejnmalmdinmhkkfafefagiiiad.svg?logo=Google%20Chrome&amp;logoColor=red&amp;color=blue"></a><a href="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad"><img alt="FeHelper-rating" src="https://img.shields.io/chrome-web-store/stars/pkgccpejnmalmdinmhkkfafefagiiiad.svg?logo=Google%20Chrome&amp;logoColor=red&amp;color=blue"></a><a href="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad"><img alt="FeHelper-users" src="https://img.shields.io/chrome-web-store/users/pkgccpejnmalmdinmhkkfafefagiiiad.svg?logo=Google%20Chrome&amp;logoColor=red&amp;color=blue"></a></div><div class="img-box"><img src="../static/img/fh-allinone.jpg"></div></div></section><section class="n-middle"><div class="top"></div><div class="bottom"></div><div class="group column-length-1"><div class="promotion"><span>FeHelper</span> <span class="content">已在Github开源，也欢迎大家提issue，或者直接提交PR加入进来！</span> <a class="text-link" target="_blank" href="https://github.com/zxlie/FeHelper">现在就去Github看看&gt;&gt;</a> <span class="x-github"><a href="https://github.com/zxlie/FeHelper" target="_blank"><img src="https://img.shields.io/github/stars/zxlie/FeHelper?style=social" alt="star"></a><a href="https://github.com/zxlie/FeHelper" target="_blank"><img src="https://img.shields.io/github/forks/zxlie/FeHelper?style=social" alt="fork"></a></span></div></div></section><div class="p-container"><h2>FH应用市场</h2><div class="group"><ul class="section-container"><li class="item current x-charset"><div class="icon active-icon">卍</div><div class="title">Charset(独立插件)</div><pre class="desc">网页字符编码集修改工具，主要解决网页访问是乱码的问题，支持上百种字符集的选择</pre><div class="btn-wrap"><span class="text-link"><a class="btn btn-sm btn-primary" target="_blank" href="https://chrome.google.com/webstore/detail/%E7%BD%91%E9%A1%B5%E7%BC%96%E7%A0%81%E4%BF%AE%E6%94%B9%EF%BC%88charset%EF%BC%89/mnnlnpbaaojjmihapdoffoicnnaokpmj">Chrome版</a> <a class="btn btn-sm btn-success" target="_blank" href="https://microsoftedge.microsoft.com/addons/detail/nobgpmgfcojjalaabiecojoeigheopjb">MS-Edge版</a> <a class="btn btn-sm btn-warning" target="_blank" href="https://github.com/zxlie/FH-Charset">Github</a></span></div></li><li class="item current" v-for="tool in Object.keys(allTools)"><div class="icon active-icon">{{allTools[tool].icon}}</div><div class="title">{{allTools[tool].name}}</div><pre class="desc">{{allTools[tool].tips}}</pre><div class="btn-wrap"><a class="text-link" :href="`../${tool}/index.html`" @click="preClick(tool,$event)">马上使用&gt;&gt;</a></div></li><li class="item current x-more"><div class="icon active-icon">?</div><div class="title">更多FH新工具</div><pre class="desc">只要你已经安装了FeHelper插件，新上架的工具会自动出现在你插件配置页的「FH应用市场」内</pre><div class="btn-wrap"><span class="text-link">敬请期待...</span></div></li></ul></div></div><div class="p-container x-screenshots"><h2>FH插件界面预览</h2><div class="x-img-box" v-for="ss in screenshots"><img :src="`../static/img/${ss}`" alt="ss"></div></div><div class="x-install-box" v-cloak v-if="installIng"><div id="fehelper_mask"></div><div id="fehelper_tips"><img src="../static/img/close-icon.png" alt="close" class="install-close-btn" @click="installIng=false"><div class="x-hold-on">插件安装中，请等待...</div><div class="x-msg">- 如果长时间无响应，估计是被墙了，可以选择下面的方式下载后再手动安装：<br><ul><li><a target="_blank" href="https://github.com/zxlie/FeHelper/tree/master/apps/static/screenshot/crx">去Github下载最新版*.crx文件直接安装</a></li><li><a href="https://chrome-extension-downloader.com/?extension=pkgccpejnmalmdinmhkkfafefagiiiad">去代理网站下载FeHelper最新版*.crx文件再安装</a></li><li><a href="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad">老老实实的去Google Chrome Webstore安装</a></li></ul></div><div class="x-msg"><div>- crx文件离线安装步骤：</div><ul><li>下载FeHelper.crx文件</li><li>浏览器打开：<a href="chrome://extensions/" target="_blank">chrome://extensions/</a></li><li>拖拽*.crx文件到浏览器窗口，完成安装</li></ul></div></div></div></div></div><div class="mod-footer"><div class="clearfix"></div><div class="footer-box">Copyright &copy; 阿烈叔 All Rights Reserved.&nbsp; <a href="http://beian.miit.gov.cn/" target="_blank" class="x-beian">京ICP备14006329号</a></div></div><script type="text/javascript" src="fh-config.js?v=0b36f6e5"></script><script type="text/javascript" src="index.js?v=9e1ed2a4"></script><div id="pageFooter" class="hide" style="display: none"><script type="text/javascript">(function(){  var _bdhmProtocol = (("https:" === document.location.protocol) ? " https://" : " http://");  document.write(unescape("%3Cscript src='" + _bdhmProtocol +  "hm.baidu.com/h.js%3F17b02fba4e62901b4289eef4c2243123' type='text/javascript'%3E%3C/script%3E"));  })();</script></div></body></html>