﻿using CommonLib;
using System;
using System.Web;

namespace Account.Web
{
    public partial class Pay1 : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            LoadOrder();
        }

        private void LoadOrder()
        {
            var payId = HttpUtility.UrlDecode(Request.QueryString["payId"]);
            var param = HttpUtility.UrlDecode(Request.QueryString["param"]);
            var type = BoxUtil.GetInt32FromObject(Request.QueryString["type"]);
            var price = HttpUtility.UrlDecode(Request.QueryString["price"]);
            var reallyPrice = HttpUtility.UrlDecode(Request.QueryString["reallyPrice"]);
            var remark = HttpUtility.UrlDecode(Request.QueryString["remark"]);
            var fromSign = Request.QueryString["sign"];
            if (!PayUtil.ValidateMd5(payId, param, price, reallyPrice, type, remark, fromSign))
            {
                LogHelper.Log.InfoFormat("同步：payId:{0},param:{1},type:{2},price:{3},reallyPrice:{4},fromSign:{5}", payId, param, type, price, reallyPrice, fromSign);
                Response.Write("error sign");
                Response.End();
            }
        }
    }
}