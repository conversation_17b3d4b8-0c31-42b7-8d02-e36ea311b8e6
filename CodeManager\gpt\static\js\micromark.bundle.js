var At=Object.defineProperty,Et=(e,r)=>{for(var t in r)At(e,t,{get:r[t],enumerable:!0})},we=document.createElement("i");function Qe(e){let r="&"+e+";";we.innerHTML=r;let t=we.textContent;return t.charCodeAt(t.length-1)===59&&e!=="semi"||t===r?!1:t}function te(e,r,t,n){let i=e.length,u=0,c;if(r<0?r=-r>i?0:i+r:r=r>i?i:r,t=t>0?t:0,n.length<1e4)c=Array.from(n),c.unshift(r,t),[].splice.apply(e,c);else for(t&&[].splice.apply(e,[r,t]);u<n.length;)c=n.slice(u,u+1e4),c.unshift(r,0),[].splice.apply(e,c),u+=1e4,r+=1e4}function V(e,r){return e.length>0?(te(e,e.length,0,r),e):r}var Se={}.hasOwnProperty;function Ct(e){let r={},t=-1;for(;++t<e.length;)zt(r,e[t]);return r}function zt(e,r){let t;for(t in r){let n=(Se.call(e,t)?e[t]:void 0)||(e[t]={}),i=r[t],u;for(u in i){Se.call(n,u)||(n[u]=[]);let c=i[u];It(n[u],Array.isArray(c)?c:c?[c]:[])}}}function It(e,r){let t=-1,n=[];for(;++t<r.length;)(r[t].add==="after"?e:n).push(r[t]);te(e,0,0,n)}function Tt(e){let r={},t=-1;for(;++t<e.length;)Dt(r,e[t]);return r}function Dt(e,r){let t;for(t in r){let n=(Se.call(e,t)?e[t]:void 0)||(e[t]={}),i=r[t],u;if(i)for(u in i)n[u]=i[u]}}function _t(e,r){let t=Number.parseInt(e,r);return t<9||t===11||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||(t&65535)===65535||(t&65535)===65534||t>1114111?"\uFFFD":String.fromCharCode(t)}var wt={'"':"quot","&":"amp","<":"lt",">":"gt"};function Ne(e){return e.replace(/["&<>]/g,r);function r(t){return"&"+wt[t]+";"}}function he(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Lt=/[!-/:-@[-`{-~\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/,ee=ie(/[A-Za-z]/),be=ie(/\d/),Mt=ie(/[\dA-Fa-f]/),Z=ie(/[\dA-Za-z]/),Ot=ie(/[!-/:-@[-`{-~]/),Le=ie(/[#-'*+\--9=?A-Z^-~]/);function ye(e){return e!==null&&(e<32||e===127)}function G(e){return e!==null&&(e<0||e===32)}function A(e){return e!==null&&e<-2}function B(e){return e===-2||e===-1||e===32}var Bt=ie(/\s/),Pt=ie(Lt);function ie(e){return r;function r(t){return t!==null&&e.test(String.fromCharCode(t))}}function me(e,r){let t=Ne(jt(e||""));if(!r)return t;let n=t.indexOf(":"),i=t.indexOf("?"),u=t.indexOf("#"),c=t.indexOf("/");return n<0||c>-1&&n>c||i>-1&&n>i||u>-1&&n>u||r.test(t.slice(0,n))?t:""}function jt(e){let r=[],t=-1,n=0,i=0;for(;++t<e.length;){let u=e.charCodeAt(t),c="";if(u===37&&Z(e.charCodeAt(t+1))&&Z(e.charCodeAt(t+2)))i=2;else if(u<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(u))||(c=String.fromCharCode(u));else if(u>55295&&u<57344){let s=e.charCodeAt(t+1);u<56320&&s>56319&&s<57344?(c=String.fromCharCode(u,s),i=1):c="\uFFFD"}else c=String.fromCharCode(u);c&&(r.push(e.slice(n,t),encodeURIComponent(c)),n=t+i+1,c=""),i&&(t+=i,i=0)}return r.join("")+e.slice(n)}var Me={}.hasOwnProperty,Oe=/^(https?|ircs?|mailto|xmpp)$/i,Rt=/^https?$/i;function qt(e={}){let r=!0,t={},n=[[]],i=[],u=[],c=Tt([{enter:{blockQuote:P,codeFenced:N,codeFencedFenceInfo:m,codeFencedFenceMeta:m,codeIndented:Q,codeText:gt,content:it,definition:oe,definitionDestinationString:ke,definitionLabelString:m,definitionTitleString:m,emphasis:ht,htmlFlow:pt,htmlText:Te,image:a,label:m,link:o,listItemMarker:O,listItemValue:L,listOrdered:D,listUnordered:x,paragraph:C,reference:m,resource:le,resourceDestinationString:ne,resourceTitleString:m,setextHeading:ct,strong:mt},exit:{atxHeading:at,atxHeadingSequence:ut,autolinkEmail:yt,autolinkProtocol:bt,blockQuote:b,characterEscapeValue:de,characterReferenceMarkerHexadecimal:De,characterReferenceMarkerNumeric:De,characterReferenceValue:St,codeFenced:M,codeFencedFence:j,codeFencedFenceInfo:K,codeFencedFenceMeta:E,codeFlowValue:dt,codeIndented:M,codeText:xt,codeTextData:de,data:de,definition:rt,definitionDestinationString:tt,definitionLabelString:ae,definitionTitleString:nt,emphasis:kt,hardBreakEscape:ze,hardBreakTrailing:ze,htmlFlow:Ie,htmlFlowData:de,htmlText:Ie,htmlTextData:de,image:Y,label:ue,labelText:se,lineEnding:ft,link:Y,listOrdered:w,listUnordered:R,paragraph:q,reference:E,referenceString:ce,resource:E,resourceDestinationString:H,resourceTitleString:fe,setextHeading:lt,setextHeadingLineSequence:st,setextHeadingText:ot,strong:Ft,thematicBreak:vt}}].concat(e.htmlExtensions||[])),s={tightStack:u,definitions:t},l={lineEndingIfNeeded:v,options:e,encode:z,raw:g,tag:S,buffer:m,resume:E,setData:d,getData:p},h=e.defaultLineEnding;return f;function f(k){let y=-1,W=0,U=[],J=[],re=[];for(;++y<k.length;)!h&&(k[y][1].type==="lineEnding"||k[y][1].type==="lineEndingBlank")&&(h=k[y][2].sliceSerialize(k[y][1])),(k[y][1].type==="listOrdered"||k[y][1].type==="listUnordered")&&(k[y][0]==="enter"?U.push(y):F(k.slice(U.pop(),y))),k[y][1].type==="definition"&&(k[y][0]==="enter"?(re=V(re,k.slice(W,y)),W=y):(J=V(J,k.slice(W,y+1)),W=y+1));J=V(J,re),J=V(J,k.slice(W)),y=-1;let X=J;for(c.enter.null&&c.enter.null.call(l);++y<k.length;){let _e=c[X[y][0]];Me.call(_e,X[y][1].type)&&_e[X[y][1].type].call(Object.assign({sliceSerialize:X[y][2].sliceSerialize},l),X[y][1])}return c.exit.null&&c.exit.null.call(l),n[0].join("")}function F(k){let y=k.length,W=0,U=0,J=!1,re;for(;++W<y;){let X=k[W];if(X[1]._container)re=void 0,X[0]==="enter"?U++:U--;else switch(X[1].type){case"listItemPrefix":{X[0]==="exit"&&(re=!0);break}case"linePrefix":break;case"lineEndingBlank":{X[0]==="enter"&&!U&&(re?re=void 0:J=!0);break}default:re=void 0}}k[0][1]._loose=J}function d(k,y){s[k]=y}function p(k){return s[k]}function m(){n.push([])}function E(){return n.pop().join("")}function S(k){r&&(d("lastWasTag",!0),n[n.length-1].push(k))}function g(k){d("lastWasTag"),n[n.length-1].push(k)}function _(){g(h||`
`)}function v(){let k=n[n.length-1],y=k[k.length-1],W=y?y.charCodeAt(y.length-1):null;W===10||W===13||W===null||_()}function z(k){return p("ignoreEncode")?k:Ne(k)}function D(k){u.push(!k._loose),v(),S("<ol"),d("expectFirstItem",!0)}function x(k){u.push(!k._loose),v(),S("<ul"),d("expectFirstItem",!0)}function L(k){if(p("expectFirstItem")){let y=Number.parseInt(this.sliceSerialize(k),10);y!==1&&S(' start="'+z(String(y))+'"')}}function O(){p("expectFirstItem")?S(">"):I(),v(),S("<li>"),d("expectFirstItem"),d("lastWasTag")}function w(){I(),u.pop(),_(),S("</ol>")}function R(){I(),u.pop(),_(),S("</ul>")}function I(){p("lastWasTag")&&!p("slurpAllLineEndings")&&v(),S("</li>"),d("slurpAllLineEndings")}function P(){u.push(!1),v(),S("<blockquote>")}function b(){u.pop(),v(),S("</blockquote>"),d("slurpAllLineEndings")}function C(){u[u.length-1]||(v(),S("<p>")),d("slurpAllLineEndings")}function q(){u[u.length-1]?d("slurpAllLineEndings",!0):S("</p>")}function N(){v(),S("<pre><code"),d("fencesCount",0)}function K(){let k=E();S(' class="language-'+k+'"')}function j(){let k=p("fencesCount")||0;k||(S(">"),d("slurpOneLineEnding",!0)),d("fencesCount",k+1)}function Q(){v(),S("<pre><code>")}function M(){let k=p("fencesCount");k!==void 0&&k<2&&s.tightStack.length>0&&!p("lastWasTag")&&_(),p("flowCodeSeenData")&&v(),S("</code></pre>"),k!==void 0&&k<2&&v(),d("flowCodeSeenData"),d("fencesCount"),d("slurpOneLineEnding")}function a(){i.push({image:!0}),r=void 0}function o(){i.push({})}function se(k){i[i.length-1].labelId=this.sliceSerialize(k)}function ue(){i[i.length-1].label=E()}function ce(k){i[i.length-1].referenceId=this.sliceSerialize(k)}function le(){m(),i[i.length-1].destination=""}function ne(){m(),d("ignoreEncode",!0)}function H(){i[i.length-1].destination=E(),d("ignoreEncode")}function fe(){i[i.length-1].title=E()}function Y(){let k=i.length-1,y=i[k],W=y.referenceId||y.labelId,U=y.destination===void 0?t[he(W)]:y;for(r=!0;k--;)if(i[k].image){r=void 0;break}y.image?(S('<img src="'+me(U.destination,e.allowDangerousProtocol?void 0:Rt)+'" alt="'),g(y.label),S('"')):S('<a href="'+me(U.destination,e.allowDangerousProtocol?void 0:Oe)+'"'),S(U.title?' title="'+U.title+'"':""),y.image?S(" />"):(S(">"),g(y.label),S("</a>")),i.pop()}function oe(){m(),i.push({})}function ae(k){E(),i[i.length-1].labelId=this.sliceSerialize(k)}function ke(){m(),d("ignoreEncode",!0)}function tt(){i[i.length-1].destination=E(),d("ignoreEncode")}function nt(){i[i.length-1].title=E()}function rt(){let k=i[i.length-1],y=he(k.labelId);E(),Me.call(t,y)||(t[y]=i[i.length-1]),i.pop()}function it(){d("slurpAllLineEndings",!0)}function ut(k){p("headingRank")||(d("headingRank",this.sliceSerialize(k).length),v(),S("<h"+p("headingRank")+">"))}function ct(){m(),d("slurpAllLineEndings")}function ot(){d("slurpAllLineEndings",!0)}function at(){S("</h"+p("headingRank")+">"),d("headingRank")}function st(k){d("headingRank",this.sliceSerialize(k).charCodeAt(0)===61?1:2)}function lt(){let k=E();v(),S("<h"+p("headingRank")+">"),g(k),S("</h"+p("headingRank")+">"),d("slurpAllLineEndings"),d("headingRank")}function de(k){g(z(this.sliceSerialize(k)))}function ft(k){if(!p("slurpAllLineEndings")){if(p("slurpOneLineEnding")){d("slurpOneLineEnding");return}if(p("inCodeText")){g(" ");return}g(z(this.sliceSerialize(k)))}}function dt(k){g(z(this.sliceSerialize(k))),d("flowCodeSeenData",!0)}function ze(){S("<br />")}function pt(){v(),Te()}function Ie(){d("ignoreEncode")}function Te(){e.allowDangerousHtml&&d("ignoreEncode",!0)}function ht(){S("<em>")}function mt(){S("<strong>")}function gt(){d("inCodeText",!0),S("<code>")}function xt(){d("inCodeText"),S("</code>")}function kt(){S("</em>")}function Ft(){S("</strong>")}function vt(){v(),S("<hr />")}function De(k){d("characterReferenceType",k.type)}function St(k){let y=this.sliceSerialize(k);y=p("characterReferenceType")?_t(y,p("characterReferenceType")==="characterReferenceMarkerNumeric"?10:16):Qe(y),g(z(y)),d("characterReferenceType")}function bt(k){let y=this.sliceSerialize(k);S('<a href="'+me(y,e.allowDangerousProtocol?void 0:Oe)+'">'),g(z(y)),S("</a>")}function yt(k){let y=this.sliceSerialize(k);S('<a href="'+me("mailto:"+y)+'">'),g(z(y)),S("</a>")}}function T(e,r,t,n){let i=n?n-1:Number.POSITIVE_INFINITY,u=0;return c;function c(l){return B(l)?(e.enter(t),s(l)):r(l)}function s(l){return B(l)&&u++<i?(e.consume(l),s):(e.exit(t),r(l))}}var Ht={tokenize:Vt};function Vt(e){let r=e.attempt(this.parser.constructs.contentInitial,n,i),t;return r;function n(s){if(s===null){e.consume(s);return}return e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),T(e,r,"linePrefix")}function i(s){return e.enter("paragraph"),u(s)}function u(s){let l=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=l),t=l,c(s)}function c(s){if(s===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(s);return}return A(s)?(e.consume(s),e.exit("chunkText"),u):(e.consume(s),c)}}var Qt={tokenize:Nt},Be={tokenize:Wt};function Nt(e){let r=this,t=[],n=0,i,u,c;return s;function s(v){if(n<t.length){let z=t[n];return r.containerState=z[1],e.attempt(z[0].continuation,l,h)(v)}return h(v)}function l(v){if(n++,r.containerState._closeFlow){r.containerState._closeFlow=void 0,i&&_();let z=r.events.length,D=z,x;for(;D--;)if(r.events[D][0]==="exit"&&r.events[D][1].type==="chunkFlow"){x=r.events[D][1].end;break}g(n);let L=z;for(;L<r.events.length;)r.events[L][1].end=Object.assign({},x),L++;return te(r.events,D+1,0,r.events.slice(z)),r.events.length=L,h(v)}return s(v)}function h(v){if(n===t.length){if(!i)return d(v);if(i.currentConstruct&&i.currentConstruct.concrete)return m(v);r.interrupt=Boolean(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return r.containerState={},e.check(Be,f,F)(v)}function f(v){return i&&_(),g(n),d(v)}function F(v){return r.parser.lazy[r.now().line]=n!==t.length,c=r.now().offset,m(v)}function d(v){return r.containerState={},e.attempt(Be,p,m)(v)}function p(v){return n++,t.push([r.currentConstruct,r.containerState]),d(v)}function m(v){if(v===null){i&&_(),g(0),e.consume(v);return}return i=i||r.parser.flow(r.now()),e.enter("chunkFlow",{contentType:"flow",previous:u,_tokenizer:i}),E(v)}function E(v){if(v===null){S(e.exit("chunkFlow"),!0),g(0),e.consume(v);return}return A(v)?(e.consume(v),S(e.exit("chunkFlow")),n=0,r.interrupt=void 0,s):(e.consume(v),E)}function S(v,z){let D=r.sliceStream(v);if(z&&D.push(null),v.previous=u,u&&(u.next=v),u=v,i.defineSkip(v.start),i.write(D),r.parser.lazy[v.start.line]){let x=i.events.length;for(;x--;)if(i.events[x][1].start.offset<c&&(!i.events[x][1].end||i.events[x][1].end.offset>c))return;let L=r.events.length,O=L,w,R;for(;O--;)if(r.events[O][0]==="exit"&&r.events[O][1].type==="chunkFlow"){if(w){R=r.events[O][1].end;break}w=!0}for(g(n),x=L;x<r.events.length;)r.events[x][1].end=Object.assign({},R),x++;te(r.events,O+1,0,r.events.slice(L)),r.events.length=x}}function g(v){let z=t.length;for(;z-- >v;){let D=t[z];r.containerState=D[1],D[0].exit.call(r,e)}t.length=v}function _(){i.write([null]),u=void 0,i=void 0,r.containerState._closeFlow=void 0}}function Wt(e,r,t){return T(e,e.attempt(this.parser.constructs.document,r,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Pe(e){if(e===null||G(e)||Bt(e))return 1;if(Pt(e))return 2}function Ee(e,r,t){let n=[],i=-1;for(;++i<e.length;){let u=e[i].resolveAll;u&&!n.includes(u)&&(r=u(r,t),n.push(u))}return r}var Ae={name:"attention",tokenize:Zt,resolveAll:$t};function $t(e,r){let t=-1,n,i,u,c,s,l,h,f;for(;++t<e.length;)if(e[t][0]==="enter"&&e[t][1].type==="attentionSequence"&&e[t][1]._close){for(n=t;n--;)if(e[n][0]==="exit"&&e[n][1].type==="attentionSequence"&&e[n][1]._open&&r.sliceSerialize(e[n][1]).charCodeAt(0)===r.sliceSerialize(e[t][1]).charCodeAt(0)){if((e[n][1]._close||e[t][1]._open)&&(e[t][1].end.offset-e[t][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[t][1].end.offset-e[t][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[t][1].end.offset-e[t][1].start.offset>1?2:1;let F=Object.assign({},e[n][1].end),d=Object.assign({},e[t][1].start);je(F,-l),je(d,l),c={type:l>1?"strongSequence":"emphasisSequence",start:F,end:Object.assign({},e[n][1].end)},s={type:l>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[t][1].start),end:d},u={type:l>1?"strongText":"emphasisText",start:Object.assign({},e[n][1].end),end:Object.assign({},e[t][1].start)},i={type:l>1?"strong":"emphasis",start:Object.assign({},c.start),end:Object.assign({},s.end)},e[n][1].end=Object.assign({},c.start),e[t][1].start=Object.assign({},s.end),h=[],e[n][1].end.offset-e[n][1].start.offset&&(h=V(h,[["enter",e[n][1],r],["exit",e[n][1],r]])),h=V(h,[["enter",i,r],["enter",c,r],["exit",c,r],["enter",u,r]]),h=V(h,Ee(r.parser.constructs.insideSpan.null,e.slice(n+1,t),r)),h=V(h,[["exit",u,r],["enter",s,r],["exit",s,r],["exit",i,r]]),e[t][1].end.offset-e[t][1].start.offset?(f=2,h=V(h,[["enter",e[t][1],r],["exit",e[t][1],r]])):f=0,te(e,n-1,t-n+3,h),t=n+h.length-f-2;break}}for(t=-1;++t<e.length;)e[t][1].type==="attentionSequence"&&(e[t][1].type="data");return e}function Zt(e,r){let t=this.parser.constructs.attentionMarkers.null,n=this.previous,i=Pe(n),u;return c;function c(l){return e.enter("attentionSequence"),u=l,s(l)}function s(l){if(l===u)return e.consume(l),s;let h=e.exit("attentionSequence"),f=Pe(l),F=!f||f===2&&i||t.includes(l),d=!i||i===2&&f||t.includes(n);return h._open=Boolean(u===42?F:F&&(i||!d)),h._close=Boolean(u===42?d:d&&(f||!F)),r(l)}}function je(e,r){e.column+=r,e.offset+=r,e._bufferIndex+=r}var Gt={name:"autolink",tokenize:Kt};function Kt(e,r,t){let n=1;return i;function i(m){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(m),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),u}function u(m){return ee(m)?(e.consume(m),c):Le(m)?h(m):t(m)}function c(m){return m===43||m===45||m===46||Z(m)?s(m):h(m)}function s(m){return m===58?(e.consume(m),l):(m===43||m===45||m===46||Z(m))&&n++<32?(e.consume(m),s):h(m)}function l(m){return m===62?(e.exit("autolinkProtocol"),p(m)):m===null||m===32||m===60||ye(m)?t(m):(e.consume(m),l)}function h(m){return m===64?(e.consume(m),n=0,f):Le(m)?(e.consume(m),h):t(m)}function f(m){return Z(m)?F(m):t(m)}function F(m){return m===46?(e.consume(m),n=0,f):m===62?(e.exit("autolinkProtocol").type="autolinkEmail",p(m)):d(m)}function d(m){return(m===45||Z(m))&&n++<63?(e.consume(m),m===45?d:F):t(m)}function p(m){return e.enter("autolinkMarker"),e.consume(m),e.exit("autolinkMarker"),e.exit("autolink"),r}}var xe={tokenize:Ut,partial:!0};function Ut(e,r,t){return T(e,n,"linePrefix");function n(i){return i===null||A(i)?r(i):t(i)}}var We={name:"blockQuote",tokenize:Xt,continuation:{tokenize:Yt},exit:Jt};function Xt(e,r,t){let n=this;return i;function i(c){if(c===62){let s=n.containerState;return s.open||(e.enter("blockQuote",{_container:!0}),s.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(c),e.exit("blockQuoteMarker"),u}return t(c)}function u(c){return B(c)?(e.enter("blockQuotePrefixWhitespace"),e.consume(c),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),r):(e.exit("blockQuotePrefix"),r(c))}}function Yt(e,r,t){return T(e,e.attempt(We,r,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Jt(e){e.exit("blockQuote")}var $e={name:"characterEscape",tokenize:en};function en(e,r,t){return n;function n(u){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(u),e.exit("escapeMarker"),i}function i(u){return Ot(u)?(e.enter("characterEscapeValue"),e.consume(u),e.exit("characterEscapeValue"),e.exit("characterEscape"),r):t(u)}}var Ze={name:"characterReference",tokenize:tn};function tn(e,r,t){let n=this,i=0,u,c;return s;function s(F){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(F),e.exit("characterReferenceMarker"),l}function l(F){return F===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(F),e.exit("characterReferenceMarkerNumeric"),h):(e.enter("characterReferenceValue"),u=31,c=Z,f(F))}function h(F){return F===88||F===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(F),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),u=6,c=Mt,f):(e.enter("characterReferenceValue"),u=7,c=be,f(F))}function f(F){let d;return F===59&&i?(d=e.exit("characterReferenceValue"),c===Z&&!Qe(n.sliceSerialize(d))?t(F):(e.enter("characterReferenceMarker"),e.consume(F),e.exit("characterReferenceMarker"),e.exit("characterReference"),r)):c(F)&&i++<u?(e.consume(F),f):t(F)}}var Re={name:"codeFenced",tokenize:nn,concrete:!0};function nn(e,r,t){let n=this,i={tokenize:D,partial:!0},u={tokenize:z,partial:!0},c=this.events[this.events.length-1],s=c&&c[1].type==="linePrefix"?c[2].sliceSerialize(c[1],!0).length:0,l=0,h;return f;function f(x){return e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),h=x,F(x)}function F(x){return x===h?(e.consume(x),l++,F):(e.exit("codeFencedFenceSequence"),l<3?t(x):T(e,d,"whitespace")(x))}function d(x){return x===null||A(x)?S(x):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),p(x))}function p(x){return x===null||G(x)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),T(e,m,"whitespace")(x)):x===96&&x===h?t(x):(e.consume(x),p)}function m(x){return x===null||A(x)?S(x):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),E(x))}function E(x){return x===null||A(x)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),S(x)):x===96&&x===h?t(x):(e.consume(x),E)}function S(x){return e.exit("codeFencedFence"),n.interrupt?r(x):g(x)}function g(x){return x===null?v(x):A(x)?e.attempt(u,e.attempt(i,v,s?T(e,g,"linePrefix",s+1):g),v)(x):(e.enter("codeFlowValue"),_(x))}function _(x){return x===null||A(x)?(e.exit("codeFlowValue"),g(x)):(e.consume(x),_)}function v(x){return e.exit("codeFenced"),r(x)}function z(x,L,O){let w=this;return R;function R(P){return x.enter("lineEnding"),x.consume(P),x.exit("lineEnding"),I}function I(P){return w.parser.lazy[w.now().line]?O(P):L(P)}}function D(x,L,O){let w=0;return T(x,R,"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4);function R(b){return x.enter("codeFencedFence"),x.enter("codeFencedFenceSequence"),I(b)}function I(b){return b===h?(x.consume(b),w++,I):w<l?O(b):(x.exit("codeFencedFenceSequence"),T(x,P,"whitespace")(b))}function P(b){return b===null||A(b)?(x.exit("codeFencedFence"),L(b)):O(b)}}}var Fe={name:"codeIndented",tokenize:un},rn={tokenize:cn,partial:!0};function un(e,r,t){let n=this;return i;function i(h){return e.enter("codeIndented"),T(e,u,"linePrefix",4+1)(h)}function u(h){let f=n.events[n.events.length-1];return f&&f[1].type==="linePrefix"&&f[2].sliceSerialize(f[1],!0).length>=4?c(h):t(h)}function c(h){return h===null?l(h):A(h)?e.attempt(rn,c,l)(h):(e.enter("codeFlowValue"),s(h))}function s(h){return h===null||A(h)?(e.exit("codeFlowValue"),c(h)):(e.consume(h),s)}function l(h){return e.exit("codeIndented"),r(h)}}function cn(e,r,t){let n=this;return i;function i(c){return n.parser.lazy[n.now().line]?t(c):A(c)?(e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),i):T(e,u,"linePrefix",4+1)(c)}function u(c){let s=n.events[n.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?r(c):A(c)?i(c):t(c)}}var on={name:"codeText",tokenize:ln,resolve:an,previous:sn};function an(e){let r=e.length-4,t=3,n,i;if((e[t][1].type==="lineEnding"||e[t][1].type==="space")&&(e[r][1].type==="lineEnding"||e[r][1].type==="space")){for(n=t;++n<r;)if(e[n][1].type==="codeTextData"){e[t][1].type="codeTextPadding",e[r][1].type="codeTextPadding",t+=2,r-=2;break}}for(n=t-1,r++;++n<=r;)i===void 0?n!==r&&e[n][1].type!=="lineEnding"&&(i=n):(n===r||e[n][1].type==="lineEnding")&&(e[i][1].type="codeTextData",n!==i+2&&(e[i][1].end=e[n-1][1].end,e.splice(i+2,n-i-2),r-=n-i-2,n=i+2),i=void 0);return e}function sn(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function ln(e,r,t){let n=this,i=0,u,c;return s;function s(d){return e.enter("codeText"),e.enter("codeTextSequence"),l(d)}function l(d){return d===96?(e.consume(d),i++,l):(e.exit("codeTextSequence"),h(d))}function h(d){return d===null?t(d):d===96?(c=e.enter("codeTextSequence"),u=0,F(d)):d===32?(e.enter("space"),e.consume(d),e.exit("space"),h):A(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),h):(e.enter("codeTextData"),f(d))}function f(d){return d===null||d===32||d===96||A(d)?(e.exit("codeTextData"),h(d)):(e.consume(d),f)}function F(d){return d===96?(e.consume(d),u++,F):u===i?(e.exit("codeTextSequence"),e.exit("codeText"),r(d)):(c.type="codeTextData",f(d))}}function Ge(e){let r={},t=-1,n,i,u,c,s,l,h;for(;++t<e.length;){for(;t in r;)t=r[t];if(n=e[t],t&&n[1].type==="chunkFlow"&&e[t-1][1].type==="listItemPrefix"&&(l=n[1]._tokenizer.events,u=0,u<l.length&&l[u][1].type==="lineEndingBlank"&&(u+=2),u<l.length&&l[u][1].type==="content"))for(;++u<l.length&&l[u][1].type!=="content";)l[u][1].type==="chunkText"&&(l[u][1]._isInFirstContentOfListItem=!0,u++);if(n[0]==="enter")n[1].contentType&&(Object.assign(r,fn(e,t)),t=r[t],h=!0);else if(n[1]._container){for(u=t,i=void 0;u--&&(c=e[u],c[1].type==="lineEnding"||c[1].type==="lineEndingBlank");)c[0]==="enter"&&(i&&(e[i][1].type="lineEndingBlank"),c[1].type="lineEnding",i=u);i&&(n[1].end=Object.assign({},e[i][1].start),s=e.slice(i,t),s.unshift(n),te(e,i,t-i+1,s))}}return!h}function fn(e,r){let t=e[r][1],n=e[r][2],i=r-1,u=[],c=t._tokenizer||n.parser[t.contentType](t.start),s=c.events,l=[],h={},f,F,d=-1,p=t,m=0,E=0,S=[E];for(;p;){for(;e[++i][1]!==p;);u.push(i),p._tokenizer||(f=n.sliceStream(p),p.next||f.push(null),F&&c.defineSkip(p.start),p._isInFirstContentOfListItem&&(c._gfmTasklistFirstContentOfListItem=!0),c.write(f),p._isInFirstContentOfListItem&&(c._gfmTasklistFirstContentOfListItem=void 0)),F=p,p=p.next}for(p=t;++d<s.length;)s[d][0]==="exit"&&s[d-1][0]==="enter"&&s[d][1].type===s[d-1][1].type&&s[d][1].start.line!==s[d][1].end.line&&(E=d+1,S.push(E),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(c.events=[],p?(p._tokenizer=void 0,p.previous=void 0):S.pop(),d=S.length;d--;){let g=s.slice(S[d],S[d+1]),_=u.pop();l.unshift([_,_+g.length-1]),te(e,_,2,g)}for(d=-1;++d<l.length;)h[m+l[d][0]]=m+l[d][1],m+=l[d][1]-l[d][0]-1;return h}var dn={tokenize:mn,resolve:hn},pn={tokenize:gn,partial:!0};function hn(e){return Ge(e),e}function mn(e,r){let t;return n;function n(s){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),i(s)}function i(s){return s===null?u(s):A(s)?e.check(pn,c,u)(s):(e.consume(s),i)}function u(s){return e.exit("chunkContent"),e.exit("content"),r(s)}function c(s){return e.consume(s),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,i}}function gn(e,r,t){let n=this;return i;function i(c){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),T(e,u,"linePrefix")}function u(c){if(c===null||A(c))return t(c);let s=n.events[n.events.length-1];return!n.parser.constructs.disable.null.includes("codeIndented")&&s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?r(c):e.interrupt(n.parser.constructs.flow,t,r)(c)}}function Ke(e,r,t,n,i,u,c,s,l){let h=l||Number.POSITIVE_INFINITY,f=0;return F;function F(g){return g===60?(e.enter(n),e.enter(i),e.enter(u),e.consume(g),e.exit(u),d):g===null||g===41||ye(g)?t(g):(e.enter(n),e.enter(c),e.enter(s),e.enter("chunkString",{contentType:"string"}),E(g))}function d(g){return g===62?(e.enter(u),e.consume(g),e.exit(u),e.exit(i),e.exit(n),r):(e.enter(s),e.enter("chunkString",{contentType:"string"}),p(g))}function p(g){return g===62?(e.exit("chunkString"),e.exit(s),d(g)):g===null||g===60||A(g)?t(g):(e.consume(g),g===92?m:p)}function m(g){return g===60||g===62||g===92?(e.consume(g),p):p(g)}function E(g){return g===40?++f>h?t(g):(e.consume(g),E):g===41?f--?(e.consume(g),E):(e.exit("chunkString"),e.exit(s),e.exit(c),e.exit(n),r(g)):g===null||G(g)?f?t(g):(e.exit("chunkString"),e.exit(s),e.exit(c),e.exit(n),r(g)):ye(g)?t(g):(e.consume(g),g===92?S:E)}function S(g){return g===40||g===41||g===92?(e.consume(g),E):E(g)}}function Ue(e,r,t,n,i,u){let c=this,s=0,l;return h;function h(p){return e.enter(n),e.enter(i),e.consume(p),e.exit(i),e.enter(u),f}function f(p){return p===null||p===91||p===93&&!l||p===94&&!s&&"_hiddenFootnoteSupport"in c.parser.constructs||s>999?t(p):p===93?(e.exit(u),e.enter(i),e.consume(p),e.exit(i),e.exit(n),r):A(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),f):(e.enter("chunkString",{contentType:"string"}),F(p))}function F(p){return p===null||p===91||p===93||A(p)||s++>999?(e.exit("chunkString"),f(p)):(e.consume(p),l=l||!B(p),p===92?d:F)}function d(p){return p===91||p===92||p===93?(e.consume(p),s++,F):F(p)}}function Xe(e,r,t,n,i,u){let c;return s;function s(d){return e.enter(n),e.enter(i),e.consume(d),e.exit(i),c=d===40?41:d,l}function l(d){return d===c?(e.enter(i),e.consume(d),e.exit(i),e.exit(n),r):(e.enter(u),h(d))}function h(d){return d===c?(e.exit(u),l(c)):d===null?t(d):A(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),T(e,h,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),f(d))}function f(d){return d===c||d===null||A(d)?(e.exit("chunkString"),h(d)):(e.consume(d),d===92?F:f)}function F(d){return d===c||d===92?(e.consume(d),f):f(d)}}function pe(e,r){let t;return n;function n(i){return A(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,n):B(i)?T(e,n,t?"linePrefix":"lineSuffix")(i):r(i)}}var xn={name:"definition",tokenize:Fn},kn={tokenize:vn,partial:!0};function Fn(e,r,t){let n=this,i;return u;function u(l){return e.enter("definition"),Ue.call(n,e,c,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(l)}function c(l){return i=he(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)),l===58?(e.enter("definitionMarker"),e.consume(l),e.exit("definitionMarker"),pe(e,Ke(e,e.attempt(kn,T(e,s,"whitespace"),T(e,s,"whitespace")),t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString"))):t(l)}function s(l){return l===null||A(l)?(e.exit("definition"),n.parser.defined.includes(i)||n.parser.defined.push(i),r(l)):t(l)}}function vn(e,r,t){return n;function n(c){return G(c)?pe(e,i)(c):t(c)}function i(c){return c===34||c===39||c===40?Xe(e,T(e,u,"whitespace"),t,"definitionTitle","definitionTitleMarker","definitionTitleString")(c):t(c)}function u(c){return c===null||A(c)?r(c):t(c)}}var Sn={name:"hardBreakEscape",tokenize:bn};function bn(e,r,t){return n;function n(u){return e.enter("hardBreakEscape"),e.enter("escapeMarker"),e.consume(u),i}function i(u){return A(u)?(e.exit("escapeMarker"),e.exit("hardBreakEscape"),r(u)):t(u)}}var yn={name:"headingAtx",tokenize:En,resolve:An};function An(e,r){let t=e.length-2,n=3,i,u;return e[n][1].type==="whitespace"&&(n+=2),t-2>n&&e[t][1].type==="whitespace"&&(t-=2),e[t][1].type==="atxHeadingSequence"&&(n===t-1||t-4>n&&e[t-2][1].type==="whitespace")&&(t-=n+1===t?2:4),t>n&&(i={type:"atxHeadingText",start:e[n][1].start,end:e[t][1].end},u={type:"chunkText",start:e[n][1].start,end:e[t][1].end,contentType:"text"},te(e,n,t-n+1,[["enter",i,r],["enter",u,r],["exit",u,r],["exit",i,r]])),e}function En(e,r,t){let n=this,i=0;return u;function u(f){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),c(f)}function c(f){return f===35&&i++<6?(e.consume(f),c):f===null||G(f)?(e.exit("atxHeadingSequence"),n.interrupt?r(f):s(f)):t(f)}function s(f){return f===35?(e.enter("atxHeadingSequence"),l(f)):f===null||A(f)?(e.exit("atxHeading"),r(f)):B(f)?T(e,s,"whitespace")(f):(e.enter("atxHeadingText"),h(f))}function l(f){return f===35?(e.consume(f),l):(e.exit("atxHeadingSequence"),s(f))}function h(f){return f===null||f===35||G(f)?(e.exit("atxHeadingText"),s(f)):(e.consume(f),h)}}var Cn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],qe=["pre","script","style","textarea"],zn={name:"htmlFlow",tokenize:Dn,resolveTo:Tn,concrete:!0},In={tokenize:_n,partial:!0};function Tn(e){let r=e.length;for(;r--&&!(e[r][0]==="enter"&&e[r][1].type==="htmlFlow"););return r>1&&e[r-2][1].type==="linePrefix"&&(e[r][1].start=e[r-2][1].start,e[r+1][1].start=e[r-2][1].start,e.splice(r-2,2)),e}function Dn(e,r,t){let n=this,i,u,c,s,l;return h;function h(o){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(o),f}function f(o){return o===33?(e.consume(o),F):o===47?(e.consume(o),m):o===63?(e.consume(o),i=3,n.interrupt?r:Q):ee(o)?(e.consume(o),c=String.fromCharCode(o),u=!0,E):t(o)}function F(o){return o===45?(e.consume(o),i=2,d):o===91?(e.consume(o),i=5,c="CDATA[",s=0,p):ee(o)?(e.consume(o),i=4,n.interrupt?r:Q):t(o)}function d(o){return o===45?(e.consume(o),n.interrupt?r:Q):t(o)}function p(o){return o===c.charCodeAt(s++)?(e.consume(o),s===c.length?n.interrupt?r:I:p):t(o)}function m(o){return ee(o)?(e.consume(o),c=String.fromCharCode(o),E):t(o)}function E(o){return o===null||o===47||o===62||G(o)?o!==47&&u&&qe.includes(c.toLowerCase())?(i=1,n.interrupt?r(o):I(o)):Cn.includes(c.toLowerCase())?(i=6,o===47?(e.consume(o),S):n.interrupt?r(o):I(o)):(i=7,n.interrupt&&!n.parser.lazy[n.now().line]?t(o):u?_(o):g(o)):o===45||Z(o)?(e.consume(o),c+=String.fromCharCode(o),E):t(o)}function S(o){return o===62?(e.consume(o),n.interrupt?r:I):t(o)}function g(o){return B(o)?(e.consume(o),g):w(o)}function _(o){return o===47?(e.consume(o),w):o===58||o===95||ee(o)?(e.consume(o),v):B(o)?(e.consume(o),_):w(o)}function v(o){return o===45||o===46||o===58||o===95||Z(o)?(e.consume(o),v):z(o)}function z(o){return o===61?(e.consume(o),D):B(o)?(e.consume(o),z):_(o)}function D(o){return o===null||o===60||o===61||o===62||o===96?t(o):o===34||o===39?(e.consume(o),l=o,x):B(o)?(e.consume(o),D):(l=null,L(o))}function x(o){return o===null||A(o)?t(o):o===l?(e.consume(o),O):(e.consume(o),x)}function L(o){return o===null||o===34||o===39||o===60||o===61||o===62||o===96||G(o)?z(o):(e.consume(o),L)}function O(o){return o===47||o===62||B(o)?_(o):t(o)}function w(o){return o===62?(e.consume(o),R):t(o)}function R(o){return B(o)?(e.consume(o),R):o===null||A(o)?I(o):t(o)}function I(o){return o===45&&i===2?(e.consume(o),q):o===60&&i===1?(e.consume(o),N):o===62&&i===4?(e.consume(o),M):o===63&&i===3?(e.consume(o),Q):o===93&&i===5?(e.consume(o),j):A(o)&&(i===6||i===7)?e.check(In,M,P)(o):o===null||A(o)?P(o):(e.consume(o),I)}function P(o){return e.exit("htmlFlowData"),b(o)}function b(o){return o===null?a(o):A(o)?e.attempt({tokenize:C,partial:!0},b,a)(o):(e.enter("htmlFlowData"),I(o))}function C(o,se,ue){return ce;function ce(ne){return o.enter("lineEnding"),o.consume(ne),o.exit("lineEnding"),le}function le(ne){return n.parser.lazy[n.now().line]?ue(ne):se(ne)}}function q(o){return o===45?(e.consume(o),Q):I(o)}function N(o){return o===47?(e.consume(o),c="",K):I(o)}function K(o){return o===62&&qe.includes(c.toLowerCase())?(e.consume(o),M):ee(o)&&c.length<8?(e.consume(o),c+=String.fromCharCode(o),K):I(o)}function j(o){return o===93?(e.consume(o),Q):I(o)}function Q(o){return o===62?(e.consume(o),M):o===45&&i===2?(e.consume(o),Q):I(o)}function M(o){return o===null||A(o)?(e.exit("htmlFlowData"),a(o)):(e.consume(o),M)}function a(o){return e.exit("htmlFlow"),r(o)}}function _n(e,r,t){return n;function n(i){return e.exit("htmlFlowData"),e.enter("lineEndingBlank"),e.consume(i),e.exit("lineEndingBlank"),e.attempt(xe,r,t)}}var wn={name:"htmlText",tokenize:Ln};function Ln(e,r,t){let n=this,i,u,c,s;return l;function l(a){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(a),h}function h(a){return a===33?(e.consume(a),f):a===47?(e.consume(a),L):a===63?(e.consume(a),D):ee(a)?(e.consume(a),R):t(a)}function f(a){return a===45?(e.consume(a),F):a===91?(e.consume(a),u="CDATA[",c=0,S):ee(a)?(e.consume(a),z):t(a)}function F(a){return a===45?(e.consume(a),d):t(a)}function d(a){return a===null||a===62?t(a):a===45?(e.consume(a),p):m(a)}function p(a){return a===null||a===62?t(a):m(a)}function m(a){return a===null?t(a):a===45?(e.consume(a),E):A(a)?(s=m,j(a)):(e.consume(a),m)}function E(a){return a===45?(e.consume(a),M):m(a)}function S(a){return a===u.charCodeAt(c++)?(e.consume(a),c===u.length?g:S):t(a)}function g(a){return a===null?t(a):a===93?(e.consume(a),_):A(a)?(s=g,j(a)):(e.consume(a),g)}function _(a){return a===93?(e.consume(a),v):g(a)}function v(a){return a===62?M(a):a===93?(e.consume(a),v):g(a)}function z(a){return a===null||a===62?M(a):A(a)?(s=z,j(a)):(e.consume(a),z)}function D(a){return a===null?t(a):a===63?(e.consume(a),x):A(a)?(s=D,j(a)):(e.consume(a),D)}function x(a){return a===62?M(a):D(a)}function L(a){return ee(a)?(e.consume(a),O):t(a)}function O(a){return a===45||Z(a)?(e.consume(a),O):w(a)}function w(a){return A(a)?(s=w,j(a)):B(a)?(e.consume(a),w):M(a)}function R(a){return a===45||Z(a)?(e.consume(a),R):a===47||a===62||G(a)?I(a):t(a)}function I(a){return a===47?(e.consume(a),M):a===58||a===95||ee(a)?(e.consume(a),P):A(a)?(s=I,j(a)):B(a)?(e.consume(a),I):M(a)}function P(a){return a===45||a===46||a===58||a===95||Z(a)?(e.consume(a),P):b(a)}function b(a){return a===61?(e.consume(a),C):A(a)?(s=b,j(a)):B(a)?(e.consume(a),b):I(a)}function C(a){return a===null||a===60||a===61||a===62||a===96?t(a):a===34||a===39?(e.consume(a),i=a,q):A(a)?(s=C,j(a)):B(a)?(e.consume(a),C):(e.consume(a),i=void 0,K)}function q(a){return a===i?(e.consume(a),N):a===null?t(a):A(a)?(s=q,j(a)):(e.consume(a),q)}function N(a){return a===62||a===47||G(a)?I(a):t(a)}function K(a){return a===null||a===34||a===39||a===60||a===61||a===96?t(a):a===62||G(a)?I(a):(e.consume(a),K)}function j(a){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),T(e,Q,"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Q(a){return e.enter("htmlTextData"),s(a)}function M(a){return a===62?(e.consume(a),e.exit("htmlTextData"),e.exit("htmlText"),r):t(a)}}var Ce={name:"labelEnd",tokenize:Rn,resolveTo:jn,resolveAll:Pn},Mn={tokenize:qn},On={tokenize:Hn},Bn={tokenize:Vn};function Pn(e){let r=-1,t;for(;++r<e.length;)t=e[r][1],(t.type==="labelImage"||t.type==="labelLink"||t.type==="labelEnd")&&(e.splice(r+1,t.type==="labelImage"?4:2),t.type="data",r++);return e}function jn(e,r){let t=e.length,n=0,i,u,c,s;for(;t--;)if(i=e[t][1],u){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[t][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(c){if(e[t][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(u=t,i.type!=="labelLink")){n=2;break}}else i.type==="labelEnd"&&(c=t);let l={type:e[u][1].type==="labelLink"?"link":"image",start:Object.assign({},e[u][1].start),end:Object.assign({},e[e.length-1][1].end)},h={type:"label",start:Object.assign({},e[u][1].start),end:Object.assign({},e[c][1].end)},f={type:"labelText",start:Object.assign({},e[u+n+2][1].end),end:Object.assign({},e[c-2][1].start)};return s=[["enter",l,r],["enter",h,r]],s=V(s,e.slice(u+1,u+n+3)),s=V(s,[["enter",f,r]]),s=V(s,Ee(r.parser.constructs.insideSpan.null,e.slice(u+n+4,c-3),r)),s=V(s,[["exit",f,r],e[c-2],e[c-1],["exit",h,r]]),s=V(s,e.slice(c+1)),s=V(s,[["exit",l,r]]),te(e,u,e.length,s),e}function Rn(e,r,t){let n=this,i=n.events.length,u,c;for(;i--;)if((n.events[i][1].type==="labelImage"||n.events[i][1].type==="labelLink")&&!n.events[i][1]._balanced){u=n.events[i][1];break}return s;function s(f){return u?u._inactive?h(f):(c=n.parser.defined.includes(he(n.sliceSerialize({start:u.end,end:n.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(f),e.exit("labelMarker"),e.exit("labelEnd"),l):t(f)}function l(f){return f===40?e.attempt(Mn,r,c?r:h)(f):f===91?e.attempt(On,r,c?e.attempt(Bn,r,h):h)(f):c?r(f):h(f)}function h(f){return u._balanced=!0,t(f)}}function qn(e,r,t){return n;function n(l){return e.enter("resource"),e.enter("resourceMarker"),e.consume(l),e.exit("resourceMarker"),pe(e,i)}function i(l){return l===41?s(l):Ke(e,u,t,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(l)}function u(l){return G(l)?pe(e,c)(l):s(l)}function c(l){return l===34||l===39||l===40?Xe(e,pe(e,s),t,"resourceTitle","resourceTitleMarker","resourceTitleString")(l):s(l)}function s(l){return l===41?(e.enter("resourceMarker"),e.consume(l),e.exit("resourceMarker"),e.exit("resource"),r):t(l)}}function Hn(e,r,t){let n=this;return i;function i(c){return Ue.call(n,e,u,t,"reference","referenceMarker","referenceString")(c)}function u(c){return n.parser.defined.includes(he(n.sliceSerialize(n.events[n.events.length-1][1]).slice(1,-1)))?r(c):t(c)}}function Vn(e,r,t){return n;function n(u){return e.enter("reference"),e.enter("referenceMarker"),e.consume(u),e.exit("referenceMarker"),i}function i(u){return u===93?(e.enter("referenceMarker"),e.consume(u),e.exit("referenceMarker"),e.exit("reference"),r):t(u)}}var Qn={name:"labelStartImage",tokenize:Nn,resolveAll:Ce.resolveAll};function Nn(e,r,t){let n=this;return i;function i(s){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(s),e.exit("labelImageMarker"),u}function u(s){return s===91?(e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelImage"),c):t(s)}function c(s){return s===94&&"_hiddenFootnoteSupport"in n.parser.constructs?t(s):r(s)}}var Wn={name:"labelStartLink",tokenize:$n,resolveAll:Ce.resolveAll};function $n(e,r,t){let n=this;return i;function i(c){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(c),e.exit("labelMarker"),e.exit("labelLink"),u}function u(c){return c===94&&"_hiddenFootnoteSupport"in n.parser.constructs?t(c):r(c)}}var ve={name:"lineEnding",tokenize:Zn};function Zn(e,r){return t;function t(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),T(e,r,"linePrefix")}}var ge={name:"thematicBreak",tokenize:Gn};function Gn(e,r,t){let n=0,i;return u;function u(l){return e.enter("thematicBreak"),i=l,c(l)}function c(l){return l===i?(e.enter("thematicBreakSequence"),s(l)):B(l)?T(e,c,"whitespace")(l):n<3||l!==null&&!A(l)?t(l):(e.exit("thematicBreak"),r(l))}function s(l){return l===i?(e.consume(l),n++,s):(e.exit("thematicBreakSequence"),c(l))}}var $={name:"list",tokenize:Xn,continuation:{tokenize:Yn},exit:er},Kn={tokenize:tr,partial:!0},Un={tokenize:Jn,partial:!0};function Xn(e,r,t){let n=this,i=n.events[n.events.length-1],u=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,c=0;return s;function s(p){let m=n.containerState.type||(p===42||p===43||p===45?"listUnordered":"listOrdered");if(m==="listUnordered"?!n.containerState.marker||p===n.containerState.marker:be(p)){if(n.containerState.type||(n.containerState.type=m,e.enter(m,{_container:!0})),m==="listUnordered")return e.enter("listItemPrefix"),p===42||p===45?e.check(ge,t,h)(p):h(p);if(!n.interrupt||p===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),l(p)}return t(p)}function l(p){return be(p)&&++c<10?(e.consume(p),l):(!n.interrupt||c<2)&&(n.containerState.marker?p===n.containerState.marker:p===41||p===46)?(e.exit("listItemValue"),h(p)):t(p)}function h(p){return e.enter("listItemMarker"),e.consume(p),e.exit("listItemMarker"),n.containerState.marker=n.containerState.marker||p,e.check(xe,n.interrupt?t:f,e.attempt(Kn,d,F))}function f(p){return n.containerState.initialBlankLine=!0,u++,d(p)}function F(p){return B(p)?(e.enter("listItemPrefixWhitespace"),e.consume(p),e.exit("listItemPrefixWhitespace"),d):t(p)}function d(p){return n.containerState.size=u+n.sliceSerialize(e.exit("listItemPrefix"),!0).length,r(p)}}function Yn(e,r,t){let n=this;return n.containerState._closeFlow=void 0,e.check(xe,i,u);function i(s){return n.containerState.furtherBlankLines=n.containerState.furtherBlankLines||n.containerState.initialBlankLine,T(e,r,"listItemIndent",n.containerState.size+1)(s)}function u(s){return n.containerState.furtherBlankLines||!B(s)?(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,c(s)):(n.containerState.furtherBlankLines=void 0,n.containerState.initialBlankLine=void 0,e.attempt(Un,r,c)(s))}function c(s){return n.containerState._closeFlow=!0,n.interrupt=void 0,T(e,e.attempt($,r,t),"linePrefix",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s)}}function Jn(e,r,t){let n=this;return T(e,i,"listItemIndent",n.containerState.size+1);function i(u){let c=n.events[n.events.length-1];return c&&c[1].type==="listItemIndent"&&c[2].sliceSerialize(c[1],!0).length===n.containerState.size?r(u):t(u)}}function er(e){e.exit(this.containerState.type)}function tr(e,r,t){let n=this;return T(e,i,"listItemPrefixWhitespace",n.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(u){let c=n.events[n.events.length-1];return!B(u)&&c&&c[1].type==="listItemPrefixWhitespace"?r(u):t(u)}}var He={name:"setextUnderline",tokenize:rr,resolveTo:nr};function nr(e,r){let t=e.length,n,i,u;for(;t--;)if(e[t][0]==="enter"){if(e[t][1].type==="content"){n=t;break}e[t][1].type==="paragraph"&&(i=t)}else e[t][1].type==="content"&&e.splice(t,1),!u&&e[t][1].type==="definition"&&(u=t);let c={type:"setextHeading",start:Object.assign({},e[i][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[i][1].type="setextHeadingText",u?(e.splice(i,0,["enter",c,r]),e.splice(u+1,0,["exit",e[n][1],r]),e[n][1].end=Object.assign({},e[u][1].end)):e[n][1]=c,e.push(["exit",c,r]),e}function rr(e,r,t){let n=this,i=n.events.length,u,c;for(;i--;)if(n.events[i][1].type!=="lineEnding"&&n.events[i][1].type!=="linePrefix"&&n.events[i][1].type!=="content"){c=n.events[i][1].type==="paragraph";break}return s;function s(f){return!n.parser.lazy[n.now().line]&&(n.interrupt||c)?(e.enter("setextHeadingLine"),e.enter("setextHeadingLineSequence"),u=f,l(f)):t(f)}function l(f){return f===u?(e.consume(f),l):(e.exit("setextHeadingLineSequence"),T(e,h,"lineSuffix")(f))}function h(f){return f===null||A(f)?(e.exit("setextHeadingLine"),r(f)):t(f)}}var ir={tokenize:ur};function ur(e){let r=this,t=e.attempt(xe,n,e.attempt(this.parser.constructs.flowInitial,i,T(e,e.attempt(this.parser.constructs.flow,i,e.attempt(dn,i)),"linePrefix")));return t;function n(u){if(u===null){e.consume(u);return}return e.enter("lineEndingBlank"),e.consume(u),e.exit("lineEndingBlank"),r.currentConstruct=void 0,t}function i(u){if(u===null){e.consume(u);return}return e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),r.currentConstruct=void 0,t}}var cr={resolveAll:Je()},or=Ye("string"),ar=Ye("text");function Ye(e){return{tokenize:r,resolveAll:Je(e==="text"?sr:void 0)};function r(t){let n=this,i=this.parser.constructs[e],u=t.attempt(i,c,s);return c;function c(f){return h(f)?u(f):s(f)}function s(f){if(f===null){t.consume(f);return}return t.enter("data"),t.consume(f),l}function l(f){return h(f)?(t.exit("data"),u(f)):(t.consume(f),l)}function h(f){if(f===null)return!0;let F=i[f],d=-1;if(F)for(;++d<F.length;){let p=F[d];if(!p.previous||p.previous.call(n,n.previous))return!0}return!1}}}function Je(e){return r;function r(t,n){let i=-1,u;for(;++i<=t.length;)u===void 0?t[i]&&t[i][1].type==="data"&&(u=i,i++):(!t[i]||t[i][1].type!=="data")&&(i!==u+2&&(t[u][1].end=t[i-1][1].end,t.splice(u+2,i-u-2),i=u+2),u=void 0);return e?e(t,n):t}}function sr(e,r){let t=0;for(;++t<=e.length;)if((t===e.length||e[t][1].type==="lineEnding")&&e[t-1][1].type==="data"){let n=e[t-1][1],i=r.sliceStream(n),u=i.length,c=-1,s=0,l;for(;u--;){let h=i[u];if(typeof h=="string"){for(c=h.length;h.charCodeAt(c-1)===32;)s++,c--;if(c)break;c=-1}else if(h===-2)l=!0,s++;else if(h!==-1){u++;break}}if(s){let h={type:t===e.length||l||s<2?"lineSuffix":"hardBreakTrailing",start:{line:n.end.line,column:n.end.column-s,offset:n.end.offset-s,_index:n.start._index+u,_bufferIndex:u?c:n.start._bufferIndex+c},end:Object.assign({},n.end)};n.end=Object.assign({},h.start),n.start.offset===n.end.offset?Object.assign(n,h):(e.splice(t,0,["enter",h,r],["exit",h,r]),t+=2)}t++}return e}function lr(e,r,t){let n=Object.assign(t?Object.assign({},t):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1}),i={},u=[],c=[],s=[],l=!0,h={consume:z,enter:D,exit:x,attempt:w(L),check:w(O),interrupt:w(O,{interrupt:!0})},f={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:E,sliceSerialize:m,now:S,defineSkip:g,write:p},F=r.tokenize.call(f,h),d;return r.resolveAll&&u.push(r),f;function p(b){return c=V(c,b),_(),c[c.length-1]!==null?[]:(R(r,0),f.events=Ee(u,f.events,f),f.events)}function m(b,C){return dr(E(b),C)}function E(b){return fr(c,b)}function S(){return Object.assign({},n)}function g(b){i[b.line]=b.column,P()}function _(){let b;for(;n._index<c.length;){let C=c[n._index];if(typeof C=="string")for(b=n._index,n._bufferIndex<0&&(n._bufferIndex=0);n._index===b&&n._bufferIndex<C.length;)v(C.charCodeAt(n._bufferIndex));else v(C)}}function v(b){l=void 0,d=b,F=F(b)}function z(b){A(b)?(n.line++,n.column=1,n.offset+=b===-3?2:1,P()):b!==-1&&(n.column++,n.offset++),n._bufferIndex<0?n._index++:(n._bufferIndex++,n._bufferIndex===c[n._index].length&&(n._bufferIndex=-1,n._index++)),f.previous=b,l=!0}function D(b,C){let q=C||{};return q.type=b,q.start=S(),f.events.push(["enter",q,f]),s.push(q),q}function x(b){let C=s.pop();return C.end=S(),f.events.push(["exit",C,f]),C}function L(b,C){R(b,C.from)}function O(b,C){C.restore()}function w(b,C){return q;function q(N,K,j){let Q,M,a,o;return Array.isArray(N)?ue(N):"tokenize"in N?ue([N]):se(N);function se(H){return fe;function fe(Y){let oe=Y!==null&&H[Y],ae=Y!==null&&H.null,ke=[...Array.isArray(oe)?oe:oe?[oe]:[],...Array.isArray(ae)?ae:ae?[ae]:[]];return ue(ke)(Y)}}function ue(H){return Q=H,M=0,H.length===0?j:ce(H[M])}function ce(H){return fe;function fe(Y){return o=I(),a=H,H.partial||(f.currentConstruct=H),H.name&&f.parser.constructs.disable.null.includes(H.name)?ne(Y):H.tokenize.call(C?Object.assign(Object.create(f),C):f,h,le,ne)(Y)}}function le(H){return l=!0,b(a,o),K}function ne(H){return l=!0,o.restore(),++M<Q.length?ce(Q[M]):j}}}function R(b,C){b.resolveAll&&!u.includes(b)&&u.push(b),b.resolve&&te(f.events,C,f.events.length-C,b.resolve(f.events.slice(C),f)),b.resolveTo&&(f.events=b.resolveTo(f.events,f))}function I(){let b=S(),C=f.previous,q=f.currentConstruct,N=f.events.length,K=Array.from(s);return{restore:j,from:N};function j(){n=b,f.previous=C,f.currentConstruct=q,f.events.length=N,s=K,P()}}function P(){n.line in i&&n.column<2&&(n.column=i[n.line],n.offset+=i[n.line]-1)}}function fr(e,r){let t=r.start._index,n=r.start._bufferIndex,i=r.end._index,u=r.end._bufferIndex,c;return t===i?c=[e[t].slice(n,u)]:(c=e.slice(t,i),n>-1&&(c[0]=c[0].slice(n)),u>0&&c.push(e[i].slice(0,u))),c}function dr(e,r){let t=-1,n=[],i;for(;++t<e.length;){let u=e[t],c;if(typeof u=="string")c=u;else switch(u){case-5:{c="\r";break}case-4:{c=`
`;break}case-3:{c=`\r
`;break}case-2:{c=r?" ":"	";break}case-1:{if(!r&&i)continue;c=" ";break}default:c=String.fromCharCode(u)}i=u===-2,n.push(c)}return n.join("")}var et={};Et(et,{attentionMarkers:()=>vr,contentInitial:()=>hr,disable:()=>Sr,document:()=>pr,flow:()=>gr,flowInitial:()=>mr,insideSpan:()=>Fr,string:()=>xr,text:()=>kr});var pr={[42]:$,[43]:$,[45]:$,[48]:$,[49]:$,[50]:$,[51]:$,[52]:$,[53]:$,[54]:$,[55]:$,[56]:$,[57]:$,[62]:We},hr={[91]:xn},mr={[-2]:Fe,[-1]:Fe,[32]:Fe},gr={[35]:yn,[42]:ge,[45]:[He,ge],[60]:zn,[61]:He,[95]:ge,[96]:Re,[126]:Re},xr={[38]:Ze,[92]:$e},kr={[-5]:ve,[-4]:ve,[-3]:ve,[33]:Qn,[38]:Ze,[42]:Ae,[60]:[Gt,wn],[91]:Wn,[92]:[Sn,$e],[93]:Ce,[95]:Ae,[96]:on},Fr={null:[Ae,cr]},vr={null:[42,95]},Sr={null:[]};function br(e={}){let r=Ct([et].concat(e.extensions||[])),t={defined:[],lazy:{},constructs:r,content:n(Ht),document:n(Qt),flow:n(ir),string:n(or),text:n(ar)};return t;function n(i){return u;function u(c){return lr(t,i,c)}}}function yr(e){for(;!Ge(e););return e}var Ve=/[\0\t\n\r]/g;function Ar(){let e=1,r="",t=!0,n;return i;function i(u,c,s){let l=[],h,f,F,d,p;for(u=r+u.toString(c),F=0,r="",t&&(u.charCodeAt(0)===65279&&F++,t=void 0);F<u.length;){if(Ve.lastIndex=F,h=Ve.exec(u),d=h&&h.index!==void 0?h.index:u.length,p=u.charCodeAt(d),!h){r=u.slice(F);break}if(p===10&&F===d&&n)l.push(-3),n=void 0;else switch(n&&(l.push(-5),n=void 0),F<d&&(l.push(u.slice(F,d)),e+=d-F),p){case 0:{l.push(65533),e++;break}case 9:{for(f=Math.ceil(e/4)*4,l.push(-2);e++<f;)l.push(-1);break}case 10:{l.push(-4),e=1;break}default:n=!0,e=1}F=d+1}return s&&(n&&l.push(-5),r&&l.push(r),l.push(null)),l}}var Er=function(e,r,t){return typeof r!="string"&&(t=r,r=void 0),qt(t)(yr(br(t).document().write(Ar()(e,r,!0))))};export{Er as micromark};
