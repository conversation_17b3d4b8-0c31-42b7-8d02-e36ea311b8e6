﻿namespace Account.Web
{
    public class FrpcEntity
    {
        public string id { get { return Account.Web.Common.CryptoService.MD5Hash(string.Format("{0}-{1}", ip, type)); } }

        /// <summary>
        /// 日期
        /// </summary>
        public string date { get; set; }

        public string ip { get; set; }

        /// <summary>
        /// IP位置
        /// </summary>
        public string loc { get; set; }

        /// <summary>
        /// 服务商
        /// </summary>
        public string isp { get; set; }

        /// <summary>
        /// 测速
        /// </summary>
        public int speed { get; set; }

        /// <summary>
        /// 1：开放7000端口；2：开放Frpc，3：HTML验证通过
        /// </summary>
        public int type { get; set; }
    }
}