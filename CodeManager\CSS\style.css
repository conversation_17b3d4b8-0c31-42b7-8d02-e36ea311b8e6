﻿*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    background: #000;
    /*background-image: url("hexabump.png");*/
    font-size: 16px;
}

body {
    padding: 15px;
    z-index: -1;
    font-family: "microsoft yahei";
}

    body::before {
        display: block;
        position: absolute;
        content: "";
        top: 0;
        right: 0;
        left: 0;
        height: 8px;
        opacity: 1;
        z-index: 0;
        background: #31dddb;
        box-shadow: 0 9px 0 rgba(255, 255, 255, 0.1);
    }

.container {
    display: block;
    position: relative;
    /*margin: 50px auto;*/
    width: 100%;
}

h3 {
    color: #fff;
    font-size: 2em;
    font-weight: bold;
    font-family: "microsoft yahei";
    text-shadow: 0 0 10px #000;
    text-align: center;
}

p {
    color: #555;
    clear: both;
}

form {
    float: left;
    display: block;
    width: 100%;
    position: relative;
}

    form fieldset {
        float: left;
        position: absolute;
        width: 100%;
        padding: 20px;
        border: none;
        border-radius: 5px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
        background: #fff;
        background: whitesmoke;
        background: -webkit-linear-gradient(0deg, #eeeeee 0%, whitesmoke 100%);
        background: -moz-linear-gradient(0deg, #eeeeee 0%, whitesmoke 100%);
        background: -o-linear-gradient(0deg, #eeeeee 0%, whitesmoke 100%);
        background: linear-gradient(0deg, #eeeeee 0%, whitesmoke 100%);
        /* W3C */
    }

        form fieldset legend {
            float: left;
            display: block;
            width: 100%;
            position: relative;
            border-bottom: 1px solid #dfdfdf;
            top: 0;
            left: 0;
            clear: both;
            margin-bottom: 20px;
            padding-bottom: 10px;
            line-height: 30px;
            color: #444;
            text-shadow: 0 1px 0 #fff;
        }

        form fieldset.beta, form fieldset.charlie {
            display: none;
        }

        form fieldset .frow {
            float: left;
            display: block;
            width: 100%;
            margin-bottom: 10px;
        }

            form fieldset .frow:last-child {
                margin-bottom: 0px;
            }

        form fieldset input {
            float: left;
            width: 100%;
            padding: 12px 6px;
            font-size: 14px;
            font-weight: 400;
            font-family: "microsoft yahei";
            border: 1px solid #dedede;
            border-radius: 5px;
            box-shadow: 0 0 0 transparent, inset 0 0 5px #eee;
            -webkit-transition: all 0.2s ease-in-out;
        }

            form fieldset input::-webkit-input-placeholder {
                color: #ccc;
                opacity: 1;
                -webkit-transition: opacity 0.2s ease-in-out;
            }

            form fieldset input:focus {
                outline: none;
                border: 1px solid #27c1bf;
                box-shadow: 0 0 10px #27c1bf;
                -webkit-transition: all 0.2s ease-in-out;
            }

                form fieldset input:focus::-webkit-input-placeholder {
                    opacity: 0.5;
                }

        form fieldset select {
            float: left;
            width: 100%;
            padding: 12px 6px;
            font-size: 14px;
            font-weight: 400;
            font-family: "microsoft yahei";
            border: 1px solid #dedede;
            border-radius: 5px;
            box-shadow: 0 0 0 transparent, inset 0 0 5px #eee;
            -webkit-transition: all 0.2s ease-in-out;
        }

            form fieldset select::-webkit-select-placeholder {
                color: #ccc;
                opacity: 1;
                -webkit-transition: opacity 0.2s ease-in-out;
            }

            form fieldset select:focus {
                outline: none;
                border: 1px solid #27c1bf;
                box-shadow: 0 0 10px #27c1bf;
                -webkit-transition: all 0.2s ease-in-out;
            }

                form fieldset select:focus::-webkit-select-placeholder {
                    opacity: 0.5;
                }

            form fieldset a.next-step,
            form fieldset a.prev-step,
            form fieldset input[type="submit"] {
                display: block;
                width: 100%;
                height: 50px;
                padding: 0;
                background: #31dddb;
                line-height: 50px;
                text-align: center;
                text-decoration: none;
                text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
                font-size: 1.2em;
                font-family: "microsoft yahei";
                font-weight: 400;
                color: #fff;
                border: 1px solid #27c1bf;
                border-radius: 4px;
                box-shadow: 0 0 0 transparent;
            }

        form fieldset a.prev-step {
            background: #4a76a8;
            border: 1px solid #1d5b90;
        }

.out, .alpha.out, .beta.out {
    z-index: 0;
    opacity: 1;
    display: block;
    -webkit-animation: out 0.75s ease forwards;
    -moz-animation: out 0.75s ease forwards;
    -o-animation: out 0.75s ease forwards;
    animation: out 0.75s ease forwards;
}

@-webkit-keyframes out {
    0% {
        -webkit-transform: scale(1);
        opacity: 1;
    }

    25% {
        -webkit-transform: scale(1.05);
    }

    99% {
        -webkit-transform: scale(0.8);
    }

    100% {
        opacity: 0;
        display: none;
    }
}

@-moz-keyframes out {
    0% {
        -moz-transform: scale(1);
        opacity: 1;
    }

    25% {
        -moz-transform: scale(1.05);
    }

    99% {
        -moz-transform: scale(0.8);
    }

    100% {
        opacity: 0;
        display: none;
    }
}

@-o-keyframes out {
    0% {
        -o-transform: scale(1);
        opacity: 1;
    }

    25% {
        -o-transform: scale(1.05);
    }

    99% {
        -o-transform: scale(0.8);
    }

    100% {
        opacity: 0;
        display: none;
    }
}

@keyframes out {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
    }

    25% {
        -webkit-transform: scale(1.05);
        transform: scale(1.05);
    }

    99% {
        -webkit-transform: scale(0.8);
        transform: scale(0.8);
    }

    100% {
        opacity: 0;
        display: none;
    }
}

.in, .beta.in {
    z-index: 30;
    display: block;
    opacity: 0;
    -webkit-animation: in 0.75s ease forwards 0.25s;
    -moz-animation: in 0.75s ease forwards 0.25s;
    -o-animation: in 0.75s ease forwards 0.25s;
    animation: in 0.75s ease forwards 0.25s;
}

@-webkit-keyframes in {
    0% {
        -webkit-transform: translate3d(150px, 0, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@-moz-keyframes in {
    0% {
        -moz-transform: translate3d(150px, 0, 0);
        opacity: 0;
    }

    100% {
        -moz-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@-o-keyframes in {
    0% {
        -o-transform: translate3d(150px, 0, 0);
        opacity: 0;
    }

    100% {
        -o-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@keyframes in {
    0% {
        -webkit-transform: translate3d(150px, 0, 0);
        -moz-transform: translate3d(150px, 0, 0);
        -o-transform: translate3d(150px, 0, 0);
        transform: translate3d(150px, 0, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0px, 0, 0);
        -moz-transform: translate3d(0px, 0, 0);
        -o-transform: translate3d(0px, 0, 0);
        transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

.alpha.in {
    z-index: 30;
    display: block;
    opacity: 0;
    -webkit-animation: in-prev 0.75s ease forwards 0.25s;
    -moz-animation: in-prev 0.75s ease forwards 0.25s;
    -o-animation: in-prev 0.75s ease forwards 0.25s;
    animation: in-prev 0.75s ease forwards 0.25s;
}

@-webkit-keyframes in-prev {
    0% {
        -webkit-transform: translate3d(-150px, 0, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@-moz-keyframes in-prev {
    0% {
        -moz-transform: translate3d(-150px, 0, 0);
        opacity: 0;
    }

    100% {
        -moz-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@-o-keyframes in-prev {
    0% {
        -o-transform: translate3d(-150px, 0, 0);
        opacity: 0;
    }

    100% {
        -o-transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

@keyframes in-prev {
    0% {
        -webkit-transform: translate3d(-150px, 0, 0);
        -moz-transform: translate3d(-150px, 0, 0);
        -o-transform: translate3d(-150px, 0, 0);
        transform: translate3d(-150px, 0, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0px, 0, 0);
        -moz-transform: translate3d(0px, 0, 0);
        -o-transform: translate3d(0px, 0, 0);
        transform: translate3d(0px, 0, 0);
        opacity: 1;
    }
}

.charlie.in {
    z-index: 30;
    display: block;
    opacity: 0;
    -webkit-animation: in-charlie 0.75s ease-out forwards 0.25s;
    -moz-animation: in-charlie 0.75s ease-out forwards 0.25s;
    -o-animation: in-charlie 0.75s ease-out forwards 0.25s;
    animation: in-charlie 0.75s ease-out forwards 0.25s;
}

@-webkit-keyframes in-charlie {
    0% {
        -webkit-transform: translate3d(0, 150px, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@-moz-keyframes in-charlie {
    0% {
        -moz-transform: translate3d(0, 150px, 0);
        opacity: 0;
    }

    100% {
        -moz-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@-o-keyframes in-charlie {
    0% {
        -o-transform: translate3d(0, 150px, 0);
        opacity: 0;
    }

    100% {
        -o-transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes in-charlie {
    0% {
        -webkit-transform: translate3d(0, 150px, 0);
        -moz-transform: translate3d(0, 150px, 0);
        -o-transform: translate3d(0, 150px, 0);
        transform: translate3d(0, 150px, 0);
        opacity: 0;
    }

    100% {
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        -o-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}
