(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[857], {
    38371: function (t, e, r) {
        "use strict";
        r.r(e),
            r.d(e, {
                default: function () {
                    return Y
                }
            });
        var n = r(1188)
            , o = r(59499)
            , i = r(93619)
            , a = r(69519)
            , l = r(86416)
            , c = r(59764)
            , s = (r(75314),
                r(11187))
            , u = r(50029)
            , f = r(87794)
            , d = r.n(f)
            , p = r(67294)
            , b = r(93162)
            , v = r(79968)
            , x = r(45179)
            , h = r(27812)
            , y = (r(89858),
                r(51024))
            , m = r(34408)
            , w = r(85893);
        function j(t, e) {
            var r = Object.keys(t);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(t);
                e && (n = n.filter((function (e) {
                    return Object.getOwnPropertyDescriptor(t, e).enumerable
                }
                ))),
                    r.push.apply(r, n)
            }
            return r
        }
        function O(t) {
            for (var e = 1; e < arguments.length; e++) {
                var r = null != arguments[e] ? arguments[e] : {};
                e % 2 ? j(Object(r), !0).forEach((function (e) {
                    (0,
                        o.Z)(t, e, r[e])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : j(Object(r)).forEach((function (e) {
                    Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                }
                ))
            }
            return t
        }
        var g = (0,
            p.forwardRef)((function (t, e) {
                var r = t.data
                    , n = t.tableWidth
                    , o = (0,
                        p.useState)()
                    , i = o[0]
                    , a = o[1];
                (0,
                    p.useImperativeHandle)(e, (function () {
                        return {
                            getContent: function () {
                                return i
                            }
                        }
                    }
                    ));
                return (0,
                    w.jsxs)("div", {
                        className: "robot-item",
                        children: [(0,
                            w.jsx)("div", {
                                className: "robot-item-title",
                                children: "\u8868\u683c"
                            }), (0,
                                w.jsx)("table", {
                                    className: "robot-table",
                                    style: {
                                        width: n,
                                        minWidth: "100%"
                                    },
                                    children: (0,
                                        w.jsx)("tbody", {
                                            children: r.map((function (t, e) {
                                                return (0,
                                                    w.jsx)("tr", {
                                                        className: "robot-table-tr",
                                                        children: t.map((function (t, n) {
                                                            return t && (0,
                                                                w.jsxs)("td", {
                                                                    className: "robot-table-td",
                                                                    rowSpan: t.row,
                                                                    colSpan: t.col,
                                                                    style: {
                                                                        width: t.width
                                                                    },
                                                                    children: [(0,
                                                                        w.jsx)("div", {
                                                                            className: "robot-table-text",
                                                                            style: {
                                                                                lineHeight: "18px",
                                                                                whiteSpace: t.width ? "pre-wrap" : "pre"
                                                                            },
                                                                            children: i && i[e] && i[e][n] ? i[e][n].text : t.text
                                                                        }), (0,
                                                                            w.jsx)(y.Z.TextArea, {
                                                                                autoSize: {
                                                                                    minRows: 1
                                                                                },
                                                                                defaultValue: t.text,
                                                                                style: {
                                                                                    lineHeight: "18px",
                                                                                    whiteSpace: t.width ? "pre-wrap" : "pre"
                                                                                },
                                                                                onChange: function (t) {
                                                                                    return function (t, e) {
                                                                                        var n = e.rowIndex
                                                                                            , o = e.cellIndex
                                                                                            , i = t.target.value
                                                                                            , l = (0,
                                                                                                h.Z)(r);
                                                                                        l[n][o] = O(O({}, l[n][o]), {}, {
                                                                                            text: i
                                                                                        }),
                                                                                            a(l)
                                                                                    }(t, {
                                                                                        rowIndex: e,
                                                                                        cellIndex: n
                                                                                    })
                                                                                }
                                                                            })]
                                                                }, t + n)
                                                        }
                                                        ))
                                                    }, e)
                                            }
                                            ))
                                        })
                                })]
                    })
            }
            ))
            , _ = function (t) {
                var e = t.lines
                    , r = t.getEditAreaRef
                    , n = (0,
                        p.useMemo)((function () {
                            return e.map((function (t) {
                                return t.text
                            }
                            ))
                        }
                        ), [e]);
                return (0,
                    w.jsxs)("div", {
                        className: "robot-item robot-text",
                        children: [(0,
                            w.jsx)("div", {
                                className: "robot-item-title",
                                children: "\u6587\u5b57"
                            }), (0,
                                w.jsx)(m.Z, {
                                    ref: r,
                                    textData: n,
                                    jsonData: {
                                        lines: e
                                    },
                                    active: !0
                                })]
                    })
            }
            , P = r(84068)
            , E = r.n(P);
        function k(t, e) {
            var r = Object.keys(t);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(t);
                e && (n = n.filter((function (e) {
                    return Object.getOwnPropertyDescriptor(t, e).enumerable
                }
                ))),
                    r.push.apply(r, n)
            }
            return r
        }
        function D(t, e) {
            var r = t.textData
                , n = (0,
                    p.useRef)({});
            function i(t, e) {
                try {
                    var r = {};
                    for (var i in n.current) {
                        var a;
                        if (null !== (a = n.current[i]) && void 0 !== a && a.getContent) {
                            var l, c = null === (l = n.current[i]) || void 0 === l ? void 0 : l.getContent(t);
                            c && (r[i] = c)
                        }
                    }
                    if ("json" === t && Array.isArray(e.tables)) {
                        var s = function (t) {
                            for (var e = 1; e < arguments.length; e++) {
                                var r = null != arguments[e] ? arguments[e] : {};
                                e % 2 ? k(Object(r), !0).forEach((function (e) {
                                    (0,
                                        o.Z)(t, e, r[e])
                                }
                                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : k(Object(r)).forEach((function (e) {
                                    Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                                }
                                ))
                            }
                            return t
                        }({}, e);
                        return s.tables.forEach((function (t, e) {
                            if (r[e])
                                if ("table_with_line" === t.type || "table_without_line" === t.type) {
                                    var n, o = r[e].reduce((function (t, e) {
                                        return [].concat((0,
                                            h.Z)(t), (0,
                                                h.Z)(e))
                                    }
                                    ), []).filter((function (t) {
                                        return t
                                    }
                                    ));
                                    (null === t || void 0 === t || null === (n = t.table_cells) || void 0 === n ? void 0 : n.length) === (null === o || void 0 === o ? void 0 : o.length) && t.table_cells.forEach((function (t, e) {
                                        var r = o[e].text;
                                        if (t.text = r,
                                            Array.isArray(t.lines)) {
                                            var n = r.split("\n");
                                            t.lines.forEach((function (t, e) {
                                                t.text = n[e]
                                            }
                                            ))
                                        }
                                    }
                                    ))
                                } else if ("plain" === t.type && t.lines) {
                                    var i, a = r[e];
                                    (null === t || void 0 === t || null === (i = t.lines) || void 0 === i ? void 0 : i.length) === (null === a || void 0 === a ? void 0 : a.length) && t.table_cells.forEach((function (t, e) {
                                        t.text = a[e].text
                                    }
                                    ))
                                }
                        }
                        )),
                            s
                    }
                    return e
                } catch (u) {
                    return e
                }
            }
            return (0,
                p.useImperativeHandle)(e, (function () {
                    return {
                        getContent: i
                    }
                }
                )),
                (0,
                    w.jsx)("div", {
                        className: E()["table-content"],
                        children: Array.isArray(r) && r.map((function (t, e) {
                            if (t.is_table || "table_with_line" === t.type || "table_without_line" === t.type) {
                                var r = t.tableData;
                                return (0,
                                    w.jsx)(g, {
                                        active: t.active,
                                        data: r.array,
                                        tableWidth: r.tableWidth,
                                        ref: function (t) {
                                            n.current[e] = t
                                        }
                                    }, t.uuid)
                            }
                            if (t.text || "plain" === t.type && t.lines) {
                                var o = t.lines;
                                return (0,
                                    w.jsx)(_, {
                                        active: t.active,
                                        lines: o,
                                        getEditAreaRef: function (t) {
                                            n.current[e] = t
                                        }
                                    }, t.uuid)
                            }
                        }
                        ))
                    })
        }
        var S = (0,
            p.forwardRef)(D)
            , N = function (t) {
                return t || ""
            }
            , W = function (t, e, r, n) {
                var o = t - r
                    , i = e - n;
                return Math.round(Math.sqrt(o * o + i * i))
            }
            , Z = function (t) {
                return t ? W(t.position[0], t.position[1], t.position[2], t.position[3]) / t.text.split("").reduce((function (t, e) {
                    return /[0-9a-zA-z\.-_:\(\)]/.test(e) ? t + 1 : t + 2
                }
                ), 0) : 0
            }
            , C = function (t, e) {
                var r = t.array
                    , n = t.cols
                    , o = r.length
                    , i = W(r[0].position[0], r[0].position[1], r[o - 1].position[2], r[o - 1].position[3])
                    , a = e && i > e ? e : i
                    , l = function (t) {
                        var e, r, n, o, i;
                        if (null === t || void 0 === t || !t.length)
                            return 6;
                        var a = [Z((null === (e = t[0]) || void 0 === e ? void 0 : e.lines) && t[0].lines[0] || t[0]), Z((null === (r = t[1]) || void 0 === r ? void 0 : r.lines) && t[1].lines[0]), Z((null === (n = t[2]) || void 0 === n ? void 0 : n.lines) && t[2].lines[0]), Z((null === (o = t[3]) || void 0 === o ? void 0 : o.lines) && t[3].lines[0]), Z((null === (i = t[4]) || void 0 === i ? void 0 : i.lines) && t[4].lines[0])].filter((function (t) {
                            return t
                        }
                        )).sort((function (t, e) {
                            return e - t
                        }
                        ));
                        return a[Math.floor(a.length / 2)]
                    }(r)
                    , c = 6 / l;
                return {
                    ratio: c,
                    tableRealWidth: Math.round(c * a) + 24 * n,
                    cellPadding: 24
                }
            }
            , R = function (t, e) {
                var r = t.cols
                    , n = t.array
                    , o = C({
                        array: n,
                        cols: r
                    }, e)
                    , i = o.ratio
                    , a = o.tableRealWidth
                    , l = o.cellPadding;
                return n.forEach((function (t) {
                    var e = function (t, e, r) {
                        return W(t.position[0], t.position[1], t.position[2], t.position[3]) * e + r
                    }(t, i, l);
                    t.width = e
                }
                )),
                {
                    array: n,
                    tableRealWidth: a
                }
            };
        function I(t) {
            var e, r = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";
            return r += "<head>",
                r += '<meta http-equiv="content-type" content="application/vnd.ms-excel; charset=UTF-8">',
                r += "\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Sheet1</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e",
                r += "<style>br{mso-data-placement:same-cell;}.border{border-collapse:collapse;border-spacing: 0;}.border td{border:.5pt solid windowtext;}</style>",
                r += "</head>",
                r += "<body><table>",
                r += t,
                r += "</table></body>",
                r += "</html>",
                (0,
                    v.Jr)((e = r,
                        window.btoa(unescape(encodeURIComponent(e)))), "application/vnd.ms-excel;charset=utf-8")
        }
        function A(t, e) {
            return "<table><tbody" + (e ? ' class="border"' : "") + ">" + t.map((function (t) {
                return "<tr>" + t.filter((function (t) {
                    return t
                }
                )).map((function (t) {
                    var e;
                    return "<td rowSpan=".concat(t.row, " colSpan=").concat(t.col, " x:str>").concat(null === (e = t.text) || void 0 === e ? void 0 : e.replace(/\n/g, "<br/>"), "</td>")
                }
                )).join("") + "</tr>"
            }
            )).join("") + "</tbody></table>"
        }
        function M(t) {
            var e, r = "", n = (null === (e = t.find((function (t) {
                return t.table_cols
            }
            ))) || void 0 === e ? void 0 : e.table_cols) || 2;
            return t.forEach((function (t) {
                if ("table_with_line" === t.type || "table_without_line" === t.type) {
                    var e, o = A(null === (e = t.tableData) || void 0 === e ? void 0 : e.array, !0);
                    r = r + o + "<br/><br/>"
                } else if ("plain" === t.type && t.lines) {
                    var i = t.lines.reduce((function (t, e) {
                        return t + e.text + "<br/>"
                    }
                    ), "")
                        , a = A([[{
                            row: 1,
                            col: n,
                            text: i
                        }]]);
                    r = r + a + "<br/><br/>"
                }
            }
            )),
                r
        }
        function T(t, e) {
            var r = Object.keys(t);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(t);
                e && (n = n.filter((function (e) {
                    return Object.getOwnPropertyDescriptor(t, e).enumerable
                }
                ))),
                    r.push.apply(r, n)
            }
            return r
        }
        function z(t) {
            for (var e = 1; e < arguments.length; e++) {
                var r = null != arguments[e] ? arguments[e] : {};
                e % 2 ? T(Object(r), !0).forEach((function (e) {
                    (0,
                        o.Z)(t, e, r[e])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : T(Object(r)).forEach((function (e) {
                    Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                }
                ))
            }
            return t
        }
        var H = ["excel", "json"]
            , L = function () {
                var t = (0,
                    p.useState)([])
                    , e = t[0]
                    , r = t[1]
                    , n = (0,
                        p.useState)()
                    , o = n[0]
                    , i = n[1]
                    , l = (0,
                        p.useState)()
                    , c = (l[0],
                        l[1])
                    , f = (0,
                        p.useRef)()
                    , h = a.ZP.useContainer()
                    , y = h.transformRes
                    , m = h.filename;
                (0,
                    p.useEffect)((function () {
                        var t, e;
                        if (null !== y && void 0 !== y && null !== (t = y.result) && void 0 !== t && null !== (e = t.tables) && void 0 !== e && e.length) {
                            var n, o, a, l = ((null === y || void 0 === y || null === (n = y.result) || void 0 === n ? void 0 : n.blocks) || (null === y || void 0 === y || null === (o = y.result) || void 0 === o ? void 0 : o.tables) || []).map((function (t, e) {
                                var r = t.uuid || Date.now() + e
                                    , n = z(z({}, t), {}, {
                                        uuid: r
                                    });
                                if (n.is_table || "table_with_line" === n.type || "table_without_line" === n.type)
                                    n.tableData = function (t, e) {
                                        for (var r = t.rows || t.table_rows, n = t.columns || t.table_cols, o = t.cells || t.table_cells, i = [], a = 0; a < r; a++) {
                                            i[a] = [];
                                            for (var l = 0; l < n; l++)
                                                i[a][l] = {
                                                    text: "",
                                                    row: 1,
                                                    col: 1
                                                }
                                        }
                                        if (!o || !o.length)
                                            return {
                                                rows: 0,
                                                cols: 0,
                                                array: [],
                                                tableWidth: 0
                                            };
                                        var c = (e ? R({
                                            rows: r,
                                            cols: n,
                                            array: o
                                        }, e) : {
                                            tableRealWidth: void 0
                                        }).tableRealWidth;
                                        return o.forEach((function (t, e) {
                                            var r = parseInt(t.start_row)
                                                , n = isNaN(parseInt(t.start_column)) ? parseInt(t.start_col) : parseInt(t.start_column)
                                                , o = t.end_row - t.start_row + 1
                                                , a = void 0 !== t.end_column ? t.end_column - t.start_column + 1 : t.end_col - t.start_col + 1
                                                , l = t.text
                                                , c = 1
                                                , s = 1
                                                , u = a > 1
                                                , f = o > 1;
                                            if (f && u)
                                                for (c = r; c <= r + o - 1; c++)
                                                    for (s = n; s <= n + a - 1; s++)
                                                        i[c][s] = null;
                                            else if (f)
                                                for (c = 1; o - 1 >= c;)
                                                    i[r + c][n] = null,
                                                        c++;
                                            else if (u)
                                                for (c = 1; a - 1 >= c;)
                                                    i[r][n + c] = null,
                                                        c++;
                                            i[r] && (i[r][n] = {
                                                text: N(l),
                                                row: o,
                                                col: a,
                                                cellIndex: e,
                                                width: t.width
                                            })
                                        }
                                        )),
                                        {
                                            rows: r,
                                            cols: n,
                                            array: i,
                                            tableWidth: c
                                        }
                                    }(n);
                                else if ("string" == typeof n.text || "plain" === n.type && n.lines) {
                                    var o;
                                    n.lines = n.lines || (null === (o = n.text) || void 0 === o ? void 0 : o.split("\n").map((function (t) {
                                        return {
                                            text: t
                                        }
                                    }
                                    ))) || []
                                }
                                return n
                            }
                            )), s = z({}, null === y || void 0 === y ? void 0 : y.result);
                            delete s.excel,
                                r(l),
                                i(s),
                                c(null === y || void 0 === y || null === (a = y.result) || void 0 === a ? void 0 : a.excel)
                        }
                    }
                    ), [y]);
                var j = function () {
                    var t = (0,
                        u.Z)(d().mark((function t(e) {
                            var r, n;
                            return d().wrap((function (t) {
                                for (; ;)
                                    switch (t.prev = t.next) {
                                        case 0:
                                            if (t.prev = 0,
                                                r = "",
                                                "json" !== e) {
                                                t.next = 6;
                                                break
                                            }
                                            r = (0,
                                                v.N2)(g("json")),
                                                t.next = 15;
                                            break;
                                        case 6:
                                            0,
                                                t.next = 13;
                                            break;
                                        case 10:
                                            r = t.sent,
                                                t.next = 15;
                                            break;
                                        case 13:
                                            n = g("text"),
                                                r = I(M(n));
                                        case 15:
                                            O(r, e),
                                                t.next = 22;
                                            break;
                                        case 18:
                                            t.prev = 18,
                                                t.t0 = t.catch(0),
                                                console.error(t.t0),
                                                s.default.error("\u670d\u52a1\u5668\u7e41\u5fd9\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5");
                                        case 22:
                                        case "end":
                                            return t.stop()
                                    }
                            }
                            ), t, null, [[0, 18]])
                        }
                        )));
                    return function (e) {
                        return t.apply(this, arguments)
                    }
                }();
                function O(t, e) {
                    var r = {
                        excel: "xls",
                        json: "json"
                    }[e] || e
                        , n = new Blob([t], {
                            type: v.cS[r]
                        })
                        , o = m.replace(/\.[a-zA-Z]+$/, ".".concat(r));
                    (0,
                        b.saveAs)(n, o)
                }
                var g = function () {
                    var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "text";
                    if (f.current) {
                        var r = {
                            text: e,
                            json: o
                        };
                        return f.current.getContent(t, r[t])
                    }
                };
                return (0,
                    w.jsx)("div", {
                        className: E()["table-result"],
                        children: (0,
                            w.jsx)(x.Z, {
                                exportList: H,
                                textData: e,
                                jsonData: o,
                                resultContent: (0,
                                    w.jsx)(S, {
                                        ref: f,
                                        textData: e,
                                        jsonData: o
                                    }),
                                getContent: g,
                                onExport: j,
                                showCopy: !1,
                                textWrapStyle: {
                                    overflowX: "auto"
                                }
                            })
                    })
            }
            , q = "table"
            , U = {
                api: "",
                price: "",
                title: "\u514d\u8d39\u901a\u7528\u8868\u683c\u8bc6\u522b\u0028\u004f\u0043\u0052\u0029\u5de5\u5177"
            }
            , X = ["\u4e0a\u4f20\u6587\u4ef6", "\u5728\u7ebf\u8bc6\u522b", "\u5bfc\u51fa\u7ed3\u679c"]
            , B = {
                title: "",
                subtitle: "",
                list: []
            }
            , F = r(37511)
            , G = r.n(F);
        function J(t, e) {
            var r = Object.keys(t);
            if (Object.getOwnPropertySymbols) {
                var n = Object.getOwnPropertySymbols(t);
                e && (n = n.filter((function (e) {
                    return Object.getOwnPropertyDescriptor(t, e).enumerable
                }
                ))),
                    r.push.apply(r, n)
            }
            return r
        }
        function Q(t) {
            for (var e = 1; e < arguments.length; e++) {
                var r = null != arguments[e] ? arguments[e] : {};
                e % 2 ? J(Object(r), !0).forEach((function (e) {
                    (0,
                        o.Z)(t, e, r[e])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(r)) : J(Object(r)).forEach((function (e) {
                    Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(r, e))
                }
                ))
            }
            return t
        }
        var V = function () {
            return (0,
                w.jsxs)("main", {
                    children: [(0,
                        w.jsxs)("div", {
                            className: G()["page-top"],
                            children: [(0,
                                w.jsx)(i.Z, {
                                    data: U
                                }), (0,
                                    w.jsx)(c.Z, {
                                        data: X,
                                        resultContent: (0,
                                            w.jsx)(L, {})
                                    })]
                        })]
                })
        }
            , $ = function (t) {
                return (0,
                    w.jsx)(a.ZP.Provider, {
                        initialState: {
                            pageInfo: {
                                service: q,
                                queryParams: {
                                    excel: 0
                                }
                            }
                        },
                        children: (0,
                            w.jsx)(V, Q({}, t))
                    })
            }
            , K = function () {
                return (0,
                    w.jsx)("div", {
                        children: (0,
                            w.jsx)($, {})
                    })
            };
        K.Layout = (0,
            n.Z)();
        var Y = K
    },
    40089: function (t, e, r) {
        (window.__NEXT_P = window.__NEXT_P || []).push(["/table", function () {
            return r(38371)
        }
        ])
    },
    84068: function (t) {
        t.exports = {
            "table-content": "UM49kcQC"
        }
    }
}, function (t) {
    t.O(0, [885, 676, 937, 121, 811, 188, 387, 97, 774, 888, 179], (function () {
        return e = 40089,
            t(t.s = e);
        var e
    }
    ));
    var e = t.O();
    _N_E = e
}
]);
