using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// URL服务 - 负责URL路径处理的核心功能
    /// 包括路径规范化、默认页面处理、路径检查
    /// </summary>
    public static class UrlService
    {
        // 常量定义
        public const string DefaultPage = "default.aspx";
        public const string DefaultIndex = "index.html";

        // 默认页面列表
        private static readonly List<string> DefaultPages = new List<string>() {
            "default.aspx", "index.aspx", "index.html", "json.aspx"
        };

        /// <summary>
        /// 检查路径是否存在
        /// </summary>
        public static bool PathExists(string physicalPath)
        {
            return File.Exists(physicalPath) || Directory.Exists(physicalPath);
        }

        /// <summary>
        /// 尝试查找目录下的默认文档
        /// </summary>
        /// <returns>如果找到默认文档，返回文件名；否则返回null</returns>
        public static string TryFindDefaultDocument(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
                return null;

            foreach (var defaultPage in DefaultPages)
            {
                var filePath = Path.Combine(directoryPath, defaultPage);
                if (File.Exists(filePath))
                {
                    return defaultPage;
                }
            }

            return null;
        }

        // 处理格式为 /zh-CN/ 或 /zh-CN 或 /en/ 或 /en 的情况
        // 修改正则表达式以匹配两字母代码或带地区的代码
        private static readonly Regex LanguageOnlyRegex = new Regex(@"^/([a-zA-Z]{2,}(?:-[a-zA-Z]{2,})?)/?$", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        // 处理常规格式 /zh-CN/page.aspx 或 /en/page.aspx
        // 修改正则表达式以匹配两字母代码或带地区的代码
        private static readonly Regex LanguageWithPathRegex = new Regex(@"^/([a-zA-Z]{2,}(?:-[a-zA-Z]{2,})?)/(.+)$", RegexOptions.Compiled | RegexOptions.IgnoreCase);

        // 处理多斜杠问题 /zh-Hans///page.aspx
        private static readonly Regex PathReplaceRegex = new Regex(@"/{2,}", RegexOptions.Compiled);

        public static bool TryExtractLanguage(string path, out string language, out string oriLanuagePath, out string actualPath)
        {
            language = string.Empty;
            oriLanuagePath = string.Empty;
            actualPath = path.TrimStart('/');

            if (string.IsNullOrEmpty(path))
                return false;

            // 处理格式为 /zh-CN/ 或 /zh-CN 或 /en/ 或 /en 的情况
            // 修改正则表达式以匹配两字母代码或带地区的代码
            Match match1 = LanguageOnlyRegex.Match(path);

            if (match1.Success)
            {
                string potentialLang = match1.Groups[1].Value;

                // 验证是否是支持的语言代码
                if (LanguageService.TryGetStandardLanguageCode(potentialLang, out language))
                {
                    oriLanuagePath = potentialLang;
                    actualPath = string.Empty; // 默认文档将由IIS或应用程序逻辑处理
                    return true;
                }
            }

            // 处理常规格式 /zh-CN/page.aspx 或 /en/page.aspx
            // 修改正则表达式以匹配两字母代码或带地区的代码
            Match match2 = LanguageWithPathRegex.Match(path);

            if (match2.Success)
            {
                string potentialLang = match2.Groups[1].Value;

                // 验证是否是支持的语言代码
                if (LanguageService.TryGetStandardLanguageCode(potentialLang, out language))
                {
                    oriLanuagePath = potentialLang;
                    // 处理多斜杠问题 /zh-Hans///page.aspx
                    actualPath = PathReplaceRegex.Replace(match2.Groups[2].Value, "/");
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查路径是否是目录
        /// </summary>
        /// <remarks>
        /// 使用物理路径检查的方式准确判断是否为目录
        /// </remarks>
        public static bool IsDirectory(string path)
        {
            // 空路径或根路径视为目录
            if (string.IsNullOrEmpty(path) || path == "/")
                return true;

            // 标准化路径
            path = path.TrimEnd('/');

            try
            {
                // 获取物理路径并直接检查是否存在
                string physicalPath = HttpContext.Current.Server.MapPath("~/" + path.TrimStart('/'));

                // 只有实际存在的目录才返回true
                return Directory.Exists(physicalPath);
            }
            catch (Exception)
            {
                // 出错时，默认当作非目录
                return false;
            }
        }

        /// <summary>
        /// 构建重定向URL，统一处理各种重定向场景
        /// </summary>
        /// <param name="context">当前的HTTP上下文</param>
        /// <param name="basePath">基础路径（通常是Request.Path）</param>
        /// <param name="languageCode">可选的语言代码，如果提供则将其添加到URL前缀</param>
        /// <returns>重定向URL或者null（如果不需要重定向）</returns>
        public static string BuildRedirectUrl(HttpContext context, string basePath, string languageCode = null)
        {
            // 预处理路径
            if (basePath == null)
                basePath = "";

            // 1. 处理语言代码（如果提供）
            string urlPrefix = languageCode != null ? "~/" + languageCode : "~/";

            // 2. 检查是否需要添加默认文档
            if (IsDirectory(basePath))
            {
                string physicalPath = context.Server.MapPath("~/" + basePath);
                string defaultDoc = TryFindDefaultDocument(physicalPath);
                if (defaultDoc != null)
                {
                    if (basePath.EndsWith("/") || string.IsNullOrEmpty(basePath))
                        basePath += defaultDoc;
                    else
                        basePath += "/" + defaultDoc;
                }
            }

            // 3. 处理查询参数
            string query = context.Request.QueryString.ToString();
            string newUrl = urlPrefix + basePath;

            // 移除lang参数
            if (!string.IsNullOrEmpty(query))
            {
                if (query.Contains("lang="))
                {
                    var queryParams = HttpUtility.ParseQueryString(query);
                    queryParams.Remove("lang");

                    string newQuery = queryParams.ToString();
                    if (!string.IsNullOrEmpty(newQuery))
                        newUrl += "?" + newQuery;
                }
                else
                {
                    newUrl += "?" + query; // 保留原始查询字符串
                }
            }

            return newUrl;
        }

        /// <summary>
        /// 修正路径错误
        /// </summary>
        public static string FixupPath(string path)
        {
            // 处理ocr误写为orc的情况
            if (path.Contains("orc/"))
            {
                return path.Replace("orc/", "ocr/");
            }

            // 处理多了一层的情况，如：ocr/ocr/index.html
            if (path.Contains("/"))
            {
                string newPath = path.Substring(path.IndexOf("/") + 1);
                string newPhysicalPath = HttpContext.Current.Server.MapPath("~/" + newPath);
                if (File.Exists(newPhysicalPath) || Directory.Exists(newPhysicalPath))
                {
                    return newPath;
                }
            }

            // 处理谷歌Bot省略二级路径的情况
            if (!path.Contains("/"))
            {
                string newPath = "ocr/" + path;
                string newPhysicalPath = HttpContext.Current.Server.MapPath("~/" + newPath);
                if (File.Exists(newPhysicalPath) || Directory.Exists(newPhysicalPath))
                {
                    return newPath;
                }
            }

            return path;
        }
    }
}
