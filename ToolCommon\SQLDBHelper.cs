﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data.SqlClient;
using System.Data;
using ToolCommon;
using System.Collections;

public class SQLDBHelper : DataPool
{
    /// <summary>
    /// ConnectionString样例：Data Source=.;Initial Catalog=MeiNv; uid=sa;Password=*****;Pooling=True; Min Pool Size=0; Max Pool Size=1000; Connection Lifetime=30;MultipleActiveResultSets=True
    /// </summary>
    public string ConnectionString { get; set; }

    public SQLDBHelper(string strCon)
    {
        ConnectionString = strCon;
    }

    private SqlConnection connection;
    /// <summary>
    /// 获取数据库连接(Connection)
    /// </summary>
    public SqlConnection Connection
    {
        get
        {
            if (connection == null)
            {
                connection = new SqlConnection(ConnectionString);
                connection.Open();
            }
            else if (connection.State == System.Data.ConnectionState.Closed)
            {
                connection.Open();
            }
            else if (connection.State == System.Data.ConnectionState.Broken)
            {
                connection.Close();
                connection.Open();
            }
            return connection;
        }
    }

    /// <summary>
    /// 执行 T-SQL 语句，并返回所受影响的行数
    /// </summary>
    /// <param name="safeSql">Sql语句</param>
    /// <returns>返回受影响的行数</returns>
    public override int ExecuteCommand(string safeSql)
    {
        DateTime dtStart = DateTime.Now;
        SqlCommand cmd = new SqlCommand(safeSql, Connection);
        int result = cmd.ExecuteNonQuery();
        if (IsWriteLog)
        {
            WriteLog("ExecuteCommand", dtStart);
        }
        return result;
    }

    /// <summary>
    /// SqlBulkCopy往数据库中批量插入数据
    /// </summary>
    /// <param name="sourceDataTable">数据源表</param>
    /// <param name="targetTableName">服务器上目标表</param>
    /// <param name="mapping">创建新的列映射，并使用列序号引用源列和目标列的列名称。</param>
    public void BulkToDB(DataTable sourceDataTable, string targetTableName, SqlBulkCopyColumnMapping[] mapping)
    {
        /*  调用方法 - 2013年05月10日编写
        //DataTable dt = Get_All_RoomState_ByHID();
        //SqlBulkCopyColumnMapping[] mapping = new SqlBulkCopyColumnMapping[4];
        //mapping[0] = new SqlBulkCopyColumnMapping("Xing_H_ID", "Xing_H_ID");
        //mapping[1] = new SqlBulkCopyColumnMapping("H_Name", "H_Name");
        //mapping[2] = new SqlBulkCopyColumnMapping("H_sName", "H_sName");
        //mapping[3] = new SqlBulkCopyColumnMapping("H_eName", "H_eName");
        //BulkToDB(dt, "Bak_Tts_Hotel_Name", mapping);
        */
        SqlConnection conn = new SqlConnection(ConnectionString);
        //SqlBulkCopy bulkCopy = new SqlBulkCopy(conn);   //用其它源的数据有效批量加载sql server表中
        //指定大容量插入是否对表激发触发器。此属性的默认值为 False。
        SqlBulkCopy bulkCopy = new SqlBulkCopy(ConnectionString, SqlBulkCopyOptions.FireTriggers);

        bulkCopy.DestinationTableName = targetTableName;    //服务器上目标表的名称
        bulkCopy.BatchSize = sourceDataTable.Rows.Count;   //每一批次中的行数

        try
        {
            conn.Open();
            if (sourceDataTable != null && sourceDataTable.Rows.Count != 0)
            {
                for (int i = 0; i < mapping.Length; i++)
                    bulkCopy.ColumnMappings.Add(mapping[i]);

                //将提供的数据源中的所有行复制到目标表中
                bulkCopy.WriteToServer(sourceDataTable);
            }
        }
        catch (Exception ex)
        {
            //throw ex;
        }
        finally
        {
            conn.Close();
            if (bulkCopy != null)
                bulkCopy.Close();
        }
    }

    public override bool ExecuteSqlTran(ArrayList lstSQL)
    {
        if (lstSQL == null || lstSQL.Count <= 0)
        {
            return false;
        }
        DateTime dtStart = DateTime.Now;
        bool result = false;
        int count = 0;
        string strTemp = string.Empty;
        try
        {
            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                conn.Open();
                using (SqlCommand cmd = conn.CreateCommand())
                {
                    SqlTransaction tran = conn.BeginTransaction();
                    cmd.Transaction = tran;
                    try
                    {
                        foreach (string item in lstSQL)
                        {
                            if (string.IsNullOrEmpty(item))
                                continue;
                            strTemp = item.Trim();
                            cmd.CommandText = strTemp;
                            count += cmd.ExecuteNonQuery();
                        }
                        tran.Commit();//如果都成功那么提交事物
                        result = true;
                    }
                    catch (System.Data.SqlClient.SqlException E)
                    {
                        tran.Rollback();
                        WriteLog(string.Format("ExecuteSqlTranSqlException    {0}/{1}  {2}{3}", count, lstSQL.Count, Environment.NewLine, strTemp) + E.Message, dtStart);
                    }
                    catch (Exception E)
                    {
                        tran.Rollback();
                        WriteLog(string.Format("ExecuteSqlTranException    {0}/{1}  {2}{3}", count, lstSQL.Count, Environment.NewLine, strTemp) + E.Message, dtStart);
                    }
                }
            }
        }
        catch (Exception E)
        {
            WriteLog(string.Format("SQLConError    {0}/{1}  {2}{3}", count, lstSQL.Count, Environment.NewLine, strTemp) + E.Message, dtStart);
            ExecuteSqlTran(lstSQL);
        }
        if (IsWriteLog)
        {
            WriteLog(string.Format("ExecuteSqlTran    {0}/{1}", count, lstSQL.Count), dtStart);
        }
        return result;
    }

    /// <summary>
    /// 执行查询，并返回所查询的结果集中的第一行中第一列的值，忽略其他行和列
    /// </summary>
    /// <param name="safeSql">Sql语句</param>
    /// <returns>返回查询结果</returns>
    public override object ExecuScalar(string safeSql)
    {
        DateTime dtStart = DateTime.Now;
        SqlCommand cmd = new SqlCommand(safeSql, Connection);
        object obj = cmd.ExecuteScalar();
        if (IsWriteLog)
        {
            WriteLog("ExecuScalar", dtStart);
        }
        return obj;
    }

    /// <summary>
    /// 获取数据集
    /// </summary>
    /// <param name="safeSql"></param>
    /// <returns></returns>
    public override DataTable GetTable(string safeSql)
    {
        DataSet ds = new DataSet();
        SqlCommand cmd = new SqlCommand(safeSql, Connection);
        cmd.CommandTimeout = 3600;//设置超时时间
        SqlDataAdapter da = new SqlDataAdapter(cmd);
        try
        {
            try
            {
                da.Fill(ds);
            }
            catch (Exception oe)
            {
            }
        }
        catch { }
        finally
        {
            da.Dispose();
            Connection.Close();
        }
        return ds.Tables[0];
    }

    /// <summary>
    /// 获取数据集
    /// </summary>
    /// <param name="safeSql"></param>
    /// <returns></returns>
    public override DataSet GetDataSet(string safeSql)
    {
        DateTime dtStart = DateTime.Now;
        DataSet ds = new DataSet();
        SqlCommand cmd = new SqlCommand(safeSql, Connection);
        SqlDataAdapter da = new SqlDataAdapter(cmd);
        da.Fill(ds);
        if (IsWriteLog)
        {
            WriteLog("GetDataSet", dtStart);
        }
        return ds;
    }

    #region 写日志

    private string GetSpanTime(DateTime dtStart)
    {
        StringBuilder strTemp = new StringBuilder();
        TimeSpan ts = new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks);
        strTemp.Append(ts.Hours > 0 ? ts.Hours + "时" : "");
        strTemp.Append(ts.Minutes > 0 ? ts.Minutes + "分" : "");
        strTemp.Append(ts.Seconds > 0 ? ts.Seconds + "秒" : "");
        strTemp.Append(ts.Milliseconds > 0 ? ts.Milliseconds + "毫秒" : "");
        return strTemp.ToString();
    }

    private void WriteLog(string action, string strSQL, DateTime dtStart)
    {
        AppLog.WriteAction(action, string.Format("SpanTime:{0}     SQL:{1}", GetSpanTime(dtStart), strSQL));
    }

    private void WriteLog(string action, DateTime dtStart)
    {
        AppLog.WriteAction(action, string.Format("SpanTime:{0}", GetSpanTime(dtStart)));
    }

    #endregion
}
