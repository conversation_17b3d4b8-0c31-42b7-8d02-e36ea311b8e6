using CommonLib;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;

namespace Account.Web.Common
{
    /// <summary>
    /// 认证相关的公共帮助类
    /// </summary>
    public static class AuthHelper
    {
        #region 验证相关

        /// <summary>
        /// 验证账号格式（邮箱或手机号）
        /// </summary>
        public static bool IsValidAccount(string account)
        {
            if (string.IsNullOrEmpty(account))
                return false;

            return BoxUtil.IsEmail(account) || BoxUtil.IsMobile(account);
        }

        /// <summary>
        /// 验证密码格式
        /// </summary>
        public static bool IsValidPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // 以客户端标准为准：6-16位数字或大小写字母
            return new Regex(@"[0-9A-Za-z].{5,15}").IsMatch(password);
        }

        /// <summary>
        /// 验证昵称格式
        /// </summary>
        public static bool IsValidNickName(string nickName)
        {
            if (string.IsNullOrEmpty(nickName))
                return false;

            // 以客户端标准为准：2-20位中英文数字字母或下划线
            return nickName.Length >= 2 && nickName.Length <= 20 &&
                   new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName);
        }

        /// <summary>
        /// 验证验证码格式
        /// </summary>
        public static bool IsValidVerifyCode(string code)
        {
            if (string.IsNullOrEmpty(code))
                return false;

            return code.Length >= 4 && code.Length <= 8;
        }

        #endregion

        #region 会话管理

        /// <summary>
        /// 获取用户会话信息
        /// </summary>
        public static UserLoginInfo GetUserSession(HttpRequest request)
        {
            return UserLoginInfoHelper.GetUserInfo(request);
        }

        /// <summary>
        /// 检查用户是否已登录
        /// </summary>
        public static bool IsUserLoggedIn(HttpRequest request)
        {
            var user = GetUserSession(request);
            return user != null && !string.IsNullOrEmpty(user.Account);
        }

        /// <summary>
        /// 要求用户登录（重定向到登录页）
        /// </summary>
        public static void RequireLogin(HttpResponse response)
        {
            response.Redirect("Login.aspx");
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        public static void Logout(HttpContext context)
        {
            UserLoginInfoHelper.LoginOut(context);
        }

        /// <summary>
        /// 设置用户会话
        /// </summary>
        public static void SetUserSession(HttpResponse response, HttpRequest request, string token, string account)
        {
            try
            {
                var expireTime = ServerTime.DateTime.AddHours(3);

                // 设置统一的Cookie过期时间
                var tokenCookie = new HttpCookie("token", token)
                {
                    Expires = expireTime,
                    HttpOnly = true, // 防止XSS攻击
                    Secure = request.IsSecureConnection // HTTPS时设置Secure
                };

                var accountCookie = new HttpCookie("account", account)
                {
                    Expires = expireTime,
                    HttpOnly = true,
                    Secure = request.IsSecureConnection
                };

                response.Cookies.Add(tokenCookie);
                response.Cookies.Add(accountCookie);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("设置用户会话失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 清除用户会话
        /// </summary>
        public static void ClearUserSession(HttpResponse response)
        {
            try
            {
                var tokenCookie = new HttpCookie("token", "")
                {
                    Expires = DateTime.Now.AddDays(-1)
                };

                var accountCookie = new HttpCookie("account", "")
                {
                    Expires = DateTime.Now.AddDays(-1)
                };

                response.Cookies.Add(tokenCookie);
                response.Cookies.Add(accountCookie);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("清除用户会话失败", ex);
            }
        }

        #endregion

        #region 响应处理

        /// <summary>
        /// 写入JSON响应 - 已废弃，请使用ResponseService.WriteJsonResponse
        /// </summary>
        [Obsolete("请使用Account.Web.Common.ResponseService.WriteJsonResponse方法")]
        public static void WriteJsonResponse(HttpResponse response, bool success, string message, object data = null)
        {
            var context = HttpContext.Current;
            if (context != null)
            {
                ResponseService.WriteJsonResponse(context, success, message, data);
            }
        }

        /// <summary>
        /// 显示消息
        /// </summary>
        public static void ShowMessage(HttpResponse response, string message, string type)
        {
            try
            {
                response.Write($"<script>showMessage('{HttpUtility.JavaScriptStringEncode(message)}', '{type}');</script>");
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("显示消息失败", ex);
                response.Write($"<script>alert('{HttpUtility.JavaScriptStringEncode(message)}');</script>");
            }
        }

        #endregion

        #region 异常处理

        /// <summary>
        /// 处理登录异常
        /// </summary>
        public static void HandleLoginException(HttpResponse response, Exception ex, bool isFormSubmit, string lang = "")
        {
            try
            {
                LogHelper.Log.Error("登录异常", ex);
                var message = "登录失败，请重试";

                if (isFormSubmit)
                {
                    ShowMessage(response, message, "error");
                }
                else
                {
                    WriteJsonResponse(response, false, message);
                }
            }
            catch (Exception innerEx)
            {
                LogHelper.Log.Error("处理登录异常失败", innerEx);
            }
        }

        /// <summary>
        /// 处理注册异常
        /// </summary>
        public static void HandleRegisterException(HttpResponse response, Exception ex, bool isFormSubmit, string lang = "")
        {
            try
            {
                LogHelper.Log.Error("注册异常", ex);
                var message = "注册失败，请重试";

                if (isFormSubmit)
                {
                    ShowMessage(response, message, "error");
                }
                else
                {
                    WriteJsonResponse(response, false, message);
                }
            }
            catch (Exception innerEx)
            {
                LogHelper.Log.Error("处理注册异常失败", innerEx);
            }
        }

        #endregion
    }
}
