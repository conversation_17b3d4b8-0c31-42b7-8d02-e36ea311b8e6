(function () {
    var search = window.location.search;
    var searchObj = {};
    if (search) {
        var searchField = search.replace(/^\?/, '').split('&');
        for (var index = 0; index < searchField.length; index++) {
            var filed = searchField[index];
            if (filed.indexOf('=') > -1) {
                var item = filed.split('=');
                searchObj[item[0]] = item[1];
            }
        }
    }
    if (searchObj.from) {
        window.localStorage.setItem('crm_leads', JSON.stringify({
            search: searchObj,
            t: Date.now()
        }));
    }
    window.get_crm_leads = function () {
        var store = params = window.localStorage.getItem('crm_leads');
        var result;
        if (store) {
            try {
                var obj = JSON.parse(store);
                var startT = obj.t || 0;
                // 有效期1h
                if (Date.now() - startT <= 1000 * 60 * 60) {
                    result = obj.search;
                } else {
                    window.localStorage.removeItem('crm_leads');
                }
            } catch (error) {
                console.error('parse crm_leads error', error);
            }
        }
        return result || {};
    };
    window.onload = function () {
        var parentElement = document.getElementsByClassName('_BEBSCQe')[0];
        if (parentElement) {
            var newDiv = document.createElement('div');
            newDiv.className = 'YNa21k4i';
            if (document.location.href.indexOf('pdf2markdown') > 0) {
                newDiv.innerHTML = `<span style="color:red; font-weight:600; font-size:22px; line-height:45px; text-align:center;">
            开发预览版[限100页],仅支持在线体验，客户端暂不含此功能，敬请期待！
</span >`;
            } else {
                newDiv.innerHTML = `
        <span style="color:red; font-weight:600; font-size:22px; line-height:45px; text-align:center;">
            网页版仅供在线体验，完整功能请下载客户端！
            <a class="ant-btn ant-btn-primary"
                href="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/cdn/update/Setup.exe">
                <b>客户端下载</b>
            </a>
            <a class="ant-btn ant-btn-dangerous" href="/desc.aspx?shownormal=1" target="_blank">
                <b style="color:red">各VIP版本功能预览</b>
            </a>
        </span>`;
            }
            parentElement.appendChild(newDiv);
        }
    };
})();
