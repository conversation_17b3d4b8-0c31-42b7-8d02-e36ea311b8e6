let JsonDiff=function(){function t(t,e){this.el=t;let o=this.codemirror=CodeMirror.fromTextArea(this.el,{lineNumbers:!0,mode:{name:"javascript",json:!0},matchBrackets:!0,theme:"tomorrow-night"});e&&o.setValue(e);let r=this;o.on("inputRead",function(t,e){"paste"===e.origin&&function(){let t=o.lineCount();o.autoFormatRange({line:0,ch:0},{line:t}),o.setSelection({line:0,ch:0})}(),i()}),o.on("keyup",i),o.on("change",i),o.on("clear",function(){console.log(arguments)});let n="";function i(){let t=o.getValue();t!==n&&r.trigger("change"),n=t}}function e(){!function(){o.clearMarkers(),r.clearMark<PERSON>();let t,e,l=o.getText(),c=r.getText();try{l&&(t=JSON.parse(l)),n&&n("left",!0)}catch(t){console.log("left ==>",t)}try{c&&(e=JSON.parse(c)),n&&n("right",!0)}catch(t){console.log("right ==>",t)}if(!t||!e)return void(t||e?t?n&&n("right",!1):n&&n("left",!1):n&&n("left-right",!1));let h=jsonpatch.compare(t,e);i&&i(h),h.forEach(function(t){try{"remove"===t.op?o.highlightRemoval(t):"add"===t.op?r.highlightAddition(t):"replace"===t.op&&(r.highlightChange(t),o.highlightChange(t))}catch(t){console.warn("error while trying to highlight diff",t)}})}()}t.prototype.getText=function(){return this.codemirror.getValue()},t.prototype.setText=function(t){return this.codemirror.setValue(t)},t.prototype.highlightRemoval=function(t){this._highlight(t,"#DD4444")},t.prototype.highlightAddition=function(t){this._highlight(t,"#4ba2ff")},t.prototype.highlightChange=function(t){this._highlight(t,"#E5E833")},t.prototype._highlight=function(t,e){let o=function(t,e){let o=parse(t).pointers,r=e.path,n={line:o[r].key?o[r].key.line:o[r].value.line,ch:o[r].key?o[r].key.column:o[r].value.column},i={line:o[r].valueEnd.line,ch:o[r].valueEnd.column};return{start:n,end:i}}(this.getText(),t);this.codemirror.markText(o.start,o.end,{css:"background-color: "+e})},t.prototype.clearMarkers=function(){this.codemirror.getAllMarks().forEach(function(t){t.clear()})};let o=null,r=null,n=null,i=null;return{init:function(l,c,h,a){return n=h,i=a,BackboneEvents.mixin(t.prototype),o=new t(l,""),r=new t(c,""),o.on("change",e),r.on("change",e),o.codemirror.on("scroll",function(){let t=o.codemirror.getScrollInfo();r.codemirror.scrollTo(t.left,t.top)}),r.codemirror.on("scroll",function(){let t=r.codemirror.getScrollInfo();o.codemirror.scrollTo(t.left,t.top)}),{left:o.codemirror,right:r.codemirror}}}}();