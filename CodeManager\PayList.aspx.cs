﻿using CommonLib;
using System;

namespace Account.Web
{
    public partial class PayList : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            {
                Response.End();
                return;
            }
        }

        protected void btnAllUser_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            hidType.Value = Request["type"];
        }

        protected void btnClear_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            PayHelper.DelByState();
        }
    }
}