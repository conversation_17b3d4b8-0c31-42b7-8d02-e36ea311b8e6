﻿{"ja":"<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ja\"><head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ja/ocr/index.html\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/ocr/index.html\" hreflang=\"x-default\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/ocr/index.html\" hreflang=\"zh\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/ocr/index.html\" hreflang=\"zh-tw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/ocr/index.html\" hreflang=\"zh-yue\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/ocr/index.html\" hreflang=\"en\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/ocr/index.html\" hreflang=\"es\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/ocr/index.html\" hreflang=\"hi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/ocr/index.html\" hreflang=\"ar\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/ocr/index.html\" hreflang=\"pt\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/ocr/index.html\" hreflang=\"bn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/ocr/index.html\" hreflang=\"ru\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/ocr/index.html\" hreflang=\"ja\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/ocr/index.html\" hreflang=\"pa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/ocr/index.html\" hreflang=\"de\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/ocr/index.html\" hreflang=\"fr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/ocr/index.html\" hreflang=\"mr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/ocr/index.html\" hreflang=\"te\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/ocr/index.html\" hreflang=\"vi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/ocr/index.html\" hreflang=\"ko\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/ocr/index.html\" hreflang=\"ta\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/ocr/index.html\" hreflang=\"ur\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/ocr/index.html\" hreflang=\"tr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/ocr/index.html\" hreflang=\"it\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/ocr/index.html\" hreflang=\"gu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/ocr/index.html\" hreflang=\"pl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/ocr/index.html\" hreflang=\"uk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/ocr/index.html\" hreflang=\"ms\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/ocr/index.html\" hreflang=\"id\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/ocr/index.html\" hreflang=\"ml\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/ocr/index.html\" hreflang=\"kn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/ocr/index.html\" hreflang=\"fa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/ocr/index.html\" hreflang=\"nl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/ocr/index.html\" hreflang=\"th\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/ocr/index.html\" hreflang=\"sw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/ocr/index.html\" hreflang=\"ro\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/ocr/index.html\" hreflang=\"my\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/ocr/index.html\" hreflang=\"or\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/ocr/index.html\" hreflang=\"he\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/ocr/index.html\" hreflang=\"am\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/ocr/index.html\" hreflang=\"fil\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/ocr/index.html\" hreflang=\"sv\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/ocr/index.html\" hreflang=\"el\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/ocr/index.html\" hreflang=\"cs\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/ocr/index.html\" hreflang=\"hu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/ocr/index.html\" hreflang=\"be\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/ocr/index.html\" hreflang=\"si\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/ocr/index.html\" hreflang=\"ne\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/ocr/index.html\" hreflang=\"km\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/ocr/index.html\" hreflang=\"sk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/ocr/index.html\" hreflang=\"bg\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/ocr/index.html\" hreflang=\"fr-ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/ocr/index.html\" hreflang=\"ha\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/ocr/index.html\" hreflang=\"yo\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/ocr/index.html\" hreflang=\"ig\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/ocr/index.html\" hreflang=\"ku\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/ocr/index.html\" hreflang=\"rw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/ocr/index.html\" hreflang=\"ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/ocr/index.html\" hreflang=\"da\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/ocr/index.html\" hreflang=\"fi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/ocr/index.html\" hreflang=\"nb\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/ocr/index.html\" hreflang=\"hr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/ocr/index.html\" hreflang=\"sr-cyrl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/ocr/index.html\" hreflang=\"sr-latn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/ocr/index.html\" hreflang=\"sq\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/ocr/index.html\" hreflang=\"so\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/ocr/index.html\" hreflang=\"zu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/ocr/index.html\" hreflang=\"ka\" />\n\n\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width\">\n\n    <title>OCRアシスタント無料OCRツール - オンライン無料OCRテキスト認識、フォーム認識ツール、PDFからWORDツール</title>\n    <meta name=\"description\" content=\"無料のOCRツール、テキスト認識、テーブル認識、PDFからMarkdown、PDFからWord、PDFからExcel、PDFからPPT、PDFからJPG、WordからPDF、ExcelからPDF、画像からPDF、WordからJPGへ\">\n    <meta name=\"keywords\" content=\"OCR、テキスト認識、フォーム認識、PDFからWordへ、画像テキストの抽出、オンラインテキスト認識、ドキュメント変換、画像からテキスト、スキャンされたドキュメント認識、Excel変換、PDFからMarkdown、PDFからWord、PDFからExcel、PDFからPPT、PDFからJPGへ、WordからPDF、ExcelからPDF、画像からPDF、WordからJPGへ\">\n    <meta name=\"next-head-count\" content=\"13\">\n    <link rel=\"preload\" href=\"static/css/bf406b4dfe4a88f7.css\" as=\"style\">\n    <link rel=\"stylesheet\" href=\"static/css/bf406b4dfe4a88f7.css\" data-n-g=\"\">\n    <link rel=\"preload\" href=\"static/css/97e00c3eababac2c.css\" as=\"style\">\n    <link rel=\"stylesheet\" href=\"static/css/97e00c3eababac2c.css\" data-n-p=\"\">\n    <link rel=\"preload\" href=\"static/css/2d170f7bb9587ee4.css\" as=\"style\">\n    <link rel=\"stylesheet\" href=\"static/css/2d170f7bb9587ee4.css\" data-n-p=\"\">\n    <noscript data-n-css=\"\"></noscript>\n    <script defer=\"\" nomodule=\"\" src=\"static/js/polyfills-c67a75d1b6f99dc8.js\"></script>\n    <script src=\"static/js/leads.js\" defer=\"\" data-nscript=\"beforeInteractive\"></script>\n    <script src=\"static/js/webpack-9e2dac2c71e11463.js\" defer=\"\"></script>\n    <script src=\"static/js/framework-bb5c596eafb42b22.js\" defer=\"\"></script>\n    <script src=\"static/js/main-ab39c4ec2bce69ad.js\" defer=\"\"></script>\n    <script src=\"static/js/_app-d736fe5cc417ac5c.js\" defer=\"\"></script>\n    <script src=\"static/js/676-58ab10dc70b27a01.js\" defer=\"\"></script>\n    <script src=\"static/js/188-3e55a14c02731598.js\" defer=\"\"></script>\n    <script src=\"static/js/index-0c964641cb03d24c.js\" defer=\"\"></script>\n</head>\n<body>\n    <div id=\"__next\" data-reactroot=\"\"><main class=\"y1fU5arT\">\n            <div>\n                <div class=\"wUHvzwDX\">\n                    <main class=\"xByDAAj_\">\n                        <div class=\"IVx7Sant\"><h1 class=\"bltIOZdJ\">OCRアシスタント無料OCRツール</h1><h3 class=\"Ryc5j9WR\"> OCR ASSISTANTの無料OCRツールを使用して、テキストや表を認識し、PDFをWORDに変換し、仕事や生活の効率を向上させます</h3></div><div class=\"olUjsfHS\">\n                            <div class=\"ciFXvpTE\">\n                                <div class=\"rL4DO8gy\">テキスト認識</div>\n                                <div class=\"ant-row nLFkHDNm\" style=\"margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px\">\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"text_recognize.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"jGh32FHy\">\n                                                        <div class=\"mrSAoIfZ\">ユニバーサルテキスト認識</div><div class=\"WjiCI_fR\">画像を編集可能な単語およびtxt形式に変換する</div>\n                                                    </div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"text_accurate.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"jGh32FHy\">\n                                                        <div class=\"mrSAoIfZ\">タイポグラフィのテキスト認識</div><div class=\"WjiCI_fR\">画像を編集可能な単語およびtxt形式に変換する</div>\n                                                    </div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"handwritten_ocr.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">手書き認識</div><div class=\"WjiCI_fR\">画像を編集可能な単語およびtxt形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"ancient_ocr.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">古書の認識</div><div class=\"WjiCI_fR\">画像を編集可能な単語およびtxt形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"ciFXvpTE\">\n                                <div class=\"rL4DO8gy\">フォーム認識</div>\n                                <div class=\"ant-row nLFkHDNm\" style=\"margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px\">\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"table.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">一般的なフォーム認識</div><div class=\"WjiCI_fR\">画像内のテーブルを編集可能なExcelファイルに変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"table_frame.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">ボーダーテーブルの認識</div><div class=\"WjiCI_fR\">画像内のテーブルを編集可能なExcelファイルに変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"table_noframe.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div>\n                                                    <div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">ボーダレステーブル認識</div><div class=\"WjiCI_fR\">画像内のテーブルを編集可能なExcelファイルに変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"ciFXvpTE\">\n                                <div class=\"rL4DO8gy\">PDFからファイルへ</div><div class=\"ant-row nLFkHDNm\" style=\"margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px\">\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"pdf2word.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-pdf-word402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">PDFからWordへ</div><div class=\"WjiCI_fR\">PDFファイルをWord形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"pdf2markdown.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-pdf-md402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">PDFからMarkdownへ</div><div class=\"WjiCI_fR\">PDFファイルをMarkdown形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"pdf2excel.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-pdf-excel402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">PDFからExcelへ</div><div class=\"WjiCI_fR\">PDFファイルをExcel形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"pdf2jpg.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-pdf-jpg402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">PDFからJPGへ</div><div class=\"WjiCI_fR\">PDFファイルをJPG形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"pdf2ppt.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-pdf-ppt402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">PDFからPPTへ</div><div class=\"WjiCI_fR\">PDFファイルをPowerPointドキュメントに変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div><div class=\"ciFXvpTE\">\n                                <div class=\"rL4DO8gy\">その他のコンバージョン</div><div class=\"ant-row nLFkHDNm\" style=\"margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px\">\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"word2pdf.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-word-pdf402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">WordからPDFへ</div><div class=\"WjiCI_fR\">WordファイルをPDF形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div><div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"excel2pdf.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-excel-pdf402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">ExcelからPDFへ</div><div class=\"WjiCI_fR\">Excel ファイルを PDF 形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"image2pdf.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-jpg-pdf402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">画像をPDFに変換</div><div class=\"WjiCI_fR\">png / jpg / bmp画像をPDFに変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                    <div style=\"padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px\" class=\"ant-col ant-col-6\">\n                                        <div class=\"__4TTnAZxA\">\n                                            <a href=\"word2jpg.html\" target=\"_self\" class=\"custom-link\">\n                                                <div class=\"la4za2iU\">\n                                                    <div class=\"nyfQamHt\">\n                                                        <span style=\"box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%\">\n                                                            <span style=\"box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%\"><img style=\"display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0\" alt=\"\" aria-hidden=\"true\" src=\"data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e\"></span>\n                                                            <img alt=\"アイコン\" src=\"static/picture/image-2Ficons2Fhome2Ficon-word-jpg402x.png_96_75.png\" decoding=\"async\" data-nimg=\"intrinsic\" style=\"position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%\">\n                                                        </span>\n                                                    </div><div class=\"jGh32FHy\"><div class=\"mrSAoIfZ\">WordからJPGへ</div><div class=\"WjiCI_fR\">WordファイルをJPG形式に変換する</div></div>\n                                                </div>\n                                            </a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </main>\n                </div>\n            </div>\n        </main>\n    </div>\n    <script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{}},\"page\":\"/\",\"query\":{},\"buildId\":\"fmSVWroe3Il4vJrWJlNDc\",\"nextExport\":true,\"autoExport\":true,\"isFallback\":false,\"scriptLoader\":[]}</script>\n\n<div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script type=\"text/javascript\">isNeedTrans=false;</script><script src=\"/static/js/translate.js\"></script></body></html>"}