﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Pay.aspx.cs" Inherits="Account.Web.Pay1" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>支付成功<%=PageTitleConst.Default_Ext %></title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <style type="text/css">
        html, body {
            color: #333;
            margin: 0;
            height: 100%;
            font-family: "Myriad Set Pro", "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: normal;
        }

        * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
            color: #000;
        }

        a, label, button, input, select {
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        body {
            background: #f3f3f3;
            color: #666;
        }

        html, body, div, dl, dt, dd, ol, ul, li, h1, h2, h3, h4, h5, h6, p, blockquote, pre, button, fieldset, form, input, legend, textarea, th, td {
            margin: 0;
            padding: 0;
        }

        a {
            text-decoration: none;
            color: #08acee;
        }

        button {
            outline: 0;
        }

        button, input, optgroup, select, textarea {
            margin: 0;
            font: inherit;
            color: inherit;
            outline: none;
        }

        li {
            list-style: none;
        }

        a {
            color: #666;
        }

        .clearfix::after {
            clear: both;
            content: ".";
            display: block;
            height: 0;
            visibility: hidden;
        }

        .clearfix {
        }

        .b-line {
            position: relative;
        }

            .b-line:after {
                content: '';
                position: absolute;
                z-index: 2;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                border-bottom: 1px solid #e2e2e2;
                -webkit-transform: scaleY(0.5);
                transform: scaleY(0.5);
                -webkit-transform-origin: 0 100%;
                transform-origin: 0 100%;
            }

        .aui-arrow {
            position: relative;
            padding-right: 0.8rem;
            color: #7f8699;
            font-size: 0.85rem;
        }

            .aui-arrow span {
                font-size: 0.8rem;
                color: #9b9b9b;
            }

            .aui-arrow:after {
                content: " ";
                display: inline-block;
                height: 6px;
                width: 6px;
                border-width: 2px 2px 0 0;
                border-color: #848484;
                border-style: solid;
                -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
                transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
                position: relative;
                top: -2px;
                position: absolute;
                top: 50%;
                margin-top: -6px;
                right: 2px;
                border-radius: 1px;
            }

        .aui-flex {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
            padding: 15px;
            position: relative;
        }

        .aui-flex-box {
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            min-width: 0;
            font-size: 14px;
            color: #333;
        }

        .aui-flexView {
            width: 100%;
            height: 100%;
            margin: 0 auto;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;
        }

        .aui-scrollView {
            width: 100%;
            height: 100%;
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -ms-flex: 1;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            position: relative;
            padding-bottom: 53px;
        }

        .aui-navBar {
            height: 44px;
            position: relative;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            z-index: 102;
            background: #f3f3f3;
        }

        .aui-navBar-item {
            height: 44px;
            min-width: 15%;
            -webkit-box-flex: 0;
            -webkit-flex: 0 0 15%;
            -ms-flex: 0 0 15%;
            flex: 0 0 15%;
            padding: 0 0.9rem;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            font-size: 0.7rem;
            white-space: nowrap;
            overflow: hidden;
            color: #808080;
            position: relative;
        }

            .aui-navBar-item:first-child {
                -webkit-box-ordinal-group: 2;
                -webkit-order: 1;
                -ms-flex-order: 1;
                order: 1;
                margin-right: -25%;
                font-size: 0.9rem;
                font-weight: bold;
            }

            .aui-navBar-item:last-child {
                -webkit-box-ordinal-group: 4;
                -webkit-order: 3;
                -ms-flex-order: 3;
                order: 3;
                -webkit-box-pack: end;
                -webkit-justify-content: flex-end;
                -ms-flex-pack: end;
                justify-content: flex-end;
                font-size: 14px;
                color: #333333;
            }

        .aui-center {
            -webkit-box-ordinal-group: 3;
            -webkit-order: 2;
            -ms-flex-order: 2;
            order: 2;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            height: 44px;
            width: 80%;
            margin-left: 22%;
        }

        .aui-center-title {
            text-align: center;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            display: block;
            text-overflow: ellipsis;
            font-size: 0.95rem;
            color: #333;
            font-weight: bold;
        }

        .icon {
            width: 19px;
            height: 19px;
            display: block;
            border: none;
            float: left;
            background-size: 19px;
            background-repeat: no-repeat;
        }

        .aui-flex-box h1 {
            color: #303741;
            font-weight: 500;
            font-size: 1rem;
            overflow: hidden;
        }

        .aui-flex-box h2 {
            color: #303741;
            font-weight: 500;
            font-size: 1rem;
            margin-bottom: 0.8rem;
        }

        .aui-flex-box p {
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-all;
            text-overflow: ellipsis;
            font-size: 0.9rem;
            margin-bottom: 0.1rem;
            color: #676767;
        }

        .aui-flex-box h3 {
            font-size: 0.9rem;
            color: #8a8a8a;
            font-weight: normal;
            width: 100%;
            overflow: hidden;
            margin-bottom: 0.3rem;
        }

        .icon-return {
            background-image: url('data:image/png;base64,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');
        }

        .icon-more {
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABaCAYAAAA4qEECAAAEJ0lEQVR4Xu2YzWtcVRjG3/fOVKRQShZug7vW2FY0S4lkIXVRkrnnjmkXRYhUs9WNulBoChZad7q2pW6kMsw9N5NNPygNpZtaCtqitij4B0QIwa+FN+fIhanUNJOcezJ5aOEZmN37nt95f/Pce+dcFX4gBhRCIUQoGhQCiqZokAEQhommaJABEIaJpmiQARCGiaZokAEQhommaJABEIaJpmiQARCGiaZokAEQhommaJABEIaJpmiQARCGiaZokAEQhommaJABEIaJpmiQARCGiaZokAEQhommaJABEIaJpmiQARCGiaZokAEQhommaJABECY40VmWve6ce1FVXxCRfSLyvYjc997/XBTFtZ3c7+zs7LOrq6tvqeqE935CVUvn3BURue2c6/Z6vd93kp+m6WFVfU1EJqqvqlbsG97729baqyHsINFpmn6oqmcHLaiq5/I8fycEWLem1WodTJLkCxGZHNBb/djvFUVRDT/0z7Bm31K0MeZXEXk+YILfrLXPBdQFl6RpelJV50MavPfzRVGcCqkNrRnm7JuKNsbcFJFXQzfmvb9QFMXbofWb1fUv18t11nLOHVpYWLhXp2dQ7bBnHyjaGPOBiHxWd9Oq+m6e51/W7Xu0fnp6ek+j0fhWRPbXXOd6s9k80ul0/q7Z97/ynZh9M9F3ROSViA3/ZK0di+j7ryVN06Oq+k3MGqp6PM/zr2N6H/YYY2Jn/85a+/JG7A1FT05ONkdGRv4UkWdiNry2tra/1+s9iOmteowxH4vIp5H9p621n0T2ytzc3K7l5eU/Imf/Z2VlZffS0lK5nr+haGNM9Rfux9jNisgJa+352P40TS+q6rGYflXt5nn+Zkxv1dNqtcaSJPkhtj9JkgPdbvexfopeZxQqenx8fNfo6Gjs5SO8dQTeOvr3ydgHwrYfhlmWHfPeX4y5fJ+qh2E14FYnokEShvH3bmZmZm9Zlrf6R/06vpfKsjyyuLj4V52m9bU7MftQDywi8pW1dnY7Qz7sbbfbbzjnLtVZK0mSl7rd7t06PYNq6x5Ytpr9iT6CZ1k2770/GSJOVU/leR50XA9Zr3/7HNrrhy1F94EficiZTTZ43lp7InSAOnXtdvuQc+7zQS+VvPcPVPV9a22t9IfuwRgzlNmDRPfv2YeTJBnz3lfH4uo16V3v/X1V/SX0VWHocOvrpqamdjebTSMiY6q6zzlXquq9it1oNC51Op3V2LVD+qr3LtudPVh0yIZYM9gARYPSQdEUDTIAwjDRFA0yAMIw0RQNMgDCMNEUDTIAwjDRFA0yAMIw0RQNMgDCMNEUDTIAwjDRFA0yAMIw0RQNMgDCMNEUDTIAwjDRFA0yAMIw0RQNMgDCMNEUDTIAwjDRFA0yAMIw0RQNMgDCMNEUDTIAwjDRFA0yAMIw0SDR/wLdV15qnSn2vQAAAABJRU5ErkJggg==');
        }

        .aui-pay-info {
            margin-top: 15px;
        }

            .aui-pay-info p em {
                text-align: right;
            }

            .aui-pay-info .aui-flex {
                padding: 0;
            }

        .aui-pay-fill {
            padding: 10px 35px;
        }

        .aui-pay-flex {
            width: 93%;
            border-radius: 10px;
            background: white;
            padding: 20px;
            margin-bottom: 20px;
        }

        .aui-pay-text {
            text-align: center;
            width: 100%;
            font-size: 14px;
            color: #939393;
        }

        .aui-pay-text {
            padding: 10px 0;
        }

            .aui-pay-text h2 {
                color: #333333;
                font-size: 35px;
                font-weight: normal;
                margin-top: 10px;
                margin-bottom: 5px;
            }

                .aui-pay-text h2 em {
                    color: #333333;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: normal;
                }

        .aui-pay-flex .aui-flex {
            padding: 0;
        }

        .aui-flex-box .aui-pay-titleS {
            font-size: 18px;
            color: #333333;
            position: relative;
            padding-left: 25px;
        }

        .aui-pay-titleS:after {
            width: 20px;
            height: 20px;
            position: absolute;
            z-index: 0;
            top: 3px;
            left: 0;
            content: "";
            background-image: url('data:image/png;base64,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');
            background-size: 20px;
        }

        .aui-pay-flex .aui-pay-info p {
            color: #6e6e6e;
            padding: 5px 0;
            font-size: 14px;
        }

            .aui-pay-flex .aui-pay-info p em {
                color: #333333;
                font-style: normal;
                font-size: 15px;
            }

        .aui-pay-com {
            width: 92%;
            margin: 0 auto;
        }

        .aui-pay-comzfb button {
            background-color: #6bc3ff;
            background: -webkit-linear-gradient(to right, #6bc3ff, #0081ff);
            background: -o-linear-gradient(to right, #6bc3ff, #0081ff);
            background: -moz-linear-gradient(to right, #6bc3ff, #0081ff);
            background: linear-gradient(to right, #6bc3ff, #0081ff);
            color: white;
            border: none;
            width: 93%;
            padding: 12px 0;
            border-radius: 10px;
            font-size: 16px;
        }

        .aui-pay-comwx button {
            background-color: #00C800;
            background: -webkit-linear-gradient(to right, #00C800, #3EC742);
            background: -o-linear-gradient(to right,#00C800, #3EC742);
            background: -moz-linear-gradient(to right, #00C800, #3EC742);
            background: linear-gradient(to right, #00C800, #3EC742 ) color: white;
            color: white;
            border: none;
            width: 93%;
            padding: 12px 0;
            border-radius: 10px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <section class="aui-flexView">
        <header class="aui-navBar aui-navBar-fixed">
            <a href="javascript:;" class="aui-navBar-item">
                <i class="icon icon-return"></i>
            </a>
            <div class="aui-center">
                <span class="aui-center-title" id="payType"></span>
            </div>
            <a href="javascript:;" class="aui-navBar-item">
                <i class="icon icon-more"></i>
            </a>
        </header>
        <section class="aui-scrollView">
            <div class="aui-pay-box">
                <div class="aui-pay-text">
                    <p>您已成功付款</p>
                    <h2><em>￥</em><span id="strPrice">0.00</span> 元</h2>
                    <p>我们将在30分钟内安排发货！</p>
                </div>
                <div class="aui-pay-fill">
                    <div class="aui-pay-flex">
                        <div class="aui-flex b-line">
                            <div class="aui-flex-box">
                                <h2 class="aui-pay-titleS">订单信息</h2>
                            </div>
                            <%--<span class="aui-arrow" onclick="window.location.href='./DescNew.aspx'">详情</span>--%>
                        </div>
                        <div class="aui-pay-info">
                            <p class="aui-flex">商品明细<em class="aui-flex-box" id="remark">-</em></p>
                            <p class="aui-flex">订单编号<em class="aui-flex-box" id="orderNo">-</em></p>
                            <p class="aui-flex">订单金额<em class="aui-flex-box" id="strPrice1">¥0.00</em></p>
                            <p class="aui-flex">实付金额<em class="aui-flex-box" id="strPrice2">¥0.00</em></p>
                            <p class="aui-flex">用户信息<em class="aui-flex-box" id="param">-</em></p>
                        </div>
                    </div>
                </div>
                <div class="aui-pay-comzfb" id="divZfb">
                    <button onclick="closePage();">完成</button>
                </div>
                <div class="aui-pay-comwx" id="divWx" style="display: none;">
                    <button onclick="closePage();">完成</button>
                </div>
            </div>
        </section>
    </section>
    <script type="text/javascript">
        function closePage() {
            window.location.href = 'about:blank';
            window.close();
        }
        function GetString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(decodeURI(r[2]));
            return '';
        }
        document.getElementById("strPrice").innerHTML = GetString("price");
        document.getElementById("strPrice1").innerHTML = "¥" + GetString("price");
        document.getElementById("strPrice2").innerHTML = "¥" + GetString("reallyPrice");
        document.getElementById("orderNo").innerHTML = GetString("payId");
        document.getElementById("param").innerHTML = GetString("param");
        document.getElementById("remark").innerHTML = GetString("remark");
        if (GetString("type") === "1") {
            document.getElementById("payType").innerHTML = "微信支付";
            document.getElementById("divZfb").style.display = "none";
            document.getElementById("divWx").style.display = "block";
        } else {
            document.getElementById("payType").innerHTML = "支付宝支付";
            document.getElementById("divZfb").style.display = "block";
            document.getElementById("divWx").style.display = "none";
        }
    </script>
</body>
</html>