﻿<%@ Page Title="在线编码解码工具 | 多种格式加密解密 | Unicode和Base64加解密" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="static/css/index-b078aaba.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <script type="text/javascript" src="static/js/vue.js"></script>
    <script src="static/js/require.js"></script>
<meta name="keywords" content="编码解码工具,Base64编码解码,Unicode转换,URL编码解码,中文编码转换,ASCII编码转换,MD5加密,SHA1加密,字符串加密解密,HTML实体编码">
<meta name="description" content="免费多功能编码解码工具，支持URL、Unicode、Base64、ASCII、HTML实体、MD5、SHA1等多种编码格式的转换与解码。为开发者提供高效中文与Unicode互转、多种加密算法支持。">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="panel panel-default" style="margin-bottom: 0px;">
        <div class="panel-heading">
            <h3 class="panel-title">在线字符串编码转换工具<span class="x-btns">
                <input id="btnCodeChange" type="button" value="转换" class="btn btn-sm btn-success">
                <input id="btnCodeClear" type="button" value="清空" class="btn btn-sm btn-warning ui-ml-10"></span></h3>
        </div>
    </div>
    <div class="panel-body mod-endecode">
            <div class="row">
                <textarea class="form-control mod-textarea ui-mb-10" id="srcText" ref="srcText" v-model="sourceContent" placeholder="粘贴需要进行转换的字符串"></textarea>
            </div>
            <div class="row">
                <table class="x-opts">
                    <tr>
                        <td class="td-label">
                            <label>加密：</label>
                        </td>
                        <td>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="uniEncode" v-model="selectedType" @click="convert()">Unicode编码<span class="x-ps">(\u开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="utf8Encode" v-model="selectedType" @click="convert()">URL编码<span class="x-ps">(%开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="gzipEncode" v-model="selectedType" @click="convert()">Gzip压缩
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="utf16Encode" v-model="selectedType" @click="convert()">UTF16编码<span class="x-ps">(\x开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="base64Encode" v-model="selectedType" @click="convert()">Base64编码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="md5Encode" v-model="selectedType" @click="convert()">MD5计算
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="hexEncode" v-model="selectedType" @click="convert()">十六进制编码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="sha1Encode" v-model="selectedType" @click="convert()">Sha1加密
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="htmlEntityEncode" v-model="selectedType" @click="convert()">HTML普通编码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="htmlEntityFullEncode" v-model="selectedType" @click="convert()">HTML深度编码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="html2js" v-model="selectedType" @click="convert()">HTML转JS
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-label">
                            <label>解密：</label>
                        </td>
                        <td>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="uniDecode" v-model="selectedType" @click="convert()">Unicode解码<span class="x-ps">(\u开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="utf8Decode" v-model="selectedType" @click="convert()">URL解码<span class="x-ps">(%开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="gzipDecode" v-model="selectedType" @click="convert()">Gzip解压
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="utf16Decode" v-model="selectedType" @click="convert()">UTF16解码<span class="x-ps">(\x开头)</span>
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="base64Decode" v-model="selectedType" @click="convert()">Base64解码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="hexDecode" v-model="selectedType" @click="convert()">十六进制解码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="htmlEntityDecode" v-model="selectedType" @click="convert()">HTML实体解码
                                </label>
                            </div>
                            <div class="radio ui-d-ib ui-mr-20">
                                <label>
                                    <input type="radio" name="codeType" value="urlParamsDecode" v-model="selectedType" @click="convert()">URL参数解析
                                </label>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="rst" class="row ui-mt-20" v-show="resultContent.length || urlResult">
                <h5>当前数据解析结果如下：</h5>
                <textarea class="form-control mod-textarea" id="rstCode" ref="rstCode" v-model="resultContent" @mouseover="getResult()" v-if="!urlResult"></textarea>
                <div class="x-url-infos" v-if="urlResult">
                    <ul>
                        <li>
                            <b>协议</b>：<span>{{urlResult.protocol}}</span>
                        </li>
                        <li>
                            <b>域名</b>：<span>{{urlResult.hostname}}</span>
                        </li>
                        <li>
                            <b>路径</b>：<span>{{urlResult.pathname}}</span>
                        </li>
                        <li>
                            <table class="table table-bordered table-hover ui-mt-10">
                                <tr>
                                <thead>
                                <th>参数Key</th>
                                <th>参数Value</th>
                                </thead>
                                </tr>
                                <tr v-for="item in urlResult.params">
                                    <td>{{item[0]}}</td>
                                    <td>{{item[1]}}</td>
                                </tr>
                            </table>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    <script type="text/javascript" src="static/js/index-cf567dc8.js"></script>
    <script type="text/javascript" src="static/js/he.js"></script>
    <script src="static/js/jquery-3.3.1.min.js"></script>
</asp:Content>
