html,
body {
  font-family: "SF Pro SC", "HanHei SC", "SF Pro Text", "Myriad Set Pro",
    "SF Pro Icons", "PingFang SC", "Helvetica Neue", "Helvetica", "Arial",
    sans-serif;
  position: relative;
  background: #fff;
  -webkit-font-smoothing: antialiased;
  font-weight: 300;
}
body {
  margin: 0;
}
ul,
ol {
  padding: 0;
}
li {
  list-style: none;
}

a:hover {
  color: inherit;
  text-decoration: none !important;
}
img {
  width: 100%;
}
h1 {
  font-weight: 600;
}

.new_table .table-bordered td,
.new_table .table-bordered th {
  border: 1px solid #fddfdf !important;
}

/* #operate{
    margin-top: 50px;
} */
.bot_show {
  display: none;
  height: 45px;
}
.bot_con {
  width: 100%;
  display: flex;
  position: fixed;
  bottom: 0;
  font-size: 26px;
  z-index: 9;
  background: #fff;
  align-items: center;
}
.bot_qu {
  display: block;
  color: #282b31;
  font-size: 16px;
  width: 50%;
  text-align: center;
  padding: 10px;
}
.bot_ico {
  width: 22px;
}

.fac_doc {
  color: #006cff;
  display: block;
  font-size: 13px;
}
.fac_doc:hover {
  color: #006cff;
}
.jian_bian {
  position: relative;
  background-image: linear-gradient(#fef5f5, #feecec);
}
.jian_bian2 {
  background-image: linear-gradient(#fef5f5, #feecec);
}

.new_ke {
  position: absolute;
  top: 0;
  left: -1px;
  background: #ff4e4e;
  color: #fff;
  padding: 4px 14px;
  font-size: 12px;
}
.new_ke2 {
  width: 76px;
  background: #ff4e4e;
  color: #fff;
  padding: 4px 14px;
  font-size: 12px;
  display: none;
}
.free_y {
  height: 34px;
  width: 140px;
  margin: auto;
  line-height: 34px;
  display: block;
  color: #ff4e4e;
  border-radius: 40px;
  background-color: #ffe4b1;
}
.free_y:hover {
  color: #ff4e4e;
  background-color: #ffedcb;
}
.konw_p {
  color: #006cff;
}
.konw_p:hover {
  color: #006cff;
}
.title_h {
  margin: 0 20%;
}

/* 头部css */
.header,
.header2 {
  position: fixed;
  width: 100%;
  z-index: 2;
  transition: all 0.5s;
  background: rgba(255, 255, 255, 0);
}
.header2 {
  display: none;
}
.head-nav {
  padding: 0 !important;
}
.head-item {
  margin-left: 40px;
}
.fixed-icon {
  position: fixed;
  bottom: 18%;
  right: 2%;
  z-index: 9;
}

.iconjiantou1 {
  display: inline-block;
  transition: all 0.3s;
}

.iconjiantou1.active {
  transform: rotate(90deg);
}
.console {
  color: #fff !important;
  width: 120px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 2px;
  background-color: #006cff;
}

.bot-text {
  color: #999;
  text-align: center;
  position: relative;
  margin-top: 20px;
  opacity: 0;
  margin: auto;
  margin-top: 20px;
}
.bot-text::after,
.bot-text::before {
  content: "";
  width: 24px;
  height: 1px;
  margin: 0 10px;
  vertical-align: middle;
  display: inline-block;
  background-color: #d3d3d3;
}

/* 加载动画 */
.spinner {
  margin: 10px auto;
  width: 50px;
  height: 60px;
  text-align: center;
  font-size: 10px;
  display: none;
}
.spinner.active {
  display: block;
}
.spinner > div {
  background-color: #006cff;
  height: 100%;
  width: 6px;
  display: inline-block;
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}
.spinner .rect2 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}
.spinner .rect3 {
  -webkit-animation-delay: -1s;
  animation-delay: -1s;
}
.spinner .rect4 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}
.spinner .rect5 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}
@-webkit-keyframes stretchdelay {
  0%,
  40%,
  100% {
    -webkit-transform: scaleY(0.4);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}
@keyframes stretchdelay {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
    -webkit-transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}
/* 、 */

.wx-con {
  width: 56px;
  height: 56px;
  cursor: pointer;
  background: #fff;
  position: relative;
  display: block;
  margin-bottom: 10px;
  border-radius: 50%;
  box-shadow: 0px 0px 15px rgb(0 0 0 / 14%);
  transition: all 0.3s ease-in 0s;
  background-image: linear-gradient(0deg, #fff, #f3f5f8);
  border: 2px solid #fff;
  right: -3rem;
}
.wechat-ico {
  width: 32px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.wx-text {
  position: absolute;
  left: -125px;
  top: 10px;
  opacity: 0;
  transition: all 0.3s;
  padding: 8px;
  font-weight: normal;
  color: #fff;
  border-radius: 2px;
  background: #555555;
  transform: scale(0);
  width: 120px;
  text-align: center;
}
.wx-con:hover .wx-text {
  opacity: 1;
  transform: scale(1);
}
.dropdown-item span {
  color: #606879;
  padding: 4px;
  position: relative;
}
.dropdown-item span::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 100%;

  border-bottom: 1px solid #dce0e9;
}
.menu-line,
.menu-line2 {
  display: none;
  width: 50px;
  height: 27px;
  cursor: pointer;
  position: relative;
}
.mid-line2 {
  display: block;
  position: absolute;
  top: 12px;
  left: 8px;
  width: 30px;
  height: 2px;
  transition: all 0.3s ease;
  background-color: #000;
}
.mid-line3 {
  display: block;
  position: absolute;
  top: 12px;
  left: 8px;
  width: 30px;
  height: 2px;
  transition: all 0.3s ease;
  background-color: #fff;
}
.mid-line2.active2,
.mid-line2.active2::before,
.mid-line2.active2::after {
  background-color: #fff;
}
.mid-line3.active2,
.mid-line3.active2::before,
.mid-line3.active2::after {
  background-color: #000;
}

.mid-line2::before,
.mid-line2::after {
  position: absolute;
  content: "";
  width: 30px;
  height: 2px;
  border-radius: 3px;
  background-color: #000;
  transition: all 0.3s ease;
}
.mid-line3::before,
.mid-line3::after {
  position: absolute;
  content: "";
  width: 30px;
  height: 2px;
  border-radius: 3px;
  background-color: #fff;
  transition: all 0.3s ease;
}
.mid-line2::before {
  bottom: 8px;
}
.mid-line3::before {
  bottom: 8px;
}
.mid-line2::after {
  top: 8px;
}
.mid-line3::after {
  top: 8px;
}
.mid-line2.active::before {
  bottom: 0;
  transform: rotate(-90deg);
}
.mid-line3.active::before {
  bottom: 0;
  transform: rotate(-90deg);
}
.mid-line2.active::after {
  opacity: 0;
}
.mid-line3.active::after {
  opacity: 0;
}
.mid-line2.active {
  transform: rotate(45deg);
}
.mid-line3.active {
  transform: rotate(45deg);
}

.header.active,
.header2.active {
  background: rgba(255, 255, 255, 0.96);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  box-shadow: 0 0 5px #888;
}
.headActive,
.headActive2 {
  background: rgba(255, 255, 255, 0.96);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}
.headActive3 {
  background: #0b1d2d;
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}
.zhe-die,
.zhe-die2 {
  padding: 0;
  outline: none !important;
}
.zhe-die2 {
  display: none;
  transition: all 0.3s;
}
.head-icon {
  background-image: url(../image/zhedie.png) !important;
}
.head-icon2 {
  background-image: url(../image/x.png) !important;
}
.logo {
  width: 170px;
  margin-right: 20px;
}
.logo-svg {
  display: none;
}
.logo-svg2 {
  display: none;
}
.header-nav {
  width: 100%;
  justify-content: space-between;
}
.header-nav .nav-item {
  position: relative;
  height: 64px;
  line-height: 64px;
  display: flex;
  align-items: center;
}
.new-activity {
  display: flex;
  align-items: center;
}
.hot-png {
  width: 32px;
  height: 15px;
}

.sign-up {
  flex-wrap: wrap;
  display: flex;
  align-items: center;
}
.sign-in,
.register {
  padding: 0 !important;
  width: 120px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 2px;
}
.sign-in {
  color: #282b31;
  margin-right: 5px;
}
.sign-in:hover {
  color: #282b31;
}
.register {
  color: #fff;
  font-weight: 500;
}
.txt-login {
  color: #fff !important;
}
.txt-login.active {
  color: #282b31 !important;
}
.txt-color,
.sign-in {
  font-size: 16px;
  font-weight: 500;
  color: #282b31;
}
.txt-color.active2 {
  color: #fff !important;
}
.txt-color:hover,
.txt-color4:hover {
  color: #006cff;
}

.txt-color::before,
.txt-color4::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0; /*靠最左边开始*/
  border-bottom: 2px solid #006cff; /*设置底部边颜色*/
  width: 0; /*宽度为0，这里的宽度是相对与li的宽度*/
  height: 100%; /*设置高度使它和li等高*/
}

.txt-color:hover::before,
.txt-color4:hover::before {
  width: 100%;
  transition: 0.2s all linear;
  transition-delay: 0.1s;
}
.txt-color4,
.txt-color5 {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
.txt-color5:hover {
  color: #fff;
}

.txt-color4.activeb,
.txt-color5.activeb {
  color: #282b31;
}
.txt-color4.activeb:hover {
  color: #006cff;
}

.product.active,
.product2.active,
.solution.active,
.solution2.active {
  color: #006cff;
}
.product.active::before,
.product2.active::before,
.solution.active::before,
.solution2.active::before {
  width: 100%; /*宽度为0，这里的宽度是相对与li的宽度*/
}
.register {
  color: #fff !important;
  background-color: #ff227a;
}
.register2 {
  color: #ff227a !important;
  background-color: #fff;
}
.register2.active {
  color: #fff !important;
  background-color: #ff227a;
}

.collapse-content,
.collapse-content2 {
  position: fixed;
  width: 100%;
  top: 64px;
  z-index: 2;
  height: 0px;
  overflow: hidden;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.96);
  /* -webkit-backdrop-filter: saturate(180%) blur(20px);
    backdrop-filter: saturate(180%) blur(20px); */
  box-shadow: 0px 6px 6px rgb(0 0 0 / 5%);
}

.collapse-content.active::after,
.collapse-content2.active::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid #eceff4;
}
.collapse-content.activeBlack::after,
.collapse-content2.activeBlack::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid #eceff4;
}

.collapse-content.active,
.collapse-content2.active {
  height: 380px;
  background: rgba(255, 255, 255, 0.96);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
}
.collapse-content.activeBlack .collapse-content2.activeBlack {
  height: 380px;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
  border-bottom: 1px solid #eceff4;
}

.collapse-item {
  padding: 0 30px 40px 20px;
}

.collapse-left-content {
  display: flex;
  justify-content: space-between;
}
.left-item {
  margin-top: 50px;
}
.left-item a {
  margin-top: 10px;
  font-weight: 500;
  font-size: 14px;
  color: #606879;
  display: block;
}
.left-item a:hover {
  color: #006cff;
}
.left-item h5 {
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecf3;
}
.collapse-right-item {
  display: flex;
  justify-content: space-around;
}
.chan-ico {
  width: 26px;
}
.little-text6 {
  font-size: 14px;
  font-weight: 400;
  color: #606879;
}
.chan-item,
.chan-item2 {
  cursor: pointer;
  padding: 15px 35px;
  justify-content: space-between;
  align-items: center;
  transition: 0.3s;
  border-radius: 4px;
  margin-top: 20px;
}
.chan-item:hover,
.chan-item2:hover {
  background-color: #eaf2ff;
}

.jiantou2 {
  color: #006cff;
  font-weight: 400;
  width: 15px;
  display: block;
  opacity: 0;
  transition: all 0.3s;
}
.chan-item:hover .jiantou2,
.chan-item2:hover .jiantou2 {
  padding-left: 5px;
  opacity: 1;
}
.little-title6 {
  font-size: 16px !important;
}
.chan-item:hover .little-title6,
.chan-item2:hover .little-title6 {
  color: #006cff;
}
.new {
  width: 30px;
}
.one-tr-bg {
  background-color: #fafbfc;
}
.one-tr-bg2 {
  background-color: #fff;
}
.soul-item {
  height: 380px;
  text-align: center;
  padding-top: 40px;
  border-right: 1px solid #eceff4;
}
.soul-item > div {
  margin-bottom: 40px;
}

/* 底部 */
.you-mb {
  margin-top: 40px;
}
.lianjie1 {
  padding-left: 0 !important;
}
.lianjie2 {
  padding-right: 0 !important;
}
.youqin a {
  font-size: 15px;
  font-weight: 500;
  color: #8a909d;
}
.youqin a:hover {
  color: #006cff !important;
}

.foot-cont {
  padding-top: 60px;
}
.bei-an {
  text-align: right;
}
.item {
  font-weight: 500;
  color: #606879;
  font-size: 16px;
}

.item > img {
  width: 20px;
}
.span-text {
  color: #fff;
  font-weight: 500;
}
.foot-content {
  width: 1180px;
  margin: 0 auto;
}
.bt-ico {
  margin-bottom: 20px;
  width: 26%;
}
.weibo-weixin > a {
  width: 36px;
  display: inline-block;
}
.weibo-weixin > img {
  width: 36px;
}
.qrcode {
  width: 80px !important;
  height: 80px !important;
  vertical-align: top;
  display: none;
  transition: all 0.5s;
}
.wx-ico:hover + .qrcode {
  display: inline-block;
}
.wb-wx {
  width: 30px;
}
.right-item a:first-child {
  margin-bottom: 20px;
}
.right-item a:hover {
  color: #006cff !important;
}
.right-item > a {
  display: block;
  color: #8a909d;
}
.right-item > h6 {
  margin-bottom: 28px;
}
.item2 {
  margin-top: 50px;
}

.xieyi {
  margin-top: 90px;
}
.xieyi > a {
  color: #8a909d;
}
.xian {
  width: 100%;
  border-bottom: 1px solid #49505f;
  margin-top: 50px;
}
.an-xu,
.gong-kai {
  text-align: center;
}
.ke-hu {
  text-align: right;
}

/* 首页css */
.carousel-inner img {
  width: 100%;
  height: 100%;
}
.index-bg {
  background-image: url(../image/indexbg.png);
  background-size: 600px auto;
  background-repeat: no-repeat;
  background-position: 75% bottom;
}

.wan-zhan {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 90%;
}
.over-hide {
  overflow: hidden;
}
.pad-left1 {
  padding-left: 50px !important;
}
.ins {
  position: absolute;
  bottom: 0;
}
.home-part2 {
  margin-bottom: 120px;
}
.index-sol-item {
  width: 205px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}
.index-sol1 {
  width: 52px;
  margin-right: 10px;
}
.lb-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 83%;
  padding: 24px 0;
}
.lb-title {
  font-size: 16px;
  font-weight: bold;
  color: #282b31;
}
.lb-title2 {
  color: #606879;
  font-size: 14px;
  font-weight: normal;
}
/* 文字动画 */
[data-animation] {
  opacity: 0;
}

[data-animation].animated {
  opacity: 1;
}
/* .animated.active{
    animation-fill-mode:none
} */
.sms-code.active {
  animation-name: none;
}
.animated {
  animation-duration: 0.8s !important;
}
/*  */
.top-part-bg {
  /* min-height: 520px; */
  min-height: 460px;
  background-color: #f5f9fc;
}
.huo-do {
  width: 58%;
  margin: auto;
  margin-top: 15px;
}
.wen-zi {
  width: 40%;
  margin: auto;
}
.wen-zi img {
  margin-top: 70px;
}

.ji-ke {
  display: block;
  color: #552d22;
  font-weight: normal;
  background: #ffeaa1;
  width: 140px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  margin: auto;
  margin-top: 40px;
}
.ji-ke:hover {
  color: #552d22;
  background: #f5e09a;
}
.top-part-bg2,
.top-part-bg2_act {
  padding: 52px 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right;
}
.top-part-bg2 {
  background-image: url(../image/new_p.png);
}

.top-part-bg4 {
  background-image: url(../image/price-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right;
}
.top-part-bg3 {
  background-image: url(../image/solution.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right;
}

.home-part3 {
  background-image: url(../image/bg-part3.jpg);
  background-size: cover;
}
.home-part7 {
  background-color: #fafbfc;
}
.section-part2,
.home-part3,
.home-part5,
.home-part6,
.home-part7 {
  margin-top: 80px;
}
.top-title {
  font-size: 44px;
  font-weight: bold;
  color: #282b31;
}
.sub-introduce {
  font-weight: normal;
  color: #606879;
  margin-top: 24px;
}
.zhu-title1 {
  font-weight: 500;
  color: #000;
  margin-bottom: 16px;
}
.fu-title1 {
  font-weight: normal;
  color: #807a7a;
}
.zhu-title {
  font-weight: 500;
  color: #fff;
  margin-bottom: 16px;
}
.fu-title {
  font-weight: 400;
  color: #b7c1d7;
}
.top-part {
  margin-top: 140px;
}
.top-part2 {
  margin-top: 120px;
}
.top-btn {
  margin-top: 90px;
}
.top-btn span {
  font-weight: normal;
  color: #606879;
}
.bo-fang {
  width: 20px;
  margin-right: 10px;
  vertical-align: text-top;
}
.text-title2 {
  margin: 80px 0 72px 0;
}
.text-title-sec {
  margin: 80px 0 50px 0;
}
.text-title-th {
  margin: 80px 0 72px 0;
}
.text-title-fo {
  margin: 60px 0 36px 0;
}
.text-title3 {
  margin: 10px 0 40px 0;
}
.text-title4 {
  border-top: 1px solid #eef0f6;
  margin-bottom: 80px;
}
.new-old-user {
  margin-top: 60px;
}
.camyu {
  margin-top: 50px;
}
.seven {
  width: 80%;
  margin-top: 20%;
}
.can-yu {
  color: #fff;
  margin-right: 20px;
  background-image: linear-gradient(to right, #f51b2f 5%, #ff6622 60%);
}
.can-yu:hover {
  color: #fff;
  background-image: linear-gradient(to right, #e71b30 5%, #f05f21 60%);
}
.free-use,
.lanxi-sw,
.can-yu {
  display: inline-block;
  width: 148px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-weight: 500;
}
.lanxi-sw {
  opacity: 0.9;
  color: #006cff;
  background-color: #eaf2ff;
}
.lanxi-sw:hover {
  color: #006cff;
  background-color: #d9e6fc;
}
.free-use {
  color: #fff;
  background-color: #006cff;
}
.free-use:hover,
.on-line:hover,
.details2:hover,
.back-price:hover,
.console:hover {
  color: #fff;
  background-color: #2989ff;
}
.box-parent {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-flow: row wrap;
  align-content: flex-start;
}
.box-child {
  text-align: center;
  box-sizing: border-box;
  flex: 0 0 19%;
  height: 210px;
  margin: 3px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  background-color: #fafbfc;
}
.little-title {
  font-weight: normal;
  color: #606879;
  font-size: 13px;
  opacity: 0;
  transition: all 0.3s;
}

.child-item {
  width: 70%;
  position: absolute;
  top: 10%;
  left: 50%;
  transform: translate(-50%, 50%);
  transition: all 0.3s;
}
.child-item > div {
  font-weight: 500;
  color: #282b31;
  margin: 20px 0 5px 0;
}
.box-child:hover {
  background-color: #fff;
}
.box-child:hover .child-item {
  transform: translate(-50%, 40%);
}
.box-child:hover .little-title {
  opacity: 1;
}
.item-ico {
  width: 50px;
}

.nav-tabs {
  display: flex;
  justify-content: space-evenly;
  background-color: #fff;
  border-bottom: 1px solid #eef0f6 !important;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.home-nav {
  display: inline-block;
  position: relative;
  text-align: center;
  margin: 0;
  padding: 15px 0;
}
.home-nav .nav-link {
  font-size: 18px;
  color: #282b31 !important;
  font-weight: 500;
  padding: 0.5rem 0;
}

.link-title:after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  border-bottom: 2px solid #006cff;
  width: 0%;
}

.link-title.active:after {
  width: 100%;
  transition: 0.2s all linear; /*设置过度时间*/
  transition-delay: 0.1s; /*过度延时*/
}
.nav-tabs .nav-link {
  border: none !important;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #006cff !important;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.left-text {
  padding-top: 60px;
}
.left-text h3 {
  color: #282b31;
}
.span-label {
  display: inline-block;
  font-size: 15px;
  border-radius: 3px;
  width: 80px;
  text-align: center;
  background: #eaedf4;
  color: #606879;
  font-weight: normal;
  height: 25px;
  line-height: 25px;
}
.business {
  color: #606879;
  font-weight: normal;
  font-size: 15px;
}
.details {
  font-size: 15px;
  display: block;
  margin-bottom: 40px;
  margin-top: 52px;
  font-weight: 500;
  color: #606879 !important;
}
.jiantou {
  transition: all 0.3s;
}
.details:hover {
  color: #006cff !important;
}
.details:hover .jiantou {
  color: #2989ff !important;
  padding-left: 5px !important;
}
.an-li {
  width: 75px;
  display: block;
  font-size: 13px;
  color: #fff;
  background-color: #006cff;
  text-align: center;
  height: 25px;
  line-height: 25px;
  font-weight: 500;
  border-radius: 13px;
  margin: 30px 0;
}
.an-li:hover {
  color: #fff;
  background-color: #2989ff;
}
.fu-wu > span {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  display: inline-block;
  margin-top: 12px;
}
.fu-wu > h2 {
  margin-top: 10px;
}
.hezuo-shop {
  display: flex;
}
.you-dian {
  color: #fff;
  padding: 60px 0 48px 0;
}
.hour::before {
  content: "";
  width: 1px;
  height: 80%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #2989ff;
}
.fang-an {
  position: relative;
  top: 28px;
  z-index: 1;
}

.home-part4 {
  position: relative;
  background-color: rgba(0, 108, 255, 0.5);
}
.advantage {
  padding: 50px 0;
}
.use-btm {
  margin-bottom: 20px;
}

.pt-icon {
  width: 104px;
  margin-bottom: 30px;
}
.pt-item {
  margin: 60px 0 10px 0;
}
.pt-title {
  margin-bottom: 20px;
  color: #282b31;
}
.pt-text {
  font-weight: 400;
  color: #606879;
  font-size: 14px;
  display: block;
  width: 70%;
  margin: auto;
}

.svg1,
.svg2,
.svg3,
.svg4,
.svg5 {
  margin: auto;
  width: 40px;
  height: 40px;
}

.xuna-nav {
  display: flex;
  justify-content: space-between;
  border-bottom: none !important;
}
.xuna-nav .nav-link {
  color: #606879;
  font-weight: 500;
  border: 0;
  padding: 0;
}
.new-item {
  font-size: 14px;
  margin-top: 20px;
}
.xuna-nav .nav-item {
  text-align: center;
  width: 20%;
  margin-bottom: 0;
  margin-left: 0;
}
.tab-pane > .row {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.xuan-cont .tab-pane {
  border-radius: 10px;
  background-color: #006cff;
  background-image: url(../image/yhzn.png);
  background-size: cover;
}
.title {
  position: relative;
}
.txt:after,
.txt:before {
  background: #eef0f6;
  content: "";
  height: 1px;
  position: absolute;
  top: 20%;
  width: 30%;
  z-index: 1;
}

/*调整背景横线的左右距离*/
.txt:before {
  left: -15%;
}

.txt:after {
  right: -15%;
}

.sanjio {
  opacity: 0;
  margin: auto;
  border: 12px solid transparent;
  border-bottom: 12px solid #005cff;
  width: 0;
  height: 0;
  margin-top: 14px;
  margin-bottom: 1px;
}
.sanjio.active {
  opacity: 1;
}
.xuna-nav .nav-link.active {
  color: #282b31 !important;
}
.left-text2 {
  padding: 72px 0 40px 70px;
}
.left-text2,
.jianjie {
  color: #fff;
}
.text-content {
  font-weight: 400;
  margin: 30px 0 90px 0;
  font-size: 14px;
  width: 90%;
}
.jianjie {
  font-size: 14px;
  font-weight: 500;
}
.jianjie:hover {
  color: #fff;
}

.part7 {
  padding-bottom: 70px;
}

.huoban-container {
  display: flex;
  flex-wrap: wrap;
}
.huoban-item {
  width: 20%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #eef0f6;
  border-left: none;
  border-bottom: none;
}

.home-part8 {
  /* background-color: #006cff; */
  background-image: url(../image/1111.png);
  background-size: cover;
}
.left-text3 {
  color: #fff;
}
.left-text3 > div {
  font-size: 16px;
  font-weight: 400;
  margin: 32px 0 40px 0;
}
.zhu-ce {
  font-size: 15px;
  color: #006cff;
  font-weight: 500;
  display: block;
  width: 140px;
  background: #fff;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
}
.zhu-ce:hover {
  color: #006cff;
  background-color: #e5f4ff;
}

.right-content {
  width: 90%;
  margin: auto;
  padding: 16px 0 32px 0;
  position: relative;
}
.right-content > h5 {
  font-size: 18px;
}
.right-content > div {
  font-size: 14px;
  font-weight: 400;
  color: #606879;
}
.right-text,
.right-text2 {
  border-radius: 3px;
  background-color: #fff;
}
.right-text2 {
  margin-top: 10px;
}
.xiang-qing {
  color: #006cff !important;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  right: 0;
  width: 80px;
}

.right-content2 {
  padding: 24px 0;
  width: 90%;
  margin: auto;
  display: flex;
  justify-content: space-between;
}
.ios-cont {
  display: flex;
  align-items: center;
}
.ios-text {
  width: 50%;
}
.ios-text > span {
  color: #606879;
  font-size: 14px;
  font-weight: normal;
}
.ios {
  margin-right: 20px;
  width: 80px;
}
.xian2 {
  width: 20px;
  position: relative;
}
.xian2::before {
  content: "";
  width: 1px;
  height: 60%;
  position: absolute;
  top: 25%;
  background: #eef0f6;
}

/* 邮件 */

.email-part3,
.email-part6 {
  background-color: #fafbfc;
}

.part1-container4 {
  padding: 135px 0 85px 0;
}

.part1-container2 {
  padding: 120px 0 100px 0;
}
.part1-container3 {
  padding: 120px 0 72px 0;
}

.eamil-introduce {
  font-weight: normal;
  color: #606879;
  margin-top: 30px;
}

.free-use2 {
  display: inline-block;
  width: 160px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  border-radius: 4px;
  background-color: #006cff;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  margin-top: 40px;
}
.free-use3 {
  color: #fff !important;
  border-radius: 26px;
}
.free-use2:hover {
  color: #fff;
  background-color: #2989ff;
}
.eamil-item {
  width: 90%;
  color: #606879;
  margin-top: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.eamil-item div:hover {
  color: #006cff;
}
.eamil-item a {
  font-weight: normal;
  color: #606879;
}
.fen-ge {
  width: 1px;
  height: 16px;
  background: #dce0e9;
}
.fg-xian {
  width: 1px;
  height: 80%;
  background-color: #c0c5d0;
}

.bt-line {
  display: block;
  width: 2%;
  height: 3px;
  background: #006cff;
  margin: auto;
  margin-top: 20px;
}
.jie-shao {
  line-height: 26px;
  margin: auto;
  margin-top: 28px;
}

.ico-item {
  width: 57px;
  display: block;
  margin: auto;
}
.ico-title {
  margin-top: 20px;
  font-weight: 500;
}
.titleActive {
  color: #282b31 !important;
}
.little-txt-img2,
.little-txt-img3 {
  display: none;
}

.show-img {
  display: block;
}
.heng-xian {
  width: 100%;
  margin: 60px auto;
  height: 1px;
  background: #e3e7f0;
}
.mail-ul {
  list-style: none;
}
.mail-ul li,
.voice-ul3 li {
  color: #606879;
  font-weight: 500;
  margin-top: 30px;
}
.voice-ul3 li {
  font-size: 18px;
  color: #282b31;
}
.voice-ul li {
  margin-top: 30px;
  font-weight: 500;
}
.voice-ul4 li {
  color: #606879;
  margin-top: 30px;
}
.mail-ul li span:hover {
  cursor: pointer;
}
.li-ico {
  width: 9%;
  margin-right: 20px;
}
.zhuan-ye {
  font-weight: 500;
  font-size: 14px;
  color: #006cff;
}
.mail-title1 {
  font-size: 34px;
  margin-top: 20px;
}
.mail-text1 {
  font-size: 16px;
  color: #606879;
  font-weight: normal;
  margin-top: 30px;
}
.ju-xing {
  width: 57px;
}
.pr-8 {
  padding-right: 8% !important;
}
.pl-8 {
  padding-left: 8% !important;
}
.little-title2 {
  font-size: 20px;
  margin-top: 20px;
  font-weight: 500;
  color: #282b31;
}
.little-text2 {
  color: #606879;
  font-size: 16px;
  font-weight: normal;
  width: 100%;
  margin: auto;
  margin-top: 20px;
}
.margin-bot {
  padding: 0;
  margin-bottom: 70px;
}

.xuan-card {
  border: 1px solid #dce0e9;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0px 2px 12px 1px rgb(0 0 0 / 8%);
  display: flex;
  justify-content: space-between;
}
.left-nav {
  margin: 0;
  list-style: none;
  border-right: 1px solid #e3e7f2;
  background-color: #fafbfc;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  padding: 40px 0 100px 0;
}
.left-nav li {
  width: 120px;
  padding: 0 20px;
  line-height: 50px;
  font-weight: 500;
  color: #606879;
}
.nav-detail {
  width: 100%;
  padding: 70px 30px 0px 30px;
  font-size: 16px;
  font-weight: 400;
  overflow: scroll;
}
.duan-luo {
  text-indent: 32px;
}
.color1 {
  color: #800;
}
.color2 {
  color: #008;
}
.color3 {
  color: #606;
}
.liActive {
  color: #006cff !important;
  background-color: #ffffff;
  border-right: 2px solid #006cff;
}
.email-api {
  padding-bottom: 80px;
}
.yan-content,
.yan-content2,
.yan-content3 {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.xuan-card,
.right-content3 {
  width: 520px;
}
.right-content3 {
  background-color: #006cff;
  padding: 70px 50px 100px 50px;
  border-radius: 12px;
  color: #fff;
  position: relative;
}
.jie-shao3 {
  font-weight: 400;
  margin-top: 36px;
}
.yu-yan {
  width: 84%;
  margin-top: 36px;
}
.xia-zai {
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  margin-top: 40px;
  width: 160px;
  position: absolute;
  right: 0;
}
.xia-zai > a {
  color: #fff;
}
.xia-zai:hover a {
  color: #fff;
}

.table-item {
  align-items: center;
}
.table-item div {
  cursor: pointer;
  color: #606879;
  font-weight: 500;
  width: 20%;
  line-height: 90px;
}
.table-item div:not(:first-child) {
  text-align: center;
}
.table-item:not(:first-child):hover {
  background-color: #f5f7fa;
}
.table-title1 {
  font-size: 26px;
  color: #282b31 !important;
}
.table-title2,
.table-title3,
.table-title4 {
  color: #282b31 !important;
}
.table-title5 {
  color: #f89800 !important;
}
.table-title6 {
  color: #006cff !important;
}
.table-item:first-child {
  border-bottom: 1px solid #e2e7ed;
}

.xiang-fang {
  cursor: pointer;
  width: 120px;
  margin: auto;
  margin-top: 36px;
  margin-bottom: 70px;
  color: #606879;
  font-weight: normal;
  text-align: center;
}
.xiang-fang > a {
  color: #606879 !important;
}
.xiang-fang:hover span,
.xiang-fang:hover a {
  color: #006cff !important;
}
.liu-cheng {
  display: flex;
  justify-content: space-between;
  text-align: center;
}
.liu-cheng img {
  width: 48px;
}
.little-title4 {
  font-size: 18px;
  color: #282b31;
  font-weight: 500;
  margin-top: 36px;
}
.liu-item {
  width: 16%;
}
/*CSS伪类用法*/
.fg-xian2:after,
.fg-xian2:before {
  background: #eef0f6;
  content: "";
  height: 1px;
  position: absolute;
  top: 24%;
  width: 50%;
}

/*调整背景横线的左右距离*/
.fg-xian2:before {
  left: -42%;
}

.fg-xian2:after {
  right: -42%;
}
.email-part7,
.shorturl-part6 {
  text-align: center;
  background-image: url(../image/bg-zhu.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  padding: 28px 0;
}
.lij-zhuc {
  color: #006cff;
  display: inline-block;
  background-color: #fff;
  width: 130px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  border-radius: 2px;
  font-size: 14px;
}
.lij-zhuc:hover {
  color: #006cff;
  background-color: #e5f4ff;
}

.zhi-dian {
  font-weight: 500;
  margin-right: 10px;
  display: inline-block;
}
.pb-youjian {
  padding-bottom: 50px;
}
.mail-color1 {
  color: #f89800;
}
.mail-color2 {
  color: #006cff !important;
}
.mail-color3 {
  color: #0012ff;
}
.mail-color4 {
  font-weight: 500;
  color: #606879;
}
.mail-color5 {
  font-weight: 500;
  color: #282b31;
}

/* 短连接 */
.shorturl-item {
  margin-top: 60px;
  display: flex;
  justify-content: space-between;
  width: 50%;
}
.shorturl-item a {
  font-weight: 500;
  color: #606879;
  position: relative;
  display: inline-block;
}
.email-a:hover {
  color: #006cff;
  text-decoration: underline;
}
.duan-png {
  width: 50px;
  margin-right: 40px;
}
.duan-right {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.little-text3 {
  font-size: 13px;
  color: #606879;
  font-weight: 400;
  margin-top: 10px;
}
.little-title3 {
  color: #282b31;
  font-weight: 500;
}
.right-text3 {
  width: calc(100% - 120px);
}
.shorturl-part3 {
  padding-bottom: 40px;
}
.gou {
  width: 16px;
  margin: auto;
}
.shorturl-tbale1 td,
.shorturl-tbale1 th,
.shorturl-tbale2 td,
.shorturl-tbale2 th,
.sms-tbale1 td,
.sms-tbale1 th {
  padding: 1.25rem !important;
  border-top: none;
  height: 66px;
  text-align: center;
  vertical-align: inherit !important;
}
.shang-wu {
  cursor: pointer;
}
.shang-wu a {
  color: #282b31;
}
.shang-wu a:hover {
  color: #006cff;
}

.shorturl-tbale1 tr:not(:first-child):hover {
  background-color: #f5f7fa;
}
.shorturl-tbale1 th,
.shorturl-tbale1 td {
  border-top: none !important;
}
.first-tr {
  border-top: 1px solid #e2e7ed;
}
.first-tr-top {
  border-bottom: 1px solid #e2e7ed;
}
.sh-item {
  height: 50px;
  margin-top: 50px;
}
.shorturl-tbale1 td,
.shorturl-tbale2 td {
  color: #282b31;
  font-weight: 400;
  border-top: 0 !important;
}
.left-stlye {
  width: 25%;
  padding-left: 15px;
  font-weight: 500 !important;
  text-align: inherit !important;
}
.first-td {
  font-weight: normal !important;
}
.xiang-mu {
  font-size: 24px;
}

.shorturl-tbale2 tr:hover {
  background-color: #f5f7fa;
}
.shorturl-color1 {
  color: #f89800;
}
.shorturl-color2 {
  color: #006cff;
}
.shorturl-color3 {
  color: #0012ff;
}
.shorturl-color4 {
  color: #7200ff;
}

/* 国际短信 */
.inter-price {
  padding-bottom: 90px;
}
.html-top {
  margin-top: 140px;
}
.html-top > img {
  width: 88%;
}
.html-top:nth-child(2) {
  margin-top: 110px !important;
}
.sms-function {
  padding-bottom: 40px;
}
.sms-price {
  margin-top: 80px;
}
.duan-xin {
  display: flex;
}
.pei-tu {
  width: 50%;
  margin-right: 40px;
}
.little-text4 {
  color: #606879;
  font-weight: 500;
  margin-top: 40px;
}
.right-content4 {
  margin-top: 60px;
}
.little-item span {
  color: #006cff;
  font-weight: 400;
  font-size: 14px;
  background-color: #dff1ff;
  display: inline-block;
  width: 110px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-top: 30px;
  border-radius: 3px;
}
.anli-text {
  color: #606879;
  font-weight: 500;
  margin-top: 60px;
}
.anli-png {
  width: 30%;
  margin-top: 20px;
  margin-right: 10px;
}
.ico-title {
  display: inline-block;
  position: relative;
}
.duanxin-ico {
  cursor: pointer;
  margin-bottom: 20px;
}
.ico-title::before {
  content: "";
  position: absolute;
  top: 20%;
  left: 0; /*靠最左边开始*/
  border-bottom: 2px solid #006cff; /*设置底部边颜色*/
  width: 0; /*宽度为0，这里的宽度是相对与li的宽度*/
  height: 100%; /*设置高度使它和li等高*/
}
.ico-title.active::before {
  /*鼠标hover，状态发生改变*/
  width: 100%;
  transition: 0.2s all linear; /*设置过度时间*/
  transition-delay: 0.1s; /*过度延时*/
}
.guojia-duan {
  width: 94%;
  margin: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.price-color {
  color: #dce0e9;
}
.price-color2 {
  font-weight: 500 !important;
}
.duan-bg {
  margin-top: 10px;
  color: #fff;
  background-image: url(../image/bj.png);
  background-size: cover;
  border-radius: 30px;
  padding: 0 30px;
  width: 320px;
}
.duan-bg2 {
  margin-top: 10px;
  width: 320px;
  background-image: url(../image/bj2.png);
  background-size: cover;
  border-radius: 30px;
  padding: 0 30px 30px;
  border: 1px solid #e3e7f2;
}
.di-qu {
  font-weight: 500;
  margin-bottom: 10px;
}
.di-qu2 {
  color: #006cff;
}
.duan-cont {
  margin: 40px 0;
}
.goujia-ul {
  list-style: none;
  margin-top: 30px;
  padding: 0;
}
.goujia-ul li {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.guojai-png {
  width: 24px;
}
.guojia-info {
  font-size: 16px;
  font-weight: 400;
}
.txt-color3 {
  color: #a1a8b7;
}
.search {
  width: 75%;
  margin: 40px auto;
}
.search-cont {
  margin-top: 60px;
}
.zhi-chi {
  margin-top: 60px;
  font-size: 22px;
}
.search-input[type="text"]::-webkit-input-placeholder {
  color: #c0c5d0;
}
.search-input {
  width: 80%;
  height: 60px;
  outline: none;
  padding-left: 20px;
  border: 1px solid #dce0e9;
  border-right: none;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.cha-xun {
  display: inline-block;
  background-color: #006cff;
  color: #fff;
  width: 20%;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border-top-right-radius: 7px;
  border-bottom-right-radius: 7px;
  font-weight: 400;
  font-size: 18px;
}
.cha-xun:hover {
  color: #ffff;
  background-color: #2989ff;
}
.intersms-con2 > .container {
  padding-bottom: 30px;
}
.country {
  font-size: 26px;
}
.txt-cu {
  font-weight: 500;
}
.search-li1 {
  line-height: 50px !important;
  border-bottom: 1px solid #eceff4;
}
.search-li {
  line-height: 60px;
  font-weight: 400;
  display: flex;
  justify-content: space-between;
}
.search-li div {
  width: 20%;
}
.search-li div:not(:first-child) {
  text-align: center;
}
.yan-content2 {
  padding-bottom: 100px;
}
.intersms-xian > .container,
.smart-con > .container {
  position: relative;
}
.intersms-xian > .container::after,
.smart-con > .container::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 5%;
  width: 90%;
  margin: auto;
  border-bottom: 1px solid #eceff4;
  text-align: center;
}

/* 语音 */
.voice-item {
  margin-top: 60px;
  display: flex;
  justify-content: space-between;
  width: 65%;
}

.right-voice {
  text-align: center;
  position: relative;
}
.yuyin {
  background-color: #fafbfc;
  padding: 80px 0;
  margin-top: 36px;
}
.yuyin2 {
  margin-top: 30px;
  background-color: #fafbfc;
  padding: 60px 0;
}
.voice-ul {
  margin-top: 50px;
}
.voice-ul li {
  font-size: 18px;
  color: #282b31;
}
.txt-img2 {
  width: 90%;
  border-radius: 23px;
}
.txt-img-s,
.txt-img-s2 {
  position: absolute;
  top: 50%;
  left: 57%;
  transform: translate(-50%, -50%);
}
.txt-img-s {
  width: 100%;
}
.voice-part3 {
  background-image: url(../image/bg-part3.png);
  background-size: cover;
  padding-bottom: 60px;
}
.voice-ul2 {
  background-color: #fff;
  height: 60px;
  line-height: 60px;
  font-size: 21px;
  margin: 0;
  border-bottom: 1px solid #e3e7f0;
}
.ul2-div {
  color: #282b31;
  font-weight: 500;
  width: 80%;
  margin: auto;
  display: flex;
  justify-content: space-around;
}
.ul2-div li {
  position: relative;
}
.ul2-div li.active {
  color: #006cff;
}

.ul2-div li::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0; /*靠最左边开始*/
  border-bottom: 2px solid #006cff; /*设置底部边颜色*/
  width: 0; /*宽度为0，这里的宽度是相对与li的宽度*/
  height: 100%; /*设置高度使它和li等高*/
}
.ul2-div li.active::before {
  /*鼠标hover，状态发生改变*/
  width: 100%;
  transition: 0.2s all linear; /*设置过度时间*/
  transition-delay: 0.1s; /*过度延时*/
}
.ul2-content {
  background-color: #f2f6fa;
  text-align: center;
}

.phone-input,
.code-input {
  width: 48%;
  margin: auto;
}
.phone-input input {
  width: 100%;
  height: 50px;
  padding-left: 20px;
  outline: none;
  border: 1px solid #e3e7f0;
  border-radius: 3px;
}

.phone-input input::-webkit-input-placeholder,
.code-input input::-webkit-input-placeholder,
.search-input::-webkit-input-placeholder {
  color: #a1a8b7;
}
.phone-input input::-moz-placeholder,
.code-input input::-moz-placeholder,
.search-input::-webkit-input-placeholder {
  color: #a1a8b7;
}
.phone-input input:-moz-placeholder,
.code-input input:-moz-placeholder,
.search-input::-webkit-input-placeholder {
  color: #a1a8b7;
}
.phone-input input:-ms-input-placeholder,
.code-input input:-ms-input-placeholder,
.search-input::-webkit-input-placeholder {
  color: #a1a8b7;
}

.code-input {
  margin-top: 40px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.code-input input {
  width: 74%;
  height: 50px;
  padding-left: 20px;
  outline: none;
  border: 1px solid #e3e7f0;
  border-radius: 3px;
}
.reloadverifycode img {
  border-radius: 2px;
}
.shi-ting {
  width: 16%;
  height: 50px;
  margin-top: 40px;
  font-weight: 400;
  color: #fff;
  border: none;
  outline: none !important;
  border-radius: 3px;
  background-color: #006cff;
}
.shi-ting:hover {
  background-color: #2989ff;
}
.yan-content3 {
  padding: 70px 0;
}
/* .voice-item-content5.active{
    padding: 32px;
    margin: 10px auto;
    display: flex;
    align-items: center;
    border-radius: 4px;
    transition: all .3s;
    position: relative;
    
} */
/* .voice-item-content5:hover{
    box-shadow: 8px 8px 20px 0 rgb(55 99 170 / 30%), -8px -8px 20px 0 #fff;
} */
.voice-item-content,
.voice-item-content2 {
  padding: 32px;
  margin: 10px auto;
  display: flex;
  align-items: center;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
  border: 2px solid #fff;
}
.voice-item-content {
  position: relative;
}
.voice-item-content::before {
  position: absolute;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #dce0e9;
}
.voice-item-content2 {
  background-color: #fafbfc;
  border: none;
}
/* .voice-item-content::after{
    position: absolute;
    height: 0%;
    content: "";
    top: 0;
    bottom: 0;
    left: 0;
    border-left: 4px solid #006cff;
    
} */
.voice-item-content.active {
  border: 2px solid #fff;
  box-shadow: 0px 0px 15px rgb(0 0 0 / 10%);
  background-image: linear-gradient(0deg, #fff, #f3f5f8);
}

/* .voice-item-content.active::after{
    height: 100%;
} */

.collapse-content::before,
.collapse-content2::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  border: 1px solid #eceff4;
}
.voice-item-content.active::before {
  border: 0px;
}

.voice-item-content:hover .factor-img img {
}

/* 短信 */
.sms-item {
  margin-top: 10px;
}
.sms-item-content {
  width: 100%;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-radius: 5px;
  border: 1px solid #e3e7f0;
  box-shadow: 0px 0px 7px 1px rgb(0 0 0 / 5%);
}
.little-title5 {
  font-weight: 500;
  color: #282b31;
  font-size: 20px;
}
.little-title8 {
  font-weight: 500;
  color: #282b31;
  font-size: 16px;
}
.little-text5 {
  font-size: 16px;
  font-weight: normal;
  color: #606879;
}
.little-text6 {
  font-size: 15px;
}
.send-content {
  display: flex;
  padding: 20px;
  margin-top: 30px;
  transition: all 0.3s;
}
.send-content.active {
  background: #fff;
}
.send-content.active .go-see {
  opacity: 1;
}
.go-see:hover {
  color: #006cff;
}

.send-img {
  width: 30px;
  margin-right: 10px;
}
.go-see {
  opacity: 0;
  display: block;
  color: #006cff;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.3s;
}
.ul2-content2 {
  text-align: center;
}
.sms-col {
  padding: 14px 24px 100px 24px;
}
.sms-top-title {
  padding: 28px 0;
  background-color: #f2f5f8;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}
.sms-top-title2 {
  padding: 28px 0;
  color: #fff;
  position: relative;
  background-image: url(../image/md.png);
  background-repeat: no-repeat;
  background-size: cover;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
}
.sms-top-title2 span {
  color: #fff;
}
.sms-top-txt {
  color: #606879;
}
.sms-rice {
  padding: 20px;
  border-radius: 15px;
  background-color: #fff;
  border: 1px solid #dce0e9;
  background-image: url(../image/bj2.png);
  background-size: 70% 100%;
  background-position: right top;
  background-repeat: no-repeat;
}

.sms-rice2 {
  height: 600px;
  overflow: hidden;
  border-radius: 15px;
  background-color: #fff;
  border: 1px solid #dce0e9;
}
.sms-rice h3,
.sms-rice2 h3 {
  margin-bottom: 16px;
}
.sms-tbale1 tr:last-child td {
  border-bottom: 0 !important;
}
.sms-tbale1 td,
.sms-tbale1 th {
  border-bottom: 1px dashed #e3e7f2 !important;
}

.affairs {
  margin: 20px 0;
}
.affairs span {
  display: inline-block;
}
.affairs > a {
  color: #fff;
}

.go-buy,
.go-buy-sms {
  display: inline-block;
  cursor: pointer;
  font-weight: 500;
  width: 130px;
  height: 40px;
  line-height: 40px;
  margin-right: 10px;
  text-align: center;
  border-radius: 4px;
}
.go-buy-sms {
  color: #006cff !important;
  border: 1px solid #006cff;
}
.go-buy {
  color: #fff !important;
  background-color: #006cff;
}
.go-buy > a {
  color: #fff !important;
}
.go-buy:hover {
  color: #fff;
  background-color: #2989ff;
}
.more-style {
  cursor: pointer;
  color: #606879 !important;
  font-weight: 400;
}
.more-style:hover {
  color: #006cff !important;
}
.qie-api {
  font-weight: 400;
  color: #006cff;
}
.send-sms {
  cursor: pointer;
}
.mt-tabpage {
  width: 100%;
}
.mt-tabpage-title {
  display: flex;
  justify-content: space-around;
}
.mt-tabpage-title .mt-tabpage-item {
  display: flex;
  align-items: center;
  width: 480px;
  padding: 32px;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s;
}
.tabpage-border::after {
  position: absolute;
  height: 0;
  content: "";
  top: 0;
  bottom: 0;
  left: 0;
  border-left: 4px solid #006cff;
}

.tabpage-border::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px solid #dce0e9;
}
.tabpage-border.mt-tabpage-item-cur::before {
  border: 0px;
}
.tabpage-border.mt-tabpage-item-cur::after {
  height: 100%;
}
.mt-tabpage-title .mt-tabpage-item-cur {
  box-shadow: 0px 0px 15px rgb(0 0 0 / 10%);
}

.mt-tabpage-count {
  position: relative;
  width: 800px;
  height: 200px;
  overflow: hidden;
}
.mt-tabpage-cont__wrap {
  position: absolute;
}
.mt-tabpage-count .mt-tabpage-item {
  width: 800px;
  height: 200px;
  line-height: 200px;
  text-align: center;
}

/*  */

.console-background {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  overflow: hidden;
  pointer-events: none;
  display: flex;
  z-index: -1;
}
.console-background [class^="submail-ele-"] {
  position: absolute;
  width: 200px;
  height: 200px;
  display: block;
  border-radius: 50%;
}
.console-background .submail-ele-red {
  left: 65%;
  top: 50%;
  z-index: 4;
  -webkit-animation: submail-ele-red 10s infinite linear;
  animation: submail-ele-red 10s infinite linear;
}
.console-background .submail-ele-red::after {
  content: " ";
  position: absolute;
  width: 200px;
  height: 200px;
  display: block;
  border-radius: 50%;
  background-color: #ff2449;
  opacity: 0.45;
  filter: blur(10px);
  -webkit-animation: submail-ele-red-container 5.5s infinite linear;
  animation: submail-ele-red-container 5.5s infinite linear;
}
.console-background .submail-ele-blue {
  left: 20%;
  top: 45%;
  z-index: 2;
}
.console-background .submail-ele-blue::after {
  content: " ";
  position: absolute;
  width: 200px;
  height: 200px;
  display: block;
  border-radius: 50%;
  background-color: #006bfc;
  opacity: 0.25;
  filter: blur(10px);
  -webkit-animation: submail-ele-blue-container 8s infinite linear;
  animation: submail-ele-blue-container 8s infinite linear;
}
.console-background .submail-ele-green {
  left: 30%;
  top: 15%;
  z-index: 1;
  -webkit-animation: submail-ele-green 12s infinite linear;
  animation: submail-ele-green 12s infinite linear;
}
.console-background .submail-ele-green::after {
  content: " ";
  position: absolute;
  width: 200px;
  height: 200px;
  display: block;
  border-radius: 50%;
  background-color: #43ffbb;
  opacity: 0.45;
  transform: scale(2);
  filter: blur(10px);
}
.console-background .submail-ele-pink {
  left: 60%;
  top: 25%;
  background-color: #ed999a;
  transform: scale(1.6);
  opacity: 0.3;
  filter: blur(10px);
  z-index: 3;
}

@keyframes submail-ele-red-container {
  0% {
    transform: scale(2.8);
    -webkit-transform: scale(2.8);
  }
  50% {
    transform: scale(3);
    -webkit-transform: scale(3);
  }
  100% {
    transform: scale(2.8);
    -webkit-transform: scale(2.8);
  }
}
@keyframes submail-ele-red {
  0% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
  50% {
    transform: translateX(10px);
    -webkit-transform: translateX(10px);
  }
  100% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
}
@-webkit-keyframes submail-ele-red-container {
  0% {
    transform: scale(2.8);
    -webkit-transform: scale(2.8);
  }
  50% {
    transform: scale(3);
    -webkit-transform: scale(3);
  }
  100% {
    transform: scale(2.8);
    -webkit-transform: scale(2.8);
  }
}
@-webkit-keyframes submail-ele-red {
  0% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
  50% {
    transform: translateX(10px);
    -webkit-transform: translateX(10px);
  }
  100% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
}
@keyframes submail-ele-blue-container {
  0% {
    transform: scale(2.4);
    -webkit-transform: scale(2.4);
  }
  50% {
    transform: scale(2.6);
    -webkit-transform: scale(2.6);
  }
  100% {
    transform: scale(2.4);
    -webkit-transform: scale(2.4);
  }
}
@-webkit-keyframes submail-ele-blue-container {
  0% {
    transform: scale(2.4);
    -webkit-transform: scale(2.4);
  }
  50% {
    transform: scale(2.6);
    -webkit-transform: scale(2.6);
  }
  100% {
    transform: scale(2.4);
    -webkit-transform: scale(2.4);
  }
}
@keyframes submail-ele-green {
  0% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
  50% {
    transform: translateX(30px);
    -webkit-transform: translateX(30px);
  }
  100% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
}
@-webkit-keyframes submail-ele-green {
  0% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
  50% {
    transform: translateX(30px);
    -webkit-transform: translateX(30px);
  }
  100% {
    transform: translateX(0px);
    -webkit-transform: translateX(0px);
  }
}

/* 一键登录 */
.chuan-png {
  width: 53%;
  padding: 50px 0 0 33px;
}
.ol-li {
  margin-top: 80px;
  width: 40%;
}
.one-you {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.ol-li li {
  color: #282b31;
  font-weight: 400;
  line-height: 24px;
}
.time-content {
  width: 130px;
  color: #fff;
  font-weight: 400;
  text-align: center;
  background-color: #f02c5a;
  padding: 6px 0;
  border-radius: 4px;
  margin: 20px 0;
}
.time-png {
  width: 28px;
  margin-right: 5px;
}
.one-you2 {
  color: #fff;
  /* background:#3366ff */
}
.one-you2 .ol-li li {
  color: #fff;
}
.time-content2 {
  background-color: #006cff;
}
.fg-xian3::after,
.fg-xian4::after {
  background: #e2e7ed;
  content: "";
  height: 1px;
  position: absolute;
  top: 30%;
  width: 60%;
}
.fg-xian3::before {
  background: #e2e7ed;
  content: "";
  height: 1px;
  position: absolute;
  top: 30%;
  width: 60%;
}

.fg-xian3:before {
  left: -50%;
}
.fg-xian3:after,
.fg-xian4:after {
  right: -50%;
}

.circle li > i {
  width: 10px;
  height: 10px;
  display: inline-block;
  background: #dce0e9;
  border-radius: 50%;
}
.circle2 li > i {
  width: 10px;
  height: 10px;
  display: inline-block;
  background: #006cff;
  border-radius: 50%;
}
.line-m {
  width: 1px;
  height: 30px;
  background: #dce0e9;
  position: relative;
  left: 5px;
}
.line-m2 {
  width: 1px;
  height: 30px;
  background: #006cff;
  position: relative;
  left: 5px;
}

.m-h6 {
  margin: 20px 0 10px 0;
}
.m-h62 {
  margin: 40px 0 10px 0;
}

/* 彩信 */
.yu-ico {
  width: 50px;
}
.jieshao {
  font-weight: normal;
  color: #606879;
}
.jieshao2 {
  margin-top: 16px;
  font-weight: normal;
  color: #606879;
}
.jin-api {
  font-size: 14px;
  font-weight: normal;
  color: #282b31;
  display: block;
  margin-top: 52px;
}
.mms-price-cont {
  display: flex;
  flex-wrap: wrap;
  padding: 30px 20px;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e9ecf3;
  border-radius: 2px;
}
.mms-rice {
  width: 70%;
}
.mms-rice span {
  display: block;
}
.dan-jia {
  font-size: 18px;
  font-weight: 500;
  color: #006cff;
}
.span-text2 {
  color: #606879;
  font-weight: 400;
  font-size: 14px;
  margin: 10px 0;
}

/* 智慧短信 */
.p-r {
  padding-left: 54px !important;
  padding-right: 30px !important;
}
.p-l {
  padding-right: 54px !important;
  padding-left: 30px !important;
}
.brand-item {
  width: 20%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

/* 身份认证 */
.sms-img {
  width: 32px;
  margin-right: 10px;
}
.factor-item {
  width: 100%;
  display: inherit;
  text-align: center;
  padding: 0 20px;
  cursor: pointer;
  height: 115px;
  overflow: hidden;
}
.factor-img img {
  width: 30px;
  margin-bottom: 5px;
}

.factor-img {
  height: 115px;
  margin: auto;
  position: relative;
  overflow: hidden;
}
.fac1,
.fac2 {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
}
.fac1 {
  transition: all 0.2s;
  opacity: 1;
  transform: translate(-50%, -50%);
}
.fac2 {
  transition: all 0.4s;
  opacity: 0;
  transform: translate(-50%, 100%);
}
.factor-item:hover .fac1 {
  opacity: 0;
}
.factor-item:hover .fac2 {
  opacity: 1;
  transform: translate(-50%, -50%);
}
.factor-item:hover .fac2 .little-title8 {
  color: #006cff;
}
.dec-fac {
  font-weight: normal;
  color: #606879;
  font-size: 13px;
}

/* 价格列表 */
.price {
  font-size: 16px;
  font-weight: 500;
  color: #a1a8b7;
}
.price-an {
  color: #fff;
  margin-top: 20px;
}

.price-item {
  background: #fc8572;
}
.jie-shao2 {
  color: #fff;
  margin: 20px 0;
  font-weight: normal;
}
.jie-shao4 {
  color: #fff;
  margin-top: 20px;
  font-weight: normal;
}
.service-item {
  display: block;
  cursor: pointer;
  color: #fff;
  font-weight: normal;
  text-align: center;
  position: relative;
  height: 42px;
  line-height: 42px;
  font-size: 16px;
}
.service-item:hover {
  color: #fff;
}
.service-item.active {
  color: #ff4e4e;
}
/*.service-item::after{
    position: absolute;
    content: '';
    top: 15%;
    left: 0;
    width: 2px;
    height: 15px;
    background-color: #eceff4;
}
.service-item:first-child::after{
    width: 0px;
}*/

.shi-wu {
  margin-top: 80px;
  font-weight: bold;
}
.price-table,
.price-table2 {
  color: #282b31;
  font-weight: 400;
  margin-top: 40px;
}

.top-border {
  border-top: 3px solid #ff4e4e;
}
.go-buy2,
.go-buy3,
.new_buy {
  letter-spacing: 1px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  border-radius: 25px;
  color: #fff !important;
  background-image: linear-gradient(to right, #ff7474, #ff4e4e);
}
.new_buy2 {
  letter-spacing: 1px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  border-radius: 25px;
  color: #ff4e4e !important;
  border: 1px solid #ff4e4e;
}
.new_con {
  display: flex;
  margin: auto;
  margin-top: 40px;
}
.new_buy,
.new_buy2 {
  display: block;
  width: 120px;
  height: 40px;
  line-height: 38px;
}

.go-buy2 {
  width: 160px;
  height: 44px;
  line-height: 42px;
  margin: auto;
  margin-top: 40px;
  margin-bottom: 70px;
}

.go-buy3 {
  display: block;
  width: 120px;
  height: 40px;
  line-height: 40px;
  margin: auto;
}

.go-buy2:hover,
.go-buy3:hover {
  color: #fff !important;
  background-image: linear-gradient(to right, #f89c9c, #fd6b6a);
}

.all-list {
  color: #006cff;
  display: block;
  font-size: 14px;
  font-weight: normal;
}
.all-list:hover {
  color: #006cff;
}
.iconjiantou1 {
  font-size: 12px !important;
}
.jiantou3 {
  position: relative;
  transition: all 0.3s;
  left: 5px;
}
.all-list:hover .jiantou3,
.title_h:hover .jiantou3,
.go-buy2:hover .jiantou3,
.go-buy3:hover .jiantou3,
.go-buy3_act:hover .jiantou3,
.new_buy:hover .jiantou3,
.new_buy2:hover .jiantou3,
.jianjie:hover .jiantou3,
.xiang-qing:hover .jiantou3,
.more-style:hover .jiantou3,
.qie-api:hover .jiantou3,
.xia-zai:hover .jiantou3,
.xiang-fang:hover .jiantou3,
.go-see:hover .jiantou3,
.more-fang:hover .jiantou3,
.details:hover .jiantou3 {
  left: 10px;
}

.price-table td,
.price-table2 td {
  height: 70px;
  vertical-align: middle !important;
}
.price-table th,
.price-table2 th {
  height: 54px;
  vertical-align: middle !important;
}
.lian-xi {
  width: 100%;
  height: 80px;
  line-height: 80px;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 80px;
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #eceff4;
  justify-content: space-around;
  background-image: linear-gradient(to right, #e7efff 5%, #fff 60%);
}
.lian-xi > div {
  color: #282b31;
  font-weight: 500;
}
.on-line {
  cursor: pointer;
  color: #fff !important;
  height: 35px;
  width: 120px;
  line-height: 35px;
  display: block;
  background: #006cee;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  border-radius: 20px;
}
.nei-rong {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.nei-con {
  width: 340px;
  padding: 30px 25px;
  margin: 40px 0 60px 0;
  border-radius: 4px;
  box-shadow: 0px 2px 10px 1px rgb(0 0 0 / 8%);
}
.new-user {
  margin-top: 20px;
  color: #606879;
  font-weight: normal;
  font-size: 15px;
}
.details2 {
  margin-top: 20px;
  font-weight: normal;
  text-align: center;
  color: #fff;
  width: 120px;
  height: 34px;
  line-height: 34px;
  border-radius: 2px;
  background-color: #006cff;
  display: block;
}

/*新用户首购  */
.new-img {
  width: 26%;
  margin: auto;
}
.text-line {
  color: #ccc;
  text-decoration: line-through !important;
}
.pro-text {
  font-size: 15px;
  color: #606877;
  font-weight: normal;
}
.new-user-bg {
  padding: 26px;
  background-image: url(../image/jb.png);
  background-repeat: no-repeat;
  background-position: right bottom;
  border: 1px solid #eef0f6;
  background-size: 20% 53%;
}
.back-price {
  display: block;
  color: #fff;
  background-color: #006cff;
  width: 20%;
  height: 40px;
  margin: auto;
  line-height: 40px;
  border-radius: 4px;
  text-align: center;
  font-weight: normal;
  margin-top: 60px;
  margin-bottom: 80px;
}
/* 5g */
.fiveg-part > .container {
  padding-bottom: 60px;
  border-bottom: 1px solid #eceff4;
}
.fiveg-img {
  width: 50px;
  margin-bottom: 26px;
}
.fg-con {
  margin: 40px 0 70px 0;
}
.little-txt7,
.little-title7,
.litile-title8 {
  font-weight: 500;
  color: #282b31;
}
.little-title7 {
  font-size: 20px;
}

.fg-item > .little-txt8 {
  font-size: 15px !important;
  color: #606879;
}
.fg-item {
  width: 96%;
  position: relative;
  padding: 28px 0 28px 20px;
  transition: all 0.4s;
  border: 2px solid #fff;
}
.fg-item.active {
  box-shadow: 0px 0px 15px rgb(0 0 0 / 10%);
  transition: all 0.3s ease-in 0s;
  background-image: linear-gradient(0deg, #fff, #f3f5f8);
}
.fg-item:hover .little-title7 {
  color: #006cff;
}

.progress-con {
  width: 5px;
  height: 100px;
  /* background-color: #ccc; */
  border-radius: 5px;
  overflow: hidden;
  position: absolute;
  right: 6px;
  top: 15px;
}

.pro-first1,
.pro-first2,
.pro-first3 {
  width: 5px;
  background-color: #ddd;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.view-use div.active {
  color: #066cff;
}
.view-use div::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 0%;
  border-bottom: 2px solid #006cff;
}

.view-use div.active:after {
  width: 100%;
  transition: 0.2s all linear; /*设置过度时间*/
  transition-delay: 0.1s; /*过度延时*/
}
.fg-view {
  margin-top: 50px;
}

.huang-guan {
  width: 20px;
  margin-right: 6px;
}
.view-txt {
  color: #606879;
  font-weight: normal;
  font-size: 14px;
  margin-top: 40px;
}

.hang-ye {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  width: 56%;
  margin: 24px 0;
}

.more-fang {
  color: #606879;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
  margin-top: 50px;
}
.more-fang:hover {
  color: #066cff;
}
.litile-title8 {
  font-size: 20px;
}
.view-top-part {
  margin-top: 80px;
}

/* 解决方案 */
.nav-solution,
.nav-solution2 {
  display: flex;
  justify-content: space-around;
  width: 50%;
  margin: auto;
  font-weight: 500;
  cursor: pointer;
}
.nav-solution2 {
  width: 80%;
}
.nav-solution a {
  color: #606879;
}
.nav-solution a:hover {
  color: #606879;
}
.nav-solution a,
.nav-solution2 div {
  position: relative;
}
.nav-solution a.active,
.nav-solution2 div.active {
  color: #006cff;
}
.nav-solution2 div {
  color: #606879;
  font-size: 18px;
}
.nav-solution a:after,
.nav-solution2 div:after {
  position: absolute;
  content: "";
  bottom: -1px;
  left: 0;
  border-bottom: 3px solid #006cff;
  width: 0%;
}

.nav-solution a.active:after,
.nav-solution2 div.active:after {
  width: 100%;
  transition: 0.2s all linear; /*设置过度时间*/
  transition-delay: 0.1s; /*过度延时*/
}
.sol-part3 {
  padding-bottom: 60px;
}
.soultion-item {
  padding-bottom: 80px;
}
.sol-img2 {
  width: 90%;
}
.sol-little-title2 {
  width: 34%;
  margin: 40px auto;
  display: flex;
  justify-content: space-around;
}
.sol-little-title2 div {
  cursor: pointer;
  color: #282b31;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 140px;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  background-color: #fafbfc;
}
.sol-little-title2 div.active {
  color: #fff;
  background-color: #006cff;
}
.sol-box {
  padding: 48px 24px;
  margin-bottom: 30px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dce0e9;
}
.sol-svg {
  width: 40px;
  margin-bottom: 22px;
}
.sol-svg2 {
  width: 24px;
}
.sol-dec {
  margin-top: 18px;
  font-size: 14px;
  font-weight: normal;
  color: #606879;
  line-height: 26px;
}
.re-con {
  display: flex;
  flex-wrap: wrap;
  margin-top: 18px;
}
.re-pro {
  width: 45%;
  display: flex;
  color: initial !important;
  padding: 14px 18px;
  margin: 0 16px 16px 0;
  border-radius: 2px;
  align-items: center;
  border: 1px solid #dce0e9;
}
.re-pro:hover {
  box-shadow: 0px 0px 15px rgb(0 0 0 / 10%);
}
.re-pro:hover .pro-title {
  color: #006cff;
}
.pro-title {
  margin-bottom: 0;
}
.sil-right {
  margin-top: 60px;
}
.little-tip {
  display: flex;
  justify-content: space-between;
  width: 70%;
  margin-top: 30px;
}
.little-tip span {
  color: #006cff;
  display: block;
  width: 80px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 2px;
  font-size: 14px;
  font-weight: bold;
  background-color: #dfecff;
}
.soultion-item2 {
  margin: 40px 0 60px 0;
}
.main-title {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #282b31 !important;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.subtitle {
  padding: 4px 8px;
  margin-left: 8px;
  font-weight: bold !important;
  font-size: 14px !important;
  background-color: #f2f6fa;
}
.bottom-sol-item {
  background-color: #fff;
  border-radius: 4px;
  padding: 30px 20px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.bottom-sol-item div {
  color: #606879;
  font-size: 15px;
  font-weight: normal;
}
.geli {
  width: 14%;
}
.solution-bottom {
  padding-bottom: 60px;
}

/* 隐私安全 */

.sec-title {
  font-size: 64px;
  letter-spacing: 3px;
}
.sec-dec {
  font-size: 18px;
  margin: 32px;
}
.down-pdf {
  color: #fff !important;
  width: 210px;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  display: inline-block;
  background-color: #006cff;
}
.down-pdf:hover {
  color: #fff !important;
  background-color: #2989ff;
}
.down-svg {
  width: 20px;
}
.change-lan {
  color: initial;
  padding: 5px 10px;
  border-radius: 2px;
  display: inline-block;
}
.change-lan:hover {
  color: initial;
  background-color: #ececec;
}
.yin-item {
  margin-top: 46px;
}
.ren-zheng {
  width: 74px;
}
.iso-txt {
  color: #006cff;
  font-size: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin: 30px 0 16px 0;
}
.iso-dec {
  color: #606879;
  font-weight: normal;
}
.iso-top {
  margin-top: 40px;
}
.iso-txt2 {
  color: #ff407a;
}
.iso-item {
  margin-top: 34px;
}
.iso-dec2 {
  font-size: 15px;
  color: #606879;
  font-weight: normal;
}
.iso-txt3 {
  color: #006cff;
}
.iso-xing {
  width: 50px;
}

/* 营销锦囊 */
.go-back:hover {
  color: #006cff;
}
.sug-input {
  width: 100%;
  outline: none;
  border: none;
  background: none;
}
.sug-input::-webkit-input-placeholder {
  color: #a1a8b7;
  font-size: 16px;
}
.search-sug {
  width: 480px;
  height: 50px;
  display: flex;
  align-items: center;
  line-height: 50px;
  margin: auto;
  margin-top: 40px;
  text-align: left;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0px 0px 12px rgb(0 0 0 / 6%);
}
.seach {
  width: 24px;
  margin: auto 10px;
}
.article-top {
  display: flex;
  justify-content: space-between;
  padding: 30px 0 10px 0;
  margin-bottom: 30px;
  border-bottom: 1px solid #eef0f6;
}
.article-item {
  display: flex;
  align-items: center;
  padding: 20px;
}
.article-png {
  width: 20%;
}
.article-png img {
  border-radius: 4px;
}
.article-con {
  width: 70%;
  margin-left: 20px;
}
.art-title1 {
  font-size: 18px;
  font-weight: 500;
}
.tip {
  color: #006cff;
  font-size: 12px;
  font-weight: 500;
  background: #deeaff;
  width: 66px;
  display: inline-block;
  height: 22px;
  line-height: 22px;
  text-align: center;
  border-radius: 2px;
}
.art-desc {
  color: #606877;
  font-size: 14px;
  margin: 20px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.art-bot {
  color: #606877;
  font-size: 12px;
  display: flex;
}
.art-info {
  margin-left: 40px;
}
.article-item:hover {
  background-color: #fafbfc;
}
.article-item:hover .art-title1 {
  color: #006cff;
}
.article-item2:hover .art-title1 {
  color: #006cff;
}
.suggestions-part > .container {
  margin-bottom: 30px;
}
.sug-con {
  width: 70%;
  margin: auto;
}
.part1-sug {
  padding: 80px 0 30px 0;
}
.bread-crumb {
  font-size: 14px;
  padding: 20px 0;
  border-bottom: 1px solid #eef0f6;
}
.bread-crumb a {
  color: #606879;
}
.bread-crumb a:hover {
  color: #006cff !important;
}

.art-info2 {
  color: #606877;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 20px 0 50px 0;
}
.art-info3 {
  color: #606877;
  font-size: 14px;
  display: inherit;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 6px 0 30px 0;
}
.art-part {
  padding-bottom: 60px;
  border-bottom: 1px solid #eef0f6;
}
.art-part p {
  font-size: 15px;
  line-height: 28px;
}
.article-content {
  margin-top: 20px;
}

.view-img {
  margin: 40px 0;
}
.h5-title1 {
  margin: 10px 0 50px 0;
}
.h5-title2 {
  margin-top: 40px;
}
.h5-title3 {
  margin: 50px 0;
}
.h6-title1 {
  margin-bottom: 30px;
}

.zan2 {
  width: 20px;
}
.give-up {
  color: #fff;
  width: 180px;
  height: 54px;
  margin: auto;
  margin-top: 60px;
  background: #006cff;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.give-up:hover {
  opacity: 0.8;
  color: #fff;
}

.art-time {
  color: #606877;
  font-size: 14px;
  display: flex;
  margin-top: 30px;
}

.text-area {
  padding: 16px 16px 23px;
  height: 100%;
  width: 100%;
  resize: none;
  border: none;
  color: #1f1f1f;
  outline: none;
  border-radius: 4px;
}
.submit-sug,
.submit-sug-log {
  color: #fff;
  border: none;
  outline: none;
  background: #006cff;
  border-radius: 3px;
  width: 120px;
  height: 40px;
  line-height: 40px;
}

.submit-sug:hover,
.submit-sug-log:hover {
  cursor: pointer;
  opacity: 0.8;
}

.article-title {
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.article-title a {
  color: #282b31;
}
.article-title:hover {
  overflow: visible;
}
.article-title a:hover {
  color: #006cff;
}
.article—con {
  margin: 30px 0 40px 0;
}

.hot-ico {
  width: 18px;
}

.shor-table-tr :hover {
  background: #fff !important;
}

/* 媒体查询 */
@media screen and (max-width: 500px) {
  .konw-sub {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
@media screen and (max-width: 576px) {
  .price-item {
    display: none;
  }
  .mms-price-cont {
    margin-bottom: 10px !important;
  }
  .mms-rice {
    width: 100%;
  }
  .new_ke {
    display: none;
  }
  .new_ke2 {
    display: block;
  }
  #chat_container {
    width: 350px !important;
    right: 15px !important;
  }

  .bot_show {
    display: block;
  }
  .inter-price {
    padding-bottom: 10px;
  }
  .pc_img {
    display: none;
  }

  .youqin a {
    font-size: 14px;
  }
  .lianjie1 {
    padding-left: 2px !important;
  }
  .lianjie2 {
    padding-left: 2px !important;
  }
  .you-mb {
    margin-top: 0;
  }
  .you-mt {
    margin-bottom: 10px;
  }
  .art-part {
    padding-bottom: 20px;
  }

  .search-sug {
    width: 90%;
  }
  .sec-title {
    font-size: 32px;
  }
  .yuyin2 {
    padding: 0;
  }
  .view-use {
    border-bottom: 0;
  }
  .lian-xi {
    padding: 10px;
  }
  .child-item {
    transform: translate(-50%, 30%);
  }
  .little-title {
    opacity: 1;
  }
  .right-item > h6 {
    margin-bottom: 8px;
  }
  .code-input {
    margin-top: 20px;
  }

  .top-part-bg {
    min-height: 400px;
  }
  .wan-zhan {
    width: 100%;
  }
  .home-part2 {
    margin-bottom: 0;
  }
  .text-title-fo {
    margin: 20px 0 36px 0;
  }
  .pad-left1 {
    padding-left: 15px !important;
  }
  .details {
    margin: 0;
  }
  .left-text {
    padding-top: 24px;
  }
  .lb-box {
    padding: 18px 0;
  }
  .index-sol-item {
    display: initial;
    width: 50%;
    text-align: center;
    margin-bottom: 16px;
  }
  .new-user-bg {
    background-size: 60% 40%;
  }
  .sol-part3 {
    padding-bottom: 10px;
  }
  .soultion-item2 {
    margin: 0;
  }
  .re-pro {
    width: 100%;
    margin: 8px auto;
  }
  .nav-solution2 {
    width: 100%;
  }
  .nav-solution2 div {
    font-size: 14px;
  }
  .voice-item-content,
  .voice-item-content2 {
    width: 100%;
  }
  .text-title-sec {
    margin: 40px 0 30px 0;
  }
  .text-title-th {
    margin: 40px 0 32px 0;
  }
  .fg-item {
    padding: 16px 0 16px 30px;
  }
  .solution-bottom {
    padding-bottom: 30px;
  }
  .soultion-item2 {
    margin-bottom: 40px;
  }
  .jin-api {
    margin-top: 40px;
  }
  .little-tip {
    width: 100%;
    margin-top: 20px;
  }
  .part1-container2 {
    padding: 110px 0 60px 0;
  }
  .nav-solution {
    width: 100%;
  }
  .sms-price {
    margin-top: 20px;
  }
  .fg-view > .container {
    padding-bottom: 30px;
  }
  .view-use {
    width: 100%;
    height: 32px;
    line-height: 32px;
  }
  .little-txt8 {
    font-size: 14px;
  }
  .view-txt,
  .hang-ye,
  .fg-view {
    margin-top: 10px;
  }
  .more-fang,
  .view-top-part {
    margin-top: 20px;
  }
  .hang-ye {
    width: 90%;
  }
  .txt-img3 {
    margin-top: 20px;
  }
  .fg-con {
    margin: 40px 0 0 0;
  }
  .fiveg-part > .container {
    padding-bottom: 30px;
  }
  .nei-con {
    margin: 10px;
  }
  .shi-wu {
    margin-top: 40px;
  }
  .go-buy2,
  .lian-xi {
    margin-top: 20px;
    margin-bottom: 30px;
  }
  .ol-li {
    padding-left: 30px;
    margin-top: 10px;
  }
  .sms-rice2 {
    height: auto;
  }
  .chuan-png {
    padding: 20px 0;
  }
  .voice-item-content,
  .chuan-png,
  .ol-li {
    width: 100%;
  }
  .yan-content,
  .yan-content2 {
    padding-bottom: 20px;
  }
  .yan-content3 {
    padding: 20px 0;
  }
  .voice-part3 {
    padding-bottom: 20px;
  }
  .head-title {
    padding: 0 10px !important;
  }
  .foot-cont {
    padding-top: 30px;
  }
  .home-part2,
  .home-part3,
  .home-part5,
  .home-part6,
  .home-part7 {
    margin-top: 30px;
  }
  .text-title2 {
    margin: 15px 0 20px 0;
  }
  .text-title3 {
    margin: 10px 0 20px 0;
  }
  .text-title4 {
    margin-bottom: 30px;
  }
  .new-old-user {
    margin-top: 35px;
  }
  .xuna-nav {
    margin-top: 20px !important;
  }
  .item {
    text-align: center;
  }
  .item2 {
    text-align: center;
    margin-top: 40px;
  }
  .item3 {
    margin: 20px 10px;
  }
  .xieyi {
    font-size: 12px;
    margin-top: 10px;
  }
  .xian {
    margin-top: 30px;
  }
  .bt-ico {
    margin: auto;
  }
  .top-title {
    font-size: 32px;
    text-align: center;
  }
  .sub-introduce {
    margin: auto;
    margin-top: 25px !important;
  }
  .top-btn {
    text-align: center;
    margin: 10px auto;
  }

  .box-child {
    flex: 0 0 97%;
  }

  .home-nav > .nav-link {
    font-size: 14px;
  }
  .box-child:hover {
    box-shadow: none;
    background-color: #fafbfc;
  }
  .box-child:hover::before {
    /*鼠标hover，状态发生改变*/
    width: 0%;
    transition: 0.2s all linear; /*设置过度时间*/
    transition-delay: 0.1s; /*过度延时*/
  }
  .ios-text > h6 {
    font-size: 12px;
  }
  .ios-text > span {
    font-size: 12px;
  }
  .you-dian {
    padding: 40px 0 20px 0;
  }
  .pt-item {
    margin-top: 15px;
  }
  .left-text2 {
    padding: 10px 0 10px 0;
  }
  .text-content {
    margin: 10px 0 15px 0;
  }
  .border-div2,
  .border-div4 {
    border-right: 0;
  }
  .huoban-item,
  .brand-item {
    padding: 10px !important;
    width: 33.33%;
  }
  .huoban-item:nth-child(3n) {
    border-right: none;
  }

  .txt-cont {
    padding-top: 0px !important;
    padding-bottom: 20px !important;
  }
  .ios {
    margin-right: 10px;
    width: 40px;
  }
  .left-text3 > div {
    margin: 20px 0;
  }
  .part7 {
    padding-bottom: 20px;
  }
  .span-text {
    font-size: 14px;
  }
  .ju-feng {
    margin-left: 0 !important;
  }
  .new-friend {
    font-size: 24px;
  }
  .table-item div {
    font-size: 12px;
  }
  .eamil-introduce,
  .free-use2,
  .eamil-item,
  .email-part2,
  .email-part3,
  .email-part4,
  .email-part5,
  .email-part6,
  .bt-line,
  .jie-shao,
  .jie-shao3,
  .yu-yan,
  .xia-zai,
  .shorturl-item,
  .voice-item,
  .onepass-item {
    margin-top: 20px;
  }
  .jie-shao {
    width: 90%;
  }
  .heng-xian {
    margin: 20px 0;
  }
  .heng-xian2 {
    margin-top: 20px;
  }
  .yuyin {
    padding: 20px 0;
  }
  .mail-title1 {
    font-size: 21px;
    width: 100%;
    margin-top: 10px;
  }
  .mail-text1 {
    width: 100%;
    margin-top: 10px;
  }
  .little-title2,
  .little-text2 {
    margin-top: 10px;
  }
  .xiang-fang {
    margin: 10px auto;
  }
  .margin-bot {
    margin-bottom: 20px;
  }
  .table-item div {
    line-height: 50px;
  }
  .pb-youjian {
    padding-bottom: 20px;
  }
  .zhi-dian {
    display: block;
    font-size: 14px;
  }
  .email-part7,
  .shorturl-part6 {
    padding: 20px 0;
  }
  .lij-zhuc {
    margin-top: 20px;
  }
  .liu-cheng {
    padding: 0;
  }
  .right-content3 {
    padding: 30px 20px 75px 20px;
  }
  .duan-png {
    margin-right: 10px;
  }
  .duan-right {
    margin-bottom: 20px;
  }
  .right-text3 {
    width: 100%;
  }
  .little-text3 {
    margin-top: auto;
  }
  .shorturl-part3 {
    padding-bottom: 0;
  }
  .pei-tu {
    display: none;
  }
  .search,
  .search-cont {
    width: 100%;
  }
}

@media screen and (min-width: 576px) and (max-width: 768px) {
  .sec-title {
    font-size: 40px;
  }

  .box-child {
    flex: 0 0 48%;
  }
  .bt-ico {
    margin: auto;
  }
  .item {
    text-align: center !important;
  }
  .left-text2 {
    padding: 20px 0 10px 10px;
  }
  .text-content {
    margin: 10px 0 15px 0;
  }
  .huoban-item,
  .brand-item {
    width: 33.33%;
  }
  .huoban-item:nth-child(3n) {
    border-right: none;
  }
  .border-div1,
  .border-div2,
  .border-div3,
  .border-div4,
  .border-div5,
  .border-div6 {
    height: auto;
  }
  .border-div2,
  .border-div4 {
    border-right: 0;
  }
  .table-title1 {
    font-size: 14px;
  }
}
@media screen and (min-width: 768px) {
  .huoban-item:nth-child(-n + 5) {
    border-top: none;
  }
  .huoban-item:nth-child(5n) {
    border-right: none;
  }
}
@media screen and (max-width: 768px) {
  .huo-do {
    width: 70%;
  }
  .wen-zi {
    width: 100% !important;
    margin: auto;
  }
  .sug-con {
    width: 90%;
  }
  .article-item {
    padding: 10px;
  }
  .article-top {
    margin-bottom: 10px;
  }
  .suggestions-part > .container {
    margin-bottom: 20px;
  }
  .art-top-title {
    flex-wrap: wrap;
  }
  .article-con {
    margin-left: 0px;
  }
  .article-png,
  .article-con {
    width: 100%;
  }
  .article-item {
    flex-wrap: wrap;
  }
  .ins {
    position: inherit;
    bottom: inherit;
  }
  .reloadverifycode {
    margin-top: 10px;
  }
  .nei-con {
    width: 100%;
  }
  .fg-xian2:after,
  .fg-xian2:before,
  .fg-xian3:after,
  .fg-xian3:before,
  .fg-xian4:after {
    width: 0;
  }
  .little-title4 {
    font-size: 14px;
  }
  .table-item div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .duan-bg,
  .duan-bg2 {
    width: 100%;
  }
}
@media screen and (min-width: 992px) {
  #product,
  #solu,
  #product2,
  #solu2,
  .nav-item .iconjiantou1 {
    display: none;
  }
  .zhe-die,
  .zhe-die2 {
    display: none !important;
  }
}
@media screen and (max-width: 992px) {
  .onlin-chat {
    flex-wrap: wrap;
    height: auto !important;
    padding: 10px;
  }
  .onlin-chat a {
    margin-top: 10px;
  }
  .head-center-con {
    display: none;
  }
  .hot_article {
    display: none;
  }
  .service-item {
    color: #fff !important;
  }
  .service-item.disabled {
    pointer-events: none;
  }
  .wen-zi img {
    margin-top: 0;
  }
  .html-top:nth-child(2) {
    margin-top: 40px !important;
  }
  .html-top {
    text-align: center;
  }
  .index-bg {
    background-size: 0px auto;
  }
  .lb-box {
    width: 100%;
  }
  .wan-zhan {
    position: inherit;
    bottom: 0;
    left: 0;
  }
  .txt-img-s,
  .txt-img-s2 {
    position: initial;
    margin-top: 24px;
    transform: translate(0, 0);
  }
  .pr-8,
  .pl-8 {
    padding: 0 !important;
  }

  .sol-little-title2 {
    width: 100%;
  }
  .head-item {
    margin-left: 0px;
  }
  .txt-color,
  .sign-in,
  .txt-color4 {
    padding: 0.5rem 1rem;
  }
  .menu-line,
  .menu-line2 {
    display: block;
  }
  .lian-xi {
    height: auto;
    line-height: inherit;
  }
  .service-item::after {
    width: 0px;
  }

  br {
    display: none;
  }
  .p-r {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .p-l {
    padding-right: 15px !important;
    padding-left: 15px !important;
  }
  .head-nav {
    padding: 0.7rem 1rem !important;
  }
  .product-item,
  .solu-item {
    margin-top: 6px;
    display: initial !important;
  }
  .header-nav .nav-item {
    height: auto;
    line-height: 32px;
  }
  .go-see {
    opacity: 1;
  }
  .phone-input,
  .code-input {
    width: 90%;
  }
  .shi-ting {
    width: auto;
  }
  .top-part {
    text-align: center;
  }
  .sign-in,
  .register {
    width: 100%;
  }
  .register {
    margin-top: 10px;
  }
  .sub-introduce {
    margin: auto;
  }
  .product.active::before,
  .product2.active::before,
  .solution.active::before,
  .solution2.active::before {
    width: 0%; /*宽度为0，这里的宽度是相对与li的宽度*/
  }
  .txt-color:hover::before {
    /*鼠标hover，状态发生改变*/
    width: 0%;
    transition: 0.2s all linear; /*设置过度时间*/
    transition-delay: 0.1s; /*过度延时*/
  }
  .collapse-content,
  .collapse-content2 {
    display: none !important;
  }
  .qrcode {
    display: block;
    margin: auto;
    margin-top: 20px;
  }
  .xiang-qing {
    left: 0;
    margin-top: 10px;
  }
  .table-title1 {
    font-size: 16px;
  }
  .email-top {
    text-align: center;
  }
  .eamil-item,
  .shorturl-item,
  .voice-item,
  .onepass-item {
    width: 100%;
  }
  .right-content3 {
    margin-top: 20px;
  }
}
@media screen and (min-width: 768px) and (max-width: 992px) {
  .table-title1 {
    font-size: 16px;
  }
  .item {
    font-size: 15px;
    text-align: center !important;
  }
  .bt-ico {
    margin: auto;
  }
  .box-child {
    flex: 0 0 31%;
  }
  .left-text2 {
    padding: 50px 0 40px 30px;
  }
  .text-content {
    margin: 20px 0 30px 0;
  }
  .slider-cases .cases-card:nth-child(-n + 5) {
    border-top: none;
  }
}

@media screen and (max-width: 1200px) {
  .wen-zi {
    width: 60%;
    margin: auto;
  }
  .xuan-card,
  .right-content3 {
    width: 100%;
  }
  .right-content3 {
    margin-top: 20px;
  }
}
@media screen and (min-width: 992px) and (max-width: 1200px) {
  .wen-zi img {
    margin-top: 40px;
  }
  .sol-little-title2 {
    width: 50%;
  }
  .box-child {
    flex: 0 0 19%;
  }
  .text-content {
    margin: 30px 0 60px 0;
  }
  .logo {
    width: 170px;
    margin-right: 0px;
  }
  .txt-colo,
  .register {
    font-size: 14px;
  }
  .header-nav .nav-item {
    margin-left: 5px;
  }
}

/* 价格活动页 */
.top-part-bg2_act {
  background-image: url(../image/new_p2.jpg);
}
.part1-container4_act {
  padding: 120px 0 164px 0;
}
.price-an_act {
  margin: auto;
  color: #fff;
  margin-top: 20px;
}
.tx-left-con {
  width: 27%;
  height: auto;
  position: absolute;
  left: 0;
  bottom: 0;
}
.tx-right-con {
  width: 100%;
  height: 50px;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 4px;
  background-image: linear-gradient(to right, #03bbff, #006cff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.tx-right-con > div {
  margin-left: 12rem;
}
.head-center-con {
  width: 1140px;
  height: 120px;
  background: #0050bc;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 30%);
  border-radius: 4px;
}
.head-center-con-item {
  width: 30%;
  height: 150px;
  background: #fff;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-image: linear-gradient(to bottom, #fff, #d8e8ff);
}
.head-center-con-item p {
  font-size: 14px;
}
.head-center-con-item img {
  width: 120px;
}
.tx-left-con {
  width: 27%;
  height: auto;
  position: absolute;
  left: 0;
  bottom: 0;
}

.shi-wu_act {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 7rem;
  font-weight: bold;
}
.pr-ico {
  width: 60px;
}
.top-border_act {
  border-top: 3px solid #006cff;
}
.jian_bian_act {
  position: relative;
  background: #fafbfc;
}

.cz-price .small-jianb {
  width: 130px;
}

.div-tx1 {
  text-align: center;
  width: 40px;
  height: 0;
  border-bottom: 28px solid #006cff;
  border-right: 7px solid transparent;
  transform: rotateX(180deg);
  position: relative;
}
.small-jianb {
  height: 28px;
  width: 130px;
  display: flex;
  background-image: url(../image/txjb.png);
  margin: auto;
  position: relative;
  margin-top: 8px;
  margin-bottom: 14px;
}
.tx-text {
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10%) rotateX(180deg);
}

.div-tx2 {
  color: #fff;
  position: absolute;
  right: 20px;
  top: 2px;
  font-size: 15px;
  font-weight: bold;
}

.go-buy3_act {
  cursor: pointer;
  font-size: 14px;
  color: #006cff !important;
}
.new_buy2_act {
  display: block;
  width: 120px;
  height: 40px;
  line-height: 38px;
  letter-spacing: 1px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  border-radius: 25px;
  color: #fff !important;
  background: #006cff;
}
.new_buy2_act:hover {
  background: #1c77f4;
}
.go-buy2_act,
.new_buy_act {
  width: 140px;
  font-size: 16px;
  font-weight: bold;
  background-image: linear-gradient(to right, #ffb830, #ff6c00);
}
.new_buy_act:hover,
.go-buy2_act:hover {
  color: #fff !important;
  background-image: linear-gradient(to right, #f8d694, #f87a1e);
}
.price-table_act {
  box-shadow: 0 3px 6px 0px rgb(0 0 0 / 15%);
}
.text-td-first {
  color: #006cff;
}
.font-size-18 {
  font-weight: bold;
  font-size: 18px !important;
}
.font-size-13 {
  font-size: 13px !important;
}
.font-size-12 {
  font-size: 12px !important;
}

.new_table_act .table-bordered td {
  padding: 0 20px;
  border: 1px solid #e3e6f0 !important;
}
.new_table_act .table-bordered th {
  padding: 15px 20px 0 20px;
  border: 1px solid #e3e6f0 !important;
}
.jie-shao_act {
  line-height: 26px;
  margin: auto;
  margin-top: 16px;
}
