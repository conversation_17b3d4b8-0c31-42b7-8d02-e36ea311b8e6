﻿using CommonLib;
using System.Text.RegularExpressions;

namespace Account.Web.Common
{
    public class CommonValidateCode
    {
        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetValidateCode(string str, string type, bool isSub = true)
        {
            var md5 = GetMD5String(string.Format("{0}{1}{2}", str, ServerTime.LocalTime.Date.ToString("yyyy-MM-dd"), type));
            var result = Regex.Replace(md5, @"[^0-9]+", "").PadRight(6, '0');
            if (isSub)
            {
                result = result.Substring(0, 6);
            }
            return result;
        }

        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。- 已废弃，请使用CryptoService.MD5Hash
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        [System.Obsolete("请使用Account.Web.Common.CryptoService.MD5Hash方法")]
        public static string GetMD5String(string str)
        {
            return Account.Web.Common.CryptoService.MD5Hash(str);
        }
    }
}