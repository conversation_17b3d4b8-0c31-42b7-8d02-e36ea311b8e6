﻿using System;

namespace Account.Web.pay._360
{
    /// <summary>
    /// 订单信息
    /// </summary>
    public class Sdk360OrderContext
    {
        /// <summary>
        /// 创建订单的信息
        /// </summary>
        public Qh360SdkOrderRequest OrderRequest { get; internal set; }

        /// <summary>
        /// 创建订单完成的信息
        /// </summary>
        public Qh360SdkOrderResponse OrderResponse { get; internal set; }
    }

    /// <summary>
    /// 1-微信，2-支付宝
    /// </summary>
    public enum Sdk360PayChannel
    {
        /// <summary>
        /// 微信
        /// </summary>
        Wx = 1,
        /// <summary>
        /// 支付宝
        /// </summary>
        Ali
    }

    /// <summary>
    /// 订单状态 10 -待付款(初始状态) 20-付款完成（待通知厂商） 30-待厂商发权益（已通知厂商） 40-售后中（厂商发起退款） 50-交易完成（正常完成，厂商完成物品发放） 60-已取消（支付超时，过期等原因） 70-交易关闭（退款完成）
    /// </summary>
    public enum Sdk360PayStatus
    {
        /// <summary>
        /// 待付款(初始状态)
        /// </summary>
        WaitPay = 10,
        /// <summary>
        /// 付款完成（待通知厂商）
        /// </summary>
        PaidBeforeNotify = 20,
        /// <summary>
        /// 待厂商发权益（已通知厂商）
        /// </summary>
        PaidAfterNotify = 30,
        /// <summary>
        /// 售后中（厂商发起退款）
        /// </summary>
        Refund = 40,
        /// <summary>
        /// 交易完成（正常完成，厂商完成物品发放）
        /// </summary>
        Success = 50,
        /// <summary>
        /// 已取消（支付超时，过期等原因）
        /// </summary>
        CancelOrExpired = 60,
        /// <summary>
        /// 交易关闭（退款完成）
        /// </summary>
        Closed = 70
    }

    /// <summary>
    /// 支付状态改变事件委托
    /// </summary>
    /// <param name="e"></param>
    public delegate void Sdk360PayStatusChangedHandler(Sdk360PayStatusChangedEventArgs e);

    /// <summary>
    /// 支付状态改变事件
    /// </summary>
    public class Sdk360PayStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 订单信息
        /// </summary>
        public Sdk360OrderContext OrderContext { get; internal set; }

        /// <summary>
        /// 支付状态
        /// </summary>
        public Sdk360PayStatus PayStatus { get; internal set; }

        /// <summary>
        /// 支付渠道
        /// </summary>
        public Sdk360PayChannel PayChannel { get; internal set; }

        /// <summary>
        /// 处理完订单完成状态后设置为true
        /// </summary>
        public bool Handled { get; set; }
    }
}
