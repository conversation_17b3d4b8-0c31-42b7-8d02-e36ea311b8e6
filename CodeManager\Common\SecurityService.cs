using CommonLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// 安全服务 - 负责安全检查和黑名单管理
    /// </summary>

    /// <summary>
    /// 请求中断原因枚举
    /// </summary>
    public enum BlockReason
    {
        /// <summary>
        /// IP黑名单 - 返回403 Forbidden
        /// </summary>
        IpBlacklisted = 403,

        /// <summary>
        /// URL黑名单 - 返回403 Forbidden
        /// </summary>
        UrlBlacklisted = 403,

        /// <summary>
        /// UserAgent黑名单 - 返回403 Forbidden
        /// </summary>
        AgentBlacklisted = 403,

        /// <summary>
        /// 资源不存在 - 返回404 Not Found
        /// </summary>
        ResourceNotFound = 404,

        /// <summary>
        /// 服务异常 - 返回500 Internal Server Error
        /// </summary>
        ServiceException = 500,

        /// <summary>
        /// 未授权访问 - 返回401 Unauthorized
        /// </summary>
        Unauthorized = 401,

        /// <summary>
        /// 请求频率过高 - 返回429 Too Many Requests
        /// </summary>
        RateLimited = 429,

        /// <summary>
        /// 默认错误 - 返回400 Bad Request
        /// </summary>
        BadRequest = 400,
        UserLog = 1000
    }

    public static class SecurityService
    {
        // 黑名单列表
        private static readonly List<string> BadAgents = new List<string>() { "curl", "python" };

        /// <summary>
        /// 检查IP和用户ID是否在黑名单中
        /// </summary>
        public static bool IsBlackListed(string ip, string uid)
        {
            if (string.IsNullOrEmpty(ip) && string.IsNullOrEmpty(uid))
                return false;

            bool result = false;

            // 检查IP黑名单
            if (!string.IsNullOrEmpty(ip))
            {
                var staticIP = CodeProcessHelper.StaticBlackIpCache.Get();
                if (staticIP != null && staticIP.Contains(ip))
                {
                    result = true;
                }

                if (!result)
                {
                    var cacheIp = CodeProcessHelper.BlackIpConfigCache.Get();
                    if (cacheIp != null)
                    {
                        if (cacheIp.Contains(ip))
                        {
                            result = true;
                        }
                        // 检查UID黑名单
                        if (!result && !string.IsNullOrEmpty(uid) && cacheIp.Contains(uid))
                        {
                            result = true;
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 检查URL是否在黑名单中
        /// </summary>
        public static bool IsUrlBlackListed(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            return CodeProcessHelper.StaticBlackUrlCache.Get()?.Any(p => path.Contains(p)) == true;
        }

        /// <summary>
        /// 检查UserAgent是否在黑名单中
        /// </summary>
        public static bool IsAgentBlackListed(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent))
                return false;

            return BadAgents.Any(p => userAgent.ToLower().StartsWith(p));
        }

        /// <summary>
        /// 添加IP到黑名单
        /// </summary>
        public static void AddToBlacklist(string ip, bool isStaticBlack = false)
        {
            if (string.IsNullOrEmpty(ip))
                return;

            if (isStaticBlack)
            {
                var old = CodeProcessHelper.StaticBlackIpCache.Get();
                if (!old.Contains(ip))
                {
                    old.Add(ip);
                    LogHelper.Log.Error(string.Format("加入永久黑名单！IP:{0}", ip));
                    CodeProcessHelper.StaticBlackIpCache.Set(old);
                }
            }
            else
            {
                var old = CodeProcessHelper.BlackIpConfigCache.Get();
                if (!old.Contains(ip))
                {
                    old.Add(ip);
                    LogHelper.Log.Error(string.Format("加入黑名单！IP:{0}", ip));
                    CodeProcessHelper.BlackIpConfigCache.Set(old);
                }
            }
        }

        /// <summary>
        /// 结束请求响应并可选择性地将IP加入黑名单
        /// </summary>
        public static bool EndResponse(this HttpContext context, string key, string value,
            BlockReason blockReason, bool isEndResponse, bool isAddToBlack, bool isShort, bool isStaticBlack)
        {
            // 记录拦截信息
            LogBlockedRequest(string.Format("{1}:{0}被拦截！原因：{2}", value, key, blockReason), isShort);

            // 可选择性地将IP加入黑名单
            if (isAddToBlack)
            {
                var strIp = context.Items["nowIP"]?.ToString();
                if (!string.IsNullOrEmpty(strIp) && !IsSpider(context.Request))
                {
                    AddToBlacklist(strIp, isStaticBlack);
                }
            }

            // 结束响应
            if (isEndResponse)
            {
                // 根据拦截原因设置不同的状态码
                context.Response.StatusCode = (int)blockReason;
                context.Response.ContentType = "application/json; charset=utf-8";
                context.Response.End();
            }

            return isEndResponse;
        }

        /// <summary>
        /// 判断是否是搜索引擎爬虫
        /// </summary>
        public static bool IsSpider(HttpRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.UserAgent))
                return false;

            string userAgent = request.UserAgent.ToLower();
            return userAgent.Contains("bot") ||
                   userAgent.Contains("spider") ||
                   userAgent.Contains("crawler") ||
                   userAgent.Contains("baidu") ||
                   userAgent.Contains("google") ||
                   userAgent.Contains("bing") ||
                   userAgent.Contains("yahoo");
        }

        /// <summary>
        /// 记录被拦截的请求
        /// </summary>
        private static void LogBlockedRequest(string message, bool isShort)
        {
            HttpRequest request = HttpContext.Current?.Request;
            if (request == null)
                return;

            try
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine(message);

                if (!isShort)
                {
                    sb.AppendLine($"IP: {request.GetIPAddress()}");
                    sb.AppendLine($"URL: {request.Url}");
                    sb.AppendLine($"Method: {request.HttpMethod}");
                    sb.AppendLine($"Referer: {request.UrlReferrer}");
                    sb.AppendLine($"UserAgent: {request.UserAgent}");
                }

                LogHelper.Log.Error(sb.ToString());
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error($"记录拦截请求失败: {ex.Message}");
            }
        }
    }
}
