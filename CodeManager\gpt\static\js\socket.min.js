import {
    hex_sha1
} from "./sha1.js";
import {
    encryptByDES,
    decryptByDES
} from "./tripledes.js";
(function () {
    let t = {};
    t.pingTimer = -1;
    t.queryTimer = -1;
    let r = new Map;
    t.chatDialog = (e, t) => {
        r.set(t, {
            id: t,
            action: "dialog",
            status: "waiting",
            time: (new Date).getTime()
        });
        let o = {
            id: t,
            method: "/chat/dialog/text",
            params: {
                msgText: e
            }
        };
        sendMsg(o)
    };
    t.chatRetry = (e, t, o) => {
        r.set(t, {
            id: t,
            action: "retry",
            status: "waiting",
            time: (new Date).getTime()
        });
        let n = {
            id: t,
            method: "/chat/dialog/retry",
            params: {
                dialogId: o + ""
            }
        };
        sendMsg(n)
    };
    t.chatHistory = () => {
        let e = getUuid();
        r.set(e, {
            id: e,
            action: "history",
            status: "waiting",
            time: (new Date).getTime()
        });
        let t = {
            id: e,
            method: "/chat/history",
            params: null
        };
        sendMsg(t)
    };
    const e = () => {
        let e = "";
        let t = "";
        let o = "";
        let n = navigator.userAgent;
        let i = n.toLowerCase();
        if (/android/.test(i)) {
            e = "android"
        } else if (/iphone|ipad|ipod/.test(i)) {
            e = "ios"
        }
        t = t ? t : "unkonw";
        o = o ? o : "unkonw";
        let r = n.match(/NetType\/\w+/) ? n.match(/NetType\/\w+/)[0] : "NetType/unknown";
        r = r.toLowerCase().replace("nettype/", "");
        return {
            platform: e,
            devsystem: t,
            devname: o,
            networkStr: r
        }
    };
    const o = window.location.host;
    const n = /^(\d+)\.(\d+)\.(\d+)\.(\d+)/;
    let i = "X01lVzjsJjOuWD8L";
    let a = "qg44D415dHPUnuO3DLbcfQ2g6Nl9TE9N";
    let s = "Yo2NpZNkgVGRKSWN";
    let l = "iZTE2V29USHROXhH";
    let url = "wss://xc.com/ws/";
    let d = true;
    const m = "0123456789abcdefghijklmnopqrstuvwxzyABCDEFGHIJKLMNOPQRSTUVWXZY";
    let g = $.cookie("devid");
    if (!g) {
        for (var w = 1; w <= 32; w++) {
            g += m.substr(Math.random() * 1e4 % m.length, 1)
        }
        g = g.substr(0, 8) + "-" + g.substr(8, 4) + "-" + g.substr(12, 4) + "-" + g.substr(16, 4) + "-" + g.substr(
            20, 12);
        g = g.toUpperCase();
        $.cookie("devid", g, {
            expires: 365
        })
    }
    let p = localStorage.getItem("nonce") ? Number(localStorage.getItem("nonce")) + 1 : 1e15;
    localStorage.setItem("nonce", p);
    let u = e();
    let y = {
        "x-dev-id": g,
        "x-dev-name": u.devname,
        "x-dev-system": u.devsystem,
        "x-dev-time": (new Date).getTime(),
        "x-dev-version": "1.0.0.1",
        "x-dev-lang": "en",
        "x-dev-screen": (document.body.clientWidth ? document.body.clientWidth : 1200) + "x" + (document.body.clientHeight ?
            document.body.clientHeight : 800),
        "x-dev-net": u.networkStr,
        "x-dev-location": "",
        "x-app-key": i,
        "x-app-platform": "web_mobile",
        "x-app-channel": "official/com.xc.chat",
        "x-app-nonce": p,
        "x-app-token": ""
    };
    let f = "NONCE";
    let h = "";
    if (d) {
        for (var w = 1; w <= 16; w++) {
            h += m.substr(Math.random() * 1e4 % m.length, 1)
        }
        f = encryptByDES(h + p, a)
    }
    let v = "";
    let x = Object.keys(y).sort();
    for (let t in x) {
        let e = x[t];
        if (v != "") {
            v += "&"
        }
        v += e + "=" + y[e]
    }
    let b = "";
    let S = "";
    if (d && b.length > 0) {
        b = encryptByDES(b, h);
        S = v + a + hex_sha1(b);
        b = toByte(b)
    } else {
        S = v + a + b
    }
    let T = hex_sha1(S);
    y["x-dev-offset"] = (new Date).getTimezoneOffset() * 60 * -1;
    y["x-app-key"] = i;
    y["x-key"] = f;
    y["x-sig"] = T;
    for (var k in y) {
        url += (url.indexOf("?") > -1 ? "&" : "?") + k.toUpperCase() + "=" + encodeURIComponent(y[k])
    }
    const toByte = e => {
        var t = window.atob(e);
        var o = t.length;
        var n = new Uint8Array(o);
        for (var i = 0; i < o; i++) {
            n[i] = t.charCodeAt(i)
        }
        return n.buffer
    };
    const I = e => {
        var t = "";
        var o = new Uint8Array(e);
        for (var n = o.byteLength, i = 0; i < n; i++) {
            t += String.fromCharCode(o[i])
        }
        return window.btoa(t)
    };
    const N = () => {
        if ("WebSocket" in window) {
            t.ws = new WebSocket(url)
        } else if ("MozWebSocket" in window) {
            t.ws = new MozWebSocket(url)
        } else {
            console.log("不支持websocket");
            return null
        }
        t.ws.binaryType = "arraybuffer";
        t.ws.onopen = e => {
            localStorage.removeItem("lockReconnect");
            E()
        };
        t.ws.onmessage = e => {
            receiveMsg(e)
        };
        t.ws.onerror = e => {
            console.log("关闭连接ws.onerror");
            clearInterval(t.pingTimer);
            U()
        };
        t.ws.onclose = e => {
            console.log("关闭连接ws.onclose");
            clearInterval(t.pingTimer);
            U()
        }
    };
    N();
    let O = 100;
    let C = 0;
    const U = () => {
        if (O > 0) {
            if (!localStorage.getItem("lockReconnect")) {
                localStorage.setItem("lockReconnect", 1);
                O--;
                C++;
                console.log("第" + C + "次重连");
                setTimeout(function () {
                    localStorage.removeItem("lockReconnect");
                    N()
                }, 5e3)
            }
        } else {
            console.log("连接已超时")
        }
    };
    const E = () => {
        setTimeout(() => {
            if (t.ws.readyState !== 1) {
                return
            }
            t.chatHistory();
            clearInterval(t.pingTimer);
            t.pingTimer = setInterval(() => {
                sendMsg({
                    id: getUuid(),
                    method: "/ping",
                    params: null
                })
            }, 15e3)
        }, 500)
    };
    const receiveMsg = e => {
        let t = e.data;
        let o = decryptByDES(I(t), l);
        console.log(o);
        let n = JSON.parse(o);
        let i = r.get(n.id);
        if (!i) {
            return
        }
        if (i.action == "history") {
            window.dialogHistory(n.result);
            W()
        } else if (i.action == "dialog") {
            window.dialogOutput(n.result, n.id)
        } else if (i.action == "retry") {
            window.dialogOutput(n.result, n.id)
        }
        r.delete(n.id);
        window.chat_status = "pass";
        $(".send-view").css("display", "flex");
        $(".send-loading").hide()
    };
    const sendMsg = e => {
        t.ws.send(toByte(encryptByDES(JSON.stringify(e), s)))
    };
    const W = () => {
        clearInterval(t.queryTimer);
        t.queryTimer = setInterval(() => {
            if (r.size == 0) {
                return
            }
            for (let t of r.keys()) {
                let e = r.get(t);
                if ((new Date).getTime() - e.time > 60 * 1e3 && e.status == "waiting") {
                    if (e.action == "dialog" || e.action == "retry") {
                        window.chat_status = "pass";
                        window.dialogOutput("", t);
                        r.delete(t)
                    }
                }
            }
        }, 1e3)
    };
    window.CHAT_API = t
})();