* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

p{
  margin-bottom:0!important;
}
pre{
  color:#d1d5db!important;
  font-size:15px!important;
}
ol{
  /* position:relative;
  top:-20px; */
  margin-top:-20px;
  padding-left:15px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f6f6f6;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #cdcdcd;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #747474;
}

::-webkit-scrollbar-corner {
  background: #f6f6f6;
}

html,
body {
  background: #353540!important;
  width: 100%;
  height: 100%;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding-bottom: 140px;
}

.wrapper {
  /* overflow-y: auto;
  height: calc(100vh - 80px); */
  
}

#tip {
  padding: 50px 20px;
  font-weight: bold;
  font-size: 22px;
  color: #d1d5db;
  line-height: 32px;
  display: none;
}

.tip-view{
  margin-top:20px;
  font-size:16px;
  font-weight: 500;
}
.tip-view ul{
  padding-left:20px;
}

.ipt-view {
  position: fixed;
  bottom: 30px;
  left: 0;
  right: 0;
  margin: auto;
  width: 90%;
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  border: 1px solid rgba(0,0,0,0.3);
  padding-right: 12px;
  background:#FFF;
  border-radius: 8px;
}

.ipt-view input {
  flex-grow: 1;
  height: 44px;
  max-height:100px;
  border: 0;
  outline: none;
  padding:12px 15px;
  background: transparent;
  font-size: 16px;
  width:100%;
  font-weight: bold;
  color:rgba(0,0,0,0.7);
}

.ipt-view button {
  margin-left: 15px;
  width: 56px;
  height: 82%;
  border-radius: 6px;
  border: 0;
  background: silver;
  color: #333;
  font-size: 14px;
  outline: none;
}

.ipt-view button:hover {
  cursor: pointer;
  opacity: 0.8;
}

.mine {
  padding: 20px 18px;
  color: #d1d5db;
  font-size: 18px;
  display: flex;
}

.avatar {
  flex-shrink: 0;
  margin-right: 12px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.msg {
  padding-top: 6px;
  width:100%;
  white-space: pre-wrap;
}
.dialog-img{
  max-width:90%;
}

.ai {
  position:relative;
  padding: 20px 18px;
  color: #d1d5db;
  font-size: 16px;
  display: flex;
  background: #444654;
  overflow-x: auto;
  white-space: pre-wrap;
}

.loading {
  position: relative;
  top: -6px;
  display: inline-block;
  font-size: 0px;
  padding: 0px;
}

.loading span {
  vertical-align: middle;
  border-radius: 100%;

  display: inline-block;
  width: 8px;
  height: 8px;
  margin: 3px 2px;
  animation: loading-animation 0.6s linear infinite alternate;
}

.loading span:nth-child(1) {
  animation-delay: -1s;
  background: rgba(255, 255, 255, 0.6);
}

.loading span:nth-child(2) {
  animation-delay: -0.8s;
  background: rgba(255, 255, 255, 0.8);
}

.loading span:nth-child(3) {
  animation-delay: -0.26666s;
  background: rgba(255, 255, 255, 1);
}

.loading span:nth-child(4) {
  animation-delay: -0.8s;
  background: rgba(255, 255, 255, 0.8);
}

.loading span:nth-child(5) {
  animation-delay: -1s;
  background: rgba(255, 255, 255, 0.4);
}

.fa-refresh {
  margin-left: 10px;
  width: 20px;
  height: 20px;
  outline: none;
}

.fa-refresh:hover {
  cursor: pointer;
  opacity: 0.8;
}

.img-error-text{
  color:rgba(255,255,255,0.5);
  font-size:14px;
}


@keyframes loading-animation {
  from {
    transform: scale(0, 0);
  }

  to {
    transform: scale(1, 1);
  }
}

.voice-loading,
.voice-loading > div {
  position: relative;
  box-sizing: border-box;
}

.voice-loading.la-dark {
  color: #333;
}

.voice-loading > div {
  display: inline-block;
  float: none;
  background-color: currentColor;
  border: 0 solid currentColor;
}

.voice-loading {
  position:fixed;
  bottom:80px;
  left:5%;
  width: 80px;
  height: 36px;
  background:rgba(0,0,0,0.3);
  border-radius:4px;
  align-items: center;
  justify-content: center;
  color: rgba(255,255,255,0.7);
  display: none;
}

.voice-loading > div {
  width: 3px;
  height: 28px;
  margin: 2px;
  margin-top: 0;
  margin-bottom: 0;
  border-radius: 0;
  animation: line-scale-pulse-out 0.9s infinite
    cubic-bezier(0.85, 0.25, 0.37, 0.85);
}

.voice-loading > div:nth-child(3) {
  animation-delay: -0.9s;
}

.voice-loading > div:nth-child(2),
.voice-loading > div:nth-child(4) {
  animation-delay: -0.7s;
}

.voice-loading > div:nth-child(1),
.voice-loading > div:nth-child(5) {
  animation-delay: -0.5s;
}

.voice-loading.la-sm {
  width: 18px;
  height: 14px;
}

.voice-loading.la-sm > div {
  width: 2px;
  height: 14px;
  margin: 1px;
  margin-top: 0;
  margin-bottom: 0;
}

.voice-loading.la-2x {
  width: 72px;
  height: 56px;
}

.voice-loading.la-2x > div {
  width: 8px;
  height: 56x;
  margin: 4px;
  margin-top: 0;
  margin-bottom: 0;
}

.voice-loading.la-3x {
  width: 108px;
  height: 84px;
}

.voice-loading.la-3x > div {
  width: 12px;
  height: 84px;
  margin: 6px;
  margin-top: 0;
  margin-bottom: 0;
}

@keyframes line-scale-pulse-out {
  0% {
    transform: scaley(1);
  }

  50% {
    transform: scaley(0.3);
  }

  100% {
    transform: scaley(1);
  }
}

.voice-view{
  position:fixed;
  right:5%;
  bottom:80px;
  width:44px;
  height:44px;
  border-radius:50%;
  background:rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color:#FFF;
}
.voice-start{
  font-size:20px;
  display: none;
}
.voice-end{
  font-size:16px;
  display: none;  
}

.btn-send{
  display: flex;
  align-items: center;
  justify-content: center;
  width:48px;
  height:32px;
  border-radius:6px;
  color:rgba(0,0,0,0.6);
  background:rgba(0,0,0,0.1);
}
.btn-send:hover{
  cursor: pointer;
  opacity:0.85;
}
.fa-send{
  display: none;
}
.skeleton-view .sk-item{
  padding: 20px 18px;
  display: flex;
}
.skeleton-view .sk-avatar{
  flex-shrink: 0;
  margin-right: 12px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.skeleton-view .sk-msg{
  margin-top:6px;
  width: 100%;
  height: 22px;
}
.skeleton-view .sk-msg-ai{
  width: 100%;
  height: 60px;
}
.skeleton{
  background: linear-gradient(90deg,#444654 25%, #696969 37%,#444654 63%);
  /* background: #444654;
  background-image: linear-gradient(90deg, #444654 25%, rgba(255,255,255,0.1) 37%, #444654 63%); */
  list-style: none;
  background-size: 400% 100%;
  background-position: 100% 50%;
  animation: skeleton-loading 1.4s ease-in-out infinite;
}
@keyframes skeleton-loading {  
  0% {    background-position: 100% 50%; }
  100% {    background-position: 0 50%; }
}


.send-view{
  display: none;
  width:100%;
  height:100%;
  align-items: center;
  justify-content: center;
}
.send-loading{
  display: flex;
  width:100%;
  height:100%;
  align-items: center;
  justify-content: center;
}
.send-loading,
.send-loading > div {
  position: relative;
  box-sizing: border-box;
}

.send-loading > div {
  display: inline-block;
  background-color: #666;
}

.send-loading > div:nth-child(1) {
  animation-delay: -200ms;
}

.send-loading > div:nth-child(2) {
  animation-delay: -100ms;
}

.send-loading > div:nth-child(3) {
  animation-delay: 0ms;
}

.send-loading > div {
  width: 5px;
  height: 5px;
  margin: 2px;
  border-radius: 100%;
  animation: ball-pulse 1.2s ease infinite;
}

@keyframes ball-pulse {
  0%,
  60%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  30% {
    opacity: 0.1;
    transform: scale(0.01);
  }
}


table {
  color:#333333;
  border-width: 1px;
  border-color: silver;
  border-collapse: collapse;
  min-width: 240px;
  text-align: center;
}
table th {
  border-width: 1px;
  padding: 8px;
  border-style: solid;
  border-color: silver;
  background-color: #dedede;
}
table td {
  border-width: 1px;
  padding: 8px;
  border-style: solid;
  border-color: silver;
  background-color: #ffffff;
}

.copied-view{
  display: none;
}
.copy-view{
  position:absolute;
  right:20px;
  top:10px;
  font-size:13px;
}
.copy-view .fa-copy{
  font-size:16px;
  margin-right:6px;
}
.copy-view .fa-check{
  font-size:16px;
  margin-right:6px;
}
.copy-view:hover{
  opacity: 0.85;
  cursor: pointer;
}