﻿//20190617
var payjson = {
    vip_details: {
        "A": [
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转10次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转20次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转30次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转50次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转50次"]
        ],
        "C": [
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转10次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转20次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转50次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转50次"]
        ],
        "D": [
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转10次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转20次"],
            ["文件大小限制50M", "文档保存30天", "视、音频保存期1小时", "人工帮转50次"]
        ]
    },
    vip_packtime: {
        "A": ["48元/6月", "68元/1年", "88元/3年", "118元/3年", "128元/3年"],
        "C": ["38元/6月", "48元/1年", "68元/3年", "88元/3年"],
        "D": ["48元/6月", "68元/1年", "88元/3年"]
    },
    vip_rank: {
        "A": ["VIP会员", "VIP会员", "VIP会员", "超级VIP会员", "超级VIP会员"],
        "C": ["VIP会员", "VIP会员", "超级VIP会员", "超级VIP会员"],
        "D": ["VIP会员", "VIP会员", "超级VIP会员"]
    },
    vip_price: {
        "A": [48.00, 68.00, 88.00, 118.00, 128.00],
        "C": [38.00, 48.00, 68.00, 88.00],
        "D": [48.00, 68.00, 88.00],
        "E": [48.00, 88.00, 128.00],
        "F": [48.00, 68.00, 88.00]
    },
    vip_month: {
        "A": [6, 12, 36, 36, 36],
        "C": [6, 12, 36, 36],
        "D": [6, 12, 36],
        "E": [3, 12, 36],
        "F": [3, 12, 36]
    },
    vip_package: {
        "A": ["89E2EB9A4FB3B3D4ACA6AD2EFFDB7FA01D0BC41A60501A2F5A86E64142BB79C9E6768F9CF2B1B3EA7912FD18AC811477", "12D48C0CBD3033712115F52178D6AB4F1D0BC41A60501A2F5A86E64142BB79C9E6768F9CF2B1B3EA7912FD18AC811477", "075EEF29520D08A0AC49AC5DB84A43801D0BC41A60501A2F5A86E64142BB79C9E6768F9CF2B1B3EA7912FD18AC811477", "B20B9E2E2D1B3D4752FAF098B06CD49496830DA10073AAACB45EEAFDA763F176BD6563B4078D5B9E3C4AEA2ADBE3BBED", "D1FD4BC58547FD74EAB0645F6F91EF6396830DA10073AAACB45EEAFDA763F176BD6563B4078D5B9E3C4AEA2ADBE3BBED"],
        "C": ["89E2EB9A4FB3B3D4FBFDF6987733A5821EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "12D48C0CBD303371B9FD46FDDBDB04CF1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "B20B9E2E2D1B3D471604221788C1D63A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "D1FD4BC58547FD74C6DDDD77DF0D091A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB"],
        "D": ["89E2EB9A4FB3B3D4B9FD46FDDBDB04CF1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "12D48C0CBD3033711604221788C1D63A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "D1FD4BC58547FD74C6DDDD77DF0D091A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB"],
        "E": ["71A8B3AA1CD3B627B9FD46FDDBDB04CF1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "12D48C0CBD303371C6DDDD77DF0D091A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "D1FD4BC58547FD74AEEDB7057CA64A282FB26F4AF2CDA5C159B1A1BE99D739AD3CB40FC569C1853D86E874F02898AA44"],
        "F": ["71A8B3AA1CD3B627B9FD46FDDBDB04CF1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "12D48C0CBD3033711604221788C1D63A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB", "D1FD4BC58547FD74C6DDDD77DF0D091A1EFDE1639EF6B470FB62D3E09539518042D1F1C4B4B9E6E6FC263FF27A69D0AB"]
    }
}
$(document).ready(function () {

    var pagetype = $(".page_buyvip").attr("data-pagetype");
    var vip_price = payjson.vip_price[pagetype];
    
    //选择套餐事件
    var SelectVipPackage = function (packindex) {
        $(".packinfo").removeClass("selected");
        $(".packinfo[data-index='" + packindex + "']").addClass("selected");
        $("#pack_product_details  li").hide();
        $("#pack_product_details  li").eq(packindex).show();
        $("#pay_packprice").html(vip_price[packindex].toFixed(0));
        if (packindex == 2) {
            $(".js-newyear-tips").show();
        } else {
            $(".js-newyear-tips").hide();
        }
    };

    //选择套餐
    $(".packinfo").click(function () {
        var vip_index = $(this).attr("data-index");
        SelectVipPackage(vip_index);
    });

    //立即购买事件
    $(".btn_buyvip").click(function () {
        SelectVipPackage($(this).attr("data-index"));
    });
});

