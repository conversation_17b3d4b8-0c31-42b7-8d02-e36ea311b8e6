﻿<%@ Page Title="OCR文字识别助手在线工具集 | 免费在线实用工具" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手在线工具集，提供多种实用功能：JSON美化格式化，文字编码解码，网页取色器，文字排版统计，多文本合并比对，时间戳转换等实用开发工具。" />
    <meta name="keywords" content="JSON美化,文字编码解码,取色器,文字排版统计,文本合并比对,计算器,正则表达式匹配,在线时间戳转换" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <iframe id="frm" style="padding-top: 50px; width: 100%; height: 100px" frameborder="0" border="0" scrolling="no" allowtransparency="yes" allow="clipboard-read;clipboard-write;fullscreen"></iframe>
    <script type="text/javascript">
        function getCurrentLanguage() {
            var pathParts = window.location.pathname.split('/');
            if (pathParts.length > 1 && pathParts[1] && pathParts[1].length > 0) {
                var possibleLang = pathParts[1];
                if (possibleLang.length >= 2 && possibleLang.indexOf('.') === -1) {
                    return possibleLang;
                }
            }
            return "zh-Hans";
        }

        const toolType = new URLSearchParams(window.location.search).get('type') || "json";

        document.getElementById('frm').src = "/" + getCurrentLanguage() + "/tool/" + toolType + ".aspx";
        var iFrames = document.getElementsByTagName('iframe');
        function iResize() {
            for (var i = 0, j = iFrames.length; i < j; i++) {
                var bHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.body ? iFrames[i].contentWindow.document.body.scrollHeight : 0;
                var dHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.documentElement ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;
                var cHeight = iFrames[i].document && iFrames[i].document.documentElement ? iFrames[i].document.documentElement.scrollHeight : 0;
                var dHeight = window.innerHeight - 100;
                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';
            }
        }
        window.setInterval("iResize()", 200);
    </script>
</asp:Content>
