/*
* Prefixed by https://autoprefixer.github.io
* PostCSS: v8.4.14,
* Autoprefixer: v10.4.7
* Browsers: last 4 version
*/

/* 除公共样式外不要写别的样式！！！ */

/* 通用样式-start */
/* 辅助线 */
/*  
  <div class="container line">
    <div class="g-line"></div>
  </div>
*/
/* 根结点 */
.root {
  width: 100%;
  overflow-x: hidden;
}
.container.line {
  position: fixed;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  z-index: 10;
}
.container.line::after,
.container.line::before {
  content: "";
  position: absolute;
  top: 0;
  height: 100vh;
  width: 1px;
  background-color: red;
  opacity: 0.1;
}
.container.line::after {
  left: 15px;
}
.container.line::before {
  right: 15px;
}
.container .g-line {
  width: 1px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 50%;
  background-color: red;
  opacity: 0.1;
  z-index: 10;
}
.container {
  position: relative;
}
/* 字体颜色 */
.color-default {
  color: #212529;
}
.color-gray {
  color: #41464f;
}
.color-white {
  color: #fff;
}
.color-blue {
  color: #1764ff;
}
/* 字号 */
.fn12 {
  font-size: 12px;
}
.fn14 {
  font-size: 14px !important;
}
.fn15 {
  font-size: 15px;
}
.fn16 {
  font-size: 16px;
}
.fn18 {
  font-size: 18px !important;
}
.fn20 {
  font-size: 20px !important;
}
.fn22 {
  font-size: 22px;
}
.fn24 {
  font-size: 24px;
}
.fn26 {
  font-size: 26px;
}
.fn30 {
  font-size: 30px;
}
.fn32 {
  font-size: 32px;
}
.fn36 {
  font-size: 36px;
}
.fn40 {
  font-size: 40px;
}
.fn48 {
  font-size: 48px;
}
.fnbold {
  font-weight: bold;
}
@media screen and (max-width: 768px) {
  .product-container h2 {
    font-size: 24px;
  }
}
@media screen and (max-width: 575px) {
  .product-container h1 {
    font-size: 28px;
  }
  .product-container h3 {
    font-size: 24px;
  }
  .product-container h4 {
    font-size: 22px;
  }
  .product-container h5 {
    font-size: 20px;
  }
  .product-container h6 {
    font-size: 18px;
  }
}
/* margin左右 负值 */
.mx_1 {
  margin-left: -0.25rem !important;
  margin-right: -0.25rem !important;
}
.mx_2 {
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}
.mx_3 {
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}
.mx_4 {
  margin-left: -1.5rem !important;
  margin-right: -1.5rem !important;
}
.mx_5 {
  margin-left: -3rem !important;
  margin-right: -3rem !important;
}
/* 文字 */
.text-l {
  text-align: left;
}
.text-c {
  text-align: center;
}
.text-r {
  text-align: right;
}
/* display */
.block {
  display: block;
}
.block-inline {
  display: inline-block;
  vertical-align: middle;
}
/* 背景色 */
.bg-gray {
  background-color: #f5f7fc;
}
/* 背景色渐变 */
.bg-linear-light {
  background: -webkit-gradient(
    linear,
    left top,
    right bottom,
    from(#f5f7fc),
    to(#ddefff)
  );
  background: -o-linear-gradient(top left, #f5f7fc, #ddefff);
  background: linear-gradient(to bottom right, #f5f7fc, #ddefff);
}
.bg-linear-card {
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
}
.bg-linear-black {
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(#24272d),
    to(#2c333d)
  );
  background: -o-linear-gradient(top, #24272d, #2c333d);
  background: linear-gradient(to bottom, #24272d, #2c333d);
}
/* 阴影 */
.shadow-1 {
  -webkit-box-shadow: 8px 4px 10px rgba(55, 99, 170, 0.1);
  box-shadow: 8px 4px 10px rgba(55, 99, 170, 0.1);
}
.shadow-2 {
  -webkit-box-shadow: -2px 2px 10px #ccc;
  box-shadow: -2px 2px 10px #ccc;
}
.shadow-3 {
  -webkit-box-shadow: 4px 4px 10px 0px rgba(0, 17, 86, 0.1);
  box-shadow: 4px 4px 10px 0px rgba(0, 17, 86, 0.1);
}
.shadow-inset-1 {
  -webkit-box-shadow: 0 0 30px 0 rgba(74, 73, 73, 0.15) inset;
  box-shadow: 0 0 30px 0 rgba(74, 73, 73, 0.15) inset;
}
/* 圆角 */
.radius-0 {
  border-radius: 0 !important;
}
.radius-2 {
  border-radius: 2px !important;
}
.radius-4 {
  border-radius: 4px !important;
}
.radius-8 {
  border-radius: 8px !important;
}
/* 标签组件 */
.tags {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.tag-item {
  background-color: #e7eaef;
  border-radius: 2px;
  text-align: center;
  padding: 0 12px;
  min-width: 20px;
  margin-right: 8px;
  font-size: 14px;
  height: 28px;
  line-height: 28px;
}
.tag-item[size="small"] {
  padding: 0 8px;
  min-width: 20px;
  margin-right: 4px;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
}
/* 按钮-默认样式 */
.my-button,
.my-button.middle {
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  text-align: center;
  border: 1px solid rgba(127, 137, 158);
  border-radius: 2px;
  -webkit-transition: background-color 0.3s ease-out;
  -o-transition: background-color 0.3s ease-out;
  transition: background-color 0.3s ease-out;
  color: rgba(26, 28, 31);
  font-size: 14px;
  cursor: pointer;
  /* background-color: transparent; */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  outline: none;
  white-space: nowrap;
  font-weight: 400;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.my-button:hover {
  text-decoration: underline !important;
}
/* 按钮禁用样式 */
.my-button[disabled],
.my-button[disabled]:hover,
.my-button[type="primary"][disabled],
.my-button[type="primary"][disabled]:hover,
.my-button[type="white"][disabled],
.my-button[type="white"][disabled]:hover {
  cursor: not-allowed;
  background-color: #e7eaef !important;
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: transparent;
  box-shadow: none;
}
/* 按钮-大小 small large 默认middle */
.my-button[size="small"] {
  height: 28px;
  line-height: 28px;
  border-radius: 2px;
  padding-left: 12px;
  padding-right: 12px;
}
.my-button[size="large"] {
  height: 40px;
  line-height: 40px;
  border-radius: 2px;
}
/*
  按钮样式：
  默认：背景无填充色，带边框
  primary：背景填充色#1764ff，无边框
  linear
*/
.my-button[type="primary"] {
  background-color: #1764ff;
  border-color: transparent;
  color: #fff !important;
}
.my-button[type="primary"]:hover {
  background-color: #2989ff;
  text-decoration: none !important;
  box-shadow: 0px 5px 20px 0px rgba(0, 17, 86, 0.1);
}
.my-button[type="white"] {
  background-color: #fff;
  border-color: transparent;
  color: #1a1c1f !important;
  box-shadow: 0px 6px 30px 0px rgba(0, 17, 86, 0.06);
}
.my-button[type="white"]:hover {
  background-color: #f3f5f9;
  text-decoration: none !important;
  border-color: transparent;
}
.my-button[type="linear"] {
  border-color: #fff;
  background: #fff;
  /* background-image: -moz-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -webkit-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  ); */
}
.my-button[type="linear"]:hover {
  box-shadow: 0px 5px 20px 0px rgba(0, 17, 86, 0.1);
  color: inherit !important;
  text-decoration: none !important;
}
/* 英文子母转为大写 */
.en-up {
  text-transform: uppercase;
}
.en-low {
  text-transform: lowercase;
}

@media (max-width: 768px) {
  .no-select {
    justify-content: center;
  }
  .en-up {
    display: none;
  }
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

b,
.fw500 {
  font-weight: 500;
}
.fw600 {
  font-weight: 600;
}
.fw700 {
  font-weight: 700;
}

.pos-r {
  position: relative;
}
.pos-a {
  position: absolute;
}
.pos-f {
  position: fixed;
}
.pos-s {
  position: sticky;
}

/* 注册模块 */
.register-section {
  background-size: cover;
  background-repeat: no-repeat;
  padding: 48px 0;
  text-align: center;
  background-image: url(../image/register_bg.png);
}

.register-section .container > h2 {
  letter-spacing: inherit;
}
.register-section p {
  margin: 0;
  /* font-size: 18px; */
}
.register-section .my-button {
  display: inline-block;
  font-size: 15px;
  font-weight: bold;
  width: 140px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
}
.register-section .my-button.bg-white {
  background: #fff;
  color: #006cff !important;
}
.register-section .my-button.bg-white:hover {
  background-color: #e5f4ff !important;
}
@font-face {
  font-family: "BarlowSemiCondensedSemiBold";
  src: url("../font/BarlowCondensed-Medium.ttf");
}

@font-face {
  font-family: "../font/zihun105hao-jianyahei";
  src: url("../font/jianyahei.eot"); /* IE9 */
  src: url("../font/jianyahei.eot") format("embedded-opentype"),
    url("../font/jianyahei.woff2") format("woff2"),
    url("../font/jianyahei.woff") format("woff"),
    url("../font/jianyahei.ttf") format("truetype"),
    url("../font/jianyahei.svg") format("svg");
  font-style: normal;
  font-weight: normal;
}
.jianyahei {
  font-family: "zihun105hao-jianyahei", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* 箭头样式 */
.roda-arrow {
  transform: rotate(0) scale(0.8);
  font-size: 12px !important;
  transition: all 0.3s ease-out;
}

/* li */
.li-disc {
  list-style: disc;
}

/* 通用样式-end */
