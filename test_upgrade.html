<!DOCTYPE html>
<html>
<head>
    <title>测试升级页面功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>升级页面功能测试</h1>
    
    <div class="test-section">
        <h3>测试后台API</h3>
        <button onclick="testMembershipAPI()">测试会员数据API</button>
        <div id="apiResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>测试数据结构</h3>
        <button onclick="testDataStructure()">检查数据结构</button>
        <div id="dataResult" class="test-result"></div>
    </div>

    <script>
        async function testMembershipAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">正在测试API...</div>';
            
            try {
                const response = await fetch('/User.ashx?op=getmembershipdata');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ API调用成功</div>
                        <div class="info">返回数据: <pre>${JSON.stringify(data.data, null, 2)}</pre></div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ API返回错误: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ API调用失败: ${error.message}</div>`;
            }
        }

        async function testDataStructure() {
            const resultDiv = document.getElementById('dataResult');
            resultDiv.innerHTML = '<div class="info">正在检查数据结构...</div>';
            
            try {
                const response = await fetch('/User.ashx?op=getmembershipdata');
                const data = await response.json();
                
                if (data.success && data.data.membershipTypes) {
                    let results = [];
                    
                    data.data.membershipTypes.forEach((type, index) => {
                        const hasFeatures = type.coreFeatures && type.coreFeatures.length > 0;
                        const hasScenarios = type.scenarios && type.scenarios.length > 0;
                        
                        results.push(`
                            <div class="info">
                                <strong>类型 ${index + 1}: ${type.typeName} (Hash: ${type.typeHash})</strong><br>
                                核心功能: ${hasFeatures ? '✓ ' + type.coreFeatures.length + '个' : '✗ 无数据'}<br>
                                适用场景: ${hasScenarios ? '✓ ' + type.scenarios.length + '个' : '✗ 无数据'}<br>
                                ${hasFeatures ? '功能列表: ' + type.coreFeatures.map(f => f.name).join(', ') : ''}<br>
                                ${hasScenarios ? '场景列表: ' + type.scenarios.join(', ') : ''}
                            </div>
                        `);
                    });
                    
                    resultDiv.innerHTML = `
                        <div class="success">✓ 数据结构检查完成</div>
                        ${results.join('')}
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ 无法获取会员类型数据</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 数据结构检查失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
