using System;
using System.Security.Cryptography;
using System.Text;

namespace Account.Web.Common
{
    /// <summary>
    /// 统一的加密服务
    /// </summary>
    public static class CryptoService
    {
        /// <summary>
        /// 统一的MD5加密方法
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="upperCase">是否返回大写，默认小写</param>
        /// <returns>MD5哈希值</returns>
        public static string MD5Hash(string input, bool upperCase = false)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
                
            using (var md5 = MD5.Create())
            {
                byte[] data = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                var sb = new StringBuilder();
                for (int i = 0; i < data.Length; i++)
                {
                    sb.Append(data[i].ToString("x2"));
                }
                return upperCase ? sb.ToString().ToUpper() : sb.ToString();
            }
        }
        
        /// <summary>
        /// 用户密码加密（兼容现有逻辑）
        /// </summary>
        /// <param name="password">原始密码</param>
        /// <returns>加密后的密码</returns>
        public static string EncryptUserPassword(string password)
        {
            return MD5Hash(password + "OCRREG", true);
        }
        
        /// <summary>
        /// 生成用户设备UID
        /// </summary>
        /// <param name="account">账号</param>
        /// <param name="mac">MAC地址</param>
        /// <param name="token">Token</param>
        /// <returns>设备UID</returns>
        public static string GenerateDeviceUID(string account, string mac, string token)
        {
            return MD5Hash($"{account}-{mac}-{token}");
        }
        
        /// <summary>
        /// 生成验证码MD5（兼容现有逻辑）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值（小写）</returns>
        public static string GetValidateCodeMD5(string input)
        {
            return MD5Hash(input);
        }
    }
}
