﻿<%@ Page Title="用户中心" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<%@ Import Namespace="System.Linq" %>
<%@ Import Namespace="System.Data" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body{padding-top:60px!important}.user-center-wrapper{min-height:calc(100vh - 60px);background:#f8f9fa;padding:20px}.user-center-container{max-width:1400px;margin:0 auto}.main-layout{display:grid;grid-template-columns:280px 1fr;gap:20px;height:calc(100vh - 100px)}.sidebar{background:#fff;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.1);border:1px solid #e9ecef;padding:0;overflow:hidden}.sidebar-header{background:#fff;color:#333;padding:20px;text-align:center;border-bottom:1px solid #f1f3f4}.user-avatar{width:80px;height:80px;border-radius:50%;background:#f8f9fa;border:3px solid #5a67d8;overflow:hidden;margin:0 auto 15px;position:relative}.user-avatar img{width:100%;height:100%;object-fit:cover}.avatar-placeholder{width:100%;height:100%;display:flex;align-items:center;justify-content:center;font-size:28px;font-weight:600;color:#5a67d8;background:#f8f9fa}.user-name{font-size:18px;font-weight:600;margin-bottom:8px;color:#333}.user-type-row{display:flex;align-items:center;justify-content:center;gap:8px}.user-type{font-size:13px;color:#666}.upgrade-btn-small{padding:4px 8px;background:#ff6b6b;color:#fff;border:none;border-radius:12px;font-size:10px;font-weight:500;cursor:pointer;text-decoration:none;display:inline-flex;align-items:center;gap:4px;transition:all 0.2s}.upgrade-btn-small:hover{background:#ff5252;transform:translateY(-1px);color:#fff;text-decoration:none}.upgrade-btn-small i{font-size:8px}.sidebar-menu{padding:0;list-style:none;margin:0}.menu-item{border-bottom:1px solid #f1f3f4}.menu-item:last-child{border-bottom:none}.menu-link{display:flex;align-items:center;gap:12px;padding:16px 20px;color:#666;text-decoration:none;transition:all 0.2s;cursor:pointer}.menu-link:hover{background:#f8f9fa;color:#5a67d8;text-decoration:none}.menu-link.active{background:#f8f9fa;color:#333}.menu-link i{width:20px;font-size:16px}.content-area{background:#fff;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.1);border:1px solid #e9ecef;overflow:hidden;display:flex;flex-direction:column}.content-header{background:#f8f9fa;border-bottom:1px solid #e9ecef;padding:20px;display:flex;align-items:center;gap:12px}.content-title{font-size:18px;font-weight:600;color:#333;margin:0}.content-body{flex:1;padding:0;overflow-y:auto}.content-body.with-padding{padding:20px}.info-content.table-content{height:100%;display:flex;flex-direction:column}.device-table-container{width:100%;overflow-x:auto;flex:1}.device-table{width:100%;border-collapse:collapse;background:#fff;table-layout:fixed}.device-table th,.device-table td{padding:16px 16px;text-align:left;border-bottom:1px solid #e9ecef;overflow:hidden;text-overflow:ellipsis;height:56px}.device-table th{background:#f8f9fa;font-weight:600;color:#333;font-size:14px;position:sticky;top:0;z-index:10;height:48px}.device-table td{color:#666;font-size:14px;vertical-align:middle}.device-table tbody tr:hover{background:#f8f9fa}.device-table td:first-child{text-align:center}.device-table td:last-child{text-align:center}.device-table tbody tr.empty-row{background:transparent!important}.device-table tbody tr.empty-row:hover{background:transparent!important}.device-table tbody tr.empty-row td{text-align:center;padding:60px 20px;color:#999;border:none}.empty-state{text-align:center;color:#999}.empty-state i{font-size:48px;margin-bottom:16px;color:#ddd;display:block}.empty-state h3{margin:0 0 8px;color:#666;font-size:18px}.empty-state p{margin:0;font-size:14px}.action-link{background:#f8f9fa;border:1px solid #e9ecef;color:#333;cursor:pointer;font-size:12px;text-decoration:none;padding:8px 16px;border-radius:6px;transition:all 0.2s;font-weight:600;white-space:nowrap;min-width:60px;text-align:center;display:inline-block}.action-link:hover{background:#e9ecef;border-color:#ced4da;text-decoration:none;transform:translateY(-1px);box-shadow:0 2px 4px rgba(0,0,0,0.1)}.action-link.danger{background:#dc3545;color:#fff;border-color:#dc3545}.action-link.danger:hover{background:#c82333;border-color:#bd2130}.action-link.success{background:#28a745;color:#fff;border-color:#28a745}.action-link.success:hover{background:#218838;border-color:#1e7e34}.status-pending{color:#ffc107;background:#fff8e1;padding:4px 8px;border-radius:4px;font-size:12px}.status-success{color:#28a745;background:#f0fff4;padding:4px 8px;border-radius:4px;font-size:12px}.status-expired{color:#dc3545;background:#fdf2f2;padding:4px 8px;border-radius:4px;font-size:12px}.pay-wechat{color:#07c160;background:#f0f9ff;padding:4px 8px;border-radius:4px;font-size:12px}.pay-alipay{color:#1677ff;background:#f0f9ff;padding:4px 8px;border-radius:4px;font-size:12px}.info-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:24px}.info-card{background:#fff;border:1px solid #e9ecef;border-radius:12px;padding:24px;transition:all 0.2s;box-shadow:0 2px 8px rgba(0,0,0,0.05)}.info-card:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.1)}.info-card-header{display:flex;align-items:center;gap:12px;margin-bottom:20px}.info-card-icon{width:48px;height:48px;background:#f1f3f4;color:#9ca3af;border-radius:10px;display:flex;align-items:center;justify-content:center;font-size:20px}.info-card-label{font-weight:600;color:#333;font-size:16px}.info-card-value{color:#666;font-size:15px;font-weight:500;line-height:1.6;margin-bottom:20px}.info-card-actions{display:flex;gap:8px;flex-wrap:wrap}.action-btn-modern{padding:8px 16px;background:#5a67d8;color:#fff;border:none;border-radius:6px;font-size:12px;font-weight:500;cursor:pointer;transition:all 0.2s;text-decoration:none;display:inline-flex;align-items:center;gap:6px}.action-btn-modern:hover{transform:translateY(-1px);color:#fff;text-decoration:none}.action-btn-modern.warning{background:#dc3545}.action-btn-modern.success{background:#28a745}.action-btn-modern.info{background:#17a2b8}.edit-inline{background:#f8f9fa;border:1px solid #e9ecef;border-radius:6px;padding:16px;margin-top:12px}.edit-inline input{width:100%;padding:10px 12px;border:1px solid #ced4da;border-radius:4px;font-size:14px;margin-bottom:12px}.edit-inline input:focus{outline:none;border-color:#5a67d8}.edit-inline-actions{display:flex;gap:10px}.btn-save-inline,.btn-cancel-inline{padding:10px 20px;border:none;border-radius:4px;font-size:12px;cursor:pointer;font-weight:500;flex:1}.btn-save-inline{background:#28a745;color:#fff}.btn-cancel-inline{background:#6c757d;color:#fff}



        /* 表格数据样式优化 */
        .device-table td.number-cell{text-align:center;font-weight:500}.device-table td.mac-cell{font-family:monospace;font-size:13px}.device-table td.ip-cell{font-family:monospace;font-size:13px}.device-table td.time-cell{font-size:13px;color:#666}.device-table td.action-cell{text-align:center}

        /* 改善状态标签对比度 */
        .status-pending{color:#b45309;background:#fef3c7;padding:6px 12px;border-radius:6px;font-size:12px;font-weight:600;border:1px solid #f59e0b}
        .status-success{color:#065f46;background:#d1fae5;padding:6px 12px;border-radius:6px;font-size:12px;font-weight:600;border:1px solid #10b981}
        .status-expired{color:#991b1b;background:#fee2e2;padding:6px 12px;border-radius:6px;font-size:12px;font-weight:600;border:1px solid #ef4444}
        .pay-wechat{color:#065f46;background:#d1fae5;padding:6px 12px;border-radius:6px;font-size:12px;font-weight:600;border:1px solid #10b981}
        .pay-alipay{color:#1e40af;background:#dbeafe;padding:6px 12px;border-radius:6px;font-size:12px;font-weight:600;border:1px solid #3b82f6}

@media (max-width:768px){.main-layout{grid-template-columns:1fr;gap:15px;height:auto}.sidebar{margin-bottom:15px}.content-area{height:auto}.info-grid{grid-template-columns:1fr}.content-body{padding:15px}.info-card{padding:15px}.user-avatar{width:60px;height:60px}.info-card-icon{width:36px;height:36px;font-size:16px}.info-card-label{font-size:14px}}
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="user-center-wrapper">
        <%
            var session = Account.Web.Common.AuthHelper.GetUserSession(Request);
            if (session == null)
            {
                Response.Redirect("Login.aspx");
                return;
            }
        %>
        <div class="user-center-container">
            <%
                var lstAllType = CommonLib.UserTypeHelper.GetCanRegUserTypes();
                CommonLib.UserTypeEnum nowUserType = CommonLib.UserTypeEnum.体验版;
                if (session != null && !string.IsNullOrEmpty(session.UserTypeName))
                {
                    Enum.TryParse(session.UserTypeName, out nowUserType);
                }
                var nextType = lstAllType.FirstOrDefault(p => p.Type > nowUserType);
            %>
            <div class="main-layout">
                <!-- 左侧菜单 -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <div class="user-avatar">
                            <img src="https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7131.8936641364-2023-06-05-1685929337304.gif" alt="头像" style="display: block;" />
                            <div class="avatar-placeholder" style="display: none;">
                                <%=session==null?"?":session.NickName.Substring(0,1) %>
                            </div>
                        </div>
                        <div class="user-name" id="sidebarNickName"><%=session==null?"": session.NickName %></div>
                        <div class="user-type-row">
                            <span class="user-type"><%=session==null?"":session.UserTypeName %></span>
                            <% if (nextType != null) { %>
                            <a href="Upgrade.aspx?type=<%=nextType.Type.GetHashCode() %>" class="upgrade-btn-small">
                                <i class="fas fa-arrow-up"></i>
                                升级
                            </a>
                            <% } %>
                        </div>
                    </div>
                    <ul class="sidebar-menu">
                        <li class="menu-item">
                            <a class="menu-link active" onclick="showUserInfo()">
                                <i class="fas fa-user-circle"></i>
                                <span>我的信息</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link" onclick="showOrderInfo()">
                                <i class="fas fa-receipt"></i>
                                <span>我的订单</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link" onclick="showDeviceInfo()">
                                <i class="fas fa-laptop"></i>
                                <span>我的设备</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="/Detail.aspx" class="menu-link">
                                <i class="fas fa-cloud-download-alt"></i>
                                <span>下载客户端</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 右侧内容区域 -->
                <div class="content-area">
                    <div class="content-header">
                        <i class="fas fa-user" id="content-icon"></i>
                        <h2 class="content-title" id="content-title-text">我的信息</h2>
                    </div>
                    <div class="content-body with-padding">
                        <!-- 我的信息内容 -->
                        <div id="user-info-content" class="info-content">
                            <div class="info-grid">
                                <!-- 账号安全 -->
                                <div class="info-card">
                                    <div class="info-card-header">
                                        <div class="info-card-icon"><i class="fas fa-user-shield"></i></div>
                                        <div class="info-card-label">账号安全</div>
                                    </div>
                                    <div class="info-card-value">
                                        <div style="margin-bottom: 12px;"><strong>登录账号：</strong><%=session==null?"":session.Account %></div>
                                        <div style="margin-bottom: 12px;"><strong>账号状态：</strong><span style="color: #28a745;">正常</span></div>
                                        <div style="font-size: 13px; color: #718096;">最后登录：<%=session==null?"":session.DtLogin.ToString("yyyy-MM-dd HH:mm") %></div>
                                    </div>
                                    <div class="edit-inline" id="editPasswordInline" style="display: none;">
                                        <input type="password" id="password" placeholder="请输入8-16位新密码" />
                                        <div class="edit-inline-actions">
                                            <button type="button" class="btn-save-inline" onclick="savePwd()">保存密码</button>
                                            <button type="button" class="btn-cancel-inline" onclick="cancelPasswordEdit()">取消</button>
                                        </div>
                                    </div>
                                    <div class="info-card-actions">
                                        <button type="button" class="action-btn-modern" id="pwdToggleBtn" onclick="togglePasswordEdit()">
                                            <i class="fas fa-key"></i> 修改密码
                                        </button>
                                    </div>
                                </div>

                                <!-- 个人资料 -->
                                <div class="info-card">
                                    <div class="info-card-header">
                                        <div class="info-card-icon"><i class="fas fa-id-card"></i></div>
                                        <div class="info-card-label">个人资料</div>
                                    </div>
                                    <div class="info-card-value">
                                        <div style="margin-bottom: 12px;" id="displayNickName"><strong>用户昵称：</strong><%=session==null?"":session.NickName %></div>
                                        <div style="margin-bottom: 12px;"><strong>注册时间：</strong><%=session==null?"":session.DtReg.ToString("yyyy-MM-dd") %></div>
                                        <div style="font-size: 13px; color: #718096;">账号已使用 <%=session==null?0:(DateTime.Now - session.DtReg).Days %> 天</div>
                                    </div>
                                    <div class="edit-inline" id="editNickNameInline" style="display: none;">
                                        <input type="text" id="nickname" placeholder="请输入您的昵称" value="<%=session==null?"":session.NickName %>" />
                                        <div class="edit-inline-actions">
                                            <button type="button" class="btn-save-inline" onclick="saveNickName()">保存昵称</button>
                                            <button type="button" class="btn-cancel-inline" onclick="cancelEditNickName()">取消</button>
                                        </div>
                                    </div>
                                    <div class="info-card-actions">
                                        <button type="button" class="action-btn-modern" id="editNickBtn" onclick="editNickName()">
                                            <i class="fas fa-edit"></i> 编辑昵称
                                        </button>
                                    </div>
                                </div>

                                <!-- 会员服务 -->
                                <div class="info-card">
                                    <div class="info-card-header">
                                        <div class="info-card-icon"><i class="fas fa-crown"></i></div>
                                        <div class="info-card-label">会员服务</div>
                                    </div>
                                    <div class="info-card-value">
                                        <div style="margin-bottom: 12px;"><strong>当前等级：</strong><%=session==null?"":session.UserTypeName %></div>
                                        <div style="margin-bottom: 12px;">
                                            <strong>到期时间：</strong><%
                                                if (session != null) {
                                                    if (session.DtExpired.Year > 1900) {
                                                        var isExpired = session.DtExpired <= DateTime.Now;
                                                        var expiredText = isExpired ? "已过期" : session.DtExpired.ToString("yyyy-MM-dd");
                                                        var textColor = isExpired ? "color: #e53e3e;" : "color: #28a745;";
                                            %><span style="<%=textColor %>"><%=expiredText %></span><%
                                                    } else {
                                            %><span style="color: #28a745;">永久有效</span><%
                                                    }
                                                }
                                            %>
                                        </div>
                                        <div style="font-size: 13px; color: #718096;">
                                            <%
                                                if (session != null && session.DtExpired.Year > 1900 && session.DtExpired > DateTime.Now) {
                                                    var remainingDays = (session.DtExpired - DateTime.Now).Days;
                                            %>剩余 <%=remainingDays %> 天<%
                                                } else if (session != null && session.DtExpired.Year <= 1900) {
                                            %>无限制使用<%
                                                } else {
                                            %>会员已过期<%
                                                }
                                            %>
                                        </div>
                                    </div>
                                    <div class="info-card-actions">
                                        <% if (nextType != null) { %>
                                        <a href="UserUpgrade.aspx?type=<%=nextType.Type.GetHashCode() %>" target="_blank" class="action-btn-modern warning">
                                            <i class="fas fa-rocket"></i> 立即升级
                                        </a>
                                        <% } %>
                                    </div>
                                </div>

                                <!-- 系统信息 -->
                                <div class="info-card">
                                    <div class="info-card-header">
                                        <div class="info-card-icon"><i class="fas fa-info-circle"></i></div>
                                        <div class="info-card-label">系统信息</div>
                                    </div>
                                    <div class="info-card-value">
                                        <div style="margin-bottom: 12px;"><strong>服务状态：</strong><span style="color: #28a745;">正常运行</span></div>
                                        <div style="font-size: 13px; color: #718096;">系统运行稳定</div>
                                    </div>
                                    <div class="info-card-actions">
                                        <a href="/Detail.aspx" target="_blank" class="action-btn-modern info">
                                            <i class="fas fa-download"></i> 下载客户端
                                        </a>
                                        <a href="Status.aspx" target="_blank" class="action-btn-modern success">
                                            <i class="fas fa-laptop"></i> 查看服务状态
                                        </a>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>

                        <!-- 我的设备内容 -->
                        <div id="device-info-content" class="info-content table-content" style="display: none;">
                            <div class="device-table-container">
                                <table class="device-table" id="gvDataSource">
                                    <thead>
                                        <tr>
                                            <th style="width: 60px;">序号</th>
                                            <th style="width: 200px;">设备名称</th>
                                            <th style="width: 180px;">IP地址</th>
                                            <th style="width: 140px;">最后活动</th>
                                            <th style="width: 100px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="deviceTableBody">
                                        <tr class="empty-row">
                                            <td colspan="5">
                                                <div class="empty-state">
                                                    <i class="fas fa-spinner fa-spin"></i>
                                                    <h3>加载中...</h3>
                                                    <p>正在获取设备信息</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 我的订单内容 -->
                        <div id="order-info-content" class="info-content table-content" style="display: none;">
                            <div class="device-table-container">
                                <table class="device-table" id="orderTable">
                                    <thead>
                                        <tr>
                                            <th style="width: 180px;">订单号</th>
                                            <th style="width: 120px;">商品名称</th>
                                            <th style="width: 80px;">金额</th>
                                            <th style="width: 100px;">支付方式</th>
                                            <th style="width: 80px;">状态</th>
                                            <th style="width: 140px;">创建时间</th>
                                            <th style="width: 80px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="orderTableBody">
                                        <tr class="empty-row">
                                            <td colspan="7">
                                                <div class="empty-state">
                                                    <i class="fas fa-spinner fa-spin"></i>
                                                    <h3>加载中...</h3>
                                                    <p>正在获取订单信息</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="static/js/auth-common.js"></script>
    <script>
var originalNickName='<%=session==null?"":session.NickName %>';var deviceDataLoaded=false;var orderDataLoaded=false;async function ajaxRequest(url,data){try{const response=await fetch(url,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(data)});return await response.json()}catch(error){console.error('AJAX请求失败:',error);return{success:false,message:'网络请求失败，请重试'}}}

function showUserInfo(){document.getElementById('content-icon').className='fas fa-user';document.getElementById('content-title-text').textContent='我的信息';document.getElementById('user-info-content').style.display='block';document.getElementById('device-info-content').style.display='none';document.getElementById('order-info-content').style.display='none';document.querySelector('.content-body').className='content-body with-padding';updateMenuButtons('user')}function showDeviceInfo(){document.getElementById('content-icon').className='fas fa-laptop';document.getElementById('content-title-text').textContent='我的设备';document.getElementById('user-info-content').style.display='none';document.getElementById('device-info-content').style.display='block';document.getElementById('order-info-content').style.display='none';document.querySelector('.content-body').className='content-body';updateMenuButtons('device');if(!deviceDataLoaded){loadDeviceData()}}function showOrderInfo(){document.getElementById('content-icon').className='fas fa-receipt';document.getElementById('content-title-text').textContent='我的订单';document.getElementById('user-info-content').style.display='none';document.getElementById('device-info-content').style.display='none';document.getElementById('order-info-content').style.display='block';document.querySelector('.content-body').className='content-body';updateMenuButtons('order');if(!orderDataLoaded){loadOrderData()}}

function loadDeviceData(){fetch('User.ashx?op=getdevices').then(response=>response.json()).then(data=>{if(data.success){renderDeviceTable(data.data);deviceDataLoaded=true;setTimeout(loadIpInfo,100)}else{console.error('加载设备数据失败:',data.message);document.getElementById('deviceTableBody').innerHTML='<tr class="empty-row"><td colspan="5"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>'+data.message+'</p></div></td></tr>'}}).catch(error=>{console.error('加载设备数据失败:',error);document.getElementById('deviceTableBody').innerHTML='<tr class="empty-row"><td colspan="5"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>无法获取设备信息，请刷新页面重试</p></div></td></tr>'})}function renderDeviceTable(devices){const tbody=document.getElementById('deviceTableBody');if(!devices||devices.length===0){tbody.innerHTML='<tr class="empty-row"><td colspan="5"><div class="empty-state"><i class="fas fa-desktop"></i><h3>暂无设备记录</h3><p>您还没有在任何设备上使用过OCR服务</p></div></td></tr>';return}let html='';devices.forEach((device,index)=>{const actionBtn=device.state==='1'?`<button type="button" class="action-link danger" data-action="disable" data-uid="${device.uid}" onclick="deviceAction(this)">禁用</button>`:`<button type="button" class="action-link" data-action="enable" data-uid="${device.uid}" onclick="deviceAction(this)">启用</button>`;html+=`<tr><td class="number-cell">${index+1}</td><td class="mac-cell">${device.mac||'未知设备'}</td><td class="ip-cell" data-ip="${device.ip}">${device.ip||'未知IP'}</td><td class="time-cell">${device.lastActivity||'未知'}</td><td class="action-cell">${actionBtn}</td></tr>`});tbody.innerHTML=html}

function loadOrderData(){fetch('User.ashx?op=getorders').then(response=>response.json()).then(data=>{if(data.success){renderOrderTable(data.data);orderDataLoaded=true}else{console.error('加载订单数据失败:',data.message);document.getElementById('orderTableBody').innerHTML='<tr class="empty-row"><td colspan="7"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>'+data.message+'</p></div></td></tr>'}}).catch(error=>{console.error('加载订单数据失败:',error);document.getElementById('orderTableBody').innerHTML='<tr class="empty-row"><td colspan="7"><div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>加载失败</h3><p>无法获取订单信息，请刷新页面重试</p></div></td></tr>'})}function renderOrderTable(orders){const tbody=document.getElementById('orderTableBody');if(!tbody){console.error('找不到orderTableBody元素');return}if(!orders||orders.length===0){tbody.innerHTML='<tr class="empty-row"><td colspan="7"><div class="empty-state"><i class="fas fa-receipt"></i><h3>暂无订单记录</h3><p>您还没有任何订单记录</p></div></td></tr>';return}let html='';orders.forEach((order,index)=>{const statusClass=getOrderStatusClass(order.state);const payTypeClass=getPayTypeClass(order.payType);let actionBtn='';if(order.state==='待支付'){actionBtn=`<button type="button" class="action-link success" onclick="payOrder('${order.orderId}')">去支付</button>`}html+=`<tr><td style="font-size:12px;">${order.orderId||'未知订单'}</td><td>${order.remark||'无备注'}</td><td style="font-weight:600;">¥${order.reallyPrice||'0.00'}</td><td><span class="${payTypeClass}">${order.payType||'未知'}</span></td><td><span class="${statusClass}">${order.state||'未知'}</span></td><td style="font-size:12px;">${order.createDate||'未知时间'}</td><td>${actionBtn}</td></tr>`});tbody.innerHTML=html}function getOrderStatusClass(state){switch(state){case '待支付':return 'status-pending';case '支付成功':return 'status-success';case '已过期':return 'status-expired';default:return ''}}function getPayTypeClass(payType){switch(payType){case '微信':return 'pay-wechat';case '支付宝':return 'pay-alipay';default:return ''}}function payOrder(orderId){window.open('ToPay.aspx?orderId='+orderId,'_blank')}

function updateMenuButtons(activeType){var menuLinks=document.querySelectorAll('.menu-link');menuLinks.forEach(function(link){link.classList.remove('active')});if(activeType==='user'){menuLinks[0].classList.add('active')}else if(activeType==='order'){menuLinks[1].classList.add('active')}else if(activeType==='device'){menuLinks[2].classList.add('active')}}async function deviceAction(button){var action=button.getAttribute('data-action');var uid=button.getAttribute('data-uid');var confirmText=action==='enable'?'确定要启用此设备吗？':'确定要禁用此设备吗？';if(!confirm(confirmText)){return}const originalText=button.innerHTML;setButtonLoading(button,true,'处理中...');try{const result=await ajaxRequest('User.ashx?op=deviceaction',{action:action,uid:uid});if(result.success){showMessage(result.message,'success');if(action==='enable'){button.className='action-link danger';button.setAttribute('data-action','disable');button.innerHTML='禁用'}else{button.className='action-link';button.setAttribute('data-action','enable');button.innerHTML='启用'}}else{showMessage(result.message,'error');button.innerHTML=originalText}}catch(error){showMessage('操作失败，请重试','error');button.innerHTML=originalText}setButtonLoading(button,false,originalText)}

function fetchLocationInfo(ip){return new Promise((resolve,reject)=>{const xhr=new XMLHttpRequest();xhr.open('GET',"https://qifu-api.baidubce.com/ip/geo/v1/district?ip="+encodeURIComponent(ip),true);xhr.onload=()=>{if(xhr.status===200){const data=JSON.parse(xhr.responseText);resolve(data)}else{reject(xhr.statusText)}};xhr.onerror=()=>reject(xhr.statusText);xhr.send()})}function loadIpInfo(){const table=document.getElementById("gvDataSource");if(!table)return;let index=1;function processNext(){if(index<table.rows.length){try{const row=table.rows[index];const cell=row.cells[2];const ip=cell.getAttribute('data-ip')||cell.innerText;if(ip&&ip!==''&&!cell.innerText.includes('(')){fetchLocationInfo(ip).then(data=>{cell.innerText=`${ip}(${data.data.country},${data.data.prov})`;index++;setTimeout(processNext,300)}).catch(error=>{console.error('获取IP地理位置失败:',error);index++;setTimeout(processNext,500)})}else{index++;setTimeout(processNext,50)}}catch(e){index++;setTimeout(processNext,50)}}}processNext()}

document.addEventListener('DOMContentLoaded',function(){showUserInfo()});function editNickName(){document.getElementById('displayNickName').style.display='none';document.getElementById('editNickBtn').style.display='none';document.getElementById('editNickNameInline').style.display='block';document.getElementById('nickname').focus();document.getElementById('nickname').select()}function cancelEditNickName(){document.getElementById('displayNickName').style.display='block';document.getElementById('editNickBtn').style.display='block';document.getElementById('editNickNameInline').style.display='none';document.getElementById('nickname').value=originalNickName}async function saveNickName(){var newNickName=document.getElementById('nickname').value.trim();if(!validateNickname(newNickName)){showMessage('昵称格式不正确，请输入2-20位的中英文、数字或下划线','error');return}if(newNickName===originalNickName){showMessage('昵称修改成功！','success');cancelEditNickName();return}const saveBtn=document.querySelector('.btn-save-inline');const originalText=saveBtn.innerHTML;setButtonLoading(saveBtn,true,'保存中...');try{const result=await ajaxRequest('User.ashx?op=updatenickname',{nickname:newNickName});if(result.success){document.getElementById('sidebarNickName').textContent=newNickName;document.getElementById('displayNickName').innerHTML='<strong>用户昵称：</strong>'+newNickName;originalNickName=newNickName;showMessage(result.message,'success');cancelEditNickName()}else{showMessage(result.message,'error')}}catch(error){showMessage('修改失败，请重试','error')}setButtonLoading(saveBtn,false,originalText)}

function togglePasswordEdit(){var isEditing=document.getElementById('editPasswordInline').style.display==='block';if(isEditing){cancelPasswordEdit()}else{document.getElementById('editPasswordInline').style.display='block';document.getElementById('pwdToggleBtn').style.display='none';document.getElementById('password').focus()}}function cancelPasswordEdit(){document.getElementById('editPasswordInline').style.display='none';document.getElementById('pwdToggleBtn').style.display='block';document.getElementById('password').value=''}async function savePwd(){var password=document.getElementById('password').value;if(!password.trim()){showMessage('请输入新密码','error');return}if(!validatePassword(password)){showMessage('密码必须为6-16位的数字或大小写字母','error');return}if(!confirm('确定要修改密码吗？修改后需要重新登录。')){return}const saveBtn=document.querySelector('#editPasswordInline .btn-save-inline');const originalText=saveBtn.innerHTML;setButtonLoading(saveBtn,true,'修改中...');try{const result=await ajaxRequest('User.ashx?op=updatepwd',{password:password});if(result.success){showMessage(result.message,'success');setTimeout(()=>{window.location.href='Login.aspx'},2000)}else{showMessage(result.message,'error');setButtonLoading(saveBtn,false,originalText)}}catch(error){showMessage('修改失败，请重试','error');setButtonLoading(saveBtn,false,originalText)}}

document.addEventListener('keydown',function(e){if(e.key==='Escape'){if(document.getElementById('editNickNameInline').style.display==='block'){cancelEditNickName()}if(document.getElementById('editPasswordInline').style.display==='block'){cancelPasswordEdit()}}})
    </script>
</asp:Content>
