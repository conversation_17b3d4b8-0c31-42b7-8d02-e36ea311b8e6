using Account.Web.Common;

namespace Account.Web.Common
{
    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public string ErrorKey { get; set; }
    }
    
    /// <summary>
    /// 统一的验证服务
    /// </summary>
    public static class ValidationService
    {
        /// <summary>
        /// 验证登录参数
        /// </summary>
        public static ValidationResult ValidateLoginParams(string username, string password, string lang = "")
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrAccountEmpty.GetTrans(lang),
                    ErrorKey = "ACCOUNT_EMPTY"
                };
            }
            
            if (!AuthHelper.IsValidAccount(username))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrAccountFormatError.GetTrans(lang),
                    ErrorKey = "ACCOUNT_FORMAT_ERROR"
                };
            }
            
            if (!AuthHelper.IsValidPassword(password))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrPwdFormatError.GetTrans(lang),
                    ErrorKey = "PASSWORD_FORMAT_ERROR"
                };
            }
            
            return new ValidationResult { IsValid = true };
        }
        
        /// <summary>
        /// 验证注册参数
        /// </summary>
        public static ValidationResult ValidateRegisterParams(string account, string password, string verify, string nickname, string lang = "")
        {
            // 先验证基本的账号密码
            var basicValidation = ValidateLoginParams(account, password, lang);
            if (!basicValidation.IsValid)
                return basicValidation;
            
            if (!AuthHelper.IsValidVerifyCode(verify))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrValidateCodeEmpty.GetTrans(lang),
                    ErrorKey = "VERIFY_CODE_EMPTY"
                };
            }
            
            if (!AuthHelper.IsValidNickName(nickname))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrNickNameFormatError.GetTrans(lang),
                    ErrorKey = "NICKNAME_FORMAT_ERROR"
                };
            }
            
            return new ValidationResult { IsValid = true };
        }
        
        /// <summary>
        /// 验证密码重置参数（未登录用户通过验证码重置）
        /// </summary>
        public static ValidationResult ValidatePasswordResetParams(string account, string password, string verify, string lang = "")
        {
            if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrAccountEmpty.GetTrans(lang),
                    ErrorKey = "ACCOUNT_EMPTY"
                };
            }
            
            if (!AuthHelper.IsValidAccount(account))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrAccountFormatError.GetTrans(lang),
                    ErrorKey = "ACCOUNT_FORMAT_ERROR"
                };
            }
            
            if (!AuthHelper.IsValidPassword(password))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrPwdFormatError.GetTrans(lang),
                    ErrorKey = "PASSWORD_FORMAT_ERROR"
                };
            }
            
            if (!AuthHelper.IsValidVerifyCode(verify))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrValidateCodeEmpty.GetTrans(lang),
                    ErrorKey = "VERIFY_CODE_EMPTY"
                };
            }
            
            return new ValidationResult { IsValid = true };
        }
        
        /// <summary>
        /// 验证注册验证码（使用OCRREG类型）
        /// </summary>
        public static ValidationResult ValidateRegisterVerifyCode(string account, string verify, string lang = "")
        {
            var validateMd5 = CommonValidateCode.GetValidateCode(account, "OCRREG");
            if (!validateMd5.Equals(verify.ToUpper()))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = UserConst.StrValidateCodeError.GetTrans(lang),
                    ErrorKey = "VERIFY_CODE_ERROR"
                };
            }

            return new ValidationResult { IsValid = true };
        }
        
        /// <summary>
        /// 验证密码重置验证码（使用OCRREGForgetPwd类型）
        /// </summary>
        public static ValidationResult ValidatePasswordResetVerifyCode(string account, string verify, string lang = "")
        {
            var validateMd5 = CommonValidateCode.GetValidateCode(account, "OCRREGForgetPwd");
            if (!validateMd5.Equals(verify.ToUpper()))
            {
                return new ValidationResult 
                { 
                    IsValid = false, 
                    ErrorMessage = UserConst.StrValidateCodeError.GetTrans(lang),
                    ErrorKey = "VERIFY_CODE_ERROR"
                };
            }
            
            return new ValidationResult { IsValid = true };
        }
    }
}
