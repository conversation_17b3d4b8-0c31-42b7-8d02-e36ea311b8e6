﻿using System;
using System.Collections.Generic;

using System.Text;
using System.Data;
using System.Collections;

namespace ToolCommon
{
    public abstract class DataPool
    {
        private bool isWriteLog = false;

        /// <summary>
        /// 是否写日志
        /// </summary>
        public bool IsWriteLog
        {
            get { return isWriteLog; }
            set { isWriteLog = value; }
        }

        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>        
        public virtual bool ExecuteSqlTran(ArrayList SQLStringList)
        {
            return true;
        }

        /// <summary>     
        /// 判断数据库表是否存在    
        /// </summary>     
        /// <param name="tableName">表明</param>     
        public virtual bool IsTableExist(string tableName)
        {
            return true;
        }

        public virtual int ExecuteCommand(string safeSql)
        {
            return 0;
        }

        public virtual object ExecuScalar(string safeSql)
        {
            return 0;
        }

        public virtual DataTable GetTable(string safeSql)
        {
            return new DataTable();
        }

        public virtual DataSet GetDataSet(string strSql)
        {
            return new DataSet();
        }
    }
}
