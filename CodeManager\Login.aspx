﻿<%@ Page Title="用户登录" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style>
        .auth-wrapper { min-height: calc(100vh - 120px); display: flex; align-items: center; justify-content: center; padding: 40px 20px; background: url('<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner_2.jpg.webp') center/cover no-repeat fixed; }
        .auth-container { width: 100%; max-width: 400px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); padding: 40px; border: 1px solid rgba(255, 255, 255, 0.2); }
        .auth-header { text-align: center; margin-bottom: 30px; }
        .auth-title { font-size: 24px; font-weight: 600; color: #333; margin-bottom: 8px; }
        .auth-subtitle { font-size: 14px; color: #666; margin: 0; }
        .auth-form input[type="text"], .auth-form input[type="password"] { width: 100%; height: 48px; padding: 0 16px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; margin-bottom: 16px; transition: border-color 0.3s; background: #fff; color: #333; }
        .auth-form input:focus { outline: none; border-color: #1764ff; box-shadow: 0 0 0 2px rgba(23, 100, 255, 0.1); }
        .auth-form input::placeholder { color: #999; }
        .auth-checkbox { display: flex; align-items: center; margin-bottom: 24px; font-size: 14px; color: #666; }
        .auth-checkbox input { margin-right: 8px; width: 16px; height: 16px; }
        .auth-button { width: 100%; height: 48px; background: linear-gradient(135deg, #1764ff 0%, #0d47a1 100%); border: none; border-radius: 4px; color: white; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s; margin-bottom: 20px; }
        .auth-button:hover { background: linear-gradient(135deg, #0d47a1 0%, #1764ff 100%); transform: translateY(-1px); box-shadow: 0 6px 20px rgba(23, 100, 255, 0.3); }
        .auth-links { text-align: center; }
        .auth-links a { color: #1764ff; text-decoration: none; font-size: 14px; margin: 0 12px; }
        .auth-links a:hover { color: #0d47a1; text-decoration: underline; }
        @media (max-width: 480px) { .auth-wrapper { padding: 20px 15px; background-attachment: scroll; } .auth-container { padding: 30px 20px; margin: 0 10px; background: rgba(255, 255, 255, 0.98); } .auth-title { font-size: 20px; } }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="auth-wrapper">
        <div class="auth-container">
            <div class="auth-header">
                <h1 class="auth-title">用户登录</h1>
                <p class="auth-subtitle">AI智能一站式平台，专注提升生产力！</p>
            </div>
            <div class="auth-form">
                <input name="username" id="txtUsername" type="text" placeholder="手机号/邮箱" autocomplete="username" required />
                <input name="password" id="txtPassword" type="password" placeholder="密码" autocomplete="current-password" required />
                <div class="auth-checkbox">
                    <input type="checkbox" id="remember" checked="checked" />
                    <label for="remember">记住我的状态</label>
                </div>
                <button type="button" id="btnLogin" class="auth-button" data-original-text="登 录">登 录</button>
                <div class="auth-links">
                    <a href="UserForgetPwd.aspx">忘记密码</a>
                    <a href="UserReg.aspx">注册会员</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://static.geetest.com/v4/gt4.js"></script>
    <script src="static/js/auth-common.js"></script>
    <script>
        var cv=false;function validateLoginForm(){var u=document.querySelector('input[name="username"]').value.trim(),p=document.querySelector('input[name="password"]').value.trim();if(!u){showMessage('请输入用户名','error');return false;}if(!p){showMessage('请输入密码','error');return false;}if(!validateAccount(u)){showMessage('请输入正确的邮箱或手机号','error');return false;}if(!validatePassword(p)){showMessage('密码长度必须为6-15位','error');return false;}return true;}
        function submitLogin(){if(!validateLoginForm())return;var btn=document.getElementById('btnLogin');setButtonLoading(btn,true,'登录中...');var fd=new FormData();fd.append('username',document.querySelector('input[name="username"]').value.trim());fd.append('password',document.querySelector('input[name="password"]').value.trim());ajaxSubmit('User.ashx?op=login',fd,function(err,resp){setButtonLoading(btn,false);if(err){showMessage(err,'error');}else if(resp.success){showMessage(resp.message,'success');setTimeout(function(){window.location=resp.data.redirectUrl;},2000);}else{showMessage(resp.message,'error');}});}
        initGeetest4({captchaId:'e47a14e55b2db9ce50dd5afa1a9952d8',product:'bind',riskType:'nine'},function(c){c.onSuccess(function(){cv=true;if(validateLoginForm()){submitLogin();}});document.getElementById('btnLogin').addEventListener('click',function(){if(!validateLoginForm())return;if(!cv){c.showCaptcha();return;}submitLogin();});});bindEnterKey('btnLogin');
    </script>
</asp:Content>
