using System.Web;
using Newtonsoft.Json;
using CommonLib;

namespace Account.Web.Common
{
    /// <summary>
    /// 统一的响应处理服务
    /// </summary>
    public static class ResponseService
    {
        /// <summary>
        /// 初始化JSON响应
        /// </summary>
        public static void InitializeJsonResponse(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            context.Response.Clear();
        }
        
        /// <summary>
        /// 写入JSON响应并结束
        /// </summary>
        public static void WriteJsonResponse(HttpContext context, bool success, string message, object data = null)
        {
            InitializeJsonResponse(context);
            
            var response = new
            {
                success = success,
                message = message,
                data = data,
                timestamp = ServerTime.LocalTime
            };
            
            context.Response.Write(JsonConvert.SerializeObject(response));
            context.Response.End();
        }
    }
}
