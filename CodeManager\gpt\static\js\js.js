
// Copyright 2012 Google Inc. All rights reserved.
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"vtp_signal":0,"function":"__c","vtp_value":0},{"function":"__c","vtp_value":"google.cn"},{"function":"__c","vtp_value":1},{"vtp_signal":0,"function":"__c","vtp_value":0},{"function":"__c","vtp_value":"google.cn"},{"function":"__c","vtp_value":1}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":6,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":true,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_isEnabled":true,"vtp_autoAddressEnabled":true,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":10},{"function":"__ccd_ga_first","priority":5,"vtp_instanceDestinationId":"G-SQ2V73809B","tag_id":17},{"function":"__set_product_settings","priority":4,"vtp_instanceDestinationId":"G-SQ2V73809B","vtp_foreignTldMacroResult":["macro",5],"vtp_isChinaVipRegionMacroResult":["macro",6],"tag_id":16},{"function":"__ogt_google_signals","priority":3,"vtp_googleSignals":"DISABLED","vtp_instanceDestinationId":"G-SQ2V73809B","vtp_serverMacroResult":["macro",4],"tag_id":15},{"function":"__ccd_ga_regscope","priority":2,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-SQ2V73809B","tag_id":14},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-SQ2V73809B","tag_id":13},{"function":"__gct","vtp_trackingId":"G-SQ2V73809B","vtp_sessionDuration":0,"vtp_googleSignals":["macro",1],"vtp_foreignTld":["macro",2],"vtp_restrictDomain":["macro",3],"vtp_eventSettings":["map"],"tag_id":7},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-SQ2V73809B","tag_id":12}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",6]],[["if",1],["add",0,7,5,4,3,2,1]]]
},
"runtime":[[50,"__ccd_conversion_marking",[46,"a"],[50,"t",[46,"u"],[52,"v",[2,[15,"q"],"parse",[7,[15,"u"]]]],[22,[30,[30,[28,[15,"v"]],[28,[16,[15,"v"],"args"]]],[21,[17,[16,[15,"v"],"args"],"length"],2]],[46,[36]]],[52,"w",[16,[16,[16,[15,"v"],"args"],1],"contextValue"]],[22,[30,[30,[30,[28,[15,"w"]],[21,[16,[15,"w"],"namespaceType"],1]],[21,[17,[16,[15,"w"],"keyParts"],"length"],1]],[21,[16,[16,[15,"w"],"keyParts"],0],"eventName"]],[46,[36,[44]]]],[52,"x",[16,[16,[15,"v"],"args"],0]],[36,[1,[15,"x"],[16,[15,"x"],"stringValue"]]]],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"e",["require","internal.registerCcdCallback"]],[52,"f","is_conversion"],[52,"g","is_first_visit"],[52,"h","is_first_visit_conversion"],[52,"i","is_session_start"],[52,"j","is_session_start_conversion"],[52,"k","syn_or_mod"],[52,"l","first_visit"],[52,"m","session_start"],[22,[16,[15,"d"],"enableCcdGaConversions"],[46,[53,[41,"u"],[41,"v"],["e",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"w"],[22,[2,[15,"w"],"getMetadata",[7,[15,"k"]]],[46,[36]]],[52,"x",[8,"preHit",[15,"w"]]],[65,"y",[17,[15,"a"],"conversionRules"],[46,[22,["c",[17,[15,"y"],"matchingRules"],[15,"x"]],[46,[2,[15,"w"],"setMetadata",[7,[15,"f"],true]],[4]]]]],[22,[2,[15,"w"],"getMetadata",[7,[15,"g"]]],[46,[22,[28,[15,"u"]],[46,[53,[52,"y",["b",[15,"w"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"y"],"setEventName",[7,[15,"l"]]],[3,"u",[8,"preHit",[15,"y"]]]]]],[65,"y",[17,[15,"a"],"conversionRules"],[46,[22,["c",[17,[15,"y"],"matchingRules"],[15,"u"]],[46,[2,[15,"w"],"setMetadata",[7,[15,"h"],true]],[4]]]]]]],[22,[2,[15,"w"],"getMetadata",[7,[15,"i"]]],[46,[22,[28,[15,"v"]],[46,[53,[52,"y",["b",[15,"w"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"y"],"setEventName",[7,[15,"m"]]],[3,"v",[8,"preHit",[15,"y"]]]]]],[65,"y",[17,[15,"a"],"conversionRules"],[46,[22,["c",[17,[15,"y"],"matchingRules"],[15,"v"]],[46,[2,[15,"w"],"setMetadata",[7,[15,"j"],true]],[4]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"n",["require","internal.setProductSettingsParameter"]],[52,"o",["require","internal.getProductSettingsParameter"]],[52,"p",["require","getContainerVersion"]],[52,"q",["require","JSON"]],[52,"r",[30,[17,[15,"a"],"instanceDestinationId"],[17,["p"],"containerId"]]],[52,"s",[30,["o",[15,"r"],"event_settings"],[8]]],[53,[41,"u"],[3,"u",0],[63,[7,"u"],[23,[15,"u"],[17,[17,[15,"a"],"conversionRules"],"length"]],[33,[15,"u"],[3,"u",[0,[15,"u"],1]]],[46,[53,[52,"v",["t",[16,[16,[17,[15,"a"],"conversionRules"],[15,"u"]],"matchingRules"]]],[22,[28,[15,"v"]],[46,[6]]],[41,"w"],[3,"w",[16,[15,"s"],[15,"v"]]],[22,[28,[15,"w"]],[46,[3,"w",[8]],[43,[15,"s"],[15,"v"],[15,"w"]]]],[43,[15,"w"],"conversion",true]]]]],["n",[15,"r"],"event_settings",[15,"s"]],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__ccd_ga_regscope",[46,"a"],[50,"k",[46,"m"],[22,[30,[28,[15,"i"]],[21,[17,[15,"i"],"length"],2]],[46,[36,false]]],[52,"n",["l",[15,"m"]]],[53,[41,"o"],[3,"o",0],[63,[7,"o"],[23,[15,"o"],[17,[15,"n"],"length"]],[33,[15,"o"],[3,"o",[0,[15,"o"],1]]],[46,[53,[52,"p",[16,[15,"n"],[15,"o"]]],[52,"q",[17,[15,"p"],"countryCode"]],[52,"r",[17,[15,"p"],"regionCode"]],[52,"s",[20,[15,"q"],[15,"i"]]],[52,"t",[30,[28,[15,"r"]],[20,[15,"r"],[15,"j"]]]],[22,[1,[15,"s"],[15,"t"]],[46,[36,true]]]]]]],[36,false]],[50,"l",[46,"m"],[52,"n",[7]],[22,[28,[15,"m"]],[46,[36,[15,"n"]]]],[52,"o",[2,[15,"m"],"split",[7,","]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[2,[16,[15,"o"],[15,"p"]],"trim",[7]]],[22,[28,[15,"q"]],[46,[6]]],[52,"r",[2,[15,"q"],"split",[7,"-"]]],[52,"s",[16,[15,"r"],0]],[52,"t",[39,[20,[17,[15,"r"],"length"],2],[15,"q"],[44]]],[22,[30,[28,[15,"s"]],[21,[17,[15,"s"],"length"],2]],[46,[6]]],[22,[1,[21,[15,"t"],[44]],[30,[23,[17,[15,"t"],"length"],4],[18,[17,[15,"t"],"length"],6]]],[46,[6]]],[2,[15,"n"],"push",[7,[8,"countryCode",[15,"s"],"regionCode",[15,"t"]]]]]]]],[36,[15,"n"]]],[52,"b",["require","getContainerVersion"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[22,[28,[17,[15,"a"],"settingsTable"]],[46,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]],[41,"f"],[52,"g",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[52,"h",[30,[17,[15,"a"],"instanceDestinationId"],[17,["b"],"containerId"]]],[52,"i",["d"]],[52,"j",["e"]],[53,[41,"m"],[3,"m",0],[63,[7,"m"],[23,[15,"m"],[17,[17,[15,"a"],"settingsTable"],"length"]],[33,[15,"m"],[3,"m",[0,[15,"m"],1]]],[46,[53,[52,"n",[16,[17,[15,"a"],"settingsTable"],[15,"m"]]],[22,[30,[17,[15,"n"],"disallowAllRegions"],["k",[17,[15,"n"],"disallowedRegions"]]],[46,[53,[52,"o",[16,[15,"g"],[17,[15,"n"],"redactFieldGroup"]]],[22,[28,[15,"o"]],[46,[6]]],[53,[41,"p"],[3,"p",0],[63,[7,"p"],[23,[15,"p"],[17,[15,"o"],"length"]],[33,[15,"p"],[3,"p",[0,[15,"p"],1]]],[46,[53,[52,"q",[16,[15,"o"],[15,"p"]]],["c",[15,"h"],[17,[15,"q"],"name"],[17,[15,"q"],"value"]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__ogt_1p_data_v2",[46,"a"],[50,"j",[46,"l","m"],[52,"n",[16,[15,"a"],[15,"l"]]],[41,"o"],[22,[20,[15,"n"],"CSS_SELECTOR"],[46,[3,"o","css_selector"]],[46,[22,[20,[15,"n"],"JS_VAR"],[46,[3,"o","js_variable"]]]]],[36,[8,"selector_type",[15,"o"],"value",[16,[15,"a"],[15,"m"]]]]],[50,"k",[46,"l","m","n","o"],[22,[28,[16,[15,"a"],[15,"o"]]],[46,[36]]],[43,[15,"l"],[15,"m"],["j",[15,"n"],[15,"o"]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getDestinationIds"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","internal.locateUserData"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[30,["c"],[7]]],[52,"i",[8,"enable_code",true]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"l",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[41,"n"],[3,"n",0],[63,[7,"n"],[23,[15,"n"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"n"],[3,"n",[0,[15,"n"],1]]],[46,[53,[52,"o",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"n"]],"exclusionSelector"]],[22,[15,"o"],[46,[2,[15,"l"],"push",[7,[15,"o"]]]]]]]]]]],[52,"m",[39,[17,[15,"a"],"isAutoCollectPiiEnabled"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"i"],"auto_detect",[8,"email",[15,"m"],"phone",[17,[15,"a"],"autoPhoneEnabled"],"address",[17,[15,"a"],"autoAddressEnabled"],"exclude_element_selectors",[15,"l"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"l",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,["k",[15,"l"],"email","emailType","emailValue"]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,["k",[15,"l"],"phone","phoneType","phoneValue"]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"m",[8]],["k",[15,"m"],"first_name","firstNameType","firstNameValue"],["k",[15,"m"],"last_name","lastNameType","lastNameValue"],["k",[15,"m"],"street","streetType","streetValue"],["k",[15,"m"],"city","cityType","cityValue"],["k",[15,"m"],"region","regionType","regionValue"],["k",[15,"m"],"country","countryType","countryValue"],["k",[15,"m"],"postal_code","postalCodeType","postalCodeValue"],[43,[15,"l"],"name_and_address",[7,[15,"m"]]]]]],[43,[15,"i"],"selectors",[15,"l"]]]]],[65,"l",[15,"h"],[46,[53,[22,[1,[1,[28,[16,[15,"b"],"enableCcdUserData"]],[28,["d",[15,"l"],"ads_customer_data_terms"]]],[28,["d",[15,"l"],"ga_customer_data_terms"]]],[46,[6]]],[41,"m"],[3,"m",[15,"i"]],[22,[1,[20,[2,[15,"l"],"indexOf",[7,"G-"]],0],[28,[16,[15,"b"],"enableEuidAutoMode"]]],[46,[53,[52,"p",[8,"enable_code",true,"selectors",[16,[15,"i"],"selectors"]]],[3,"m",[15,"p"]]]]],["f",[15,"l"],"user_data_settings",[15,"m"]],[22,[28,[16,[15,"b"],"enableCcdUserData"]],[46,[6]]],[52,"n",[16,[15,"m"],"auto_detect"]],[22,[28,[15,"n"]],[46,[6]]],[52,"o",[51,"",[7,"p"],[52,"q",[2,[15,"p"],"getMetadata",[7,"user_data_from_automatic"]]],[22,[15,"q"],[46,[36,[15,"q"]]]],[52,"r",["e",[8,"excludeElementSelectors",[16,[15,"n"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"n"],"email"],"phone",[16,[15,"n"],"phone"],"address",[16,[15,"n"],"address"]]]]],[52,"s",[1,[15,"r"],[16,[15,"r"],"elements"]]],[52,"t",[8]],[22,[1,[15,"s"],[18,[17,[15,"s"],"length"],0]],[46,[53,[41,"u"],[3,"u",0],[63,[7,"u"],[23,[15,"u"],[17,[15,"s"],"length"]],[33,[15,"u"],[3,"u",[0,[15,"u"],1]]],[46,[53,[52,"v",[16,[15,"s"],[15,"u"]]],[22,[20,[16,[15,"v"],"type"],"email"],[46,[43,[15,"t"],"email",[16,[15,"v"],"userData"]],[4]]]]]]]]],[2,[15,"p"],"setMetadata",[7,"user_data_from_automatic",[15,"t"]]],[36,[15,"t"]]]],["g",[15,"l"],[51,"",[7,"p"],[2,[15,"p"],"setMetadata",[7,"user_data_from_automatic_getter",[15,"o"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__ogt_google_signals",[46,"a"],[52,"b",["require","internal.setProductSettingsParameter"]],[52,"c",["require","getContainerVersion"]],[52,"d",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"e",[30,[17,[15,"a"],"instanceDestinationId"],[17,["c"],"containerId"]]],["b",[15,"e"],"google_signals",[20,[17,[15,"a"],"serverMacroResult"],1]],[22,[17,[15,"d"],"enableGa4OnoRemarketing"],[46,["b",[15,"e"],"google_ono",[20,[17,[15,"a"],"serverMacroResult"],2]]]],[2,[15,"a"],"gtmOnSuccess",[7]]],[50,"__set_product_settings",[46,"a"],[52,"b",["require","internal.setProductSettingsParameter"]],[52,"c",["require","getContainerVersion"]],[52,"d",[30,[17,[15,"a"],"instanceDestinationId"],[17,["c"],"containerId"]]],["b",[15,"d"],"google_tld",[17,[15,"a"],"foreignTldMacroResult"]],["b",[15,"d"],"ga_restrict_domain",[20,[17,[15,"a"],"isChinaVipRegionMacroResult"],1]],[2,[15,"a"],"gtmOnSuccess",[7]]]]
,"permissions":{"__ccd_conversion_marking":{"read_container_data":{}},"__ccd_ga_first":{},"__ccd_ga_last":{},"__ccd_ga_regscope":{"read_container_data":{}},"__ogt_1p_data_v2":{"read_dom_elements":{"selectors":[{"type":"css","value":"*"}]},"access_dom_element_property":{"properties":[{"property":"textContent","read":true,"write":false},{"property":"value","read":true,"write":false},{"property":"tagName","read":true,"write":false},{"property":"children","read":true,"write":false},{"property":"childElementCount","read":true,"write":false}]}},"__ogt_google_signals":{"read_container_data":{}},"__set_product_settings":{"read_container_data":{}}}

,"security_groups":{
"google":["__ccd_conversion_marking","__ccd_ga_first","__ccd_ga_last","__ccd_ga_regscope","__ogt_1p_data_v2","__ogt_google_signals","__set_product_settings"]}
,"infra":["__ccd_conversion_marking","__ccd_ga_first","__ccd_ga_last","__ccd_ga_regscope","__ogt_1p_data_v2","__ogt_google_signals","__set_product_settings"]

};


/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var da,ea=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},fa=function(a){return a.raw=a},ha="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ia;
if("function"==typeof Object.setPrototypeOf)ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},ma={};try{ma.__proto__=ka;ja=ma.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var na=ia,pa=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ll=b.prototype},qa=this||self,ra=function(a){return a};var sa=function(a,b){this.h=a;this.B=b};var ta=function(a){return"number"===typeof a&&0<=a&&isFinite(a)&&0===a%1||"string"===typeof a&&"-"!==a[0]&&a===""+parseInt(a,10)},ua=function(){this.D={};this.F=!1;this.N={}},va=function(a,b){var c=[],d;for(d in a.D)if(a.D.hasOwnProperty(d))switch(d=d.substr(5),b){case 1:c.push(d);break;case 2:c.push(a.get(d));break;case 3:c.push([d,a.get(d)])}return c};ua.prototype.get=function(a){return this.D["dust."+a]};ua.prototype.set=function(a,b){this.F||(a="dust."+a,this.N.hasOwnProperty(a)||(this.D[a]=b))};
ua.prototype.has=function(a){return this.D.hasOwnProperty("dust."+a)};var wa=function(a,b){b="dust."+b;a.F||a.N.hasOwnProperty(b)||delete a.D[b]};ua.prototype.Qb=function(){this.F=!0};ua.prototype.He=function(){return this.F};var xa=function(a){this.B=new ua;this.h=[];this.D=!1;a=a||[];for(var b in a)a.hasOwnProperty(b)&&(ta(b)?this.h[Number(b)]=a[Number(b)]:this.B.set(b,a[b]))};da=xa.prototype;da.toString=function(a){if(a&&0<=a.indexOf(this))return"";for(var b=[],c=0;c<this.h.length;c++){var d=this.h[c];null===d||void 0===d?b.push(""):d instanceof xa?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(d.toString())}return b.join(",")};
da.set=function(a,b){if(!this.D)if("length"===a){if(!ta(b))throw Error("RangeError: Length property must be a valid integer.");this.h.length=Number(b)}else ta(a)?this.h[Number(a)]=b:this.B.set(a,b)};da.get=function(a){return"length"===a?this.length():ta(a)?this.h[Number(a)]:this.B.get(a)};da.length=function(){return this.h.length};da.Pb=function(){for(var a=va(this.B,1),b=0;b<this.h.length;b++)a.push(b+"");return new xa(a)};var ya=function(a,b){ta(b)?delete a.h[Number(b)]:wa(a.B,b)};da=xa.prototype;
da.pop=function(){return this.h.pop()};da.push=function(a){return this.h.push.apply(this.h,Array.prototype.slice.call(arguments))};da.shift=function(){return this.h.shift()};da.splice=function(a,b,c){return new xa(this.h.splice.apply(this.h,arguments))};da.unshift=function(a){return this.h.unshift.apply(this.h,Array.prototype.slice.call(arguments))};da.has=function(a){return ta(a)&&this.h.hasOwnProperty(a)||this.B.has(a)};da.Qb=function(){this.D=!0;Object.freeze(this.h);this.B.Qb()};da.He=function(){return this.D};var za=function(){this.quota={}};za.prototype.reset=function(){this.quota={}};var Aa=function(a,b){this.U=a;this.N=function(c,d,e){return c.apply(d,e)};this.D=b;this.B=new ua;this.h=this.F=void 0};Aa.prototype.add=function(a,b){Ba(this,a,b,!1)};var Ba=function(a,b,c,d){if(!a.B.He())if(d){var e=a.B;e.set(b,c);e.N["dust."+b]=!0}else a.B.set(b,c)};Aa.prototype.set=function(a,b){this.B.He()||(!this.B.has(a)&&this.D&&this.D.has(a)?this.D.set(a,b):this.B.set(a,b))};Aa.prototype.get=function(a){return this.B.has(a)?this.B.get(a):this.D?this.D.get(a):void 0};
Aa.prototype.has=function(a){return!!this.B.has(a)||!(!this.D||!this.D.has(a))};var Ca=function(a){var b=new Aa(a.U,a);a.F&&(b.F=a.F);b.N=a.N;b.h=a.h;return b};var Da=function(){},Ea=function(a){return"function"===typeof a},k=function(a){return"string"===typeof a},Ga=function(a){return"number"===typeof a&&!isNaN(a)},Ia=Array.isArray,Ka=function(a,b){if(a&&Ia(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]},La=function(a,b){if(!Ga(a)||!Ga(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)},Na=function(a,b){for(var c=new Ma,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1},m=function(a,
b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])},Oa=function(a){return!!a&&("[object Arguments]"===Object.prototype.toString.call(a)||Object.prototype.hasOwnProperty.call(a,"callee"))},Pa=function(a){return Math.round(Number(a))||0},Qa=function(a){return"false"===String(a).toLowerCase()?!1:!!a},Ra=function(a){var b=[];if(Ia(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b},Sa=function(a){return a?a.replace(/^\s+|\s+$/g,""):""},Ta=function(){return new Date(Date.now())},
Ua=function(){return Ta().getTime()},Ma=function(){this.prefix="gtm.";this.values={}};Ma.prototype.set=function(a,b){this.values[this.prefix+a]=b};Ma.prototype.get=function(a){return this.values[this.prefix+a]};
var Va=function(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c},Xa=function(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}},Ya=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])},Za=function(a){for(var b in a)if(a.hasOwnProperty(b))return!0;return!1},$a=function(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c},bb=function(a,b){var c=z;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(0<=
b.indexOf(d))return}return d},cb=function(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c},db=/^\w{1,9}$/,eb=function(a,b){a=a||{};b=b||",";var c=[];m(a,function(d,e){db.test(d)&&e&&c.push(d)});return c.join(b)},fb=function(a,b){function c(){++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c};var gb=function(a,b){ua.call(this);this.U=a;this.eb=b};pa(gb,ua);gb.prototype.toString=function(){return this.U};gb.prototype.Pb=function(){return new xa(va(this,1))};gb.prototype.h=function(a,b){return this.eb.apply(new hb(this,a),Array.prototype.slice.call(arguments,1))};gb.prototype.B=function(a,b){try{return this.h.apply(this,Array.prototype.slice.call(arguments,0))}catch(c){}};
var jb=function(a,b){for(var c,d=0;d<b.length&&!(c=ib(a,b[d]),c instanceof sa);d++);return c},ib=function(a,b){try{var c=a.get(String(b[0]));if(!(c&&c instanceof gb))throw Error("Attempting to execute non-function "+b[0]+".");return c.h.apply(c,[a].concat(b.slice(1)))}catch(e){var d=a.F;d&&d(e,b.context?{id:b[0],line:b.context.line}:null);throw e;}},hb=function(a,b){this.B=a;this.h=b},E=function(a,b){return Ia(b)?ib(a.h,b):b},F=function(a){return a.B.U};var kb=function(){ua.call(this)};pa(kb,ua);kb.prototype.Pb=function(){return new xa(va(this,1))};var lb={map:function(a){for(var b=new kb,c=0;c<arguments.length-1;c+=2){var d=E(this,arguments[c])+"",e=E(this,arguments[c+1]);b.set(d,e)}return b},list:function(a){for(var b=new xa,c=0;c<arguments.length;c++){var d=E(this,arguments[c]);b.push(d)}return b},fn:function(a,b,c){var d=this.h,e=E(this,b);if(!(e instanceof xa))throw Error("Error: non-List value given for Fn argument names.");var f=Array.prototype.slice.call(arguments,2);return new gb(a,function(){return function(g){var h=Ca(d);void 0===
h.h&&(h.h=this.h.h);for(var l=Array.prototype.slice.call(arguments,0),n=0;n<l.length;n++)if(l[n]=E(this,l[n]),l[n]instanceof sa)return l[n];for(var p=e.get("length"),q=0;q<p;q++)q<l.length?h.add(e.get(q),l[q]):h.add(e.get(q),void 0);h.add("arguments",new xa(l));var r=jb(h,f);if(r instanceof sa)return"return"===r.h?r.B:r}}())},control:function(a,b){return new sa(a,E(this,b))},undefined:function(){}};var mb=function(){this.D=new za;this.h=new Aa(this.D)},nb=function(a,b,c){var d=new gb(b,c);d.Qb();a.h.set(b,d)},ob=function(a,b,c){lb.hasOwnProperty(b)&&nb(a,c||b,lb[b])};mb.prototype.execute=function(a,b){var c=Array.prototype.slice.call(arguments,0);return this.B(c)};mb.prototype.B=function(a){for(var b,c=0;c<arguments.length;c++)b=ib(this.h,arguments[c]);return b};mb.prototype.F=function(a,b){var c=Ca(this.h);c.h=a;for(var d,e=1;e<arguments.length;e++)d=ib(c,arguments[e]);return d};function pb(){for(var a=qb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function rb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var qb,sb;function tb(a){qb=qb||rb();sb=sb||pb();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,l=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(qb[l],qb[n],qb[p],qb[q])}return b.join("")}
function ub(a){function b(l){for(;d<a.length;){var n=a.charAt(d++),p=sb[n];if(null!=p)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return l}qb=qb||rb();sb=sb||pb();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(64===h&&-1===e)return c;c+=String.fromCharCode(e<<2|f>>4);64!=g&&(c+=String.fromCharCode(f<<4&240|g>>2),64!=h&&(c+=String.fromCharCode(g<<6&192|h)))}};var vb={},wb=function(a,b){vb[a]=vb[a]||[];vb[a][b]=!0},xb=function(){delete vb.GA4_EVENT},yb=function(a){var b=vb[a];if(!b||0===b.length)return"";for(var c=[],d=0,e=0;e<b.length;e++)0===e%8&&0<e&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);0<d&&c.push(String.fromCharCode(d));return tb(c.join("")).replace(/\.+$/,"")};var zb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};var Ab,Bb=function(){if(void 0===Ab){var a=null,b=qa.trustedTypes;if(b&&b.createPolicy){try{a=b.createPolicy("goog#html",{createHTML:ra,createScript:ra,createScriptURL:ra})}catch(c){qa.console&&qa.console.error(c.message)}Ab=a}else Ab=a}return Ab};var Db=function(a,b){this.h=b===Cb?a:""};Db.prototype.toString=function(){return this.h+""};var Eb=function(a){return a instanceof Db&&a.constructor===Db?a.h:"type_error:TrustedResourceUrl"},Cb={},Fb=function(a){var b=a,c=Bb(),d=c?c.createScriptURL(b):b;return new Db(d,Cb)};var Gb=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;function Hb(){var a=qa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}function Ib(a){return-1!=Hb().indexOf(a)};function Jb(){return Ib("Firefox")||Ib("FxiOS")}function Kb(){return(Ib("Chrome")||Ib("CriOS"))&&!Ib("Edge")||Ib("Silk")};var Lb={},Mb=function(a,b){this.h=b===Lb?a:""};Mb.prototype.toString=function(){return this.h.toString()};/*

 SPDX-License-Identifier: Apache-2.0
*/
var Nb={};var Ob=function(){},Pb=function(a){this.h=a};pa(Pb,Ob);Pb.prototype.toString=function(){return this.h};function Qb(a,b){if(void 0!==a.tagName){if("script"===a.tagName.toLowerCase())throw Error("");if("style"===a.tagName.toLowerCase())throw Error("");}a.innerHTML=b instanceof Mb&&b.constructor===Mb?b.h:"type_error:SafeHtml"}
function Rb(a,b){var c=[new Pb(Sb[0].toLowerCase(),Nb)];if(0===c.length)throw Error("");var d=c.map(function(f){var g;if(f instanceof Pb)g=f.h;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return 0!==e.indexOf(f)}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};function Tb(a){var b=a=Ub(a),c=Bb(),d=c?c.createHTML(b):b;return new Mb(d,Lb)}function Ub(a){return null===a?"null":void 0===a?"undefined":a};var Vb={},z=window,I=document,Wb=navigator,Xb=I.currentScript&&I.currentScript.src,Yb=function(a,b){var c=z[a];z[a]=void 0===c?b:c;return z[a]},Zb=function(a,b){b&&(a.addEventListener?a.onload=b:a.onreadystatechange=function(){a.readyState in{loaded:1,complete:1}&&(a.onreadystatechange=null,b())})},$b={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},ac={onload:1,src:1,width:1,height:1,style:1};function bc(a,b,c){b&&m(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
var cc=function(a,b,c,d,e){var f=I.createElement("script");bc(f,d,$b);f.type="text/javascript";f.async=!0;var g;g=Fb(Ub(a));f.src=Eb(g);var h,l,n,p=null==(n=(l=(f.ownerDocument&&f.ownerDocument.defaultView||window).document).querySelector)?void 0:n.call(l,"script[nonce]");(h=p?p.nonce||p.getAttribute("nonce")||"":"")&&f.setAttribute("nonce",h);Zb(f,b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var q=I.getElementsByTagName("script")[0]||I.body||I.head;q.parentNode.insertBefore(f,q)}return f},dc=function(){if(Xb){var a=
Xb.toLowerCase();if(0===a.indexOf("https://"))return 2;if(0===a.indexOf("http://"))return 3}return 1},ec=function(a,b,c,d,e){var f;f=void 0===f?!0:f;var g=e,h=!1;g||(g=I.createElement("iframe"),h=!0);bc(g,c,ac);d&&m(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");if(h){var l=I.body&&I.body.lastChild||I.body||I.head;l.parentNode.insertBefore(g,l)}Zb(g,b);void 0!==a&&(g.src=a);return g},fc=function(a,b,c){var d=new Image(1,1);d.onload=
function(){d.onload=null;b&&b()};d.onerror=function(){d.onerror=null;c&&c()};d.src=a},gc=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,!!d):a.attachEvent&&a.attachEvent("on"+b,c)},hc=function(a,b,c){a.removeEventListener?a.removeEventListener(b,c,!1):a.detachEvent&&a.detachEvent("on"+b,c)},J=function(a){z.setTimeout(a,0)},ic=function(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null},jc=function(a){var b=a.innerText||a.textContent||"";b&&" "!=b&&(b=b.replace(/^[\s\xa0]+|[\s\xa0]+$/g,
""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b},kc=function(a){var b=I.createElement("div");Qb(b,Tb("A<div>"+a+"</div>"));b=b.lastChild;for(var c=[];b.firstChild;)c.push(b.removeChild(b.firstChild));return c},lc=function(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null},mc=function(a){var b;try{b=Wb.sendBeacon&&Wb.sendBeacon(a)}catch(c){wb("TAGGING",15)}b||fc(a)},nc=function(a,
b){var c=a[b];c&&"string"===typeof c.animVal&&(c=c.animVal);return c},oc=function(){var a=z.performance;if(a&&Ea(a.now))return a.now()},pc=function(){return z.performance||void 0};var qc=function(a,b){return E(this,a)&&E(this,b)},rc=function(a,b){return E(this,a)===E(this,b)},sc=function(a,b){return E(this,a)||E(this,b)},tc=function(a,b){a=E(this,a);b=E(this,b);return-1<String(a).indexOf(String(b))},uc=function(a,b){a=String(E(this,a));b=String(E(this,b));return a.substring(0,b.length)===b},vc=function(a,b){a=E(this,a);b=E(this,b);switch(a){case "pageLocation":var c=z.location.href;b instanceof kb&&b.get("stripProtocol")&&(c=c.replace(/^https?:\/\//,""));return c}};var yc=function(){this.h=new mb;wc(this)};yc.prototype.execute=function(a){return this.h.B(a)};var wc=function(a){ob(a.h,"map");var b=function(c,d){nb(a.h,c,d)};b("and",qc);b("contains",tc);b("equals",rc);b("or",sc);b("startsWith",uc);b("variable",vc)};var zc=function(a){if(a instanceof zc)return a;this.Da=a};zc.prototype.toString=function(){return String(this.Da)};var Bc=function(a){ua.call(this);this.h=a;this.set("then",Ac(this));this.set("catch",Ac(this,!0));this.set("finally",Ac(this,!1,!0))};pa(Bc,kb);var Ac=function(a,b,c){b=void 0===b?!1:b;c=void 0===c?!1:c;return new gb("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof gb||(d=void 0);e instanceof gb||(e=void 0);var f=Ca(this.h),g=function(l){return function(n){return c?(l.h(f),a.h):l.h(f,n)}},h=a.h.then(d&&g(d),e&&g(e));return new Bc(h)})};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license. */
var Cc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Dc=function(a){if(null==a)return String(a);var b=Cc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Ec=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Fc=function(a){if(!a||"object"!=Dc(a)||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Ec(a,"constructor")&&!Ec(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return void 0===
b||Ec(a,b)},K=function(a,b){var c=b||("array"==Dc(a)?[]:{}),d;for(d in a)if(Ec(a,d)){var e=a[d];"array"==Dc(e)?("array"!=Dc(c[d])&&(c[d]=[]),c[d]=K(e,c[d])):Fc(e)?(Fc(c[d])||(c[d]={}),c[d]=K(e,c[d])):c[d]=e}return c};var Hc=function(a,b,c){var d=[],e=[],f=function(h,l){for(var n=va(h,1),p=0;p<n.length;p++)l[n[p]]=g(h.get(n[p]))},g=function(h){var l=d.indexOf(h);if(-1<l)return e[l];if(h instanceof xa){var n=[];d.push(h);e.push(n);for(var p=h.Pb(),q=0;q<p.length();q++)n[p.get(q)]=g(h.get(p.get(q)));return n}if(h instanceof Bc)return h.h;if(h instanceof kb){var r={};d.push(h);e.push(r);f(h,r);return r}if(h instanceof gb){var u=function(){for(var v=Array.prototype.slice.call(arguments,0),w=0;w<v.length;w++)v[w]=Gc(v[w],
b,c);var y=new Aa(b?b.U:new za);b&&(y.h=b.h);return g(h.h.apply(h,[y].concat(v)))};d.push(h);e.push(u);f(h,u);return u}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(h instanceof zc&&t)return h.Da;switch(typeof h){case "boolean":case "number":case "string":case "undefined":return h;case "object":if(null===h)return null}};return g(a)},Gc=function(a,
b,c){var d=[],e=[],f=function(h,l){for(var n in h)h.hasOwnProperty(n)&&l.set(n,g(h[n]))},g=function(h){var l=d.indexOf(h);if(-1<l)return e[l];if(Ia(h)||Oa(h)){var n=new xa([]);d.push(h);e.push(n);for(var p in h)h.hasOwnProperty(p)&&n.set(p,g(h[p]));return n}if(Fc(h)){var q=new kb;d.push(h);e.push(q);f(h,q);return q}if("function"===typeof h){var r=new gb("",function(x){for(var A=Array.prototype.slice.call(arguments,0),B=0;B<A.length;B++)A[B]=Hc(E(this,A[B]),b,c);return g((0,this.h.N)(h,h,A))});d.push(h);
e.push(r);f(h,r);return r}var w=typeof h;if(null===h||"string"===w||"number"===w||"boolean"===w)return h;var y=!1;switch(c){case 1:y=
!0;break;case 2:y=!1;break;default:}if(void 0!==h&&y)return new zc(h)};return g(a)};var Ic=function(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b},Jc=function(a){if(void 0===a||Ia(a)||Fc(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1};var Kc={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));for(var e=1;e<arguments.length;e++)if(arguments[e]instanceof xa)for(var f=arguments[e],g=0;g<f.length();g++)c.push(f.get(g));else c.push(arguments[e]);return new xa(c)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.h(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.h(a,this.get(e),e,this)&&d.push(this.get(e));return new xa(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.h(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=void 0===c?0:Number(c);0>e&&(e=Math.max(d+e,0));for(var f=e;f<d;f++)if(this.has(f)&&
this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;void 0!==c&&(e=0>c?d+c:Math.min(c,e));for(var f=e;0<=f;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.h(a,this.get(e),e,this));return new xa(d)},pop:function(){return this.pop()},push:function(a,b){return this.push.apply(this,
Array.prototype.slice.call(arguments,1))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(void 0!==c)e=c;else{if(0===d)throw Error("TypeError: Reduce on List with no elements.");for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Error("TypeError: Reduce on List with no elements.");}for(var h=f;h<d;h++)this.has(h)&&(e=b.h(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(void 0!==c)e=c;else{if(0===d)throw Error("TypeError: ReduceRight on List with no elements.");
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Error("TypeError: ReduceRight on List with no elements.");}for(var h=f;0<=h;h--)this.has(h)&&(e=b.h(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=Ic(this),b=a.length-1,c=0;0<=b;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):ya(this,c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();void 0===b&&(b=0);b=0>b?Math.max(d+b,0):Math.min(b,d);c=void 0===c?d:0>c?
Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new xa(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.h(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=Ic(this);void 0===b?c.sort():c.sort(function(e,f){return Number(b.h(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):ya(this,d);return this},splice:function(a,b,c,d){return this.splice.apply(this,Array.prototype.splice.call(arguments,
1,arguments.length-1))},toString:function(){return this.toString()},unshift:function(a,b){return this.unshift.apply(this,Array.prototype.slice.call(arguments,1))}};var Lc="charAt concat indexOf lastIndexOf match replace search slice split substring toLowerCase toLocaleLowerCase toString toUpperCase toLocaleUpperCase trim".split(" "),Mc=new sa("break"),Nc=new sa("continue"),Oc=function(a,b){return E(this,a)+E(this,b)},Pc=function(a,b){return E(this,a)&&E(this,b)},Qc=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);if(!(c instanceof xa))throw Error("Error: Non-List argument given to Apply instruction.");if(null===a||void 0===a)throw Error("TypeError: Can't read property "+
b+" of "+a+".");var d="number"===typeof a;if("boolean"===typeof a||d){if("toString"===b){if(d&&c.length()){var e=Hc(c.get(0));try{return a.toString(e)}catch(q){}}return a.toString()}throw Error("TypeError: "+a+"."+b+" is not a function.");}if("string"===typeof a){if(0<=Lc.indexOf(b)){var f=Hc(c);return Gc(a[b].apply(a,f),this.h)}throw Error("TypeError: "+b+" is not a function");}if(a instanceof xa){if(a.has(b)){var g=a.get(b);if(g instanceof gb){var h=Ic(c);h.unshift(this.h);return g.h.apply(g,h)}throw Error("TypeError: "+
b+" is not a function");}if(0<=Kc.supportedMethods.indexOf(b)){var l=Ic(c);l.unshift(this.h);return Kc[b].apply(a,l)}}if(a instanceof gb||a instanceof kb){if(a.has(b)){var n=a.get(b);if(n instanceof gb){var p=Ic(c);p.unshift(this.h);return n.h.apply(n,p)}throw Error("TypeError: "+b+" is not a function");}if("toString"===b)return a instanceof gb?a.U:a.toString();if("hasOwnProperty"===b)return a.has.apply(a,Ic(c))}if(a instanceof zc&&"toString"===b)return a.toString();throw Error("TypeError: Object has no '"+
b+"' property.");},Rc=function(a,b){a=E(this,a);if("string"!==typeof a)throw Error("Invalid key name given for assignment.");var c=this.h;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=E(this,b);c.set(a,d);return d},Sc=function(a){var b=Ca(this.h),c=jb(b,Array.prototype.slice.apply(arguments));if(c instanceof sa)return c},Tc=function(){return Mc},Uc=function(a){for(var b=E(this,a),c=0;c<b.length;c++){var d=E(this,b[c]);if(d instanceof sa)return d}},Vc=function(a){for(var b=
this.h,c=0;c<arguments.length-1;c+=2){var d=arguments[c];if("string"===typeof d){var e=E(this,arguments[c+1]);Ba(b,d,e,!0)}}},Wc=function(){return Nc},Xc=function(a,b,c){var d=new xa;b=E(this,b);for(var e=0;e<b.length;e++)d.push(b[e]);var f=[51,a,d].concat(Array.prototype.splice.call(arguments,2,arguments.length-2));this.h.add(a,E(this,f))},Yc=function(a,b){return E(this,a)/E(this,b)},Zc=function(a,b){a=E(this,a);b=E(this,b);var c=a instanceof zc,d=b instanceof zc;return c||d?c&&d?a.Da==b.Da:!1:a==
b},$c=function(a){for(var b,c=0;c<arguments.length;c++)b=E(this,arguments[c]);return b};function ad(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=jb(f,d);if(g instanceof sa){if("break"===g.h)break;if("return"===g.h)return g}}}function bd(a,b,c){if("string"===typeof b)return ad(a,function(){return b.length},function(f){return f},c);if(b instanceof kb||b instanceof xa||b instanceof gb){var d=b.Pb(),e=d.length();return ad(a,function(){return e},function(f){return d.get(f)},c)}}
var cd=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return bd(function(e){d.set(a,e);return d},b,c)},dd=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return bd(function(e){var f=Ca(d);Ba(f,a,e,!0);return f},b,c)},ed=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return bd(function(e){var f=Ca(d);f.add(a,e);return f},b,c)},gd=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return fd(function(e){d.set(a,e);return d},b,c)},hd=
function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return fd(function(e){var f=Ca(d);Ba(f,a,e,!0);return f},b,c)},id=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);var d=this.h;return fd(function(e){var f=Ca(d);f.add(a,e);return f},b,c)};
function fd(a,b,c){if("string"===typeof b)return ad(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof xa)return ad(a,function(){return b.length()},function(d){return b.get(d)},c);throw new TypeError("The value is not iterable.");}
var jd=function(a,b,c,d){function e(p,q){for(var r=0;r<f.length();r++){var u=f.get(r);q.add(u,p.get(u))}}var f=E(this,a);if(!(f instanceof xa))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.h;d=E(this,d);var h=Ca(g);for(e(g,h);ib(h,b);){var l=jb(h,d);if(l instanceof sa){if("break"===l.h)break;if("return"===l.h)return l}var n=Ca(g);e(h,n);ib(n,c);h=n}},kd=function(a){a=E(this,a);var b=this.h,c=!1;if(c&&!b.has(a))throw new ReferenceError(a+" is not defined.");return b.get(a)},ld=function(a,b){var c;a=E(this,a);b=E(this,b);if(void 0===a||null===a)throw Error("TypeError: cannot access property of "+a+".");if(a instanceof kb||a instanceof xa||a instanceof gb)c=a.get(b);else if("string"===typeof a)"length"===b?c=a.length:ta(b)&&(c=a[b]);else if(a instanceof zc)return;return c},md=function(a,b){return E(this,a)>E(this,
b)},nd=function(a,b){return E(this,a)>=E(this,b)},od=function(a,b){a=E(this,a);b=E(this,b);a instanceof zc&&(a=a.Da);b instanceof zc&&(b=b.Da);return a===b},pd=function(a,b){return!od.call(this,a,b)},qd=function(a,b,c){var d=[];E(this,a)?d=E(this,b):c&&(d=E(this,c));var e=jb(this.h,d);if(e instanceof sa)return e},rd=function(a,b){return E(this,a)<E(this,b)},sd=function(a,b){return E(this,a)<=E(this,b)},td=function(a,b){return E(this,a)%E(this,b)},ud=function(a,b){return E(this,a)*E(this,b)},vd=function(a){return-E(this,
a)},wd=function(a){return!E(this,a)},xd=function(a,b){return!Zc.call(this,a,b)},yd=function(){return null},zd=function(a,b){return E(this,a)||E(this,b)},Ad=function(a,b){var c=E(this,a);E(this,b);return c},Bd=function(a){return E(this,a)},Cd=function(a){return Array.prototype.slice.apply(arguments)},Dd=function(a){return new sa("return",E(this,a))},Ed=function(a,b,c){a=E(this,a);b=E(this,b);c=E(this,c);if(null===a||void 0===a)throw Error("TypeError: Can't set property "+b+" of "+a+".");(a instanceof
gb||a instanceof xa||a instanceof kb)&&a.set(b,c);return c},Fd=function(a,b){return E(this,a)-E(this,b)},Gd=function(a,b,c){a=E(this,a);var d=E(this,b),e=E(this,c);if(!Ia(d)||!Ia(e))throw Error("Error: Malformed switch instruction.");for(var f,g=!1,h=0;h<d.length;h++)if(g||a===E(this,d[h]))if(f=E(this,e[h]),f instanceof sa){var l=f.h;if("break"===l)return;if("return"===l||"continue"===l)return f}else g=!0;if(e.length===d.length+1&&(f=E(this,e[e.length-1]),f instanceof sa&&("return"===f.h||"continue"===
f.h)))return f},Hd=function(a,b,c){return E(this,a)?E(this,b):E(this,c)},Id=function(a){a=E(this,a);return a instanceof gb?"function":typeof a},Jd=function(a){for(var b=this.h,c=0;c<arguments.length;c++){var d=arguments[c];"string"!==typeof d||b.add(d,void 0)}},Kd=function(a,b,c,d){var e=E(this,d);if(E(this,c)){var f=jb(this.h,e);if(f instanceof sa){if("break"===f.h)return;if("return"===f.h)return f}}for(;E(this,a);){var g=jb(this.h,e);if(g instanceof sa){if("break"===g.h)break;if("return"===g.h)return g}E(this,
b)}},Ld=function(a){return~Number(E(this,a))},Pd=function(a,b){return Number(E(this,a))<<Number(E(this,b))},Qd=function(a,b){return Number(E(this,a))>>Number(E(this,b))},Rd=function(a,b){return Number(E(this,a))>>>Number(E(this,b))},Sd=function(a,b){return Number(E(this,a))&Number(E(this,b))},Td=function(a,b){return Number(E(this,a))^Number(E(this,b))},Ud=function(a,b){return Number(E(this,a))|Number(E(this,b))};var Wd=function(){this.h=new mb;Vd(this)};Wd.prototype.execute=function(a){return Xd(this.h.B(a))};
var Yd=function(a,b,c){return Xd(a.h.F(b,c))},Vd=function(a){var b=function(d,e){ob(a.h,d,String(e))};b("control",49);b("fn",51);b("list",7);b("map",8);b("undefined",44);var c=function(d,e){nb(a.h,String(d),e)};c(0,Oc);c(1,Pc);c(2,Qc);c(3,Rc);c(53,Sc);c(4,Tc);c(5,Uc);c(52,Vc);c(6,Wc);c(9,Uc);c(50,Xc);c(10,Yc);c(12,Zc);c(13,$c);c(47,cd);c(54,dd);c(55,ed);c(63,jd);c(64,gd);c(65,hd);c(66,id);c(15,kd);c(16,ld);c(17,ld);c(18,md);c(19,nd);c(20,od);c(21,pd);c(22,qd);c(23,rd);c(24,sd);c(25,td);c(26,ud);c(27,
vd);c(28,wd);c(29,xd);c(45,yd);c(30,zd);c(32,Ad);c(33,Ad);c(34,Bd);c(35,Bd);c(46,Cd);c(36,Dd);c(43,Ed);c(37,Fd);c(38,Gd);c(39,Hd);c(40,Id);c(41,Jd);c(42,Kd);c(58,Ld);c(57,Pd);c(60,Qd);c(61,Rd);c(56,Sd);c(62,Td);c(59,Ud)};function Xd(a){if(a instanceof sa||a instanceof gb||a instanceof xa||a instanceof kb||a instanceof zc||null===a||void 0===a||"string"===typeof a||"number"===typeof a||"boolean"===typeof a)return a};var Zd=function(){var a=function(b){return{toString:function(){return b}}};return{Li:a("consent"),Fg:a("convert_case_to"),Gg:a("convert_false_to"),Hg:a("convert_null_to"),Ig:a("convert_true_to"),Jg:a("convert_undefined_to"),zl:a("debug_mode_metadata"),Wa:a("function"),Cf:a("instance_name"),Aj:a("live_only"),Bj:a("malware_disabled"),Cj:a("metadata"),Fj:a("original_activity_id"),Dl:a("original_vendor_template_id"),Cl:a("once_on_load"),Ej:a("once_per_event"),Jh:a("once_per_load"),Fl:a("priority_override"),
Gl:a("respected_consent_types"),Nh:a("setup_tags"),Bb:a("tag_id"),Sh:a("teardown_tags")}}();var ue;
var ve=[],we=[],xe=[],ye=[],ze=[],Ae={},Be,Ce,Fe=function(){var a=Ee;Ce=Ce||a},Ge,He=function(a,b){var c={};c["function"]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c},Ie=function(a,b){var c=a["function"],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Ae[c],f={},g;for(g in a)a.hasOwnProperty(g)&&0===g.indexOf("vtp_")&&(e&&d&&d.Xh&&d.Xh(a[g]),f[void 0!==e?g:g.substr(4)]=a[g]);e&&d&&d.Wh&&(f.vtp_gtmCachedValues=d.Wh);if(b){if(null==
b.name){var h;a:{var l=b.index;if(null==l)h="";else{var n;switch(b.type){case 2:n=ve[l];break;case 1:n=ye[l];break;default:h="";break a}var p=n&&n[Zd.Cf];h=p?String(p):""}}b.name=h}e&&(f.vtp_gtmEntityIndex=b.index,f.vtp_gtmEntityName=b.name)}return void 0!==e?e(f):ue(c,f,b)},Ke=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Je(a[e],b,c));return d},Je=function(a,b,c){if(Ia(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Je(a[e],
b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=ve[f];if(!g||b.Zf(g))return;c[f]=!0;var h=String(g[Zd.Cf]);try{var l=Ke(g,b,c);l.vtp_gtmEventId=b.id;b.priorityId&&(l.vtp_gtmPriorityId=b.priorityId);d=Ie(l,{event:b,index:f,type:2,name:h});Ge&&(d=Ge.Sj(d,l))}catch(x){b.ii&&b.ii(x,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Je(a[n],b,c)]=Je(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Je(a[q],b,c);Ce&&(p=p||r===Ce.se);
d.push(r)}return Ce&&p?Ce.Uj(d):d.join("");case "escape":d=Je(a[1],b,c);if(Ce&&Ia(a[1])&&"macro"===a[1][0]&&Ce.yk(a))return Ce.Rk(d);d=String(d);for(var u=2;u<a.length;u++)$d[a[u]]&&(d=$d[a[u]](d));return d;case "tag":var t=a[1];if(!ye[t])throw Error("Unable to resolve tag reference "+t+".");return d={ci:a[2],index:t};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v["function"]=a[1];var w=Le(v,b,c),y=!!a[4];return y||2!==w?y!==(1===w):null;default:throw Error("Attempting to expand unknown Value type: "+
a[0]+".");}}return a},Le=function(a,b,c){try{return Be(Ke(a,b,c))}catch(d){JSON.stringify(a)}return 2};var Me=function(a,b,c){var d;d=Error.call(this);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.B=a;this.h=c};pa(Me,Error);function Ne(a,b){if(Ia(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Ne(a[c],b[c])}};var Oe=function(a,b){var c;c=Error.call(this);this.message=c.message;"stack"in c&&(this.stack=c.stack);this.Nk=a;this.B=b;this.h=[]};pa(Oe,Error);var Qe=function(){return function(a,b){a instanceof Oe||(a=new Oe(a,Pe));b&&a.h.push(b);throw a;}};function Pe(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;0<b;b--)Ga(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;0<c;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};var Te=function(a){function b(r){for(var u=0;u<r.length;u++)d[r[u]]=!0}for(var c=[],d=[],e=Re(a),f=0;f<we.length;f++){var g=we[f],h=Se(g,e);if(h){for(var l=g.add||[],n=0;n<l.length;n++)c[l[n]]=!0;b(g.block||[])}else null===h&&b(g.block||[]);}for(var p=[],q=0;q<ye.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p},Se=function(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(0===e)return!1;if(2===e)return null}for(var f=
a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(2===h)return null;if(1===h)return!1}return!0},Re=function(a){var b=[];return function(c){void 0===b[c]&&(b[c]=Le(xe[c],a));return b[c]}};var Ue={Sj:function(a,b){b[Zd.Fg]&&"string"===typeof a&&(a=1==b[Zd.Fg]?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Zd.Hg)&&null===a&&(a=b[Zd.Hg]);b.hasOwnProperty(Zd.Jg)&&void 0===a&&(a=b[Zd.Jg]);b.hasOwnProperty(Zd.Ig)&&!0===a&&(a=b[Zd.Ig]);b.hasOwnProperty(Zd.Gg)&&!1===a&&(a=b[Zd.Gg]);return a}};var Ve=function(){this.h={}};function We(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e].call(void 0,b,c,d),g+="."}catch(h){g="string"===typeof h?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Me(c,d,g);}}function Xe(a,b,c){return function(){var d=arguments[0];if(d){var e=a.h[d],f=a.h.all;if(e||f){var g=c.apply(void 0,Array.prototype.slice.call(arguments,0));We(e,b,d,g);We(f,b,d,g)}}}};var $e=function(){var a=data.permissions||{},b=L.H,c=this;this.B=new Ve;this.h={};var d={},e=Xe(this.B,b,function(){var f=arguments[0];return f&&d[f]?d[f].apply(void 0,Array.prototype.slice.call(arguments,0)):{}});m(a,function(f,g){var h={};m(g,function(l,n){var p=Ye(l,n);h[l]=p.assert;d[l]||(d[l]=p.ba)});c.h[f]=function(l,n){var p=h[l];if(!p)throw Ze(l,{},"The requested permission "+l+" is not configured.");var q=Array.prototype.slice.call(arguments,0);p.apply(void 0,q);e.apply(void 0,q)}})},bf=
function(a){return af.h[a]||function(){}};function Ye(a,b){var c=He(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=Ze;try{return Ie(c)}catch(d){return{assert:function(e){throw new Me(e,{},"Permission "+e+" is unknown.");},ba:function(){for(var e={},f=0;f<arguments.length;++f)e["arg"+(f+1)]=arguments[f];return e}}}}function Ze(a,b,c){return new Me(a,b,c)};var cf=!1;var df={};df.yl=Qa('');df.Xj=Qa('');var ef=cf,ff=df.Xj,gf=df.yl;var hf=function(a,b){var c=String(a);return c};
var of=function(a){var b={},c=0,d=jf?27:10;m(a,function(f,g){if(void 0!==g)if(g=hf(g,100),kf.hasOwnProperty(f))b[kf[f]]=lf(g);else if(mf.hasOwnProperty(f)){var h=mf[f],l=lf(g);b.hasOwnProperty(h)||(b[h]=l)}else if("category"===f)for(var n=lf(g).split("/",5),p=0;p<n.length;p++){var q=nf[p],r=n[p];b.hasOwnProperty(q)||(b[q]=r)}else if(c<d){var u=10>c?""+c:String.fromCharCode(65+c-10);b["k"+u]=lf(hf(f,40));b["v"+u]=lf(g);c++}});var e=[];m(b,function(f,g){e.push(""+f+g)});return e.join("~")},lf=function(a){return(""+
a).replace(/~/g,function(){return"~~"})},jf=!1;jf=!0;var kf={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",
promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},mf={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},nf=["ca","c2","c3","c4","c5"];var pf=function(a){var b=[];m(a,function(c,d){null!=d&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},qf=function(a,b,c,d){this.Ba=a.Ba;this.Ub=a.Ub;this.Sf=a.Sf;this.h=b;this.F=c;this.D=pf(a.Ba);this.B=pf(a.Sf);this.N=this.B.length;if(d&&16384<this.N)throw Error("EVENT_TOO_LARGE");};var rf=function(){this.events=[];this.h=this.Ba="";this.D=0;this.B=!1};rf.prototype.add=function(a){return this.F(a)?(this.events.push(a),this.Ba=a.D,this.h=a.h,this.D+=a.N,this.B=a.F,!0):!1};rf.prototype.F=function(a){var b=20>this.events.length&&16384>a.N+this.D,c=this.Ba===a.D&&this.h===a.h&&this.B===a.F;return 0==this.events.length||b&&c};
var sf=function(a,b){m(a,function(c,d){null!=d&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})},tf=function(a,b){var c=[];a.D&&c.push(a.D);b&&c.push("_s="+b);sf(a.Ub,c);var d=!1;a.B&&(c.push(a.B),d=!0);var e=c.join("&"),f="",g=e.length+a.h.length+1;d&&2048<g&&(f=c.pop(),e=c.join("&"));return{ng:e,body:f}},uf=function(a,b){var c=a.events;if(1==c.length)return tf(c[0],b);var d=[];a.Ba&&d.push(a.Ba);for(var e={},f=0;f<c.length;f++)m(c[f].Ub,function(u,t){null!=t&&(e[u]=e[u]||{},e[u][String(t)]=
e[u][String(t)]+1||1)});var g={};m(e,function(u,t){var v,w=-1,y=0;m(t,function(x,A){y+=A;var B=(x.length+u.length+2)*(A-1);B>w&&(v=x,w=B)});y==c.length&&(g[u]=v)});sf(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),l=[],n={},p=0;p<c.length;n={Jd:n.Jd},p++){var q=[];n.Jd={};m(c[p].Ub,function(u){return function(t,v){g[t]!=""+v&&(u.Jd[t]=v)}}(n));c[p].B&&q.push(c[p].B);sf(n.Jd,q);l.push(q.join("&"))}var r=l.join("\r\n");return{ng:h,body:r}};var yf=/:[0-9]+$/,zf=/^\d+\.fls\.doubleclick\.net$/,Af=function(a,b,c,d){for(var e=[],f=a.split("&"),g=0;g<f.length;g++){var h=f[g].split("=");if(decodeURIComponent(h[0]).replace(/\+/g," ")===b){var l=h.slice(1).join("=");if(!c)return d?l:decodeURIComponent(l).replace(/\+/g," ");e.push(d?l:decodeURIComponent(l).replace(/\+/g," "))}}return c?e:void 0},Df=function(a,b,c,d,e){b&&(b=String(b).toLowerCase());if("protocol"===b||"port"===b)a.protocol=Bf(a.protocol)||Bf(z.location.protocol);"port"===b?a.port=
String(Number(a.hostname?a.port:z.location.port)||("http"===a.protocol?80:"https"===a.protocol?443:"")):"host"===b&&(a.hostname=(a.hostname||z.location.hostname).replace(yf,"").toLowerCase());return Cf(a,b,c,d,e)},Cf=function(a,b,c,d,e){var f,g=Bf(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Ef(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(yf,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substr(h[0].length))}break;case "port":f=
String(Number(a.port)||("http"===g?80:"https"===g?443:""));break;case "path":a.pathname||a.hostname||wb("TAGGING",1);f="/"===a.pathname.substr(0,1)?a.pathname:"/"+a.pathname;var l=f.split("/");0<=(d||[]).indexOf(l[l.length-1])&&(l[l.length-1]="");f=l.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Af(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=1<n.length?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f},
Bf=function(a){return a?a.replace(":","").toLowerCase():""},Ef=function(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=0>c?a.href:a.href.substr(0,c)}return b},Ff=function(a){var b=I.createElement("a");a&&(b.href=a);var c=b.pathname;"/"!==c[0]&&(a||wb("TAGGING",1),c="/"+c);var d=b.hostname.replace(yf,"");return{href:b.href,protocol:b.protocol,host:b.host,hostname:d,pathname:c,search:b.search,hash:b.hash,port:b.port}},Gf=function(a){function b(n){var p=n.split("=")[0];return 0>d.indexOf(p)?n:
p+"=0"}function c(n){return n.split("&").map(b).filter(function(p){return void 0!==p}).join("&")}var d="gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),e=Ff(a),f=a.split(/[?#]/)[0],g=e.search,h=e.hash;"?"===g[0]&&(g=g.substring(1));"#"===h[0]&&(h=h.substring(1));g=c(g);h=c(h);""!==g&&(g="?"+g);""!==h&&(h="#"+h);var l=""+f+g+h;"/"===l[l.length-1]&&(l=l.substring(0,l.length-1));return l},Hf=function(a){var b=Ff(z.location.href),c=Df(b,"host",!1);if(c&&c.match(zf)){var d=Df(b,
"path").split(a+"=");if(1<d.length)return d[1].split(";")[0].split("?")[0]}};var If=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Jf(a,b){a=String(a);b=String(b);var c=a.length-b.length;return 0<=c&&a.indexOf(b,c)===c}var Kf=new Ma;function Lf(a,b,c){var d=c?"i":void 0;try{var e=String(b)+d,f=Kf.get(e);f||(f=new RegExp(b,d),Kf.set(e,f));return f.test(a)}catch(g){return!1}}function Mf(a,b){return 0<=String(a).indexOf(String(b))}function Nf(a,b){return String(a)===String(b)}
function Of(a,b){return Number(a)>=Number(b)}function Rf(a,b){return Number(a)<=Number(b)}function Sf(a,b){return Number(a)>Number(b)}function Tf(a,b){return Number(a)<Number(b)}function Uf(a,b){return 0===String(a).indexOf(String(b))}
function Vf(a,b){function c(g){var h=Ff(g),l=Df(h,"protocol"),n=Df(h,"host",!0),p=Df(h,"port"),q=Df(h,"path").toLowerCase().replace(/\/$/,"");if(void 0===l||"http"===l&&"80"===p||"https"===l&&"443"===p)l="web",p="default";return[l,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0};var bg=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function cg(a,b){return"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a<<2|b]};var dg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|DustMap|List|OpaqueValue)$/i,eg={Fn:"function",DustMap:"Object",List:"Array"},M=function(a,b,c){for(var d=0;d<b.length;d++){var e=dg.exec(b[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g="!"===e[2],h=e[3],l=c[d];if(null==l){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if("*"!==h){var n=typeof l;l instanceof gb?n="Fn":l instanceof xa?n="List":l instanceof kb?n="DustMap":l instanceof zc&&(n="OpaqueValue");
if(n!=h)throw Error("Error in "+a+". Argument "+f+" has type "+(eg[n]||n)+", which does not match required type "+(eg[h]||h)+".");}}};function fg(a){return""+a}
function gg(a,b){var c=[];return c};var hg=function(a,b){var c=new gb(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=E(this,d[e]);return b.apply(this,d)});c.Qb();return c},ig=function(a,b){var c=new kb,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];Ea(e)?c.set(d,hg(a+"_"+d,e)):(Ga(e)||k(e)||"boolean"===typeof e)&&c.set(d,e)}c.Qb();return c};var jg=function(a,b){M(F(this),["apiName:!string","message:?string"],arguments);var c={},d=new kb;return d=ig("AssertApiSubject",c)};var kg=function(a,b){M(F(this),["actual:?*","message:?string"],arguments);if(a instanceof Bc)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new kb;return d=ig("AssertThatSubject",c)};function lg(a){return function(){for(var b=[],c=this.h,d=0;d<arguments.length;++d)b.push(Hc(arguments[d],c));return Gc(a.apply(null,b))}}var ng=function(){for(var a=Math,b=mg,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=lg(a[e].bind(a)))}return c};function og(a,b){var c=null;return c}og.O="internal.createRegExp";var pg=function(a){var b;return b};var qg=function(a){var b;return b};var rg=function(a){return encodeURI(a)};var sg=function(a){return encodeURIComponent(a)};function tg(a,b){var c=!1;M(F(this),["booleanExpression:!string","context:?DustMap"],arguments);var d=JSON.parse(a);if(!d)throw Error("Invalid boolean expression string was given.");var e=b?Hc(b):{};c=ug(d,e);return c}
var vg=function(a,b){for(var c=0;c<b.length;c++){if(void 0===a)return;a=a[b[c]]}return a},wg=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return 2>a.length?void 0:vg(c.getHitData(a[1]),a.slice(2));case "metadata":return 2>a.length?void 0:vg(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
xg=function(a,b){if(a){if(void 0!==a.contextValue){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&0!==e.length){var f=d.namespaceType;switch(f){case 1:c=wg(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(void 0!==a.booleanExpressionValue)return ug(a.booleanExpressionValue,b);if(void 0!==a.booleanValue)return!!a.booleanValue;if(void 0!==a.stringValue)return String(a.stringValue);if(void 0!==a.integerValue)return Number(a.integerValue);
if(void 0!==a.doubleValue)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},ug=function(a,b){var c=a.args;if(!Ia(c)||0===c.length)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return xg(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return 0<c.length;case 3:return!d(c[0]);
case 4:return Lf(d(c[0]),d(c[1]),!1);case 5:return Nf(d(c[0]),d(c[1]));case 6:return Uf(d(c[0]),d(c[1]));case 7:return Jf(d(c[0]),d(c[1]));case 8:return Mf(d(c[0]),d(c[1]));case 9:return Tf(d(c[0]),d(c[1]));case 10:return Rf(d(c[0]),d(c[1]));case 11:return Sf(d(c[0]),d(c[1]));case 12:return Of(d(c[0]),d(c[1]));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 13.');}};
tg.O="internal.evaluateBooleanExpression";var yg=function(a){M(F(this),["message:?string"],arguments);};var zg=function(a,b){M(F(this),["min:!number","max:!number"],arguments);return La(a,b)};var O=function(a,b,c){var d=a.h.h;if(!d)throw Error("Missing program state.");d.Qj.apply(null,Array.prototype.slice.call(arguments,1))};var Ag=function(){O(this,"read_container_data");var a=new kb;a.set("containerId",'G-SQ2V73809B');a.set("version",'1');a.set("environmentName",'');a.set("debugMode",ef);a.set("previewMode",gf);a.set("environmentMode",ff);a.Qb();return a};var Bg=function(){return(new Date).getTime()};var Cg=function(a){if(null===a)return"null";if(a instanceof xa)return"array";if(a instanceof gb)return"function";if(a instanceof zc){a=a.Da;if(void 0===a.constructor||void 0===a.constructor.name){var b=String(a);return b.substring(8,b.length-1)}return String(a.constructor.name)}return typeof a};var Dg=function(a){function b(c){return function(d){try{return c(d)}catch(e){(ef||gf)&&a.call(this,e.message)}}}return{parse:b(function(c){return Gc(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Hc(c))})}};var Eg=function(a){return Pa(Hc(a,this.h))};var Fg=function(a){return Number(Hc(a,this.h))};var Gg=function(a){return null===a?"null":void 0===a?"undefined":a.toString()};var Hg=function(a,b,c){var d=null,e=!1;return e?d:null};var mg="floor ceil round max min abs pow sqrt".split(" ");var Ig=function(){var a={};return{kk:function(b){return a.hasOwnProperty(b)?a[b]:void 0},ol:function(b,c){a[b]=c},reset:function(){a={}}}},Jg=function(a,b){return function(){var c=Array.prototype.slice.call(arguments,0);c.unshift(b);return gb.prototype.h.apply(a,c)}},Kg=function(a,b){M(F(this),["apiName:!string","mock:?*"],arguments);};var Lg={};
Lg.keys=function(a){return new xa};
Lg.values=function(a){return new xa};
Lg.entries=function(a){return new xa};
Lg.freeze=function(a){return a};Lg.delete=function(a,b){return!1};var Ng=function(){this.h={};this.B={};};Ng.prototype.get=function(a,b){var c=this.h.hasOwnProperty(a)?this.h[a]:void 0;return c};
Ng.prototype.add=function(a,b,c){if(this.h.hasOwnProperty(a))throw"Attempting to add a function which already exists: "+a+".";if(this.B.hasOwnProperty(a))throw"Attempting to add an API with an existing private API name: "+a+".";this.h[a]=c?void 0:Ea(b)?hg(a,b):ig(a,b)};function Og(a,b){var c=void 0;return c};function Pg(){var a={};return a};function Qg(a,b){var c=!1;return c}Qg.O="internal.testRegExp";var Sg=function(a){return Rg?I.querySelectorAll(a):null},Tg=function(a,b){if(!Rg)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!I.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(null!==d&&1===d.nodeType);
return null},Ug=!1;if(I.querySelectorAll)try{var Vg=I.querySelectorAll(":root");Vg&&1==Vg.length&&Vg[0]==I.documentElement&&(Ug=!0)}catch(a){}var Rg=Ug;var Q=function(a){wb("GTM",a)};
var Wg=function(a){return null==a?"":k(a)?Sa(String(a)):"e0"},Yg=function(a){return a.replace(Xg,"")},$g=function(a){return Zg(a.replace(/\s/g,""))},Zg=function(a){return Sa(a.replace(ah,"").toLowerCase())},ch=function(a){a=a.replace(/[\s-()/.]/g,"");"+"!==a.charAt(0)&&(a="+"+a);return bh.test(a)?a:"e0"},eh=function(a){var b=a.toLowerCase().split("@");if(2==b.length){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(dh.test(c))return c}return"e0"},hh=function(a,
b){window.Promise||b([]);Promise.all(a.map(function(c){return c.value&&-1!==fh.indexOf(c.name)?gh(c.value).then(function(d){c.value=d}):Promise.resolve()})).then(function(){b(a)}).catch(function(){b([])})},gh=function(a){if(""===a||"e0"===a)return Promise.resolve(a);if(z.crypto&&z.crypto.subtle){if(ih.test(a))return Promise.resolve(a);try{var b=jh(a);return z.crypto.subtle.digest("SHA-256",b).then(function(c){var d=Array.from(new Uint8Array(c)).map(function(e){return String.fromCharCode(e)}).join("");
return z.btoa(d).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}).catch(function(){return"e2"})}catch(c){return Promise.resolve("e2")}}else return Promise.resolve("e1")},jh=function(a){var b;if(z.TextEncoder)b=(new TextEncoder("utf-8")).encode(a);else{for(var c=[],d=0;d<a.length;d++){var e=a.charCodeAt(d);128>e?c.push(e):2048>e?c.push(192|e>>6,128|e&63):55296>e||57344<=e?c.push(224|e>>12,128|e>>6&63,128|e&63):(e=65536+((e&1023)<<10|a.charCodeAt(++d)&1023),c.push(240|e>>18,128|e>>12&63,128|
e>>6&63,128|e&63))}b=new Uint8Array(c)}return b},ah=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,dh=/^\S+@\S+\.\S+$/,bh=/^\+\d{10,15}$/,Xg=/[.~]/g,kh=/^[0-9A-Za-z_-]{43}$/,ih=/^[0-9A-Fa-f]{64}$/,lh={},mh=(lh.email="em",lh.phone_number="pn",lh.first_name="fn",lh.last_name="ln",lh.street="sa",lh.city="ct",lh.region="rg",lh.country="co",lh.postal_code="pc",lh.error_code="ec",lh),nh={},oh=(nh.email="sha256_email_address",nh.phone_number="sha256_phone_number",nh.first_name="sha256_first_name",nh.last_name=
"sha256_last_name",nh.street="sha256_street",nh),ph=function(a,b){function c(u,t,v,w){var y=Wg(u);""!==y&&(ih.test(y)?l.push({name:t,value:y,index:w}):l.push({name:t,value:v(y),index:w}))}function d(u,t){var v=u;if(k(v)||Ia(v)){v=Ia(u)?u:[u];for(var w=0;w<v.length;++w){var y=Wg(v[w]),x=ih.test(y);t&&!x&&Q(89);!t&&x&&Q(88)}}}function e(u,t){var v=u[t];d(v,!1);var w=oh[t];u.hasOwnProperty(w)&&(u.hasOwnProperty(t)&&Q(90),v=u[w],d(v,!0));return v}function f(u,t,v){var w=e(u,t);w=Ia(w)?w:[w];for(var y=
0;y<w.length;++y)c(w[y],t,v)}function g(u,t,v,w){var y=e(u,t);c(y,t,v,w)}function h(u){return function(t){Q(64);return u(t)}}var l=[];if("https:"===z.location.protocol){f(a,"email",eh);f(a,"phone_number",ch);f(a,"first_name",h($g));f(a,"last_name",h($g));var n=a.home_address||{};f(n,"street",h(Zg));f(n,"city",h(Zg));f(n,"postal_code",h(Yg));f(n,"region",h(Zg));f(n,"country",h(Yg));var p=a.address||{};p=Ia(p)?p:[p];for(var q=0;q<p.length;q++){var r=p[q];g(r,"first_name",$g,q);g(r,"last_name",$g,q);
g(r,"street",Zg,q);g(r,"city",Zg,q);g(r,"postal_code",Yg,q);g(r,"region",Zg,q);g(r,"country",Yg,q)}hh(l,b)}else l.push({name:"error_code",value:"e3",index:void 0}),b(l)},qh=function(a,b){ph(a,function(c){for(var d=["tv.1"],e=0,f=0;f<c.length;++f){var g=c[f].name,h=c[f].value,l=c[f].index,n=mh[g];n&&h&&(-1===fh.indexOf(g)||/^e\d+$/.test(h)||kh.test(h)||ih.test(h))&&(void 0!==l&&(n+=l),d.push(n+"."+h),e++)}1===c.length&&"error_code"===c[0].name&&(e=0);b(encodeURIComponent(d.join("~")),e)})},rh=function(a){if(z.Promise)try{return new Promise(function(b){qh(a,
function(c,d){b({mg:c,Qk:d})})})}catch(b){}},fh=Object.freeze(["email","phone_number","first_name","last_name","street"]);var T={g:{K:"ad_storage",W:"analytics_storage",Re:"region",Bg:"consent_updated",Se:"wait_for_update",Pi:"app_remove",Qi:"app_store_refund",Ri:"app_store_subscription_cancel",Si:"app_store_subscription_convert",Ti:"app_store_subscription_renew",Kg:"add_payment_info",Lg:"add_shipping_info",Gc:"add_to_cart",Hc:"remove_from_cart",Mg:"view_cart",Yb:"begin_checkout",Ic:"select_item",Gb:"view_item_list",Zb:"select_promotion",Hb:"view_promotion",Ia:"purchase",Jc:"refund",Ja:"view_item",Ng:"add_to_wishlist",
Ui:"first_open",Vi:"first_visit",Fa:"gtag.config",Ka:"gtag.get",Wi:"in_app_purchase",Kc:"page_view",Xi:"session_start",Xe:"user_engagement",ac:"gclid",oa:"ads_data_redaction",da:"allow_ad_personalization_signals",Ye:"allow_custom_scripts",Yi:"allow_display_features",Nd:"allow_enhanced_conversions",Ib:"allow_google_signals",Ga:"allow_interest_groups",Od:"auid",Zi:"auto_detection_enabled",Jb:"aw_remarketing",Ze:"aw_remarketing_only",Pd:"discount",Qd:"aw_feed_country",Rd:"aw_feed_language",ja:"items",
Sd:"aw_merchant_id",Og:"aw_basket_type",Td:"campaign_content",Ud:"campaign_id",Vd:"campaign_medium",Wd:"campaign_name",Lc:"campaign",Xd:"campaign_source",Yd:"campaign_term",ub:"client_id",aj:"content_group",bj:"content_type",La:"conversion_cookie_prefix",Mc:"conversion_id",ya:"conversion_linker",Nc:"conversion_api",vb:"cookie_domain",Sa:"cookie_expires",wb:"cookie_flags",Oc:"cookie_name",af:"cookie_path",ib:"cookie_prefix",bc:"cookie_update",Pc:"country",sa:"currency",Zd:"customer_lifetime_value",
Qc:"custom_map",cj:"debug_mode",fa:"developer_id",dj:"disable_merchant_reported_purchases",ej:"dc_custom_params",fj:"dc_natural_search",bf:"dynamic_event_settings",gj:"affiliation",Pg:"checkout_option",Qg:"checkout_step",ij:"coupon",cf:"item_list_name",df:"list_name",jj:"promotions",ae:"shipping",Rg:"tax",be:"engagement_time_msec",Rc:"enhanced_client_id",Sc:"enhanced_conversions",Sg:"enhanced_conversions_automatic_settings",ce:"estimated_delivery_date",ef:"euid_logged_in_state",cc:"event_callback",
fc:"event_developer_id_string",Tg:"event",de:"event_settings",ee:"event_timeout",kj:"experiments",ff:"firebase_id",fe:"first_party_collection",he:"_x_20",Kb:"_x_19",Ug:"fledge",Vg:"flight_error_code",Wg:"flight_error_message",Xg:"gac_gclid",ie:"gac_wbraid",Yg:"gac_wbraid_multiple_conversions",hf:"ga_restrict_domain",jf:"ga_temp_client_id",Zg:"gdpr_applies",ah:"geo_granularity",xb:"value_callback",jb:"value_key",Al:"google_ono",kb:"google_signals",Tc:"google_tld",je:"groups",bh:"gsa_experiment_id",
dh:"iframe_state",ke:"ignore_referrer",kf:"internal_traffic_results",me:"is_legacy_loaded",eh:"is_passthrough",Ta:"language",lf:"legacy_developer_id_string",za:"linker",ic:"accept_incoming",Lb:"decorate_forms",V:"domains",jc:"url_position",fh:"method",Uc:"new_customer",gh:"non_interaction",lj:"optimize_id",hh:"page_hostname",Vc:"page_path",Ua:"page_referrer",kc:"page_title",ih:"passengers",jh:"phone_conversion_callback",mj:"phone_conversion_country_code",kh:"phone_conversion_css_class",nj:"phone_conversion_ids",
lh:"phone_conversion_number",mh:"phone_conversion_options",nh:"quantity",Wc:"redact_device_info",nf:"redact_enhanced_user_id",oj:"redact_ga_client_id",pj:"redact_user_id",ne:"referral_exclusion_definition",Mb:"restricted_data_processing",qj:"retoken",oh:"screen_name",Nb:"screen_resolution",rj:"search_term",Na:"send_page_view",Ob:"send_to",Xc:"session_duration",oe:"session_engaged",pf:"session_engaged_time",yb:"session_id",pe:"session_number",Yc:"delivery_postal_code",qh:"temporary_client_id",sj:"tracking_id",
qf:"traffic_type",Va:"transaction_id",va:"transport_url",rh:"trip_type",Zc:"update",zb:"url_passthrough",rf:"_user_agent_architecture",sf:"_user_agent_bitness",tf:"_user_agent_full_version_list",uf:"_user_agent_mobile",vf:"_user_agent_model",wf:"_user_agent_platform",xf:"_user_agent_platform_version",yf:"_user_agent_wow64",wa:"user_data",sh:"user_data_auto_latency",th:"user_data_auto_meta",uh:"user_data_auto_multi",vh:"user_data_auto_selectors",wh:"user_data_auto_status",xh:"user_data_mode",zf:"user_data_settings",
Aa:"user_id",Oa:"user_properties",yh:"us_privacy_string",qa:"value",qe:"wbraid",zh:"wbraid_multiple_conversions",Fh:"_host_name",Gh:"_in_page_command",Hh:"_is_passthrough_cid",Ih:"non_personalized_ads",ed:"_sst_parameters",hb:"conversion_label",Ma:"page_location",hc:"global_developer_id_string",ph:"tc_privacy_string"}},sh={},th=Object.freeze((sh[T.g.da]=1,sh[T.g.Nd]=1,sh[T.g.Ib]=1,sh[T.g.ja]=1,sh[T.g.vb]=1,sh[T.g.Sa]=1,sh[T.g.wb]=1,sh[T.g.Oc]=1,sh[T.g.af]=1,sh[T.g.ib]=1,sh[T.g.bc]=1,sh[T.g.Qc]=1,
sh[T.g.fa]=1,sh[T.g.bf]=1,sh[T.g.cc]=1,sh[T.g.de]=1,sh[T.g.ee]=1,sh[T.g.fe]=1,sh[T.g.hf]=1,sh[T.g.kb]=1,sh[T.g.Tc]=1,sh[T.g.je]=1,sh[T.g.kf]=1,sh[T.g.me]=1,sh[T.g.za]=1,sh[T.g.nf]=1,sh[T.g.ne]=1,sh[T.g.Mb]=1,sh[T.g.Na]=1,sh[T.g.Ob]=1,sh[T.g.Xc]=1,sh[T.g.pf]=1,sh[T.g.Yc]=1,sh[T.g.va]=1,sh[T.g.Zc]=1,sh[T.g.zf]=1,sh[T.g.Oa]=1,sh[T.g.ed]=1,sh));Object.freeze([T.g.Ma,T.g.Ua,T.g.kc,T.g.Ta,T.g.oh,T.g.Aa,T.g.ff,T.g.aj]);
var uh={},vh=Object.freeze((uh[T.g.Pi]=1,uh[T.g.Qi]=1,uh[T.g.Ri]=1,uh[T.g.Si]=1,uh[T.g.Ti]=1,uh[T.g.Ui]=1,uh[T.g.Vi]=1,uh[T.g.Wi]=1,uh[T.g.Xi]=1,uh[T.g.Xe]=1,uh)),wh={},xh=Object.freeze((wh[T.g.Kg]=1,wh[T.g.Lg]=1,wh[T.g.Gc]=1,wh[T.g.Hc]=1,wh[T.g.Mg]=1,wh[T.g.Yb]=1,wh[T.g.Ic]=1,wh[T.g.Gb]=1,wh[T.g.Zb]=1,wh[T.g.Hb]=1,wh[T.g.Ia]=1,wh[T.g.Jc]=1,wh[T.g.Ja]=1,wh[T.g.Ng]=1,wh)),yh=Object.freeze([T.g.da,T.g.Ib,T.g.bc]),zh=Object.freeze([].concat(yh)),Ah=Object.freeze([T.g.Sa,T.g.ee,T.g.Xc,T.g.pf,T.g.be]),
Bh=Object.freeze([].concat(Ah)),Ch={},Dh=(Ch[T.g.K]="1",Ch[T.g.W]="2",Ch),Eh={},Fh=Object.freeze((Eh[T.g.da]=1,Eh[T.g.Nd]=1,Eh[T.g.Ga]=1,Eh[T.g.Jb]=1,Eh[T.g.Ze]=1,Eh[T.g.Pd]=1,Eh[T.g.Qd]=1,Eh[T.g.Rd]=1,Eh[T.g.ja]=1,Eh[T.g.Sd]=1,Eh[T.g.La]=1,Eh[T.g.ya]=1,Eh[T.g.vb]=1,Eh[T.g.Sa]=1,Eh[T.g.wb]=1,Eh[T.g.ib]=1,Eh[T.g.sa]=1,Eh[T.g.Zd]=1,Eh[T.g.fa]=1,Eh[T.g.dj]=1,Eh[T.g.Sc]=1,Eh[T.g.ce]=1,Eh[T.g.ff]=1,Eh[T.g.fe]=1,Eh[T.g.me]=1,Eh[T.g.Ta]=1,Eh[T.g.Uc]=1,Eh[T.g.Ma]=1,Eh[T.g.Ua]=1,Eh[T.g.jh]=1,Eh[T.g.kh]=1,
Eh[T.g.lh]=1,Eh[T.g.mh]=1,Eh[T.g.Mb]=1,Eh[T.g.Na]=1,Eh[T.g.Ob]=1,Eh[T.g.Yc]=1,Eh[T.g.Va]=1,Eh[T.g.va]=1,Eh[T.g.Zc]=1,Eh[T.g.zb]=1,Eh[T.g.wa]=1,Eh[T.g.Aa]=1,Eh[T.g.qa]=1,Eh));Object.freeze(T.g);var Gh={},Oh=z.google_tag_manager=z.google_tag_manager||{},Ph=Math.random();Gh.oc="32f0";Gh.dd=Number("0")||0;Gh.ka="dataLayer";Gh.Ni="ChAIgLrcnwYQpa/0k5j7nckjEiUAZL4LMGeihBFw8qj3cRREwsLqFo9y6sW0h4E2Q6NqF+Hww0QcGgK8hQ\x3d\x3d";var Qh={__cl:!0,__ecl:!0,__ehl:!0,__evl:!0,__fal:!0,__fil:!0,__fsl:!0,__hl:!0,__jel:!0,__lcl:!0,__sdl:!0,__tl:!0,__ytl:!0},Rh={__paused:!0,__tg:!0},Sh;for(Sh in Qh)Qh.hasOwnProperty(Sh)&&(Rh[Sh]=!0);var Th=Qa("true"),Uh,Vh=!1;Vh=!0;
Uh=Vh;var Wh,Xh=!1;Wh=Xh;var Yh,Zh=!1;Yh=Zh;var $h,ai=!1;$h=ai;Gh.Md="www.googletagmanager.com";
var bi=""+Gh.Md+(Uh?"/gtag/js":"/gtm.js"),ci=null,di=null,ei={},fi={},gi={},hi=function(){var a=Oh.sequence||1;Oh.sequence=a+1;return a};Gh.Mi="true";var ii="";Gh.we=ii;var ji=new Ma,ki={},li={},oi={name:Gh.ka,set:function(a,b){K(cb(a,b),ki);mi()},get:function(a){return ni(a,2)},reset:function(){ji=new Ma;ki={};mi()}},ni=function(a,b){return 2!=b?ji.get(a):pi(a)},pi=function(a,b){var c=a.split(".");b=b||[];for(var d=ki,e=0;e<c.length;e++){if(null===d)return!1;if(void 0===d)break;d=d[c[e]];if(-1!==b.indexOf(d))return}return d},qi=function(a,b){li.hasOwnProperty(a)||(ji.set(a,b),K(cb(a,b),ki),mi())},ri=function(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist",
"gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=ni(c,1);if(Ia(d)||Fc(d))d=K(d);li[c]=d}},mi=function(a){m(li,function(b,c){ji.set(b,c);K(cb(b),ki);K(cb(b,c),ki);a&&delete li[b]})},si=function(a,b){var c,d=1!==(void 0===b?2:b)?pi(a):ji.get(a);"array"===Dc(d)||"object"===Dc(d)?c=K(d):c=d;return c};var ti={Mf:"CN",ri:"",vk:"true",ek:""},ui=function(){var a="";a=ti.ek;return a};var vi,wi=!1;function xi(){wi=!0;vi=vi||{}}var yi=function(a){wi||xi();return vi[a]};var zi=function(){var a=z.screen;return{width:a?a.width:0,height:a?a.height:0}},Ai=function(a){if(I.hidden)return!0;var b=a.getBoundingClientRect();if(b.top==b.bottom||b.left==b.right||!z.getComputedStyle)return!0;var c=z.getComputedStyle(a,null);if("hidden"===c.visibility)return!0;for(var d=a,e=c;d;){if("none"===e.display)return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");0<=h&&(g=g.substring(h+8,g.indexOf(")",h)),"%"==g.charAt(g.length-1)&&(g=g.substring(0,g.length-1)),f=Math.min(g,
f))}if(void 0!==f&&0>=f)return!0;(d=d.parentElement)&&(e=z.getComputedStyle(d,null))}return!1};var Ji={};
var Li=function(a,b,c){if(a){var d=a.element,e={cb:a.cb,tagName:d.tagName,type:1};b&&(e.querySelector=Ki(d));c&&(e.isVisible=!Ai(d));return e}},Oi=function(a){if(0!=a.length){var b;b=Mi(a,function(c){return!Ni.test(c.cb)});b=Mi(b,function(c){return"INPUT"===c.element.tagName.toUpperCase()});b=Mi(b,function(c){return!Ai(c.element)});return b[0]}},Mi=function(a,b){if(1>=a.length)return a;var c=a.filter(b);return 0==c.length?a:c},Ki=function(a){var b;if(a===I.body)b="body";else{var c;if(a.id)c="#"+a.id;
else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===a){e=g+1;break a}e=-1}else e=1}d=Ki(a.parentElement)+">:nth-child("+e+")"}else d="";c=d}b=c}return b},Pi=!0,Qi=!1,Ri="";Ri=ti.vk;Ji.Ji=Ri;var Si=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,Ti=/@(gmail|googlemail)\./i,Ni=/support|noreply/i,Ui="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Vi=["BR"],Wi={},Xi=function(a){a=a||{wc:!0,xc:!0};a.nb=a.nb||{email:!0,phone:!0,address:!0};var b,c=a,d=!!c.wc+"."+!!c.xc;c&&c.kd&&c.kd.length&&(d+="."+c.kd.join("."));c&&c.nb&&(d+="."+c.nb.email+"."+c.nb.phone+"."+c.nb.address);b=d;var e=Wi[b];if(e&&200>Ua()-e.timestamp)return e.result;
var f;var g=[],h=I.body;if(h){for(var l=h.querySelectorAll("*"),n=0;n<l.length&&1E4>n;n++){var p=l[n];if(!(0<=Ui.indexOf(p.tagName.toUpperCase()))&&p.children instanceof HTMLCollection){for(var q=!1,r=0;r<p.childElementCount&&1E4>r;r++)if(!(0<=Vi.indexOf(p.children[r].tagName.toUpperCase()))){q=!0;break}q||g.push(p)}}f={elements:g,status:1E4<l.length?"2":"1"}}else f={elements:g,status:"4"};var u=f,t=u.status,v=[],w;if(a.nb&&a.nb.email){for(var y=u.elements,x=[],A=0;A<y.length;A++){var B=y[A],C=B.textContent;
"INPUT"===B.tagName.toUpperCase()&&B.value&&(C=B.value);if(C){var D=C.match(Si);if(D){var H=D[0],G;if(z.location){var N=Cf(z.location,"host",!0);G=0<=H.toLowerCase().indexOf(N)}else G=!1;G||x.push({element:B,cb:H})}}}var R=a&&a.kd;if(R&&0!==R.length){for(var aa=[],oa=0;oa<x.length;oa++){for(var P=!0,S=0;S<R.length;S++){var la=R[S];if(la&&Tg(x[oa].element,la)){P=!1;break}}P&&aa.push(x[oa])}v=aa}else v=x;w=Oi(v);10<x.length&&(t="3")}var ca=[];!a.wi&&w&&(v=[w]);for(var ba=0;ba<v.length;ba++)ca.push(Li(v[ba],
a.wc,a.xc));var Fa={elements:ca.slice(0,10),pg:Li(w,a.wc,a.xc),status:t};Wi[b]={timestamp:Ua(),result:Fa};return Fa},Yi=function(a){return a.tagName+":"+a.isVisible+":"+a.cb.length+":"+Ti.test(a.cb)};
var Zi=function(a,b,c){if(!c)return!1;var d=c.selector_type,e=String(c.value),f;if("js_variable"===d){e=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"");for(var g=e.split(","),h=0;h<g.length;h++){var l=g[h].trim();if(l){if(0===l.indexOf("dataLayer."))f=ni(l.substring(10));else{var n=l.split(".");f=z[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(void 0!==f)break}}}else if("css_selector"===d&&Rg){var q=Sg(e);if(q&&0<q.length){f=[];for(var r=0;r<q.length&&r<("email"===b||"phone_number"===b?5:1);r++)f.push(jc(q[r])||
Sa(q[r].value));f=1===f.length?f[0]:f}}return f?(a[b]=f,!0):!1},$i=function(a){if(a){var b={},c=!1;c=Zi(b,"email",a.email)||c;c=Zi(b,"phone_number",a.phone)||c;b.address=[];for(var d=a.name_and_address||[],e=0;e<d.length;e++){var f={};c=Zi(f,"first_name",d[e].first_name)||c;c=Zi(f,"last_name",d[e].last_name)||c;c=Zi(f,"street",d[e].street)||c;c=Zi(f,"city",d[e].city)||c;c=Zi(f,"region",d[e].region)||c;c=Zi(f,"country",d[e].country)||c;c=Zi(f,"postal_code",d[e].postal_code)||c;b.address.push(f)}return c?
b:void 0}},aj=function(a){return a.D[T.g.zf]},bj=function(a){var b=U(a,T.g.Sc)||{},c=!1;m(b,function(d,e){var f=e.enhanced_conversions_mode;if("automatic"===f||"manual"===f)c=!0});return c},cj=function(a){if(!Fc(a))return!1;var b=a.mode;return"auto_detect"===b||"selectors"===b||"code"===b||!!a.enable_code},dj=function(a){if(a){if("selectors"===a.mode||Fc(a.selectors))return $i(a.selectors);if("auto_detect"===a.mode||Fc(a.auto_detect)){var b;var c=a.auto_detect;if(c){var d=Xi({wc:!1,xc:!1,kd:c.exclude_element_selectors,
nb:{email:!!c.email,phone:!!c.phone,address:!!c.address}}).elements,e={};if(0<d.length)for(var f=0;f<d.length;f++){var g=d[f];if(1===g.type){e.email=g.cb;break}}b=e}else b=void 0;return b}}};var hj=new function(a,b){this.h=a;this.defaultValue=void 0===b?!1:b}(1933);var ij=function(a){ij[" "](a);return a};ij[" "]=function(){};var kj=function(){var a=jj,b="Xf";if(a.Xf&&a.hasOwnProperty(b))return a.Xf;var c=new a;return a.Xf=c};var jj=function(){var a={};this.h=function(){var b=hj.h,c=hj.defaultValue;return null!=a[b]?a[b]:c};this.B=function(){a[hj.h]=!0}};var lj=[];function mj(){var a=Yb("google_tag_data",{});a.ics||(a.ics={entries:{},set:nj,update:oj,addListener:pj,notifyListeners:qj,active:!1,usedDefault:!1,usedUpdate:!1,accessedDefault:!1,accessedAny:!1,wasSetLate:!1});return a.ics}
function nj(a,b,c,d,e,f){var g=mj();g.usedDefault||!g.accessedDefault&&!g.accessedAny||(g.wasSetLate=!0);g.active=!0;g.usedDefault=!0;if(void 0!=b){var h=g.entries,l=h[a]||{},n=l.region,p=c&&k(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(""===d||p===e||(p===d?n!==e:!p&&!n)){var q=!!(f&&0<f&&void 0===l.update),r={region:p,initial:"granted"===b,update:l.update,quiet:q};if(""!==d||!1!==l.initial)h[a]=r;q&&z.setTimeout(function(){h[a]===r&&r.quiet&&(r.quiet=!1,rj(a),qj(),wb("TAGGING",
2))},f)}}}function oj(a,b){var c=mj();c.usedDefault||c.usedUpdate||!c.accessedAny||(c.wasSetLate=!0);c.active=!0;c.usedUpdate=!0;if(void 0!=b){var d=sj(c,a),e=c.entries,f=e[a]=e[a]||{};f.update="granted"===b;var g=sj(c,a);f.quiet?(f.quiet=!1,rj(a)):g!==d&&rj(a)}}function pj(a,b){lj.push({Lf:a,dk:b})}function rj(a){for(var b=0;b<lj.length;++b){var c=lj[b];Ia(c.Lf)&&-1!==c.Lf.indexOf(a)&&(c.mi=!0)}}
function qj(a,b){for(var c=0;c<lj.length;++c){var d=lj[c];if(d.mi){d.mi=!1;try{d.dk({consentEventId:a,consentPriorityId:b})}catch(e){}}}}function sj(a,b){var c=a.entries[b]||{};return void 0!==c.update?c.update:c.initial}
var tj=function(a){var b=mj();b.accessedAny=!0;return sj(b,a)},uj=function(a){var b=mj();b.accessedDefault=!0;return(b.entries[a]||{}).initial},vj=function(a){var b=mj();b.accessedAny=!0;return!(b.entries[a]||{}).quiet},wj=function(){if(!kj().h())return!1;var a=mj();a.accessedAny=!0;return a.active},xj=function(){var a=mj();a.accessedDefault=!0;return a.usedDefault},yj=function(a,b){mj().addListener(a,b)},zj=function(a,b){mj().notifyListeners(a,b)},Aj=function(a,b){function c(){for(var e=0;e<b.length;e++)if(!vj(b[e]))return!0;
return!1}if(c()){var d=!1;yj(b,function(e){d||c()||(d=!0,a(e))})}else a({})},Bj=function(a,b){function c(){for(var f=[],g=0;g<d.length;g++){var h=d[g];!1===tj(h)||e[h]||(f.push(h),e[h]=!0)}return f}var d=k(b)?[b]:b,e={};c().length!==d.length&&yj(d,function(f){var g=c();0<g.length&&(f.Lf=g,a(f))})};function Cj(){}function Dj(){};function Ej(a){for(var b=[],c=0;c<Fj.length;c++){var d=a(Fj[c]);b[c]=!0===d?"1":!1===d?"0":"-"}return b.join("")}
var Fj=[T.g.K,T.g.W],Gj=function(a){var b=a[T.g.Re];b&&Q(40);var c=a[T.g.Se];c&&Q(41);for(var d=Ia(b)?b:[b],e={Cc:0};e.Cc<d.length;e={Cc:e.Cc},++e.Cc)m(a,function(f){return function(g,h){if(g!==T.g.Re&&g!==T.g.Se){var l=d[f.Cc],n=ti.Mf,p=ti.ri;mj().set(g,h,l,n,p,c)}}}(e))},Hj=function(a,b){m(a,function(c,d){mj().update(c,d)});zj(b.eventId,b.priorityId)},Ij=function(a){var b=tj(a);return void 0!=b?b:!0},Jj=function(){return"G1"+Ej(tj)},Kj=function(a,b){yj(a,b)},Lj=function(a,b){Bj(a,b)},Mj=function(a,
b){Aj(a,b)};var Nj=function(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;0<=d;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=0!==c?b^c>>21:b;return b};var Oj=function(a,b,c){for(var d=[],e=b.split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h==a){var l=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");l&&c&&(l=decodeURIComponent(l));d.push(l)}}return d};var Pj=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Qj=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};function Rj(a){return"null"!==a.origin};var Uj=function(a,b,c,d){return Sj(d)?Oj(a,String(b||Tj()),c):[]},Xj=function(a,b,c,d,e){if(Sj(e)){var f=Vj(a,d,e);if(1===f.length)return f[0].id;if(0!==f.length){f=Wj(f,function(g){return g.Ce},b);if(1===f.length)return f[0].id;f=Wj(f,function(g){return g.yd},c);return f[0]?f[0].id:void 0}}};function Yj(a,b,c,d){var e=Tj(),f=window;Rj(f)&&(f.document.cookie=a);var g=Tj();return e!=g||void 0!=c&&0<=Uj(b,g,!1,d).indexOf(c)}
var ck=function(a,b,c,d){function e(w,y,x){if(null==x)return delete h[y],w;h[y]=x;return w+"; "+y+"="+x}function f(w,y){if(null==y)return delete h[y],w;h[y]=!0;return w+"; "+y}if(!Sj(c.qb))return 2;var g;void 0==b?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Zj(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var l;c.expires instanceof Date?l=c.expires.toUTCString():null!=c.expires&&(l=""+c.expires);g=e(g,"expires",l);g=e(g,"max-age",c.Jk);g=e(g,"samesite",
c.bl);c.il&&(g=f(g,"secure"));var n=c.domain;if(n&&"auto"===n.toLowerCase()){for(var p=ak(),q=void 0,r=!1,u=0;u<p.length;++u){var t="none"!==p[u]?p[u]:void 0,v=e(g,"domain",t);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!bk(t,c.path)&&Yj(v,a,b,c.qb))return 0}if(q&&!r)throw q;return 1}n&&"none"!==n.toLowerCase()&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return bk(n,c.path)?1:Yj(g,a,b,c.qb)?0:1},dk=function(a,b,c){null==c.path&&(c.path="/");c.domain||(c.domain="auto");return ck(a,
b,c)};function Wj(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],l=b(h);l===c?d.push(h):void 0===f||l<f?(e=[h],f=l):l===f&&e.push(h)}return 0<d.length?d:e}function Vj(a,b,c){for(var d=[],e=Uj(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||-1!==b.indexOf(h)){var l=g.shift();l&&(l=l.split("-"),d.push({id:g.join("."),Ce:1*l[0]||1,yd:1*l[1]||1}))}}return d}
var Zj=function(a){a&&1200<a.length&&(a=a.substring(0,1200));return a},ek=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,fk=/(^|\.)doubleclick\.net$/i,bk=function(a,b){return fk.test(window.document.location.hostname)||"/"===b&&ek.test(a)},Tj=function(){return Rj(window)?window.document.cookie:""},ak=function(){var a=[],b=window.document.location.hostname.split(".");if(4===b.length){var c=b[b.length-1];if(parseInt(c,10).toString()===c)return["none"]}for(var d=b.length-2;0<=d;d--)a.push(b.slice(d).join("."));
var e=window.document.location.hostname;fk.test(e)||ek.test(e)||a.push("none");return a},Sj=function(a){if(!kj().h()||!a||!wj())return!0;if(!vj(a))return!1;var b=tj(a);return null==b?!0:!!b};var gk=function(a){var b=Math.round(2147483647*Math.random());return a?String(b^Nj(a)&2147483647):String(b)},hk=function(a){return[gk(a),Math.round(Ua()/1E3)].join(".")},kk=function(a,b,c,d,e){var f=ik(b);return Xj(a,f,jk(c),d,e)},lk=function(a,b,c,d){var e=""+ik(c),f=jk(d);1<f&&(e+="-"+f);return[b,e,a].join(".")},ik=function(a){if(!a)return 1;a=0===a.indexOf(".")?a.substr(1):a;return a.split(".").length},jk=function(a){if(!a||"/"===a)return 1;"/"!==a[0]&&(a="/"+a);"/"!==a[a.length-1]&&(a+="/");return a.split("/").length-
1};function mk(a,b,c,d){var e,f=Number(null!=a.Db?a.Db:void 0);0!==f&&(e=new Date((b||Ua())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,qb:d}};var nk;var rk=function(){var a=ok,b=pk,c=qk(),d=function(g){a(g.target||g.srcElement||{})},e=function(g){b(g.target||g.srcElement||{})};if(!c.init){gc(I,"mousedown",d);gc(I,"keyup",d);gc(I,"submit",e);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){b(this);f.call(this)};c.init=!0}},sk=function(a,b,c,d,e){var f={callback:a,domains:b,fragment:2===c,placement:c,forms:d,sameHost:e};qk().decorators.push(f)},tk=function(a,b,c){for(var d=qk().decorators,e={},f=0;f<d.length;++f){var g=
d[f],h;if(h=!c||g.forms)a:{var l=g.domains,n=a,p=!!g.sameHost;if(l&&(p||n!==I.location.hostname))for(var q=0;q<l.length;q++)if(l[q]instanceof RegExp){if(l[q].test(n)){h=!0;break a}}else if(0<=n.indexOf(l[q])||p&&0<=l[q].indexOf(n)){h=!0;break a}h=!1}if(h){var r=g.placement;void 0==r&&(r=g.fragment?2:1);r===b&&Ya(e,g.callback())}}return e};function qk(){var a=Yb("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var uk=/(.*?)\*(.*?)\*(.*)/,vk=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,wk=/^(?:www\.|m\.|amp\.)+/,xk=/([^?#]+)(\?[^#]*)?(#.*)?/;function yk(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}var Ak=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];void 0!==d&&d===d&&null!==d&&"[object Object]"!==d.toString()&&(b.push(c),b.push(tb(String(d))))}var e=b.join("*");return["1",zk(e),e].join("*")};
function zk(a,b){var c=[Wb.userAgent,(new Date).getTimezoneOffset(),Wb.userLanguage||Wb.language,Math.floor(Ua()/60/1E3)-(void 0===b?0:b),a].join("*"),d;if(!(d=nk)){for(var e=Array(256),f=0;256>f;f++){for(var g=f,h=0;8>h;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}nk=d;for(var l=4294967295,n=0;n<c.length;n++)l=l>>>8^nk[(l^c.charCodeAt(n))&255];return((l^-1)>>>0).toString(36)}
function Bk(){return function(a){var b=Ff(z.location.href),c=b.search.replace("?",""),d=Af(c,"_gl",!1,!0)||"";a.query=Ck(d)||{};var e=Df(b,"fragment").match(yk("_gl"));a.fragment=Ck(e&&e[3]||"")||{}}}function Dk(a,b){var c=yk(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
var Ek=function(a,b){b||(b="_gl");var c=xk.exec(a);if(!c)return"";var d=c[1],e=Dk(b,(c[2]||"").slice(1)),f=Dk(b,(c[3]||"").slice(1));e.length&&(e="?"+e);f.length&&(f="#"+f);return""+d+e+f},Fk=function(a){var b=Bk(),c=qk();c.data||(c.data={query:{},fragment:{}},b(c.data));var d={},e=c.data;e&&(Ya(d,e.query),a&&Ya(d,e.fragment));return d},Ck=function(a){try{var b=Gk(a,3);if(void 0!==b){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ub(d[e+1]);c[f]=g}wb("TAGGING",6);return c}}catch(h){wb("TAGGING",
8)}};function Gk(a,b){if(a){var c;a:{for(var d=a,e=0;3>e;++e){var f=uk.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&"1"===g[1]){var h=g[3],l;a:{for(var n=g[2],p=0;p<b;++p)if(n===zk(h,p)){l=!0;break a}l=!1}if(l)return h;wb("TAGGING",7)}}}
function Hk(a,b,c,d){function e(p){p=Dk(a,p);var q=p.charAt(p.length-1);p&&"&"!==q&&(p+="&");return p+n}d=void 0===d?!1:d;var f=xk.exec(c);if(!f)return"";var g=f[1],h=f[2]||"",l=f[3]||"",n=a+"="+b;d?l="#"+e(l.substring(1)):h="?"+e(h.substring(1));return""+g+h+l}
function Ik(a,b){var c="FORM"===(a.tagName||"").toUpperCase(),d=tk(b,1,c),e=tk(b,2,c),f=tk(b,3,c);if(Za(d)){var g=Ak(d);c?Jk("_gl",g,a):Kk("_gl",g,a,!1)}if(!c&&Za(e)){var h=Ak(e);Kk("_gl",h,a,!0)}for(var l in f)if(f.hasOwnProperty(l))a:{var n=l,p=f[l],q=a;if(q.tagName){if("a"===q.tagName.toLowerCase()){Kk(n,p,q);break a}if("form"===q.tagName.toLowerCase()){Jk(n,p,q);break a}}"string"==typeof q&&Hk(n,p,q)}}
function Kk(a,b,c,d){if(c.href){var e=Hk(a,b,c.href,void 0===d?!1:d);Gb.test(e)&&(c.href=e)}}
function Jk(a,b,c){if(c&&c.action){var d=(c.method||"").toLowerCase();if("get"===d){for(var e=c.childNodes||[],f=!1,g=0;g<e.length;g++){var h=e[g];if(h.name===a){h.setAttribute("value",b);f=!0;break}}if(!f){var l=I.createElement("input");l.setAttribute("type","hidden");l.setAttribute("name",a);l.setAttribute("value",b);c.appendChild(l)}}else if("post"===d){var n=Hk(a,b,c.action);Gb.test(n)&&(c.action=n)}}}
function ok(a){try{var b;a:{for(var c=a,d=100;c&&0<d;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;"http:"!==f&&"https:"!==f||Ik(e,e.hostname)}}catch(g){}}function pk(a){try{if(a.action){var b=Df(Ff(a.action),"host");Ik(a,b)}}catch(c){}}
var Lk=function(a,b,c,d){rk();sk(a,b,"fragment"===c?2:1,!!d,!1)},Mk=function(a,b){rk();sk(a,[Cf(z.location,"host",!0)],b,!0,!0)},Nk=function(){var a=I.location.hostname,b=vk.exec(I.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e="s"===g?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(0===d.indexOf("xn--"))return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(wk,""),l=e.replace(wk,""),n;if(!(n=h===l)){var p="."+l;n=h.substring(h.length-p.length,
h.length)===p}return n},Ok=function(a,b){return!1===a?!1:a||b||Nk()};var Pk={},Qk=function(a){return void 0==Pk[a]?!1:Pk[a]};var Rk=["1"],Sk={},Tk={},Yk=function(a,b){b=void 0===b?!0:b;var c=Uk(a.prefix);if(!Sk[c])if(Vk(c,a.path,a.domain)){if(Qk("enable_auid_cross_domain")){var d=Tk[Uk(a.prefix)];Wk(a,d?d.id:void 0,d?d.fg:void 0)}}else{if(Qk("enable_auid_fl_iframe")){var e=Hf("auiddc");if(e){wb("TAGGING",17);Sk[c]=e;return}}if(b){var f=Uk(a.prefix),g=hk();if(0===Xk(f,g,a)){var h=Yb("google_tag_data",{});h._gcl_au||(h._gcl_au=g)}Vk(c,a.path,a.domain)}}};
function Wk(a,b,c){var d=Uk(a.prefix),e=Sk[d];if(e){var f=e.split(".");if(2===f.length){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Ua()/1E3)));Xk(d,h,a,1E3*g)}}}}function Xk(a,b,c,d){var e=lk(b,"1",c.domain,c.path),f=mk(c,d);f.qb="ad_storage";return dk(a,e,f)}function Vk(a,b,c){var d=kk(a,b,c,Rk,"ad_storage");if(!d)return!1;Zk(a,d);return!0}
function Zk(a,b){var c=b.split(".");5===c.length?(Sk[a]=c.slice(0,2).join("."),Tk[a]={id:c.slice(2,4).join("."),fg:Number(c[4])||0}):3===c.length?Tk[a]={id:c.slice(0,2).join("."),fg:Number(c[2])||0}:Sk[a]=b}function Uk(a){return(a||"_gcl")+"_au"}function $k(a){wj()||a();Aj(function(){tj("ad_storage")&&a();Bj(a,"ad_storage")},["ad_storage"])}
function al(a){var b=Fk(!0),c=Uk(a.prefix);$k(function(){var d=b[c];if(d){Zk(c,d);var e=1E3*Number(Sk[c].split(".")[1]);if(e){wb("TAGGING",16);var f=mk(a,e);f.qb="ad_storage";var g=lk(d,"1",a.domain,a.path);dk(c,g,f)}}})}function bl(a,b,c,d){d=d||{};var e=function(){var f=Uk(d.prefix),g={},h=kk(f,d.path,d.domain,Rk,"ad_storage");if(!h)return g;g[f]=h;return g};$k(function(){Lk(e,a,b,c)})};var cl=[];cl[7]=!0;cl[9]=!0;cl[27]=!0;
cl[11]=!0;cl[13]=!0;cl[15]=!0;
cl[16]=!0;cl[25]=!0;
cl[36]=!0;
cl[38]=!0;cl[40]=!0;
cl[43]=!0;cl[45]=!0;a:{for(var dl,el,fl=0;dl===el;)if(dl=Math.floor(2*Math.random()),el=Math.floor(2*Math.random()),fl++,20<fl)break a;dl?cl[46]=!0:cl[47]=!0}cl[52]=!0;cl[57]=!0;
cl[58]=!0;cl[60]=!0;cl[61]=!0;cl[65]=!0;cl[68]=!0;
cl[69]=!0;cl[72]=!0;
cl[76]=!0;cl[77]=!0;
var V=function(a){return!!cl[a]};var gl=function(){Oh.dedupe_gclid||(Oh.dedupe_gclid=""+hk());return Oh.dedupe_gclid};var hl=function(){var a=!1;return a};var L={H:"G-SQ2V73809B",fb:"102422883"},il={ki:"G-SQ2V73809B|GT-5MXXBVV",li:"G-SQ2V73809B"};L.Df=Qa("");
var jl=function(){return il.ki?il.ki.split("|"):[L.H]},kl=function(){return il.li?il.li.split("|"):[]},Cl=function(){this.container={};this.destination={};this.canonical={}},El=function(){for(var a=Dl(),b=jl(),c=0;c<b.length;c++){var d=a.container[b[c]];!d||Ga(d)?a.container[b[c]]={state:2}:d.state=2}for(var e=kl(),f=0;f<e.length;f++){var g=a.destination[e[f]];g&&0===g.state&&Q(93);g?g.state=2:a.destination[e[f]]={state:2}}a.canonical[L.fb]=2},Fl=function(a){return!!Dl().container[a]},Gl=function(){var a=
Dl().container,b;for(b in a)if(a.hasOwnProperty(b)){var c=a[b];if(Ga(c)){if(1===c)return!0}else if(1===c.state)return!0}return!1},Hl=function(){var a={};m(Dl().destination,function(b,c){0===c.state&&(a[b]=c)});return a};function Dl(){var a=Oh.tidr;a||(a=new Cl,Oh.tidr=a);return a}var Il={"":"n",UA:"u",AW:"a",DC:"d",G:"e",GF:"f",GT:"t",HA:"h",MC:"m",GTM:"g",OPT:"o"},Jl={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7},Kl=function(a){var b=L.H.split("-"),c=b[0].toUpperCase();if(V(45)){var d={};d.Vj=L.H;d.Yk=Gh.dd;d.al=Gh.oc;d.Hk=L.Df?2:1;Uh?(d.Me=Jl[c],d.Me||(d.Me=0)):d.Me=$h?13:10;Yh?d.hg=1:hl()?d.hg=2:d.hg=3;var e;var f=d.Me,g=d.hg;void 0===f?e="":(g||(g=0),e=""+cg(1,1)+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f<<2|g]);var h=d.Hl,l=4+e+(h?""+cg(2,
1)+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]:""),n,p=d.al;n=p&&bg.test(p)?""+cg(3,2)+p:"";var q,r=d.Yk;q=r?""+cg(4,1)+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[r]:"";var u;var t=d.Vj;if(t&&a){var v=t.split("-"),w=v[0].toUpperCase();if("GTM"!==w&&"OPT"!==w)u="";else{var y=v[1];u=""+cg(5,3)+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[1+y.length]+(d.Hk||0)+y}}else u="";return l+n+q+u}var x=Il[c]||"i",A=a&&"GTM"===c?b[1]:"OPT"===
c?b[1]:"",B="w";Uh&&(B=hl()?"s":"o");Wh?("w"===B&&(B="x"),"o"===B&&(B="q")):Yh?("w"===B&&(B="y"),"o"===B&&(B="r")):$h&&(B="z");return"2"+B+x+(4===Gh.oc.length?Gh.oc.slice(1):Gh.oc)+A};function Ll(a,b){if(""===a)return b;var c=Number(a);return isNaN(c)?b:c};var Ml=function(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)};function Nl(){return Ib("iPhone")&&!Ib("iPod")&&!Ib("iPad")}function Ol(){Nl()||Ib("iPad")||Ib("iPod")};Ib("Opera");Ib("Trident")||Ib("MSIE");Ib("Edge");!Ib("Gecko")||-1!=Hb().toLowerCase().indexOf("webkit")&&!Ib("Edge")||Ib("Trident")||Ib("MSIE")||Ib("Edge");-1!=Hb().toLowerCase().indexOf("webkit")&&!Ib("Edge")&&Ib("Mobile");Ib("Macintosh");Ib("Windows");Ib("Linux")||Ib("CrOS");var Pl=qa.navigator||null;Pl&&(Pl.appVersion||"").indexOf("X11");Ib("Android");Nl();Ib("iPad");Ib("iPod");Ol();Hb().toLowerCase().indexOf("kaios");var Ql=function(a,b,c,d){for(var e=b,f=c.length;0<=(e=a.indexOf(c,e))&&e<d;){var g=a.charCodeAt(e-1);if(38==g||63==g){var h=a.charCodeAt(e+f);if(!h||61==h||38==h||35==h)return e}e+=f+1}return-1},Rl=/#|$/,Sl=function(a,b){var c=a.search(Rl),d=Ql(a,0,b,c);if(0>d)return null;var e=a.indexOf("&",d);if(0>e||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,-1!==e?e:0).replace(/\+/g," "))},Tl=/[?&]($|#)/,Ul=function(a,b,c){for(var d,e=a.search(Rl),f=0,g,h=[];0<=(g=Ql(a,f,b,e));)h.push(a.substring(f,
g)),f=Math.min(a.indexOf("&",g)+1||e,e);h.push(a.slice(f));d=h.join("").replace(Tl,"$1");var l,n=null!=c?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");0>r&&(r=d.length);var u=d.indexOf("?"),t;0>u||u>r?(u=r,t=""):t=d.substring(u+1,r);q=[d.slice(0,u),t,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;l=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else l=d;return l};var Vl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};function Wl(a){if(!a||!I.head)return null;var b=Xl("META");I.head.appendChild(b);b.httpEquiv="origin-trial";b.content=a;return b}
var Yl=function(){if(z.top==z)return 0;var a=z.location.ancestorOrigins;if(a)return a[a.length-1]==z.location.origin?1:2;var b;var c=z.top;try{var d;if(d=!!c&&null!=c.location.href)b:{try{ij(c.foo);d=!0;break b}catch(e){}d=!1}b=d}catch(e){b=!1}return b?1:2},Xl=function(a,b){b=void 0===b?document:b;return b.createElement(String(a).toLowerCase())};function Zl(a,b,c,d){d=void 0===d?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Xl("IMG",a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=zb(g,e);0<=h&&Array.prototype.splice.call(g,h,1)}e.removeEventListener&&e.removeEventListener("load",f,!1);e.removeEventListener&&e.removeEventListener("error",f,!1)};Ml(e,"load",f);Ml(e,"error",f)}d&&(e.attributionsrc="");e.src=b;a.google_image_requests.push(e)}
var am=function(a){var b;b=void 0===b?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Vl(a,function(d,e){d&&(c+="&"+e+"="+encodeURIComponent(d))});$l(c,b)},$l=function(a,b){var c=window,d;b=void 0===b?!1:b;d=void 0===d?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors",e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Zl(c,a,void 0===b?!1:b,void 0===d?!1:d)};var bm=function(){};var cm=function(a){void 0!==a.addtlConsent&&"string"!==typeof a.addtlConsent&&(a.addtlConsent=void 0);void 0!==a.gdprApplies&&"boolean"!==typeof a.gdprApplies&&(a.gdprApplies=void 0);return void 0!==a.tcString&&"string"!==typeof a.tcString||void 0!==a.listenerId&&"number"!==typeof a.listenerId?2:a.cmpStatus&&"error"!==a.cmpStatus?0:3},dm=function(a,b){b=void 0===b?{}:b;this.B=a;this.h=null;this.N={};this.eb=0;var c;this.U=null!=(c=b.sl)?c:500;var d;this.F=null!=(d=b.Il)?d:!1;this.D=null};pa(dm,bm);
dm.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.F},d=Qj(function(){return a(c)}),e=0;-1!==this.U&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.U));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=cm(c),c.internalBlockOnErrors=b.F,h&&0===c.internalErrorState||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{em(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};dm.prototype.removeEventListener=function(a){a&&a.listenerId&&em(this,"removeEventListener",null,a.listenerId)};
var gm=function(a,b,c){var d;d=void 0===d?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(void 0!==f){e=f[void 0===d?"755":d];break a}}e=void 0}var g=e;if(0===g)return!1;var h=c;2===c?(h=0,2===g&&(h=1)):3===c&&(h=1,1===g&&(h=0));var l;if(0===h)if(a.purpose&&a.vendor){var n=fm(a.vendor.consents,void 0===d?"755":d);l=n&&"1"===b&&a.purposeOneTreatment&&"CH"===a.publisherCC?!0:n&&fm(a.purpose.consents,b)}else l=!0;else l=1===h?a.purpose&&a.vendor?fm(a.purpose.legitimateInterests,
b)&&fm(a.vendor.legitimateInterests,void 0===d?"755":d):!0:!0;return l},fm=function(a,b){return!(!a||!a[b])},em=function(a,b,c,d){c||(c=function(){});if("function"===typeof a.B.__tcfapi){var e=a.B.__tcfapi;e(b,2,c,d)}else if(hm(a)){im(a);var f=++a.eb;a.N[f]=c;if(a.h){var g={};a.h.postMessage((g.__tcfapiCall={command:b,version:2,callId:f,parameter:d},g),"*")}}else c({},!1)},hm=function(a){if(a.h)return a.h;var b;a:{for(var c=a.B,d=0;50>d;++d){var e;try{e=!(!c.frames||!c.frames.__tcfapiLocator)}catch(h){e=
!1}if(e){b=c;break a}var f;b:{try{var g=c.parent;if(g&&g!=c){f=g;break b}}catch(h){}f=null}if(!(c=f))break}b=null}a.h=b;return a.h},im=function(a){a.D||(a.D=function(b){try{var c;c=("string"===typeof b.data?JSON.parse(b.data):b.data).__tcfapiReturn;a.N[c.callId](c.returnValue,c.success)}catch(d){}},Ml(a.B,"message",a.D))},jm=function(a){if(!1===a.gdprApplies)return!0;void 0===a.internalErrorState&&(a.internalErrorState=cm(a));return"error"===a.cmpStatus||0!==a.internalErrorState?a.internalBlockOnErrors?
(am({e:String(a.internalErrorState)}),!1):!0:"loaded"!==a.cmpStatus||"tcloaded"!==a.eventStatus&&"useractioncomplete"!==a.eventStatus?!1:!0};var km={1:0,3:0,4:0,7:3,9:3,10:3},lm=Ll('',500);function mm(){var a=Oh.tcf||{};return Oh.tcf=a}
var qm=function(){var a=mm(),b=new dm(z,{sl:-1});if(!0===z.gtag_enable_tcf_support&&!a.active&&("function"===typeof z.__tcfapi||"function"===typeof b.B.__tcfapi||null!=hm(b))){a.active=!0;a.Ie={};nm();a.tcString="tcunavailable";try{b.addEventListener(function(c){if(0!==c.internalErrorState)om(a),pm(a);else{var d;a.gdprApplies=c.gdprApplies;if(!1===c.gdprApplies){var e={},f;for(f in km)km.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if("tcloaded"===c.eventStatus||"useractioncomplete"===
c.eventStatus||"cmpuishown"===c.eventStatus){var g={},h;for(h in km)if(km.hasOwnProperty(h))if("1"===h){var l,n=c,p=!0;p=void 0===p?!1:p;l=jm(n)?!1===n.gdprApplies||"tcunavailable"===n.tcString||void 0===n.gdprApplies&&!p||"string"!==typeof n.tcString||!n.tcString.length?!0:gm(n,"1",0):!1;g["1"]=l}else g[h]=gm(c,h,km[h]);d=g}d&&(a.tcString=c.tcString||"tcempty",a.Ie=d,pm(a))}})}catch(c){om(a),pm(a)}}};function om(a){a.type="e";a.tcString="tcunavailable"}
function nm(){var a={},b=(a.ad_storage="denied",a.wait_for_update=lm,a);Gj(b)}function pm(a){var b={},c=(b.ad_storage=a.Ie["1"]?"granted":"denied",b);Hj(c,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:rm()})}var rm=function(){var a=mm();return a.active?a.tcString||"":""},sm=function(){var a=mm();return a.active&&void 0!==a.gdprApplies?a.gdprApplies?"1":"0":""},tm=function(a){if(!km.hasOwnProperty(String(a)))return!0;var b=mm();return b.active&&b.Ie?!!b.Ie[String(a)]:!0};var um=function(a){var b=String(a[Zd.Wa]||"").replace(/_/g,"");0===b.indexOf("cvt")&&(b="cvt");return b},vm=0<=z.location.search.indexOf("?gtm_latency=")||0<=z.location.search.indexOf("&gtm_latency=");var wm=["L","S","Y"],xm=["S","E"],ym={sampleRate:"0.005000",Hi:"",Gi:Number("5"),Fi:Number("")},zm;if(!(zm=vm)){var Am=Math.random(),Bm=ym.sampleRate;zm=Am<Bm}
var Cm=zm,Dm="https://www.googletagmanager.com/a?id="+L.H+"&cv=1",Em={label:L.H+" Container",children:[{label:"Initialization",children:[]}]};function Fm(){return[Dm,"&v=3&t=t","&pid="+La(),"&rv="+Gh.oc].join("")}var Gm=Fm();function Hm(){Gm=Fm()}var Im={},Jm="",Km="",Lm="",Mm="",Nm=[],Om="",Pm={},Qm=!1,Rm={},Sm={},Tm={},Um="",Vm=void 0,Wm={},Xm={},Ym=void 0,Zm=5;0<ym.Gi&&(Zm=ym.Gi);
var $m=function(a,b){for(var c=0,d=[],e=0;e<a;++e)d.push(0);return{zk:function(){return c<a?!1:Ua()-d[c%a]<b},Vk:function(){var f=c++%a;d[f]=Ua()}}}(Zm,1E3),an=1E3,bn="";
function cn(a){var b=Vm;if(void 0===b)return"";var c=yb("GTM"),d=yb("TAGGING"),e=yb("HEALTH"),f=Gm,g=Im[b]?"":"&es=1",h=Wm[b],l=dn(b),n=en(),p=Jm,q=Km,r=Um,u=fn(a),t=Lm,v=Mm,w;return[f,g,h,l,c?"&u="+c:"",d?"&ut="+d:"",e?"&h="+e:"",n,p,q,r,u,t,v,w,Om?"&dl="+encodeURIComponent(Om):"",0<Nm.length?"&tdp="+Nm.join("."):"",Gh.dd?
"&x="+Gh.dd:"","&z=0"].join("")}function hn(){Ym&&(z.clearTimeout(Ym),Ym=void 0);if(void 0!==Vm&&(!Im[Vm]||Jm||Km))if(Xm[Vm]||$m.zk()||0>=an--)Q(1),Xm[Vm]=!0;else{$m.Vk();var a=cn(!0);fc(a);if(Mm||Om&&0<Nm.length){var b=a.replace("/a?","/td?");fc(b)}Im[Vm]=!0;Om=Mm=Lm=Um=Km=Jm="";Nm=[]}}function jn(){Ym||(Ym=z.setTimeout(hn,500))}function kn(a){return a.match(/^(gtm|gtag)\./)?encodeURIComponent(a):"*"}function ln(){2022<=cn().length&&hn()}
function en(){return"&tc="+ye.filter(function(a){return a}).length}
var nn=function(a,b){if(Cm&&!Xm[a]&&Vm!==a){hn();Vm=a;Lm=Jm="";Wm[a]="&e="+kn(b)+"&eid="+a;jn();}},on=function(a,b,c,d){if(Cm&&b){var e=um(b),f=c+e;if(!Xm[a]){a!==Vm&&(hn(),Vm=a);Jm=Jm?Jm+"."+f:"&tr="+f;var g=b["function"];if(!g)throw Error("Error: No function name given for function call.");var h=(Ae[g]?"1":"2")+e;Lm=Lm?Lm+"."+h:"&ti="+h;jn();ln()}}},pn=function(a,b,c){if(Cm&&a&&a[Zd.Bb]){var d=b+"."+a[Zd.Bb];
Tm[d]=c;"html"==um(a)&&bn==d&&(Jm+=":"+Math.floor(c))}};
function fn(a){}
function dn(a){}
var wn=function(a,b,c){if(Cm&&void 0!==a&&!Xm[a]){a!==Vm&&(hn(),Vm=a);var d=c+b;Km=Km?Km+"."+d:"&epr="+d;jn();ln()}},xn=function(a,b,c){},gn=void 0;var yn=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);128>e?b[c++]=e:(2048>e?b[c++]=e>>6|192:(55296==(e&64512)&&d+1<a.length&&56320==(a.charCodeAt(d+1)&64512)?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Jb();Nl()||Ib("iPod");Ib("iPad");!Ib("Android")||Kb()||Jb()||Ib("Opera")||Ib("Silk");Kb();!Ib("Safari")||Kb()||Ib("Coast")||Ib("Opera")||Ib("Edge")||Ib("Edg/")||Ib("OPR")||Jb()||Ib("Silk")||Ib("Android")||Ol();var zn={},An=null,Bn=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);255<e&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;void 0===f&&(f=0);if(!An){An={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],l=0;5>l;l++){var n=g.concat(h[l].split(""));zn[l]=n;for(var p=0;p<n.length;p++){var q=n[p];void 0===An[q]&&(An[q]=p)}}}for(var r=zn[f],u=Array(Math.floor(b.length/3)),t=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
x=b[v+1],A=b[v+2],B=r[y>>2],C=r[(y&3)<<4|x>>4],D=r[(x&15)<<2|A>>6],H=r[A&63];u[w++]=""+B+C+D+H}var G=0,N=t;switch(b.length-v){case 2:G=b[v+1],N=r[(G&15)<<2]||t;case 1:var R=b[v];u[w]=""+r[R>>2]+r[(R&3)<<4|G>>4]+N+t}return u.join("")};var Cn="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Dn(a){var b;return null!=(b=a.google_tag_data)?b:a.google_tag_data={}}function En(){var a=z.google_tag_data,b;if(null!=a&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Fn(){var a,b;return null!=(b=null==(a=z.google_tag_data)?void 0:a.uach_promise)?b:null}
function Gn(a){var b,c;return"function"===typeof(null==(b=a.navigator)?void 0:null==(c=b.userAgentData)?void 0:c.getHighEntropyValues)}function Hn(){var a=z;if(!Gn(a))return null;var b=Dn(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Cn).then(function(d){null!=b.uach||(b.uach=d);return d});return b.uach_promise=c};
var In,Jn=function(){if(Gn(z)&&(In=Ua(),!Fn())){var a=Hn();a&&(a.then(function(){Q(95);}),a.catch(function(){Q(96)}))}},Ln=function(a){var b=Kn.xl,c=function(g,h){try{a(g,h)}catch(l){}},d=En();if(d)c(d);else{var e=Fn();if(e){b=
Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=z.setTimeout(function(){c.vd||(c.vd=!0,Q(106),c(null,Error("Timeout")))},b);e.then(function(g){c.vd||(c.vd=!0,Q(104),z.clearTimeout(f),c(g))}).catch(function(g){c.vd||(c.vd=!0,Q(105),z.clearTimeout(f),c(null,g))})}else c(null)}},Mn=function(a,b){a&&(b.C[T.g.rf]=a.architecture,b.C[T.g.sf]=a.bitness,a.fullVersionList&&(b.C[T.g.tf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|")),
b.C[T.g.uf]=a.mobile?"1":"0",b.C[T.g.vf]=a.model,b.C[T.g.wf]=a.platform,b.C[T.g.xf]=a.platformVersion,b.C[T.g.yf]=a.wow64?"1":"0")};var Nn=function(a){for(var b=[],c=I.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({yg:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b};
function On(a,b){var c=Nn(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!("1"!==f[0]||b&&3>f.length||!b&&3!==f.length)&&Number(f[1])){d[c[e].yg]||(d[c[e].yg]=[]);var g={version:f[0],timestamp:1E3*Number(f[1]),ia:f[2]};b&&3<f.length&&(g.labels=f.slice(3));d[c[e].yg].push(g)}}return d};var Pn=/^\w+$/,Qn=/^[\w-]+$/,Rn={aw:"_aw",dc:"_dc",gf:"_gf",ha:"_ha",gp:"_gp",gb:"_gb"},Sn=function(){if(!kj().h()||!wj())return!0;var a=tj("ad_storage");return null==a?!0:!!a},Tn=function(a,b){vj("ad_storage")?Sn()?a():Bj(a,"ad_storage"):b?wb("TAGGING",3):Aj(function(){Tn(a,!0)},["ad_storage"])},Vn=function(a){return Un(a).map(function(b){return b.ia})},Un=function(a){var b=[];if(!Rj(z)||!I.cookie)return b;var c=Uj(a,I.cookie,void 0,"ad_storage");if(!c||0==c.length)return b;for(var d={},e=0;e<c.length;d=
{Hd:d.Hd},e++){var f=Wn(c[e]);if(null!=f){var g=f,h=g.version;d.Hd=g.ia;var l=g.timestamp,n=g.labels,p=Ka(b,function(q){return function(r){return r.ia===q.Hd}}(d));p?(p.timestamp=Math.max(p.timestamp,l),p.labels=Xn(p.labels,n||[])):b.push({version:h,ia:d.Hd,timestamp:l,labels:n})}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Yn(b)};function Xn(a,b){for(var c={},d=[],e=0;e<a.length;e++)c[a[e]]=!0,d.push(a[e]);for(var f=0;f<b.length;f++)c[b[f]]||d.push(b[f]);return d}
function Zn(a){return a&&"string"==typeof a&&a.match(Pn)?a:"_gcl"}
var ao=function(){var a=Ff(z.location.href),b=Df(a,"query",!1,void 0,"gclid"),c=Df(a,"query",!1,void 0,"gclsrc"),d=Df(a,"query",!1,void 0,"wbraid"),e=Df(a,"query",!1,void 0,"dclid");if(!b||!c||!d){var f=a.hash.replace("#","");b=b||Af(f,"gclid",!1);c=c||Af(f,"gclsrc",!1);d=d||Af(f,"wbraid",!1)}return $n(b,c,e,d)},$n=function(a,b,c,d){var e={},f=function(g,h){e[h]||(e[h]=[]);e[h].push(g)};e.gclid=a;e.gclsrc=b;e.dclid=c;void 0!==d&&Qn.test(d)&&(e.gbraid=d,f(d,"gb"));if(void 0!==a&&a.match(Qn))switch(b){case void 0:f(a,
"aw");break;case "aw.ds":f(a,"aw");f(a,"dc");break;case "ds":f(a,"dc");break;case "3p.ds":f(a,"dc");break;case "gf":f(a,"gf");break;case "ha":f(a,"ha")}c&&f(c,"dc");return e},co=function(a){var b=ao();Tn(function(){bo(b,!1,a)})};
function bo(a,b,c,d,e){function f(w,y){var x=eo(w,g);x&&(dk(x,y,h),l=!0)}c=c||{};e=e||[];var g=Zn(c.prefix);d=d||Ua();var h=mk(c,d,!0);h.qb="ad_storage";var l=!1,n=Math.round(d/1E3),p=function(w){var y=["GCL",n,w];0<e.length&&y.push(e.join("."));return y.join(".")};a.aw&&f("aw",p(a.aw[0]));a.dc&&f("dc",p(a.dc[0]));a.gf&&f("gf",p(a.gf[0]));a.ha&&f("ha",p(a.ha[0]));a.gp&&f("gp",p(a.gp[0]));if(!l&&a.gb){var q=a.gb[0],r=eo("gb",g),u=!1;if(!b)for(var t=Un(r),v=0;v<t.length;v++)t[v].ia===q&&t[v].labels&&
0<t[v].labels.length&&(u=!0);u||f("gb",p(q))}}
var go=function(a,b){var c=Fk(!0);Tn(function(){for(var d=Zn(b.prefix),e=0;e<a.length;++e){var f=a[e];if(void 0!==Rn[f]){var g=eo(f,d),h=c[g];if(h){var l=Math.min(fo(h),Ua()),n;b:{var p=l;if(Rj(z))for(var q=Uj(g,I.cookie,void 0,"ad_storage"),r=0;r<q.length;++r)if(fo(q[r])>p){n=!0;break b}n=!1}if(!n){var u=mk(b,l,!0);u.qb="ad_storage";dk(g,h,u)}}}}bo($n(c.gclid,c.gclsrc),!1,b)})},eo=function(a,b){var c=Rn[a];if(void 0!==c)return b+c},fo=function(a){return 0!==ho(a.split(".")).length?1E3*(Number(a.split(".")[1])||
0):0};function Wn(a){var b=ho(a.split("."));return 0===b.length?null:{version:b[0],ia:b[2],timestamp:1E3*(Number(b[1])||0),labels:b.slice(3)}}function ho(a){return 3>a.length||"GCL"!==a[0]&&"1"!==a[0]||!/^\d+$/.test(a[1])||!Qn.test(a[2])?[]:a}
var io=function(a,b,c,d,e){if(Ia(b)&&Rj(z)){var f=Zn(e),g=function(){for(var h={},l=0;l<a.length;++l){var n=eo(a[l],f);if(n){var p=Uj(n,I.cookie,void 0,"ad_storage");p.length&&(h[n]=p.sort()[p.length-1])}}return h};Tn(function(){Lk(g,b,c,d)})}},Yn=function(a){return a.filter(function(b){return Qn.test(b.ia)})},jo=function(a,b){if(Rj(z)){for(var c=Zn(b.prefix),d={},e=0;e<a.length;e++)Rn[a[e]]&&(d[a[e]]=Rn[a[e]]);Tn(function(){m(d,function(f,g){var h=Uj(c+g,I.cookie,void 0,"ad_storage");h.sort(function(u,
t){return fo(t)-fo(u)});if(h.length){var l=h[0],n=fo(l),p=0!==ho(l.split(".")).length?l.split(".").slice(3):[],q={},r;r=0!==ho(l.split(".")).length?l.split(".")[2]:void 0;q[f]=[r];bo(q,!0,b,n,p)}})})}};function ko(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
var lo=function(a){function b(e,f,g){g&&(e[f]=g)}if(wj()){var c=ao();if(ko(c,a)){var d={};b(d,"gclid",c.gclid);b(d,"dclid",c.dclid);b(d,"gclsrc",c.gclsrc);b(d,"wbraid",c.gbraid);Mk(function(){return d},3);Mk(function(){var e={};return e._up="1",e},1)}}},mo=function(a,b,c,d){var e=[];c=c||{};if(!Sn())return e;var f=Un(a);if(!f.length)return e;for(var g=0;g<f.length;g++)-1===(f[g].labels||[]).indexOf(b)?e.push(0):e.push(1);if(d)return e;if(1!==e[0]){var h=f[0],l=f[0].timestamp,n=[h.version,Math.round(l/
1E3),h.ia].concat(h.labels||[],[b]).join("."),p=mk(c,l,!0);p.qb="ad_storage";dk(a,n,p)}return e};function no(a,b){var c=Zn(b),d=eo(a,c);if(!d)return 0;for(var e=Un(d),f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function oo(a){var b=0,c;for(c in a)for(var d=a[c],e=0;e<d.length;e++)b=Math.max(b,Number(d[e].timestamp));return b}var po=function(a){var b=Math.max(no("aw",a),oo(Sn()?On():{}));return Math.max(no("gb",a),oo(Sn()?On("_gac_gb",!0):{}))>b};var uo=/[A-Z]+/,vo=/\s/,wo=function(a){if(k(a)){a=Sa(a);var b=a.indexOf("-");if(!(0>b)){var c=a.substring(0,b);if(uo.test(c)){for(var d=a.substring(b+1).split("/"),e=0;e<d.length;e++)if(!d[e]||vo.test(d[e])&&("AW"!==c||1!==e))return;return{id:a,prefix:c,X:c+"-"+d[0],P:d}}}}},yo=function(a){for(var b={},c=0;c<a.length;++c){var d=wo(a[c]);d&&(b[d.id]=d)}xo(b);var e=[];m(b,function(f,g){e.push(g)});return e};
function xo(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];"AW"===d.prefix&&d.P[1]&&b.push(d.X)}for(var e=0;e<b.length;++e)delete a[b[e]]};var zo=function(a,b,c,d){var e=dc(),f;if(1===e)a:{var g=bi;g=g.toLowerCase();for(var h="https://"+g,l="http://"+g,n=1,p=I.getElementsByTagName("script"),q=0;q<p.length&&100>q;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(0===r.indexOf(l)){f=3;break a}1===n&&0===r.indexOf(h)&&(n=2)}}f=n}else f=e;return(2===f||d||"http:"!=z.location.protocol?a:b)+c};var Lo=function(a,b,c){this.target=a;this.eventName=b;this.s=c;this.C={};this.metadata=K(c.eventMetadata||{});this.M=!1},Mo=function(a,b,c){var d=U(a.s,b);void 0!==d?a.C[b]=d:void 0!==c&&(a.C[b]=c)},No=function(a,b,c){var d=yi(a.target.X);return d&&d.hasOwnProperty(b)?d[b]:c};function Oo(a){return{getDestinationId:function(){return a.target.X},getEventName:function(){return a.eventName},setEventName:function(b){return void(a.eventName=b)},getHitData:function(b){return a.C[b]},setHitData:function(b,c){return void(a.C[b]=c)},setHitDataIfNotDefined:function(b,c){void 0===a.C[b]&&(a.C[b]=c)},copyToHitData:function(b,c){Mo(a,b,c)},getMetadata:function(b){return a.metadata[b]},setMetadata:function(b,c){return void(a.metadata[b]=c)},abort:function(){return void(a.M=!0)},getProcessedEvent:function(){return a},
getFromEventContext:function(b){return U(a.s,b)}}};var Qo=function(a){var b=Po[a.target.X];if(!a.M&&b)for(var c=Oo(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.M=!0}if(a.M)break}},Ro=function(a,b){var c=Po[a];c||(c=Po[a]=[]);c.push(b)},Po={};var fp=function(a,b,c,d,e,f,g,h,l,n,p,q){this.eventId=a;this.priorityId=b;this.h=c;this.N=d;this.B=e;this.F=f;this.U=g;this.D=h;this.eventMetadata=l;this.aa=n;this.Z=p;this.J=q},U=function(a,b,c){if(void 0!==a.h[b])return a.h[b];if(void 0!==a.N[b])return a.N[b];if(void 0!==a.B[b])return a.B[b];Cm&&gp(a,a.F[b],a.U[b])&&(Q(71),Q(79));return void 0!==a.F[b]?a.F[b]:void 0!==a.D[b]?a.D[b]:c},hp=function(a){function b(g){for(var h=Object.keys(g),l=0;l<h.length;++l)c[h[l]]=1}var c={};b(a.h);b(a.N);b(a.B);
b(a.F);if(Cm)for(var d=Object.keys(a.U),e=0;e<d.length;e++){var f=d[e];if("event"!==f&&"gtm"!==f&&"tagTypeBlacklist"!==f&&!c.hasOwnProperty(f)){Q(71);Q(80);break}}return Object.keys(c)},ip=function(a,b,c){function d(l){Fc(l)&&m(l,function(n,p){f=!0;e[n]=p})}var e={},f=!1;c&&1!==c||(d(a.D[b]),d(a.F[b]),d(a.B[b]),d(a.N[b]));c&&2!==c||d(a.h[b]);if(Cm){var g=f,h=e;e={};f=!1;c&&1!==c||(d(a.D[b]),d(a.U[b]),d(a.B[b]),d(a.N[b]));c&&2!==c||d(a.h[b]);if(f!==g||gp(a,e,h))Q(71),Q(81);f=g;e=h}return f?e:void 0},
jp=function(a){var b=[T.g.Lc,T.g.Td,T.g.Ud,T.g.Vd,T.g.Wd,T.g.Xd,T.g.Yd],c={},d=!1,e=function(h){for(var l=0;l<b.length;l++)void 0!==h[b[l]]&&(c[b[l]]=h[b[l]],d=!0);return d};if(e(a.h)||e(a.N)||e(a.B))return c;e(a.F);if(Cm){var f=c,g=d;c={};d=!1;e(a.U);gp(a,c,f)&&(Q(71),Q(82));c=f;d=g}if(d)return c;e(a.D);return c},gp=function(a,b,c){if(!Cm)return!1;try{if(b===c)return!1;var d=Dc(b);if(d!==Dc(c)||!(Fc(b)&&Fc(c)||"array"===d))return!0;if("array"===d){if(b.length!==c.length)return!0;for(var e=0;e<b.length;e++)if(gp(a,
b[e],c[e]))return!0}else{for(var f in c)if(!b.hasOwnProperty(f))return!0;for(var g in b)if(!c.hasOwnProperty(g)||gp(a,b[g],c[g]))return!0}}catch(h){Q(72)}return!1},kp=function(a,b){this.vj=a;this.wj=b;this.F={};this.Dh={};this.h={};this.N={};this.B={};this.bd={};this.D={};this.Fc=function(){};this.eb=function(){};this.U=!1},lp=function(a,b){a.F=b;return a},mp=function(a,b){a.Dh=b;return a},np=function(a,b){a.h=b;return a},op=function(a,b){a.N=b;return a},pp=function(a,b){a.B=b;return a},qp=function(a,
b){a.bd=b;return a},rp=function(a,b){a.D=b||{};return a},sp=function(a,b){a.Fc=b;return a},tp=function(a,b){a.eb=b;return a},up=function(a){a.U=!0;return a},vp=function(a){return new fp(a.vj,a.wj,a.F,a.Dh,a.h,a.N,a.B,a.bd,a.D,a.Fc,a.eb,a.U)};function Ap(){return"attribution-reporting"}function Bp(a){var b;b=void 0===b?document:b;var c;return!(null==(c=b.featurePolicy)||!c.allowedFeatures().includes(a))};var Cp=!1;function Dp(){if(Bp("join-ad-interest-group")&&Ea(Wb.joinAdInterestGroup))return!0;Cp||(Wl('A751Xsk4ZW3DVQ8WZng2Dk5s3YzAyqncTzgv+VaE6wavgTY0QHkDvUTET1o7HanhuJO8lgv1Vvc88Ij78W1FIAAAAAB7eyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGV0YWdtYW5hZ2VyLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjgwNjUyNzk5LCJpc1RoaXJkUGFydHkiOnRydWV9'),Cp=!0);return Bp("join-ad-interest-group")&&Ea(Wb.joinAdInterestGroup)}
function Ep(a,b){var c=void 0;try{c=I.querySelector('iframe[data-tagging-id="'+b+'"]')}catch(e){}if(c){var d=Number(c.dataset.loadTime);if(d&&6E4>Ua()-d){wb("TAGGING",9);return}}else try{if(50<=I.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]').length){wb("TAGGING",10);return}}catch(e){}ec(a,void 0,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Ua()},c)}function Fp(){return V(60)?"https://td.doubleclick.net":"https://googleads.g.doubleclick.net"};var Gp=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Hp=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Ip=/^\d+\.fls\.doubleclick\.net$/,Jp=/;gac=([^;?]+)/,Kp=/;gacgb=([^;?]+)/,Lp=/;gclaw=([^;?]+)/,Mp=/;gclgb=([^;?]+)/;
function Np(a,b){if(Ip.test(I.location.host)){var c=I.location.href.match(b);return c&&2==c.length&&c[1].match(Gp)?decodeURIComponent(c[1]):""}var d=[],e;for(e in a){for(var f=[],g=a[e],h=0;h<g.length;h++)f.push(g[h].ia);d.push(e+":"+f.join(","))}return 0<d.length?d.join(";"):""}
var Op=function(a,b,c){var d=Sn()?On("_gac_gb",!0):{},e=[],f=!1,g;for(g in d){var h=mo("_gac_gb_"+g,a,b,c);f=f||0!==h.length&&h.some(function(l){return 1===l});e.push(g+":"+h.join(","))}return{hk:f?e.join(";"):"",gk:Np(d,Kp)}};function Pp(a,b,c){if(Ip.test(I.location.host)){var d=I.location.href.match(c);if(d&&2==d.length&&d[1].match(Hp))return[{ia:d[1]}]}else return Un((a||"_gcl")+b);return[]}
var Qp=function(a){return Pp(a,"_aw",Lp).map(function(b){return b.ia}).join(".")},Rp=function(a){return Pp(a,"_gb",Mp).map(function(b){return b.ia}).join(".")},Sp=function(a,b){var c=mo((b&&b.prefix||"_gcl")+"_gb",a,b);return 0===c.length||c.every(function(d){return 0===d})?"":c.join(".")};var Tp=function(){if(Ea(z.__uspapi)){var a="";try{z.__uspapi("getUSPData",1,function(b,c){if(c&&b){var d=b.uspString;d&&RegExp("^[\\da-zA-Z-]{1,20}$").test(d)&&(a=d)}})}catch(b){}return a}};var Dq={I:{Ag:"ads_conversion_hit",Te:"container_execute_start",Dg:"container_setup_end",Ue:"container_setup_start",Cg:"container_execute_end",Eg:"container_yield_end",Ve:"container_yield_start",Ah:"event_execute_end",Bh:"event_setup_end",ad:"event_setup_start",Ch:"ga4_conversion_hit",nc:"page_load",El:"pageview",Ab:"snippet_load",Oh:"tag_callback_error",Ph:"tag_callback_failure",Qh:"tag_callback_success",Rh:"tag_execute_end",qc:"tag_execute_start"}};var Eq=!1,Fq="L S Y E TC HTC".split(" "),Gq=["S","E"],Hq=["TS","TE"];
var dr=function(a,b,c,d,e,f){var g={};return g},er=function(a){var b=!1;return b},fr=function(a,b){},gr=function(){var a={};return a},Xq=function(){var a={};return a},ir=function(){},hr=function(){},jr=function(a,b,c){},
kr=function(a){pc()&&pc().mark(L.H+"_"+a+"_start")},lr=function(a){if(pc()){var b=pc(),c=L.H+"_"+a+"_start",d=L.H+"_"+a+"_duration";b.measure(d,c);var e=pc().getEntriesByName(d)[0];b.clearMarks(c);b.clearMeasures(d);var f=Oh._p||{};void 0===f[a]&&(f[a]=e.duration,Oh._p=f);return e.duration}},mr=function(){var a=dr("PAGEVIEW");if(Pq(a.lb,"mark")[0]){var b=pc();b.clearMarks(a.lb);b.clearMeasures("GTM-"+Dq.I.nc+":to:PAGEVIEW")}var c=dr(Dq.I.nc);er(a)&&fr(a,c)};var nr=function(a,b){var c=z,d,e=c.GooglebQhCsO;e||(e={},c.GooglebQhCsO=e);d=e;if(d[a])return!1;d[a]=[];d[a][0]=b;return!0};var or=function(a,b){var c=Sl(a,"fmt");if(b){var d=Sl(a,"random"),e=Sl(a,"label")||"";if(!d)return!1;var f=Bn(decodeURIComponent(e.replace(/\+/g," "))+":"+decodeURIComponent(d.replace(/\+/g," ")));if(!nr(f,b))return!1}c&&4!=c&&(a=Ul(a,"rfmt",c));var g=Ul(a,"fmt",4);cc(g,function(){z.google_noFurtherRedirects&&b&&b.call&&(z.google_noFurtherRedirects=null,b())},void 0,void 0,I.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Er=function(){this.h={}},Fr=function(a,b,c){null!=c&&(a.h[b]=c)},Gr=function(a){return Object.keys(a.h).map(function(b){return encodeURIComponent(b)+"="+encodeURIComponent(a.h[b])}).join("&")},Ir=function(a,b,c,d){};function Kr(a,b){if(a){var c=""+a;0!==c.indexOf("http://")&&0!==c.indexOf("https://")&&(c="https://"+c);"/"===c[c.length-1]&&(c=c.substring(0,c.length-1));return Ff(""+c+b).href}}function Lr(){return!!Gh.we&&"SGTM_TOKEN"!==Gh.we.split("@@").join("")};var Nr=function(a,b,c,d){if(!Mr()&&!Fl(a)){var e=c?"/gtag/js":"/gtm.js",f="?id="+encodeURIComponent(a)+"&l="+Gh.ka,g=0===a.indexOf("GTM-");g||(f+="&cx=c");var h=Lr();h&&(f+="&sign="+Gh.we);var l=Wh||Yh?Kr(b,e+f):void 0;if(!l){var n=Gh.Md+e;h&&Xb&&g&&(n=Xb.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);l=zo("https://","http://",n+f)}Dl().container[a]={state:1,context:d};cc(l)}},Or=function(a,b,c){var d;if(d=!Mr()){var e=Dl().destination[a];d=!(e&&e.state)}if(d)if(Gl())Dl().destination[a]={state:0,
transportUrl:b,context:c},Q(91);else{var f="/gtag/destination?id="+encodeURIComponent(a)+"&l="+Gh.ka+"&cx=c";Lr()&&(f+="&sign="+Gh.we);var g=Wh||Yh?Kr(b,f):void 0;g||(g=zo("https://","http://",Gh.Md+f));Dl().destination[a]={state:1,context:c};cc(g)}};function Mr(){if(hl()){return!0}return!1};var Pr=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),Qr={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},Rr={cl:["ecl"],customPixels:["customScripts","html"],
ecl:["cl"],ehl:["hl"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},Sr="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" "),Vr=function(a){var b=ni("gtm.allowlist")||ni("gtm.whitelist");b&&Q(9);Uh&&(b=["google","gtagfl","lcl","zone"]);Tr()&&(Uh?
Q(116):Q(117),Ur&&(b=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728.")));var c=b&&$a(Ra(b),Qr),d=ni("gtm.blocklist")||ni("gtm.blacklist");d||(d=ni("tagTypeBlacklist"))&&Q(3);d?Q(8):d=[];Tr()&&(d=Ra(d),d.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));0<=Ra(d).indexOf("google")&&Q(2);var e=d&&$a(Ra(d),Rr),f={};return function(g){var h=g&&g[Zd.Wa];if(!h||"string"!=typeof h)return!0;h=h.replace(/^_*/,"");if(void 0!==f[h])return f[h];var l=fi[h]||
[],n=a(h,l);if(b){var p;if(p=n)a:{if(0>c.indexOf(h))if(l&&0<l.length)for(var q=0;q<l.length;q++){if(0>c.indexOf(l[q])){Q(11);p=!1;break a}}else{p=!1;break a}p=!0}n=p}var r=!1;if(d){var u=0<=e.indexOf(h);if(u)r=u;else{var t=Na(e,l||[]);t&&Q(10);r=t}}var v=!n||r;v||!(0<=l.indexOf("sandboxedScripts"))||c&&-1!==c.indexOf("sandboxedScripts")||(v=Na(e,Sr));return f[h]=v}},Ur=!1;
var Tr=function(){return Pr.test(z.location&&z.location.hostname)};var Wr={initialized:11,complete:12,interactive:13},Xr={},Yr=Object.freeze((Xr[T.g.Na]=!0,Xr)),Zr=0<=I.location.search.indexOf("?gtm_diagnostics=")||0<=I.location.search.indexOf("&gtm_diagnostics="),as=function(a,b,c){if(Cm&&"config"===a&&!(1<wo(b).P.length)){var d,e=Yb("google_tag_data",{});e.td||(e.td={});d=e.td;var f=K(c.F);K(c.h,f);var g=[],h;for(h in d){var l=$r(d[h],f);l.length&&(Zr&&console.log(l),g.push(h))}if(g.length){if(g.length){var n=b+"*"+g.join(".");Mm=Mm?Mm+"!"+n:"&tdc="+n}wb("TAGGING",
Wr[I.readyState]||14)}d[b]=f}};function bs(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}function $r(a,b,c,d){c=void 0===c?{}:c;d=void 0===d?"":d;if(a===b)return[];var e=function(q,r){var u=r[q];return void 0===u?Yr[q]:u},f;for(f in bs(a,b)){var g=(d?d+".":"")+f,h=e(f,a),l=e(f,b),n="object"===Dc(h)||"array"===Dc(h),p="object"===Dc(l)||"array"===Dc(l);if(n&&p)$r(h,l,c,g);else if(n||p||h!==l)c[g]=!0}return Object.keys(c)};var cs=!1,ds=0,es=[];function fs(a){if(!cs){var b=I.createEventObject,c="complete"==I.readyState,d="interactive"==I.readyState;if(!a||"readystatechange"!=a.type||c||!b&&d){cs=!0;for(var e=0;e<es.length;e++)J(es[e])}es.push=function(){for(var f=0;f<arguments.length;f++)J(arguments[f]);return 0}}}function gs(){if(!cs&&140>ds){ds++;try{I.documentElement.doScroll("left"),fs()}catch(a){z.setTimeout(gs,50)}}}var hs=function(a){cs?a():es.push(a)};var js=function(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:L.H}};var ls=function(a,b){this.h=!1;this.F=[];this.N={tags:[]};this.U=!1;this.B=this.D=0;ks(this,a,b)},ms=function(a,b,c,d){if(Rh.hasOwnProperty(b)||"__zone"===b)return-1;var e={};Fc(d)&&(e=K(d,e));e.id=c;e.status="timeout";return a.N.tags.push(e)-1},ns=function(a,b,c,d){var e=a.N.tags[b];e&&(e.status=c,e.executionTime=d)},os=function(a){if(!a.h){for(var b=a.F,c=0;c<b.length;c++)b[c]();a.h=!0;a.F.length=0}},ks=function(a,b,c){void 0!==b&&a.ye(b);c&&z.setTimeout(function(){return os(a)},Number(c))};
ls.prototype.ye=function(a){var b=this,c=Xa(function(){return J(function(){a(L.H,b.N)})});this.h?c():this.F.push(c)};var ps=function(a){a.D++;return Xa(function(){a.B++;a.U&&a.B>=a.D&&os(a)})},qs=function(a){a.U=!0;a.B>=a.D&&os(a)};var rs={},ss=function(){return z.GoogleAnalyticsObject&&z[z.GoogleAnalyticsObject]},ts=!1;
function ws(){return z.GoogleAnalyticsObject||"ga"}
var xs=function(a){},ys=function(a,b){return function(){var c=ss(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),l=0>g.indexOf("&tid="+b);l&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);l&&(f.set("hitPayload",
g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};function Ds(a,b,c,d){var e=ye[a],f=Es(a,b,c,d);if(!f)return null;var g=Je(e[Zd.Nh],c,[]);if(g&&g.length){var h=g[0];f=Ds(h.index,{aa:f,Z:1===h.ci?b.terminate:f,terminate:b.terminate},c,d)}return f}
function Es(a,b,c,d){function e(){if(f[Zd.Bj])h();else{var w=Ke(f,c,[]),y=w[Zd.Li];if(null!=y)for(var x=0;x<y.length;x++)if(!Ij(y[x])){h();return}var A=ms(c.Cb,String(f[Zd.Wa]),Number(f[Zd.Bb]),w[Zd.Cj]),B=!1;w.vtp_gtmOnSuccess=function(){if(!B){B=!0;var G=Ua()-H;on(c.id,ye[a],"5",G);ns(c.Cb,A,"success",G);V(70)&&jr(c,f,Dq.I.Qh);g()}};w.vtp_gtmOnFailure=function(){if(!B){B=!0;var G=Ua()-H;on(c.id,ye[a],"6",G);ns(c.Cb,A,"failure",G);V(70)&&jr(c,f,Dq.I.Ph);h()}};w.vtp_gtmTagId=f.tag_id;w.vtp_gtmEventId=
c.id;c.priorityId&&(w.vtp_gtmPriorityId=c.priorityId);on(c.id,f,"1");var C=function(){var G=Ua()-H;on(c.id,f,"7",G);ns(c.Cb,A,"exception",G);V(70)&&jr(c,f,Dq.I.Oh);B||(B=!0,h())};if(V(70)){var D=dr(Dq.I.qc,L.H,c.id,Number(f[Zd.Bb]),c.name,um(f));er(D)}var H=Ua();try{Ie(w,{event:c,index:a,type:1})}catch(G){C(G)}V(70)&&jr(c,f,Dq.I.Rh)}}var f=ye[a],g=b.aa,h=b.Z,l=b.terminate;if(c.Zf(f))return null;var n=Je(f[Zd.Sh],c,[]);if(n&&n.length){var p=n[0],q=Ds(p.index,{aa:g,Z:h,terminate:l},c,d);if(!q)return null;g=q;h=2===p.ci?l:q}if(f[Zd.Jh]||f[Zd.Ej]){var r=f[Zd.Jh]?ze:c.pl,u=g,t=h;if(!r[a]){e=Xa(e);var v=Fs(a,r,e);g=
v.aa;h=v.Z}return function(){r[a](u,t)}}return e}function Fs(a,b,c){var d=[],e=[];b[a]=Gs(d,e,c);return{aa:function(){b[a]=Hs;for(var f=0;f<d.length;f++)d[f]()},Z:function(){b[a]=Is;for(var f=0;f<e.length;f++)e[f]()}}}function Gs(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function Hs(a){a()}function Is(a,b){b()};var Ks=function(a,b){return 1===arguments.length?Js("set",a):Js("set",a,b)},Ls=function(a,b){return 1===arguments.length?Js("config",a):Js("config",a,b)},Ms=function(a,b,c){c=c||{};c[T.g.Ob]=a;return Js("event",b,c)};function Js(a){return arguments}var Ns=function(){this.h=[];this.B=[]};Ns.prototype.enqueue=function(a,b,c){var d=this.h.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;c.eventId=b;c.fromContainerExecution=!0;c.priorityId=d;var e={message:a,notBeforeEventId:b,priorityId:d,messageContext:c};this.h.push(e);for(var f=0;f<this.B.length;f++)try{this.B[f](e)}catch(g){}};Ns.prototype.listen=function(a){this.B.push(a)};
Ns.prototype.get=function(){for(var a={},b=0;b<this.h.length;b++){var c=this.h[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Ns.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.h.length;d++){var e=this.h[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.h=c;return b};var Ps=function(a,b,c){Os().enqueue(a,b,c)},Rs=function(){var a=Qs;Os().listen(a)};function Os(){var a=Oh.mb;a||(a=new Ns,Oh.mb=a);return a}var Zs=function(a){var b=Oh.zones;return b?b.getIsAllowedFn(jl(),a):function(){return!0}},$s=function(a){var b=Oh.zones;return b?b.isActive(jl(),a):!0};var ct=function(a,b){for(var c=[],d=0;d<ye.length;d++)if(a[d]){var e=ye[d];var f=ps(b.Cb);try{var g=Ds(d,{aa:f,Z:f,terminate:f},b,d);if(g){var h=c,l=h.push,n=d,p=e["function"];if(!p)throw"Error: No function name given for function call.";var q=Ae[p];l.call(h,{zi:n,ni:q?q.priorityOverride||0:0,execute:g})}else at(d,b),f()}catch(u){f()}}c.sort(bt);for(var r=0;r<c.length;r++)c[r].execute();return 0<c.length};function bt(a,b){var c,d=b.ni,e=a.ni;c=d>e?1:d<e?-1:0;var f;if(0!==c)f=c;else{var g=a.zi,h=b.zi;f=g>h?1:g<h?-1:0}return f}
function at(a,b){if(Cm){var c=function(d){var e=b.Zf(ye[d])?"3":"4",f=Je(ye[d][Zd.Nh],b,[]);f&&f.length&&c(f[0].index);on(b.id,ye[d],e);var g=Je(ye[d][Zd.Sh],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var It=!1,dt;
var Ot=function(a){var b=Ua(),c=a["gtm.uniqueEventId"],d=a["gtm.priorityId"],e=a.event;if(V(70)){var f=dr(Dq.I.ad,L.H,c,void 0,e);er(f)}if("gtm.js"===e){if(It)return!1;It=!0;}var l,n=!1;if($s(c))l=Zs(c);else{if("gtm.js"!==e&&"gtm.init"!==e&&"gtm.init_consent"!==e)return!1;n=!0;l=Zs(Number.MAX_SAFE_INTEGER)}nn(c,
e);var p=a.eventCallback,q=a.eventTimeout,r={id:c,priorityId:d,name:e,Zf:Vr(l),pl:[],ii:function(){Q(6);wb("HEALTH",0)},Wh:Kt(),Xh:Lt(c),Cb:new ls(function(){if(V(70)){var x=dr(Dq.I.Ah,L.H,c,void 0,e);if(er(x)){var A=dr(Dq.I.ad,L.H,c,void 0,e);fr(x,A)}if("gtm.load"===e){var B=dr(Dq.I.Cg,L.H);if(er(B)){var C=dr(Dq.I.Te,L.H);fr(B,C)}ir();hr()}}p&&p.apply(p,[].slice.call(arguments,
0))},q)},u=Te(r);n&&(u=Mt(u));if(V(70)){var t=dr(Dq.I.Bh,L.H,c,void 0,e);if(er(t)){var v=dr(Dq.I.ad,L.H,c,void 0,e);fr(t,v)}}var w=ct(u,r),y=!1;
qs(r.Cb);"gtm.js"!==e&&"gtm.sync"!==e||xs(L.H);return Nt(u,w)||y};function Lt(a){return function(b){Cm&&(Jc(b)||xn(a,"input",b))}}function Kt(){var a={};a.event=si("event",1);a.ecommerce=si("ecommerce",1);a.gtm=si("gtm");a.eventModel=si("eventModel");return a}
function Mt(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(ye[c][Zd.Wa]);if(Qh[d]||void 0!==ye[c][Zd.Fj]||gi[d])b[c]=!0;V(58)||0!==ye[c][Zd.Wa].indexOf("__ccd")&&0!==ye[c][Zd.Wa].indexOf("__ogt")&&"__set_product_settings"!==ye[c][Zd.Wa]||(b[c]=!0)}return b}function Nt(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&ye[c]&&!Rh[String(ye[c][Zd.Wa])])return!0;return!1}var Qt=function(a,b,c,d){Pt.push("event",[b,a],c,d)},Rt=function(a,b,c,d){Pt.push("get",[a,b],c,d)},St=function(){this.status=1;this.N={};this.h={};this.B={};this.U=null;this.F={};this.D=!1},Tt=function(a,b,c,d){var e=Ua();this.type=a;this.B=e;this.ca=b||"";this.h=c;this.messageContext=d},Ut=function(){this.B={};this.D={};this.h=[]},Vt=function(a,b){var c=wo(b);return a.B[c.X]=a.B[c.X]||new St},Wt=function(a,b,c,d){if(d.ca){var e=Vt(a,d.ca),f=e.U;if(f){var g=K(c),h=K(e.N[d.ca]),l=K(e.F),n=K(e.h),
p=K(a.D),q={};if(Cm)try{q=K(ki)}catch(v){Q(72)}var r=wo(d.ca).prefix,u=function(v){wn(d.messageContext.eventId,r,v);var w=g[T.g.cc];w&&J(w)},t=vp(tp(sp(rp(pp(op(qp(np(mp(lp(new kp(d.messageContext.eventId,d.messageContext.priorityId),g),h),l),n),p),q),d.messageContext.eventMetadata),function(){if(u){var v=u;u=void 0;v("2")}}),function(){if(u){var v=u;u=void 0;v("3")}}));try{wn(d.messageContext.eventId,r,"1"),as(d.type,d.ca,t),f(d.ca,b,d.B,t)}catch(v){wn(d.messageContext.eventId,r,"4")}}}};
Ut.prototype.register=function(a,b,c){var d=Vt(this,a);3!==d.status&&(d.U=b,d.status=3,c&&(K(d.h,c),d.h=c),this.flush())};Ut.prototype.push=function(a,b,c,d){if(void 0!==c){if(!wo(c))return;if(c){var e=wo(c);e&&1===Vt(this,c).status&&(Vt(this,c).status=2,this.push("require",[{}],e.X,{}))}Vt(this,c).D&&(d.deferrable=!1)}this.h.push(new Tt(a,c,b,d));d.deferrable||this.flush()};
Ut.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.h.length;){var f=this.h[0];if(f.messageContext.deferrable)!f.ca||Vt(this,f.ca).D?(f.messageContext.deferrable=!1,this.h.push(f)):c.push(f),this.h.shift();else{var g=void 0;switch(f.type){case "require":g=Vt(this,f.ca);if(3!==g.status&&!a){this.h.push.apply(this.h,c);return}break;case "set":m(f.h[0],function(r,u){K(cb(r,u),b.D)});break;case "config":g=Vt(this,f.ca);e.rb={};m(f.h[0],function(r){return function(u,t){K(cb(u,t),r.rb)}}(e));
var h=!!e.rb[T.g.Zc];delete e.rb[T.g.Zc];var l=wo(f.ca),n=l.X===l.id;h||(n?g.F={}:g.N[f.ca]={});g.D&&h||Wt(this,T.g.Fa,e.rb,f);g.D=!0;n?K(e.rb,g.F):(K(e.rb,g.N[f.ca]),Q(70));d=!0;break;case "event":g=Vt(this,f.ca);e.Gd={};m(f.h[0],function(r){return function(u,t){K(cb(u,t),r.Gd)}}(e));Wt(this,f.h[1],e.Gd,f);break;case "get":g=Vt(this,f.ca);var p={},q=(p[T.g.jb]=f.h[0],p[T.g.xb]=f.h[1],p);Wt(this,T.g.Ka,q,f)}this.h.shift();Xt(this,f)}e={rb:e.rb,Gd:e.Gd}}this.h.push.apply(this.h,c);d&&this.flush()};
var Xt=function(a,b){if("require"!==b.type)if(b.ca)for(var c=Vt(a,b.ca).B[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.B)if(a.B.hasOwnProperty(e)){var f=a.B[e];if(f&&f.B)for(var g=f.B[b.type]||[],h=0;h<g.length;h++)g[h]()}},Yt=function(a,b){var c=Pt,d=K(b);K(Vt(c,a).h,d);Vt(c,a).h=d},Pt=new Ut;var af;var Zt={},$t={},au=function(a){for(var b=[],c=[],d={},e=0;e<a.length;d={Ld:d.Ld,Id:d.Id},e++){var f=a[e];if(0<=f.indexOf("-"))d.Ld=wo(f),d.Ld&&(Ka(kl(),function(p){return function(q){return p.Ld.X===q}}(d))?b.push(f):c.push(f));else{var g=Zt[f]||[];d.Id={};g.forEach(function(p){return function(q){return p.Id[q]=!0}}(d));for(var h=jl(),l=0;l<h.length;l++)if(d.Id[h[l]]){b=b.concat(kl());break}var n=$t[f]||[];n.length&&(b=b.concat(n))}}return{Ik:b,Lk:c}},bu=function(a){m(Zt,function(b,c){var d=c.indexOf(a);
0<=d&&c.splice(d,1)})},cu=function(a){m($t,function(b,c){var d=c.indexOf(a);0<=d&&c.splice(d,1)})};var du="HA GF G UA AW DC MC".split(" "),eu=!1,fu=!1;function gu(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:hi()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
var hu={config:function(a,b){var c=gu(a,b);if(!(2>a.length)&&k(a[1])){var d={};if(2<a.length){if(void 0!=a[2]&&!Fc(a[2])||3<a.length)return;d=a[2]}var e=wo(a[1]);if(e){nn(c.eventId,"gtag.config");var f=e.X,g=e.id!==f;if(g?-1===kl().indexOf(f):-1===jl().indexOf(f)){if(!V(61)||!d[T.g.me]){var h=d[T.g.va]||Pt.D[T.g.va];g?Or(f,h,{source:2,fromContainerExecution:b.fromContainerExecution}):Nr(f,h,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(Th&&!g&&!d[T.g.Zc]){var l=fu;fu=!0;
if(l)return}eu||Q(43);if(!b.noTargetGroup)if(g){cu(e.id);var n=e.id,p=d[T.g.je]||"default";p=String(p).split(",");for(var q=0;q<p.length;q++){var r=$t[p[q]]||[];$t[p[q]]=r;0>r.indexOf(n)&&r.push(n)}}else{bu(e.id);var u=e.id,t=d[T.g.je]||"default";t=t.toString().split(",");for(var v=0;v<t.length;v++){var w=Zt[t[v]]||[];Zt[t[v]]=w;0>w.indexOf(u)&&w.push(u)}}delete d[T.g.je];var y=b.eventMetadata||{};y.hasOwnProperty("is_external_event")||(y.is_external_event=!b.fromContainerExecution);b.eventMetadata=
y;delete d[T.g.cc];for(var x=g?[e.id]:kl(),A=0;A<x.length;A++){var B=K(b);Pt.push("config",[d],x[A],B)}}}}},consent:function(a,b){if(3===a.length){Q(39);var c=gu(a,b),d=a[1];"default"===d?Gj(a[2]):"update"===d&&Hj(a[2],c)}},event:function(a,b){var c=a[1];if(!(2>a.length)&&k(c)){var d;if(2<a.length){if(!Fc(a[2])&&void 0!=a[2]||3<a.length)return;d=a[2]}var e=d,f={},g=(f.event=c,f);e&&(g.eventModel=K(e),e[T.g.cc]&&(g.eventCallback=e[T.g.cc]),e[T.g.ee]&&(g.eventTimeout=e[T.g.ee]));var h=gu(a,b),l=h.eventId,
n=h.priorityId;g["gtm.uniqueEventId"]=l;n&&(g["gtm.priorityId"]=n);if("optimize.callback"===c)return g.eventModel=g.eventModel||{},g;var p;var q=d,r=q&&q[T.g.Ob];void 0===r&&(r=ni(T.g.Ob,2),void 0===r&&(r="default"));if(k(r)||Ia(r)){var u=r.toString().replace(/\s+/g,"").split(","),t=au(u),v=t.Ik,w=t.Lk;if(w.length)for(var y=q&&q[T.g.va]||Pt.D[T.g.va],x=0;x<w.length;x++){var A=wo(w[x]);A&&Or(A.X,y,{source:3,fromContainerExecution:b.fromContainerExecution})}p=yo(v)}else p=void 0;var B=p;if(B){nn(l,
c);for(var C=[],D=0;D<B.length;D++){var H=B[D],G=K(b);if(-1!==du.indexOf(H.prefix)){var N=K(d),R=G.eventMetadata||{};R.hasOwnProperty("is_external_event")||(R.is_external_event=!G.fromContainerExecution);G.eventMetadata=R;delete N[T.g.cc];Qt(c,N,H.id,G)}C.push(H.id)}g.eventModel=g.eventModel||{};0<B.length?g.eventModel[T.g.Ob]=C.join():delete g.eventModel[T.g.Ob];eu||Q(43);return b.noGtmEvent?void 0:g}}},get:function(a,b){Q(53);if(4===a.length&&k(a[1])&&k(a[2])&&Ea(a[3])){var c=wo(a[1]),d=String(a[2]),
e=a[3];if(c){eu||Q(43);var f=Pt.D[T.g.va];if(!Ka(kl(),function(h){return c.X===h}))Or(c.X,f,{source:4,fromContainerExecution:b.fromContainerExecution});else if(-1!==du.indexOf(c.prefix)){gu(a,b);var g={};Cj(K((g[T.g.jb]=d,g[T.g.xb]=e,g)));Rt(d,function(h){J(function(){return e(h)})},c.id,b)}}}},js:function(a,b){if(2==a.length&&a[1].getTime){eu=!0;var c=gu(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(3===
a.length&&k(a[1])&&Ea(a[2])){var b=a[1],c=a[2],d=af.B;d.h[b]?d.h[b].push(c):d.h[b]=[c];if(Q(74),"all"===a[1]){Q(75);var e=!1;try{e=a[2](L.H,"unknown",{})}catch(f){}e||Q(76)}}else{Q(73);}},set:function(a,b){var c;2==a.length&&Fc(a[1])?c=K(a[1]):3==a.length&&k(a[1])&&
(c={},Fc(a[2])||Ia(a[2])?c[a[1]]=K(a[2]):c[a[1]]=a[2]);if(c){var d=gu(a,b),e=d.eventId,f=d.priorityId;K(c);var g=K(c);Pt.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);V(30)&&delete c.event;b.overwriteModelFields=!0;return c}}},iu={policy:!0};var ju=function(a){var b=z[Gh.ka].hide;if(b&&void 0!==b[a]&&b.end){b[a]=!1;var c=!0,d;for(d in b)if(b.hasOwnProperty(d)&&!0===b[d]){c=!1;break}c&&(b.end(),b.end=null)}},ku=function(a){var b=z[Gh.ka],c=b&&b.hide;c&&c.end&&(c[a]=!0)};var lu=!1,mu=[];function nu(){if(!lu){lu=!0;for(var a=0;a<mu.length;a++)J(mu[a])}}var ou=function(a){lu?J(a):mu.push(a)};var Fu=function(a){if(Eu(a))return a;this.Da=a};Fu.prototype.getUntrustedMessageValue=function(){return this.Da};var Eu=function(a){return!a||"object"!==Dc(a)||Fc(a)?!1:"getUntrustedMessageValue"in a};Fu.prototype.getUntrustedMessageValue=Fu.prototype.getUntrustedMessageValue;var Gu=0,Hu={},Iu=[],Ju=[],Ku=!1,Lu=!1;function Mu(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}var Nu=function(a){return z[Gh.ka].push(a)},Ou=function(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return Nu(a)},Pu=function(a,b){var c=Oh[Gh.ka],d=c?c.subscribers:1,e=0,f=!1,g=void 0;b&&(g=z.setTimeout(function(){f||(f=!0,a());g=void 0},b));return function(){++e===d&&(g&&(z.clearTimeout(g),g=void 0),f||(a(),f=!0))}};
function Qu(a,b){var c=a._clear||b.overwriteModelFields;m(a,function(e,f){"_clear"!==e&&(c&&qi(e),qi(e,f))});ci||(ci=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;"number"!==typeof d&&(d=hi(),a["gtm.uniqueEventId"]=d,qi("gtm.uniqueEventId",d));return Ot(a)}
function Ru(a){if(null==a||"object"!==typeof a)return!1;if(a.event)return!0;if(Oa(a)){var b=a[0];if("config"===b||"event"===b||"js"===b||"get"===b)return!0}return!1}
function Su(){var a;if(Ju.length)a=Ju.shift();else if(Iu.length)a=Iu.shift();else return;var b;var c=a;if(Ku||!Ru(c.message))b=c;else{Ku=!0;var d=c.message["gtm.uniqueEventId"];"number"!==typeof d&&(d=c.message["gtm.uniqueEventId"]=hi());var e={},f={message:(e.event="gtm.init_consent",e["gtm.uniqueEventId"]=d-2,e),messageContext:{eventId:d-2}},g={},h={message:(g.event="gtm.init",g["gtm.uniqueEventId"]=d-1,g),messageContext:{eventId:d-1}};Iu.unshift(h,c);if(Cm&&L.H){var l;if(L.Df){var n=L.H,p=Dl().destination[n];
l=p&&p.context}else{var q=L.H,r=Dl().container[q];l=r&&r.context}var u=l,t,v=Ff(z.location.href);t=v.hostname+v.pathname;var w=u&&u.fromContainerExecution,y=u&&u.source,x=L.H,A=L.fb,B=L.Df;Om||(Om=t);Nm.push(x+";"+A+";"+(w?1:0)+";"+(y||0)+";"+(B?1:0))}b=f}return b}
function Tu(){for(var a=!1,b;!Lu&&(b=Su());){Lu=!0;delete ki.eventModel;mi();var c=b,d=c.message,e=c.messageContext;if(null==d)Lu=!1;else{e.fromContainerExecution&&ri();try{if(Ea(d))try{d.call(oi)}catch(y){}else if(Ia(d)){var f=d;if(k(f[0])){var g=f[0].split("."),h=g.pop(),l=f.slice(1),n=ni(g.join("."),2);if(null!=n)try{n[h].apply(n,l)}catch(y){}}}else{var p=void 0,q=!1;if(Oa(d)){a:{if(d.length&&
k(d[0])){var r=hu[d[0]];if(r&&(!e.fromContainerExecution||!iu[d[0]])){p=r(d,e);break a}}p=void 0}(q=p&&"set"===d[0]&&!!p.event)&&Q(101)}else p=d;if(p){var u=Qu(p,e);a=u||a;q&&u&&Q(113)}}}finally{e.fromContainerExecution&&mi(!0);var t=d["gtm.uniqueEventId"];if("number"===typeof t){for(var v=Hu[String(t)]||[],w=0;w<v.length;w++)Ju.push(Uu(v[w]));v.length&&Ju.sort(Mu);delete Hu[String(t)];t>Gu&&(Gu=t)}Lu=!1}}}
return!a}function Vu(){if(V(70)){var b=dr(Dq.I.Te,L.H);er(b);if(Wu()){var c=dr(Dq.I.Eg,L.H);if(er(c)){var d=dr(Dq.I.Ve,L.H);fr(c,d)}}}var e=Tu();try{ju(L.H)}catch(f){}return e}
function Qs(a){if(Gu<a.notBeforeEventId){var b=String(a.notBeforeEventId);Hu[b]=Hu[b]||[];Hu[b].push(a)}else Ju.push(Uu(a)),Ju.sort(Mu),J(function(){Lu||Tu()})}function Uu(a){return{message:a.message,messageContext:a.messageContext}}
var Xu=function(){function a(g){var h={};if(Eu(g)){var l=g;g=Eu(l)?l.getUntrustedMessageValue():void 0;h.fromContainerExecution=!0}return{message:g,messageContext:h}}var b=Yb(Gh.ka,[]),c=Oh[Gh.ka]=Oh[Gh.ka]||{};!0===c.pruned&&Q(83);Hu=Os().get();Rs();hs(function(){if(!c.gtmDom){c.gtmDom=!0;var g={};b.push((g.event="gtm.dom",g))}});ou(function(){if(!c.gtmLoad){c.gtmLoad=!0;var g={};b.push((g.event="gtm.load",g))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var g;if(0<Oh.SANDBOXED_JS_SEMAPHORE){g=
[];for(var h=0;h<arguments.length;h++)g[h]=new Fu(arguments[h])}else g=[].slice.call(arguments,0);var l=g.map(function(r){return a(r)});Iu.push.apply(Iu,l);var n=d.apply(b,g),p=Math.max(100,Number("1000")||300);if(this.length>p)for(Q(4),c.pruned=!0;this.length>p;)this.shift();var q="boolean"!==typeof n||n;return Tu()&&q};var e=b.slice(0).map(function(g){return a(g)});Iu.push.apply(Iu,e);if(Wu()){if(V(70)){var f=dr(Dq.I.Ve,L.H);er(f)}J(Vu)}},Wu=function(){var a=!0;return a};function Yu(a){if(null==a||0===a.length)return!1;var b=Number(a),c=Ua();return b<c+3E5&&b>c-9E5}function Zu(a){return a&&0===a.indexOf("pending:")?Yu(a.substr(8)):!1};var Ee={};Ee.se=new String("undefined");var Av=z.clearTimeout,Bv=z.setTimeout,W=function(a,b,c,d){if(hl()){b&&J(b)}else return cc(a,b,c,d)},Cv=function(){return new Date},Dv=function(){return z.location.href},Ev=function(a){return Df(Ff(a),"fragment")},Fv=function(a){return Ef(Ff(a))},Gv=function(a,b){return ni(a,b||2)},Hv=function(a,b,c){return b?Ou(a,b,c):Nu(a)},Iv=function(a,b){z[a]=b},X=function(a,b,c){b&&(void 0===z[a]||c&&!z[a])&&(z[a]=b);return z[a]},
Jv=function(a,b,c){return Uj(a,b,void 0===c?!0:!!c)},Kv=function(a,b,c){return 0===dk(a,b,c)},Lv=function(a,b){if(hl()){b&&J(b)}else ec(a,b)},Mv=function(a){return!!fv(a,"init",!1)},Nv=function(a){dv(a,"init",!0)},Ov=function(a,b,c){Cm&&(Jc(a)||xn(c,b,a))};function lw(a){return mw(a)?1:0}
function mw(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=K(a,{});K({arg1:c[d],any_of:void 0},e);if(lw(e))return!0}return!1}switch(a["function"]){case "_cn":return Mf(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<If.length;g++){var h=If[g];if(b[h]){f=b[h](c);break a}}}catch(l){}f=!1}return f;case "_ew":return Jf(b,c);case "_eq":return Nf(b,c);case "_ge":return Of(b,c);case "_gt":return Sf(b,c);case "_lc":return 0<=String(b).split(",").indexOf(String(c));
case "_le":return Rf(b,c);case "_lt":return Tf(b,c);case "_re":return Lf(b,c,a.ignore_case);case "_sw":return Uf(b,c);case "_um":return Vf(b,c)}return!1};function nw(a,b){var c=this;}nw.R="addConsentListener";var ow;var pw=function(a){for(var b=0;b<a.length;++b)if(ow)try{a[b]()}catch(c){Q(77)}else a[b]()};function qw(a,b,c){var d=this,e;return e}qw.O="internal.addDataLayerEventListener";function rw(a,b,c){}rw.R="addDocumentEventListener";function sw(a,b,c,d){}sw.R="addElementEventListener";function tw(a){}tw.R="addEventCallback";
function xw(a){}xw.O="internal.addFormAbandonmentListener";var yw={},zw=[],Aw={},Bw=0,Cw=0;
function Jw(a,b){}Jw.O="internal.addFormInteractionListener";
function Qw(a,b){}Qw.O="internal.addFormSubmitListener";
function Vw(a){}Vw.O="internal.addGaSendListener";var Ww={},Xw=[];
var dx=function(a,b){};
dx.O="internal.addHistoryChangeListener";function ex(a,b,c){}ex.R="addWindowEventListener";function fx(a,b){return!0}fx.R="aliasInWindow";function gx(a,b,c){}gx.O="internal.appendRemoteConfigParameter";function hx(){var a=2;return a};function ix(a,b){var c;return c}ix.R="callInWindow";function jx(a){}jx.R="callLater";function kx(a){}kx.O="callOnDomReady";function lx(a){}lx.O="callOnWindowLoad";function mx(a){var b;return b}mx.O="internal.computeGtmParameter";function nx(a,b){var c;var d=Gc(c,this.h,hx());void 0===d&&void 0!==c&&Q(45);return d}nx.R="copyFromDataLayer";function ox(a){var b;return b}ox.R="copyFromWindow";function px(a,b){var c;M(F(this),["preHit:!DustMap","dustOptions:?DustMap"],arguments);var d=Hc(b)||{},e=Hc(a,this.h,1).getProcessedEvent(),f=new Lo(e.target,e.eventName,e.s);d.omitHitData||K(e.C,f.C);d.omitMetadata?f.metadata={}:K(e.metadata,f.metadata);f.M=e.M;c=Gc(Oo(f),this.h,1);return c}px.O="internal.copyPreHit";function qx(a,b){var c=null,d=hx();return Gc(c,this.h,d)}qx.R="createArgumentsQueue";function rx(a){var b;return Gc(b,this.h,
hx())}rx.R="createQueue";var sx={},tx=[],ux={},vx=0,wx=0;
function Cx(a,b){var c=this;return b}Cx.O="internal.enableAutoEventOnFormInteraction";
function Hx(a,b){var c=this;return b}Hx.O="internal.enableAutoEventOnFormSubmit";
function Mx(){var a=this;}Mx.O="internal.enableAutoEventOnGaSend";var Nx={},Ox=[];
function Vx(a,b){var c=this;return b}Vx.O="internal.enableAutoEventOnHistoryChange";
function Zx(a,b){var c=this;return b}Zx.O="internal.enableAutoEventOnLinkClick";var $x,ay;
function jy(a,b){var c=this;return b}jy.O="internal.enableAutoEventOnScroll";var Sb=fa(["data-gtm-yt-inspected-"]),ky=["www.youtube.com","www.youtube-nocookie.com"],ly,my=!1;
function wy(a,b){var c=this;return b}wy.O="internal.enableAutoEventOnYouTubeActivity";var xy;function yy(a){var b=!1;return b}yy.O="internal.evaluateMatchingRules";
var By=function(a,b,c){if(c)switch(c.type){case "event_name":return a;case "const":return c.const_value;case "event_param":var d=c.event_param.param_name;if(d===T.g.Vc)return zy(b);return b[d]}},Fy=function(a,b,c,d){Cy=!1;if(c&&!Dy(a,b,c))return!1;if(!d||0===
d.length)return!0;for(var e=0;e<d.length;e++)if(Ey(a,b,d[e].predicates||[]))return!0;return!1},Ey=function(a,b,c){for(var d=0;d<c.length;d++)if(!Dy(a,b,c[d]))return!1;return!0},Dy=function(a,b,c){var d=c.values||[],e=By(a,b,d[0]),f=By(a,b,d[1]),g=c.type;if("eqi"===g||"swi"===g||"ewi"===g||"cni"===g)k(e)&&(e=e.toLowerCase()),k(f)&&(f=f.toLowerCase());var h=!1;switch(g){case "eq":case "eqi":h=Nf(e,f);break;case "sw":case "swi":h=Uf(e,f);break;case "ew":case "ewi":h=Jf(e,f);break;case "cn":case "cni":h=
Mf(e,f);break;case "lt":h=Tf(e,f);break;case "le":h=Rf(e,f);break;case "gt":h=Sf(e,f);break;case "ge":h=Of(e,f);break;case "re":case "rei":h=Lf(e,f,"rei"===g)}return!!c.negate!==h},Cy=!1;var zy=function(a){var b=a[T.g.Vc];if(b)return b;Cy=!0;var c=a[T.g.Ma];if(k(c)){var d=V(57);if(Ea(URL))try{var e=new URL(c);return e.pathname+Gy(d?e.search:"")}catch(h){return}var f=Ff(c);if(f.hostname){var g=d?Df(f,"query"):"";g&&(g="?"+g);return Df(f,"path")+
Gy(g)}}},Gy=function(a){if(!V(72)||!a)return a;var b=a.split("&"),c=[];b[0]=b[0].substring(1);for(var d=0;d<b.length;d++){var e=b[d],f=e.indexOf("=");Hy[0<=f?e.substring(0,f):e]||c.push(b[d])}return c.length?"?"+c.join("&"):""},Hy=Object.freeze({__utma:1,__utmb:1,__utmc:1,__utmk:1,__utmv:1,__utmx:1,__utmz:1,__ga:1,_gac:1,_gl:1,dclid:1,gbraid:1,gclid:1,gclsrc:1,utm_campaign:1,utm_content:1,utm_expid:1,utm_id:1,utm_medium:1,utm_nooverride:1,utm_referrer:1,utm_source:1,utm_term:1,wbraid:1});
function Iy(a,b){var c=!1;return c}Iy.O="internal.evaluatePredicates";var Jy=function(a){var b;return b};function Ky(a,b){b=void 0===b?!0:b;var c;return c}Ky.R="getCookieValues";function Ly(){return ti.Mf}Ly.O="internal.getCountryCode";function My(){var a=[];a=kl();return Gc(a)}My.O="internal.getDestinationIds";function Ny(a){var b=null;return b}Ny.R="getElementById";var Oy={};Oy.enableAdsConversionValidation=V(83);Oy.enableAdsHistoryChangeEvents=V(36);Oy.enableAlwaysSendFormStart=V(38);Oy.enableCcdEmForm=V(82);Oy.enableCcdEnhancedMeasurement=V(41);Oy.enableCcdEventBlocking=V(40);Oy.enableCcdEventEditingAndCreation=V(26);Oy.enableCcdGaConversions=V(39);Oy.enableCcdPreAutoPiiDetection=V(49);Oy.enableCcdUserData=V(16);Oy.enableEesPagePath=V(43);Oy.enableEuidAutoMode=V(37);Oy.enableGa4OnoRemarketing=V(34);Oy.enableGaGamWindowSet=V(67);
Oy.enableRegExpSandboxApis=V(84);Oy.includeQueryInEesPagePath=V(57);Oy.pixieWebSetDeclaredConsentState=V(85);Oy.autoPiiEligible=!0;function Py(){return Gc(Oy)}Py.O="internal.getFlags";function Qy(a,b){var c;M(F(this),["targetId:!string","name:!string"],arguments);var d=yi(a)||{};c=Gc(d[b],this.h);return c}Qy.O="internal.getProductSettingsParameter";function Ry(a,b){var c;return c}Ry.R="getQueryParameters";function Sy(a,b){var c;return c}Sy.R="getReferrerQueryParameters";function Ty(a){var b="";return b}Ty.R="getReferrerUrl";function Uy(){return ti.ri}Uy.O="internal.getRegionCode";function Vy(a,b){var c;return c}Vy.O="internal.getRemoteConfigParameter";function Wy(a){var b="";return b}Wy.R="getUrl";function Xy(){O(this,"get_user_agent");return Wb.userAgent}Xy.R="getUserAgent";function Yy(a){if(!a)return{};var b=a.ck;return js(b.type,b.index,b.name)}function Zy(a){return a?{originatingEntity:Yy(a)}:{}};
function az(a,b){}az.R="gtagSet";function bz(a,b){}bz.R="injectHiddenIframe";var cz={};
function ez(a,b,c,d){}var fz=Object.freeze({dl:1,id:1}),gz={};
function hz(a,b,c,d){}ez.R="injectScript";hz.O="internal.injectScript";function iz(a){var b=!0;return b}iz.R="isConsentGranted";var jz=function(){var a=Dg(function(b){this.h.h.log("error",b)});a.R="JSON";return a};var kz=function(){return!1},lz={getItem:function(a){var b=null;return b},setItem:function(a,
b){return!1},removeItem:function(a){}};var mz=["textContent","value","tagName","children","childElementCount"];
function nz(a){var b;O(this,"read_dom_elements","css","*");for(var c=0;c<mz.length;c++)O(this,"access_dom_element_property",I.body,"read",mz[c]);var d=Hc(a)||{},e=Xi({wc:!!d.includeSelector,xc:!!d.includeVisibility,kd:d.excludeElementSelectors,nb:d.fieldFilters,wi:!!d.selectMultipleElements});b=new kb;var f=new xa;b.set("elements",f);for(var g=e.elements,h=0;h<g.length;h++)f.push(oz(g[h]));void 0!==e.pg&&b.set("preferredEmailElement",
oz(e.pg));b.set("status",e.status);return b}var oz=function(a){var b=new kb;b.set("userData",a.cb);b.set("tagName",a.tagName);void 0!==a.querySelector&&b.set("querySelector",a.querySelector);void 0!==a.isVisible&&b.set("isVisible",a.isVisible);switch(a.type){case 1:b.set("type","email")}return b};nz.O="internal.locateUserData";function pz(){}pz.R="logToConsole";function qz(a){var b=void 0;return b}qz.R="parseUrl";function rz(a){}rz.O="internal.processAsNewEvent";function sz(a,b){var c=!1;return c}sz.R="queryPermission";function tz(){var a="";return a}tz.R="readCharacterSet";function uz(){var a="";return a}uz.R="readTitle";function vz(a,b){var c=this;M(F(this),["destinationId:!string","callback:!Fn"],arguments),Ro(a,function(d){b.h(c.h,Gc(d,c.h,1))});}vz.O="internal.registerCcdCallback";var wz=Object.freeze(["config","event","get","set"]);function xz(a,b,c){}xz.O="internal.registerGtagCommandListener";function yz(a,b){var c=!1;return c}yz.O="internal.removeDataLayerEventListener";function zz(){}zz.R="resetDataLayer";
var Az=function(a){var b=!1;return b},Bz=function(a){return No(a,T.g.hf,!1)},Cz=function(a){if(a.metadata.is_merchant_center)return!1;var b=U(a.s,T.g.fe);return!(!0!==b&&"true"!==b||!U(a.s,T.g.va))},Dz=function(a){var b=a.metadata.user_data;if(Fc(b))return b},Ez=function(a,b){var c=No(a,T.g.de,a.s.D[T.g.de]);if(c&&void 0!==c[b||a.eventName])return c[b||a.eventName]},
Fz=function(a,b,c){a.C[T.g.ed]||(a.C[T.g.ed]={});a.C[T.g.ed][b]=c};var Gz=!1,Hz=function(a){var b=a.eventName===T.g.Kc&&wj()&&Cz(a),c=a.metadata.batch_on_navigation,d=a.metadata.is_conversion,e=a.metadata.is_session_start,f=a.metadata.create_dc_join,g=a.metadata.create_google_join,h=a.metadata.euid_mode_enabled&&!!Dz(a);return!(!Wb.sendBeacon||d||h||e||f||g||b||!c&&Gz)};var Iz=function(a){wb("GA4_EVENT",a)};var Kz=function(a){return!a||Jz.test(a)||vh.hasOwnProperty(a)},Lz=function(a,b,c){for(var d=c.event_param_ops||[],e=0;e<d.length;e++){var f=d[e];if(f.edit_param){var g=f.edit_param.param_name,h=By(a,b,f.edit_param.param_value),l;if(h){var n=Number(h);l=isNaN(n)?h:n}else l=h;b[g]=l}else f.delete_param&&delete b[f.delete_param.param_name]}},Jz=/^(_|ga_|google_|gtag\.|firebase_).*$/;
var Mz=function(a){var b=0,c=0;return{start:function(){b=Ua()},stop:function(){c=this.get()},get:function(){var d=0;a.dg()&&(d=Ua()-b);return d+c}}},Nz=function(){this.h=void 0;this.B=0;this.isActive=this.isVisible=this.D=!1;this.N=this.F=void 0};da=Nz.prototype;da.xj=function(a){var b=this;if(!this.h){this.D=I.hasFocus();this.isVisible=!I.hidden;this.isActive=!0;var c=function(d,e,f){gc(d,e,function(g){b.h.stop();f(g);b.dg()&&b.h.start()})};c(z,"focus",function(){b.D=!0});c(z,"blur",function(){b.D=
!1});c(z,"pageshow",function(d){b.isActive=!0;d.persisted&&Q(56);b.N&&b.N()});c(z,"pagehide",function(){b.isActive=!1;b.F&&b.F()});c(I,"visibilitychange",function(){b.isVisible=!I.hidden});Cz(a)&&-1===(Wb.userAgent||"").indexOf("Firefox")&&-1===(Wb.userAgent||"").indexOf("FxiOS")&&c(z,"beforeunload",function(){Gz=!0});this.ug();this.B=0}};da.ug=function(){this.B+=this.Ee();this.h=Mz(this);this.dg()&&this.h.start()};da.ul=function(a){var b=this.Ee();0<b&&(a.C[T.g.be]=b)};da.rk=function(a){a.C[T.g.be]=
void 0;this.ug();this.B=0};da.dg=function(){return this.D&&this.isVisible&&this.isActive};da.nk=function(){return this.B+this.Ee()};da.Ee=function(){return this.h&&this.h.get()||0};da.Xk=function(a){this.F=a};da.si=function(a){this.N=a};function Oz(){return z.gaGlobal=z.gaGlobal||{}}var Pz=function(){var a=Oz();a.hid=a.hid||La();return a.hid},Qz=function(a,b){var c=Oz();if(void 0==c.vid||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
var Rz=function(a,b,c){var d=a.metadata.client_id_source;if(void 0===d||c<=d)a.C[T.g.ub]=b,a.metadata.client_id_source=c},Uz=function(a,b){var c;var d=b.metadata.cookie_options,e=d.prefix+"_ga",f=mk(d,void 0,void 0,T.g.W);if(!1===U(b.s,T.g.bc)&&Sz(b)===a)c=!0;else{var g=lk(a,Tz[0],d.domain,d.path);c=1!==dk(e,g,f)}return c},Sz=function(a){var b=a.metadata.cookie_options,c=b.prefix+"_ga",d=kk(c,b.domain,b.path,Tz,T.g.W);if(!d){var e=String(U(a.s,T.g.Oc,""));e&&e!=c&&(d=kk(e,b.domain,b.path,Tz,T.g.W))}return d},
Tz=["GA1"],Vz=function(a,b){var c=a.C[T.g.ub];if(b&&c===b)return c;if(c){c=""+c;if(!Uz(c,a))return Q(31),a.M=!0,"";Qz(c,Ij(T.g.W));return c}Q(32);a.M=!0;return""};
var Yz=function(a,b,c){if(!b)return a;if(!a)return b;var d=Wz(a);if(!d)return b;var e,f=Pa(null!=(e=U(c.s,T.g.Xc))?e:30);if(!(Math.floor(c.metadata.event_start_timestamp_ms/1E3)>d.wd+60*f))return a;var g=Wz(b);if(!g)return a;g.Tb=d.Tb+1;var h;return null!=(h=Xz(g.sessionId,g.Tb,g.zc,g.wd,g.eg,g.Rb,g.jd))?h:b},aA=function(a,b){var c=b.metadata.cookie_options,d=Zz(b,c),e=lk(a,$z[0],c.domain,c.path),f={qb:T.g.W,domain:c.domain,path:c.path,expires:c.Db?new Date(Ua()+1E3*c.Db):void 0,flags:c.flags};V(52)&&
dk(d,void 0,f);return 1!==dk(d,e,f)},bA=function(a){var b=a.metadata.cookie_options,c=Zz(a,b),d=kk(c,b.domain,b.path,$z,T.g.W);if(!d||!Cm&&!V(52))return d;var e=Uj(c,void 0,void 0,T.g.W);if(d&&1<e.length){Q(114);for(var f=void 0,g=void 0,h=0;h<e.length;h++){var l=e[h].split(".");if(!(7>l.length)){var n=Number(l[5]);n&&(!g||n>g)&&(g=n,f=e[h])}}f&&!f.endsWith(d)&&(Q(115),V(52)&&(d=f.split(".").slice(2).join(".")))}return d},Xz=function(a,b,c,d,e,f,g){if(a&&b){var h=[a,b,Pa(c),d,e];h.push(f?"1":"0");
h.push(g||"0");return h.join(".")}},$z=["GS1"],Zz=function(a,b){return b.prefix+"_ga_"+a.target.P[0]},Wz=function(a){if(a){var b=a.split(".");if(!(5>b.length||7<b.length)){7>b.length&&Q(67);var c=Number(b[1]),d=Number(b[3]),e=Number(b[4]||0);c||Q(118);d||Q(119);isNaN(e)&&Q(120);if(!V(74)||c&&d&&!isNaN(e))return{sessionId:b[0],Tb:c,zc:!!Number(b[2]),wd:d,eg:e,Rb:"1"===b[5],jd:"0"!==b[6]?b[6]:void 0}}}},cA=function(a){return Xz(a.C[T.g.yb],a.C[T.g.pe],a.C[T.g.oe],Math.floor(a.metadata.event_start_timestamp_ms/
1E3),a.metadata.join_timer_sec||0,!!a.metadata[T.g.ef],a.C[T.g.Rc])};
var dA=function(a){var b=U(a.s,T.g.za),c=a.s.D[T.g.za];if(c===b)return c;var d=K(b);c&&c[T.g.V]&&(d[T.g.V]=(d[T.g.V]||[]).concat(c[T.g.V]));return d},eA=function(a,b){var c=Fk(!0);return"1"!==c._up?{}:{clientId:c[a],xi:c[b]}},fA=function(a,b,c){var d=Fk(!0),e=d[b];e&&(Rz(a,e,2),Uz(e,a));var f=d[c];f&&aA(f,a);return!(!e||!f)},gA=!1,hA=function(a){var b=dA(a)||{},c=a.metadata.cookie_options,d=c.prefix+"_ga",e=Zz(a,c);Ok(b[T.g.ic],!!b[T.g.V])&&fA(a,d,e)&&(gA=!0);b[T.g.V]&&Lk(function(){var f={},g=Sz(a);
g&&(f[d]=g);var h=bA(a);h&&(f[e]=h);var l=Uj("FPLC",void 0,void 0,T.g.W);l.length&&(f._fplc=l[0]);return f},b[T.g.V],b[T.g.jc],!!b[T.g.Lb])},jA=function(a){if(!U(a.s,T.g.zb))return{};var b=a.metadata.cookie_options,c=b.prefix+"_ga",d=Zz(a,b);Mk(function(){var e;if(Ij("analytics_storage"))e={};else{var f={};e=(f._up="1",f[c]=a.C[T.g.ub],f[d]=cA(a),f)}return e},1);return!Ij("analytics_storage")&&iA()?eA(c,d):{}},iA=function(){var a=Cf(z.location,"host"),b=Cf(Ff(I.referrer),"host");return a&&b?a===b||
0<=a.indexOf("."+b)||0<=b.indexOf("."+a)?!0:!1:!1},kA=function(a){if(!a)return a;var b=String(a);b=Ek(b);return b=Ek(b,"_ga")};var lA=function(){var a=!0;tm(7)&&tm(9)&&tm(10)||(a=!1);return a},mA=function(){var a=!0;tm(3)&&tm(4)||(a=!1);return a};
var nA=function(a,b){wj()&&(a.gcs=Jj(),b.metadata.is_consent_update&&(a.gcu="1"))},qA=function(a){if(a.metadata.is_merchant_center)return"https://www.merchant-center-analytics.goog/mc/collect";var b=Kr(U(a.s,T.g.va),"/g/collect");if(b)return b;var c=No(a,T.g.kb,U(a.s,T.g.kb));var d=U(a.s,T.g.Ib);return c&&!Bz(a)&&!1!==d&&lA()&&Ij(T.g.K)&&Ij(T.g.W)?oA():pA()},rA=!1;
rA=!0;var sA={};sA[T.g.sj]="tid";sA[T.g.ub]="cid";sA[T.g.Ta]="ul";sA[T.g.ff]="_fid";sA[T.g.qf]="tt";sA[T.g.ke]="ir";sA[T.g.Nb]="sr";sA[T.g.hc]="gdid";sA[T.g.Wc]="_rdi";sA[T.g.ah]="_geo";
sA[T.g.Hh]="gtm_up";sA[T.g.rf]="uaa",sA[T.g.sf]="uab",sA[T.g.tf]="uafvl",sA[T.g.uf]="uamb",sA[T.g.vf]="uam",sA[T.g.wf]="uap",sA[T.g.xf]="uapv",sA[T.g.yf]="uaw";var tA={};tA[T.g.yb]="sid";tA[T.g.pe]="sct";tA[T.g.oe]="seg";tA[T.g.Ma]="dl";V(80)&&(tA[T.g.Vc]="dp");tA[T.g.Ua]="dr";
tA[T.g.kc]="dt";tA[T.g.sa]="cu";tA[T.g.Aa]="uid";tA[T.g.Td]="cc";tA[T.g.Ud]="ci";tA[T.g.Vd]="cm";tA[T.g.Wd]="cn";tA[T.g.Xd]="cs";tA[T.g.Yd]="ck";var uA={};uA[T.g.be]="_et";uA[T.g.fc]="edid";var vA={};vA[T.g.Td]="cc";vA[T.g.Ud]="ci";vA[T.g.Vd]="cm";vA[T.g.Wd]="cn";vA[T.g.Xd]="cs";vA[T.g.Yd]="ck";var wA={},xA=Object.freeze((wA[T.g.wa]=!0,wA)),pA=function(){var a="www";rA&&ui()&&(a=ui());return"https://"+a+".google-analytics.com/g/collect"},oA=function(){var a;rA&&""!==ui()&&(a=ui());return"https://"+
(a?a+".":"")+"analytics.google.com/g/collect"},yA=function(a,b,c){var d={},e={},f={};d.v="2";d.tid=a.target.X;d.gtm=Kl();d._p=Pz();c&&(d.em=c);a.metadata.create_google_join&&(d._gaz=
1);nA(d,a);var g=a.C[T.g.hc];g&&(d.gdid=g);e.en=hf(a.eventName,40);a.metadata.is_first_visit&&(e._fv=a.metadata.is_first_visit_conversion?2:1);a.metadata.is_new_to_site&&(e._nsi=1);a.metadata.is_session_start&&(e._ss=a.metadata.is_session_start_conversion?2:1);a.metadata.is_conversion&&(e._c=1);a.metadata.is_external_event&&(e._ee=1);if(a.metadata.is_ecommerce){var h=a.C[T.g.ja]||U(a.s,T.g.ja);if(Ia(h))for(var l=0;l<h.length&&200>l;l++)e["pr"+(l+1)]=of(h[l])}var n=a.C[T.g.fc];n&&(e.edid=n);var p=
function(t,v){if("object"!==typeof v||!xA[t]){t=hf(t,40);var w="ep."+t,y="epn."+t;t=Ga(v)?y:w;var x=Ga(v)?w:y;e.hasOwnProperty(x)&&delete e[x];e[t]=hf(v,100)}},q=function(t){Cz(a)&&"object"===typeof t&&m(t||{},function(v,w){"object"!==typeof w&&(d["sst."+v]=hf(w,420))})};m(a.C,function(t,v){if(void 0!==v&&!th.hasOwnProperty(t)){null===v&&(v="");var w;t!==T.g.Rc?w=!1:a.metadata.euid_mode_enabled?(d.ecid=v,w=!0):w=void 0;if(!w&&t!==T.g.ef){var y=v;!0===v&&(y="1");!1===v&&(y="0");var x;if(sA[t])x=sA[t],
d[x]=hf(y,420);else if(tA[t])x=tA[t],f[x]=hf(y,"dl"===x&&V(81)?1E3:420);else if(uA[t])x=uA[t],e[x]=hf(y,420);else if("_"===t.charAt(0))d[t]=hf(y,420);else{var A;vA[t]?A=!0:t!==T.g.Lc?A=!1:("object"!==typeof v&&p(t,v),A=!0);A||p(t,v)}}}});V(65)&&q(a.C[T.g.ed]);var r=a.C[T.g.Oa]||{};!1!==U(a.s,T.g.da)&&mA()||(r._npa="1");V(28)&&!1===U(a.s,T.g.Ib)&&(d.ngs="1");m(r,function(t,v){if(void 0!==v)if(null===v&&(v=""),t===T.g.Aa&&!f.uid)f.uid=hf(v,36);else if(b[t]!==v){var w=(Ga(v)?"upn.":"up.")+hf(t,24);e[w]=
hf(v,36);b[t]=v}});var u=!1;return qf.call(this,{Ba:d,Ub:f,Sf:e},qA(a),Cz(a),u)||this};pa(yA,qf);var zA=function(){var a=Ua(),b=a+864E5,c=20,d=5E3;return function(){var e=Ua();e>=b&&(b=e+864E5,d=5E3);if(1>d)return!1;c=Math.min(c+(e-a)/1E3*5,20);a=e;if(1>c)return!1;d--;c--;return!0}};
var AA=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},BA=function(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")},CA=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");0<=d&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;return b},DA=function(a,b){var c=new z.XMLHttpRequest;c.withCredentials=!0;var d=b?"POST":"GET",e="",f=0,g=Ff(a),h=CA(g),l=BA(g);c.onprogress=function(n){if(200===
c.status){e+=c.responseText.substring(f);f=n.loaded;for(var p=AA(e,h),q=p.indexOf("\n\n");-1!==q;){var r;a:{var u,t=p.substring(0,q).split("\n"),v="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];u=v?v.call(t):{next:ea(t)};var w=u.next().value,y=u.next().value;if(w.startsWith("event: message")&&y.startsWith("data: "))try{r=JSON.parse(y.substring(y.indexOf(":")+1));break a}catch(H){}r=void 0}var x=r;if(x){var A=x.send_pixel||[];if(Array.isArray(A))for(var B=0;B<A.length;B++)fc(A[B]);
if(V(66)){var C=x.send_beacon||[];if(Array.isArray(C))for(var D=0;D<C.length;D++)mc(C[D])}}p=p.substring(q+2);q=p.indexOf("\n\n")}e=p}};c.open(d,l);c.send(b)};
var GA=function(a,b,c,d){var e=a+"?"+b;EA&&(d=!(0===e.indexOf(pA())||0===e.indexOf(oA())));d&&!Gz?DA(e,c):FA(a,b,c)},HA=function(a){},
IA=function(a,b){function c(u){q.push(u+"="+encodeURIComponent(""+a.Ba[u]))}var d=b.jl,e=b.kl,f=b.qk,g=b.Nj,h=b.Mj,l=b.xk,n=b.ml,p=b.wk;if(d||e||n){var q=[];c("tid");c("cid");c("gtm");q.push("aip=1");a.Ub.uid&&!p&&q.push("uid="+encodeURIComponent(""+a.Ub.uid));d&&(FA("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&")),Dj("https://stats.g.doubleclick.net/g/collect?v=2&"+
q.join("&")));if(e){q.push("z="+La());if(!l){var r=f&&0===f.indexOf("google.")&&"google.com"!=f?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",f):void 0;r&&fc(r+q.join("&"))}V(28)&&g&&h&&Dp()&&function(){var u=Fp()+"/td/ga/rul?";q=[];c("tid");q.push("gacid="+encodeURIComponent(String(a.Ba.cid)));c("gtm");q.push("aip=1");q.push("fledge=1");q.push("z="+La());Ep(u+q.join("&"),a.Ba.tid)}()}n&&HA(a)}},EA=!1;var JA=function(){this.F=1;this.N={};this.h=new rf;this.B=-1};JA.prototype.D=function(a,b){var c=this,d=new yA(a,this.N,b),e=Hz(a);e&&this.h.F(d)||this.flush();if(e&&this.h.add(d)){if(0>this.B){var f=z.setTimeout,g;Cz(a)?KA?(KA=!1,g=LA):g=MA:g=5E3;this.B=f.call(z,function(){return c.flush()},g)}}else{var h=tf(d,this.F++);GA(d.h,h.ng,h.body,d.F);var l=a.metadata.create_dc_join,n=a.metadata.create_google_join,p=!1!==U(a.s,T.g.Ga),q=
!1!==U(a.s,T.g.da),r={eventId:a.s.eventId,priorityId:a.s.priorityId},u={jl:l,kl:n,qk:String(No(a,T.g.Tc,U(a.s,T.g.Tc))),Nj:p,Mj:q,xk:Bz(a),wk:a.metadata.euid_mode_enabled,Kl:r};IA(d,u)}if(V(70)){var t=dr(Dq.I.Ch,L.fb,a.s.eventId,void 0,a.eventName);er(t)&&fr(t)}};JA.prototype.add=function(a){a.metadata.euid_mode_enabled&&!Gz?this.U(a):
this.D(a)};JA.prototype.flush=function(){if(this.h.events.length){var a=uf(this.h,this.F++);GA(this.h.h,a.ng,a.body,this.h.B);this.h=new rf;0<=this.B&&(z.clearTimeout(this.B),this.B=-1)}};JA.prototype.U=function(a){var b=this,c=Dz(a);c?qh(c,function(d){b.D(a,1===d.split("~").length?void 0:d)}):this.D(a)};var FA=function(a,b,c){var d=a+"?"+b;if(c)try{Wb.sendBeacon&&Wb.sendBeacon(d,c)}catch(e){wb("TAGGING",15)}else mc(d)},LA=Ll('',500),MA=Ll('',
5E3),KA=!0;var NA=window,OA=document,PA=function(a){var b=NA._gaUserPrefs;if(b&&b.ioo&&b.ioo()||a&&!0===NA["ga-disable-"+a])return!0;try{var c=NA.external;if(c&&c._gaUserPrefs&&"oo"==c._gaUserPrefs)return!0}catch(f){}for(var d=Oj("AMP_TOKEN",String(OA.cookie),!0),e=0;e<d.length;e++)if("$OPT_OUT"==d[e])return!0;return OA.getElementById("__gaOptOutExtension")?!0:!1};
var QA=function(a,b,c){c||(c=function(){});void 0!==a.C[b]&&(a.C[b]=c(a.C[b]))},RA=function(a,b){var c=T.g.K;Ij(c)||Lj(function(){b.metadata.is_consent_update=!0;var d=Dh[c||""];d&&Fz(b,"gcut",d);a.Uh(b)},c)},SA=function(a,b,c){void 0===c&&(c={});if("object"===typeof b)for(var d in b)SA(a+"."+d,b[d],c);else c[a]=b;return c},TA=!1;var Kn={Zj:"",xl:Number("")},UA={},VA=(UA[T.g.Td]=!0,UA[T.g.Ud]=!0,UA[T.g.Vd]=!0,UA[T.g.Wd]=!0,UA[T.g.Xd]=!0,UA[T.g.Yd]=!0,UA),WA=function(a){this.U=a;this.eb=new JA;this.h=void 0;this.F=new Nz;this.B=this.D=void 0;this.N=!1;this.bd=void 0;this.Fc=!1};da=WA.prototype;da.Sk=function(a,b,c){var d=this,e=wo(this.U);if(e)if(c.eventMetadata.is_external_event&&"_"===a.charAt(0))c.Z();
else{a!==T.g.Fa&&a!==T.g.Ka&&Kz(a)&&Q(58);XA(c.h);var f=new Lo(e,a,c);f.metadata.event_start_timestamp_ms=b;var g=[T.g.W];(No(f,T.g.kb,U(f.s,T.g.kb))||Cz(f))&&g.push(T.g.K);var h=function(){Mj(function(){d.Tk(f)},g)};V(11)&&V(25)?Ln(h):h()}else c.Z()};da.Tk=function(a){this.B=a;try{PA(a.target.X)&&(Q(28),a.M=!0);if(0<=Kn.Zj.replace(/\s+/g,"").split(",").indexOf(a.eventName))a.M=!0;else{var b=Ez(a);b&&b.blacklisted&&(a.M=!0)}var c=I.location.protocol;"http:"!=c&&"https:"!=c&&(Q(29),a.M=!0);Wb&&"preview"==
Wb.loadPurpose&&(Q(30),a.M=!0);var d=Oh.grl;d||(d=zA(),Oh.grl=d);d()||(Q(35),a.M=!0);if(a.M){a.s.Z();xb();return}var e={prefix:String(U(a.s,T.g.ib,"")),path:String(U(a.s,T.g.af,"/")),flags:String(U(a.s,T.g.wb,"")),domain:String(U(a.s,T.g.vb,"auto")),Db:Number(U(a.s,T.g.Sa,63072E3))};a.metadata.cookie_options=e;YA(a);this.yj(a);this.F.ul(a);a.metadata.is_merchant_center?a.metadata.euid_mode_enabled=!1:U(a.s,T.g.nf)?a.metadata.euid_mode_enabled=!1:No(a,"ccd_add_1p_data",!1)?a.metadata.euid_mode_enabled=
!0:a.metadata.euid_mode_enabled=V(16)?!1:cj(aj(a.s));if(a.metadata.euid_mode_enabled){var f=aj(a.s);if(cj(f)){var g=U(a.s,T.g.wa);if(No(a,"ccd_add_1p_data",!1))null===g?a.metadata.user_data_from_code=null:(f.enable_code&&Fc(g)&&(a.metadata.user_data_from_code=g),Fc(f.selectors)&&!a.metadata.user_data_from_manual&&(a.metadata.user_data_from_manual=$i(f.selectors)));else if(void 0!==g)a.metadata.user_data=g,a.C._udm="c";else{var h=dj(f);a.metadata.user_data=h;if("selectors"===f.mode||Fc(f.selectors))a.C._udm=
"s";else if("auto_detect"===f.mode||Fc(f.auto_detect))a.C._udm="a"}}}var l=this.oi,n;U(a.s,T.g.zb)&&(Ij(T.g.W)||U(a.s,T.g.ub)||(a.C[T.g.Hh]=!0));var p;var q;q=void 0===q?3:q;var r=z.location.href;if(r){var u=Ff(r).search.replace("?",""),t=Af(u,"_gl",!1,!0)||"";p=t?void 0!==Gk(t,q):!1}else p=!1;p&&Cz(a)&&Fz(a,"glv",1);a.eventName===T.g.Fa?(U(a.s,T.g.zb)&&lo(["aw","dc"]),hA(a),n=jA(a)):n={};l.call(this,n);a.eventName==T.g.Fa&&(U(a.s,T.g.Na,!0)?(a.s.h[T.g.fa]&&(a.s.B[T.g.fa]=a.s.h[T.g.fa],a.s.h[T.g.fa]=
void 0,a.C[T.g.fa]=void 0),a.eventName=T.g.Kc):a.M=!0);var v=eb(ip(a.s,T.g.fa,1),".");v&&(a.C[T.g.hc]=v);var w=eb(ip(a.s,T.g.fa,2),".");w&&(a.C[T.g.fc]=w);var y=this.D,x=this.F,A=!this.Fc,B=this.h,C=U(a.s,T.g.ub),D=C?1:8;a.metadata.is_new_to_site=!1;C||(C=Sz(a),D=3);C||(C=B,D=5);if(!C){var H=Ij(T.g.W),G=Oz();C=!G.from_cookie||H?G.vid:void 0;D=6}C?C=""+C:(C=hk(),D=7,a.metadata.is_first_visit=a.metadata.is_new_to_site=!0);Rz(a,C,D);var N=Math.floor(a.metadata.event_start_timestamp_ms/1E3),R=void 0;
a.metadata.is_new_to_site||(R=bA(a)||y);var aa=Pa(U(a.s,T.g.Xc,30));aa=Math.min(475,aa);aa=Math.max(5,aa);var oa=Pa(U(a.s,T.g.pf,1E4)),P=Wz(R);a.metadata.is_first_visit=!1;a.metadata.is_session_start=!1;a.metadata.join_timer_sec=0;P&&P.eg&&(a.metadata.join_timer_sec=Math.max(0,P.eg-Math.max(0,N-P.wd)));var S=!1;P||(S=a.metadata.is_first_visit=!0,P={sessionId:String(N),Tb:1,zc:!1,wd:N,Rb:!1,jd:void 0});N>P.wd+60*aa&&(S=!0,P.sessionId=String(N),P.Tb++,P.zc=!1,P.jd=void 0);if(S)a.metadata.is_session_start=
!0,x.rk(a);else if(x.nk()>oa||a.eventName==T.g.Kc)P.zc=!0;a.metadata.euid_mode_enabled?U(a.s,T.g.Aa)?P.Rb=!0:(P.Rb&&(P.jd=void 0),P.Rb=!1):P.Rb=!1;var la=P.jd;if(a.metadata.euid_mode_enabled){var ca=U(a.s,T.g.Rc),ba=ca?1:8;ca||(ca=la,ba=4);ca||(ca=gk(),ba=7);var Fa=ba,Wa=a.metadata.enhanced_client_id_source;if(void 0===Wa||Fa<=Wa)a.C[T.g.Rc]=ca.toString(),a.metadata.enhanced_client_id_source=Fa}A?(Mo(a,T.g.yb,P.sessionId),Mo(a,T.g.pe,P.Tb),Mo(a,T.g.oe,P.zc?1:0)):(a.C[T.g.yb]=P.sessionId,a.C[T.g.pe]=
P.Tb,a.C[T.g.oe]=P.zc?1:0);a.metadata[T.g.ef]=P.Rb?1:0;ZA(a);var Ha="",Ja=I.location;if(Ja){var ab=Ja.pathname||"";"/"!=ab.charAt(0)&&(ab="/"+ab);Ha=Ja.protocol+"//"+Ja.hostname+ab+Ja.search}Mo(a,T.g.Ma,Ha);var Md=T.g.Ua,xc;a:{var De=Uj("_opt_expid",void 0,void 0,T.g.W)[0];if(De){var et=decodeURIComponent(De).split("$");if(3===et.length){xc=et[2];break a}}if(void 0!==Oh.ga4_referrer_override)xc=Oh.ga4_referrer_override;else{var ft=ni("gtm.gtagReferrer."+a.target.X);xc=ft?""+ft:I.referrer}}Mo(a,Md,
xc||void 0);Mo(a,T.g.kc,I.title);Mo(a,T.g.Ta,(Wb.language||"").toLowerCase());var gt=zi();Mo(a,T.g.Nb,gt.width+"x"+gt.height);V(80)&&Mo(a,T.g.Vc);a.metadata.create_dc_join=!1;a.metadata.create_google_join=!1;if(!(V(66)&&Cz(a)||a.metadata.is_merchant_center||!1===U(a.s,T.g.Ib))&&lA()&&Ij(T.g.K)){var Hh=No(a,T.g.kb,U(a.s,T.g.kb));if(a.metadata.is_session_start||U(a.s,T.g.jf)){a.metadata.create_dc_join=
!!Hh;}var ht;ht=a.metadata.join_timer_sec;Hh&&0===(ht||0)&&(a.metadata.join_timer_sec=60,a.metadata.create_google_join=!0)}$A(a);xh.hasOwnProperty(a.eventName)&&(a.metadata.is_ecommerce=!0,Mo(a,T.g.ja),Mo(a,T.g.sa));Mo(a,T.g.qf);for(var it=U(a.s,T.g.kf)||[],ll=0;ll<it.length;ll++){var jt=it[ll];if(jt.rule_result){Mo(a,
T.g.qf,jt.traffic_type);Iz(3);break}}if(!a.metadata.is_merchant_center&&U(a.s,T.g.va)){var kt=dA(a)||{},tD=(Ok(kt[T.g.ic],!!kt[T.g.V])?Fk(!0)._fplc:void 0)||(0<Uj("FPLC",void 0,void 0,T.g.W).length?void 0:"0");a.C._fplc=tD}if(void 0!==U(a.s,T.g.ke))Mo(a,T.g.ke);else{var lt=U(a.s,T.g.ne),ml,Ih;a:{if(gA){var nl=dA(a)||{};if(nl&&nl[T.g.V])for(var mt=Df(Ff(a.C[T.g.Ua]),"host",!0),Jh=nl[T.g.V],Pf=0;Pf<Jh.length;Pf++)if(Jh[Pf]instanceof RegExp){if(Jh[Pf].test(mt)){Ih=!0;break a}}else if(0<=mt.indexOf(Jh[Pf])){Ih=
!0;break a}}Ih=!1}if(!(ml=Ih)){var Kh;if(Kh=lt)a:{for(var nt=lt.include_conditions||[],uD=Df(Ff(a.C[T.g.Ua]),"host",!0),ol=0;ol<nt.length;ol++)if(nt[ol].test(uD)){Kh=!0;break a}Kh=!1}ml=Kh}ml&&(a.C[T.g.ke]="1",Iz(4))}Cz(a)&&(Lr()||Fz(a,"uc",ti.Mf),wj()&&Fz(a,"rnd",gl()));if(V(66)&&V(65)&&Cz(a)){No(a,T.g.kb,!1)&&Fz(a,"gse",1);!1===U(a.s,
T.g.Ib)&&Fz(a,"ngs",1);Bz(a)&&Fz(a,"ga_rd",1);lA()||Fz(a,"ngst",1);var ot=No(a,T.g.Tc);ot&&Fz(a,"etld",ot);var pt=rA?ui():"";pt&&Fz(a,"gcsub",pt)}Cz(a)&&wj()&&(xj()&&Fz(a,"gcd","G1"+Ej(uj)),U(a.s,T.g.oa)&&Fz(a,"adr",1));if(Cz(a)){var qt=Tp();qt&&Fz(a,"us_privacy",qt);var rt=sm();rt&&Fz(a,"gdpr",rt);var st=rm();st&&Fz(a,"gdpr_consent",st)}a:if(V(11))if(!Gn(z))Q(87);else if(void 0!==In){Q(85);var tt=En();if(tt){if(V(59)){if(U(a.s,T.g.Wc)&&!Cz(a))break a}else if(U(a.s,T.g.Wc))break a;Mn(tt,a)}else Q(86)}V(61)&&
U(a.s,T.g.me)&&Iz(12);if(V(78)){var pl=Bp(Ap());pl||aB||(aB=!0,Wl('A751Xsk4ZW3DVQ8WZng2Dk5s3YzAyqncTzgv+VaE6wavgTY0QHkDvUTET1o7HanhuJO8lgv1Vvc88Ij78W1FIAAAAAB7eyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGV0YWdtYW5hZ2VyLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjgwNjUyNzk5LCJpc1RoaXJkUGFydHkiOnRydWV9'),pl=Bp(Ap()));pl&&(a.C[T.g.Nc]="1")}if(a.eventName==T.g.Ka){var vt=U(a.s,T.g.jb),vD=U(a.s,T.g.xb),wt=void 0;wt=a.C[vt];vD(wt||U(a.s,vt));a.M=!0}if(!V(26)&&!a.s.eventMetadata.syn_or_mod){var ql=U(a.s,T.g.bf);if(ql){var Nd=K(a.s.h);K(a.C,Nd);for(var xt=ql.edit_rules||[],yt=!1,rl=0;rl<xt.length;rl++){var Lh;a:{var Mh=a,Od=xt[rl];if(Fy(Mh.eventName,Nd,Od.event_name_predicate,Od.conditions||
[])){if(Od.new_event_name){var zt=k(Od.new_event_name)?String(Od.new_event_name):By(Mh.eventName,Nd,Od.new_event_name);if(Kz(zt)){Lh=!1;break a}Mh.eventName=String(zt)}Lz(Mh.eventName,Nd,Od);Iz(2);Lh=!0}else Lh=!1}Lh&&(yt=!0)}for(var At=ql.synthesis_rules||[],sl=0;sl<At.length;sl++){var tl=a,Qf=At[sl];if(Fy(tl.eventName,Nd,Qf.event_name_predicate,Qf.conditions||[])){var ul=Qf.new_event_name;if(!Kz(ul)){var Bt=Qf.merge_source_event_params?K(Nd):{};Lz(ul,Bt,Qf);var Ct={},vl={eventMetadata:(Ct.syn_or_mod=
!0,Ct)};vl.eventMetadata.event_usage=[11];Cy&&vl.eventMetadata.event_usage.push(10);var wD=Ms(tl.target.X,ul,Bt);Ps(wD,tl.s.eventId,vl);Iz(1)}}}if(yt){for(var wl={},Dt={eventMetadata:(wl.syn_or_mod=!0,wl.is_external_event=!!a.s.eventMetadata.is_external_event,wl)},Et,xl=[],Ft=vb.GA4_EVENT||[],Nh=0;Nh<Ft.length;Nh++)Ft[Nh]&&xl.push(Nh);(Et=0<xl.length?xl:void 0)&&(Dt.eventMetadata.event_usage=Et);var xD=Ms(a.target.X,a.eventName,Nd);Ps(xD,a.s.eventId,Dt);a.M=!0}}}Qo(a);bB(a);var yl=a.metadata.event_usage;
if(Ia(yl))for(var zl=0;zl<yl.length;zl++)Iz(yl[zl]);var Gt=yb("GA4_EVENT");Gt&&(a.C._eu=Gt);Mo(a,T.g.Aa);Mo(a,T.g.Oa);if(a.metadata.speculative||a.M){a.s.Z();xb();return}var zD=this.oi,Ht,AD=this.h,Al;a:{var Bl=cA(a);if(Bl){if(aA(Bl,a)){Al=Bl;break a}Q(25);a.M=!0}Al=void 0}var BD=Al;Ht={clientId:Vz(a,AD),xi:BD};zD.call(this,Ht);this.Fc=!0;this.ql(a);if(Cz(a)){var CD=a.metadata.is_conversion;("page_view"===a.eventName||CD)&&RA(this,a)}this.F.ug();this.bd=cB(a,this.bd);Mo(a,T.g.ah);U(a.s,T.g.Wc)&&(a.C[T.g.Wc]=
!0,V(75)&&Cz(a)||QA(a,T.g.Nb));if(a.M){a.s.Z();xb();return}this.Uh(a);a.s.aa()}catch(yE){a.s.Z()}xb()};da.Uh=function(a){this.eb.add(a)};da.oi=function(a){var b=a.clientId,c=a.xi;b&&c&&(this.h=b,this.D=c)};da.flush=function(){this.eb.flush()};da.ql=function(a){var b=this;if(!this.N){var c=Ij(T.g.W);Kj([T.g.W],function(){var d=Ij(T.g.W);if(c^d&&b.B&&b.D&&b.h){var e=b.h;if(d){var f=Sz(b.B);if(f){b.h=f;var g=bA(b.B);g&&(b.D=Yz(g,b.D,b.B))}else Uz(b.h,b.B),Qz(b.h,!0);aA(b.D,b.B);
var h={};h[T.g.jf]=e;var l=Ms(b.U,T.g.Xe,h);Ps(l,a.s.eventId,{});}else{b.D=void 0;b.h=void 0;z.gaGlobal={};}c=d}});this.N=!0}};da.yj=function(a){a.eventName!==T.g.Ka&&this.F.xj(a)};var bB=function(a){if(Cz(a)){var b=function(d){var e=SA(T.g.wa,d);m(e,function(f,g){a.C[f]=g})},c=U(a.s,T.g.wa);void 0!==c?b(c):b(a.metadata.user_data);a.metadata.user_data=
void 0}},YA=function(a){function b(c,d){th[c]||void 0===d||(a.C[c]=d)}m(a.s.B,b);m(a.s.h,b)},ZA=function(a){var b=jp(a.s),c=function(d,e){VA[d]&&(a.C[d]=e)};Fc(b[T.g.Lc])?m(b[T.g.Lc],function(d,e){c((T.g.Lc+"_"+d).toLowerCase(),e)}):m(b,c)},$A=function(a){var b=function(c){return!!c&&c.conversion};a.metadata.is_conversion=b(Ez(a));a.metadata.is_first_visit&&(a.metadata.is_first_visit_conversion=b(Ez(a,"first_visit")));a.metadata.is_session_start&&(a.metadata.is_session_start_conversion=b(Ez(a,"session_start")))},
cB=function(a,b){var c=void 0;return c},aB=!1;
function XA(a){m(a,function(c){"_"===c.charAt(0)&&delete a[c]});var b=a[T.g.Oa]||{};m(b,function(c){"_"===c.charAt(0)&&delete b[c]})};var dB=function(a){if("prerender"==I.visibilityState)return!1;a();return!0},eB=function(a){if(!dB(a)){var b=!1,c=function(){!b&&dB(a)&&(b=!0,hc(Vb.document,"visibilitychange",c),Q(55))};gc(Vb.document,"visibilitychange",c);Q(54)}};var gB=function(a,b){eB(function(){var c=wo(a);if(c){var d=fB(c,b);Pt.register(a,d)}});};function fB(a,b){var c=function(){};var d=new WA(a.id),e="MC"===a.prefix;c=function(f,g,h,l){e&&(l.eventMetadata.is_merchant_center=!0);d.Sk(g,h,l)};hB(a,d,b);return c}
function hB(a,b,c){var d=b.F,e={},f={eventId:c,eventMetadata:(e.batch_on_navigation=!0,e)};d.Xk(function(){Gz=!0;Pt.flush();1E3<=d.Ee()&&Wb.sendBeacon&&Qt(T.g.Xe,{},a.id,f);b.flush();d.si(function(){Gz=!1;d.si()})});};var FC=fB;function HC(a,b,c,d){}HC.O="internal.sendGtagEvent";function IC(a,b,c){}IC.R="sendPixel";function JC(a,b,c,d){var e=this;d=void 0===d?!0:d;var f=!1;return f}JC.R="setCookie";function KC(a){M(F(this),["consentSettings:!DustMap"],arguments);for(var b=a.Pb(),c=b.length(),d=0;d<c;d++){var e=b.get(d);e===T.g.Re||V(17)&&e===T.g.Se||O(this,"access_consent",e,"write")}var f=this.h.h,g=f.eventId,h=Zy(f),l=Js("consent","default",Hc(a));Ps(l,g,h)}KC.R="setDefaultConsentState";function LC(a,b,c){return!1}LC.R="setInWindow";function MC(a,b,c){M(F(this),["targetId:!string","name:!string","value:!*"],arguments);var d=yi(a)||{};d[b]=Hc(c,this.h);var e=a;wi||xi();vi[e]=d;}MC.O="internal.setProductSettingsParameter";function NC(a,b,c){M(F(this),["targetId:!string","name:!string","value:!*"],arguments);for(var d=b.split("."),e=Vt(Pt,a).h,f=0;f<d.length-1;f++){if(void 0===e[d[f]])e[d[f]]={};else if(!Fc(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=Hc(c,this.h);}NC.O="internal.setRemoteConfigParameter";function OC(a,b,c,d){var e=this;}OC.R="sha256";function PC(a,b,c){}
PC.O="internal.sortRemoteConfigParameters";var QC={},RC={};QC.R="templateStorage";QC.getItem=function(a){var b=null;return b};
QC.setItem=function(a,b){};
QC.removeItem=function(a){};QC.clear=function(){};var SC=function(a){var b;return b};function TC(a){M(F(this),["consentSettings:!DustMap"],arguments);var b=Hc(a),c;for(c in b)b.hasOwnProperty(c)&&O(this,"access_consent",c,"write");var d=this.h.h;Ps(Js("consent","update",b),d.eventId,Zy(d))}TC.R="updateConsentState";var UC=function(){var a=new Ng,b=function(d){var e=d.O;if(a.B.hasOwnProperty(e))throw"Attempting to add a private function which already exists: "+e+".";if(a.h.hasOwnProperty(e))throw"Attempting to add a private function with an existing API name: "+e+".";a.B[e]=Ea(d)?hg(e,d):ig(e,d)},c=function(d){return a.add(d.R,d)};c(nw);c(tw);c(fx);c(ix);c(jx);c(nx);c(ox);c(qx);c(jz());c(rx);c(Ky);c(Ry);c(Sy);c(Ty);c(Wy);c(az);c(bz);c(ez);c(iz);c(pz);c(qz);c(sz);c(tz);c(uz);c(IC);c(JC);c(KC);c(LC);c(OC);c(QC);
c(TC);a.add("Math",ng());a.add("Object",Lg);a.add("TestHelper",Pg());a.add("assertApi",jg);a.add("assertThat",kg);a.add("decodeUri",pg);a.add("decodeUriComponent",qg);a.add("encodeUri",rg);a.add("encodeUriComponent",sg);a.add("fail",yg);a.add("generateRandom",zg);a.add("getContainerVersion",Ag);a.add("getTimestamp",Bg);a.add("getTimestampMillis",Bg);a.add("getType",Cg);a.add("makeInteger",Eg);a.add("makeNumber",Fg);a.add("makeString",Gg);a.add("makeTableMap",Hg);a.add("mock",Kg);a.add("fromBase64",
Jy,!("atob"in z));a.add("localStorage",lz,!kz());a.add("toBase64",SC,!("btoa"in z));b(qw);b(Jw);b(Qw);b(Vw);b(dx);b(gx);b(lx);b(px);b(Cx);b(Hx);b(Mx);b(Vx);b(Zx);b(jy);b(wy);b(tg);b(yy);b(Ly);b(My);b(Py);b(Qy);b(Uy);b(Vy);b(hz);b(nz);b(rz);b(vz);b(xz);b(yz);b(HC);b(MC);b(NC);b(PC);
V(84)&&(b(og),b(Qg));return function(d){var e;if(a.h.hasOwnProperty(d))e=a.get(d,this);else{var f;if(f=a.B.hasOwnProperty(d)){var g=!1,h=this.h.h;if(h){var l=h.pd();if(l){0!==l.indexOf("__cvt_")&&(g=!0);}}f=g}if(f){var n=a.B.hasOwnProperty(d)?a.B[d]:void 0;e=n}else throw Error(d+" is not a valid API name.");}return e}};var VC=function(){return!1},WC=function(){var a={};return function(b,c,d){}};var XC;
function YC(){var a=XC;return function(b,c,d){var e=d&&d.event;ZC(c);var f=new kb;m(c,function(q,r){var u=Gc(r);void 0===u&&void 0!==r&&Q(44);f.set(q,u)});a.h.h.F=Qe();var g={Qj:bf(b),eventId:void 0!==e?e.id:void 0,priorityId:void 0!==e?e.priorityId:void 0,ye:void 0!==e?function(q){return e.Cb.ye(q)}:void 0,pd:function(){return b},log:function(){},ck:{index:d&&d.index,type:d&&d.type,name:d&&d.name}};if(VC()){var h=WC(),l=void 0,n=void 0;g.Ra={xg:[],fd:{},Ya:function(q,r,u){1===r&&(l=q);7===r&&(n=
u);h(q,r,u)},ig:Ig()};g.log=function(q,r){if(l){var u=Array.prototype.slice.call(arguments,1);h(l,4,{level:q,source:n,message:u})}}}var p=Yd(a,g,[b,f]);a.h.h.F=void 0;p instanceof sa&&"return"===p.h&&(p=p.B);return Hc(p)}}function ZC(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;Ea(b)&&(a.gtmOnSuccess=function(){J(b)});Ea(c)&&(a.gtmOnFailure=function(){J(c)})}
function $C(){XC.h.h.N=function(a,b,c){Oh.SANDBOXED_JS_SEMAPHORE=Oh.SANDBOXED_JS_SEMAPHORE||0;Oh.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Oh.SANDBOXED_JS_SEMAPHORE--}}}function aD(a){void 0!==a&&m(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");fi[e]=fi[e]||[];fi[e].push(b)}})};var bD=encodeURI,Y=encodeURIComponent,cD=function(a,b,c){fc(a,b,c)},dD=function(a,b){if(!a)return!1;var c=Df(Ff(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;0<f&&"."!=e.charAt(0)&&(f--,e="."+e);if(0<=f&&c.indexOf(e,f)==f)return!0}}return!1},eD=function(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&
a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null};var Z={m:{}};
Z.m.c=["google"],function(){(function(a){Z.__c=a;Z.__c.o="c";Z.__c.isVendorTemplate=!0;Z.__c.priorityOverride=0;Z.__c.isInfrastructure=!1})(function(a){Ov(a.vtp_value,"c",a.vtp_gtmEventId);return a.vtp_value})}();
Z.m.e=["google"],function(){(function(a){Z.__e=a;Z.__e.o="e";Z.__e.isVendorTemplate=!0;Z.__e.priorityOverride=0;Z.__e.isInfrastructure=!1})(function(a){return String(a.vtp_gtmCachedValues.event)})}();
Z.m.v=["google"],function(){(function(a){Z.__v=a;Z.__v.o="v";Z.__v.isVendorTemplate=!0;Z.__v.priorityOverride=0;Z.__v.isInfrastructure=!1})(function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=Gv(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1),d=void 0!==c?c:a.vtp_defaultValue;Ov(d,"v",a.vtp_gtmEventId);return d})}();








Z.m.read_container_data=["google"],function(){(function(a){Z.__read_container_data=a;Z.__read_container_data.o="read_container_data";Z.__read_container_data.isVendorTemplate=!0;Z.__read_container_data.priorityOverride=0;Z.__read_container_data.isInfrastructure=!1})(function(){return{assert:function(){},ba:function(){return{}}}})}();


Z.m.read_dom_elements=["google"],function(){function a(b,c,d){return{type:c,value:d}}(function(b){Z.__read_dom_elements=b;Z.__read_dom_elements.o="read_dom_elements";Z.__read_dom_elements.isVendorTemplate=!0;Z.__read_dom_elements.priorityOverride=0;Z.__read_dom_elements.isInfrastructure=!1})(function(b){for(var c=b.vtp_selectors||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g];switch(h.type){case "id":e.push(h.value);break;case "css":f.push(h.value)}}return{assert:function(l,
n,p){switch(n){case "id":if(-1<e.indexOf(p))return;break;case "css":if(-1<f.indexOf(p))return;break;default:throw d(l,{},"Unknown selector type "+n+".");}throw d(l,{},"Prohibited selector value "+p+" for selector type "+n+".");},ba:a}})}();
Z.m.gct=["google"],function(){function a(d){for(var e=[],f=0;f<d.length;f++)try{e.push(new RegExp(d[f]))}catch(g){}return e}function b(d){return d.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}function c(d){for(var e=[],f=0;f<d.length;f++){var g=d[f].matchValue,h;switch(d[f].matchType){case "BEGINS_WITH":h="^"+b(g);break;case "ENDS_WITH":h=b(g)+"$";break;case "EQUALS":h="^"+b(g)+"$";break;case "REGEX":h=g;break;default:h=b(g)}e.push(h)}return e}(function(d){Z.__gct=d;Z.__gct.o="gct";Z.__gct.isVendorTemplate=
!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1})(function(d){var e={},f=d.vtp_sessionDuration;0<f&&(e[T.g.Xc]=f);e[T.g.de]=d.vtp_eventSettings;e[T.g.bf]=d.vtp_dynamicEventSettings;e[T.g.kb]=1===d.vtp_googleSignals;e[T.g.Tc]=d.vtp_foreignTld;e[T.g.hf]=1===d.vtp_restrictDomain;e[T.g.kf]=d.vtp_internalTrafficResults;var g=T.g.za,h=d.vtp_linker;h&&h[T.g.V]&&(h[T.g.V]=a(h[T.g.V]));e[g]=h;var l=T.g.ne,n=d.vtp_referralExclusionDefinition;n&&n.include_conditions&&(n.include_conditions=a(n.include_conditions));
e[l]=n;var p=d.vtp_trackingId,q=Vt(Pt,p).h,r=q.referral_exclusion_conditions;r&&(r.length&&"object"===typeof r[0]&&(r=c(r)),e[T.g.ne]={include_conditions:a(r)});var u=q.cross_domain_conditions;if(u){u.length&&"object"===typeof u[0]&&(u=c(u));var t={};e[T.g.za]=(t[T.g.V]=a(u),t[T.g.Lb]=!0,t[T.g.ic]=!0,t[T.g.jc]="query",t)}Yt(p,e);gB(p,d.vtp_gtmEventId);J(d.vtp_gtmOnSuccess)})}();

Z.m.get=["google"],function(){(function(a){Z.__get=a;Z.__get.o="get";Z.__get.isVendorTemplate=!0;Z.__get.priorityOverride=0;Z.__get.isInfrastructure=!1})(function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Ms(String(b.streamId),d,c);Ps(f,e.eventId,e);a.vtp_gtmOnSuccess()})}();



Z.m.access_dom_element_property=["google"],function(){function a(b,c,d,e){var f={property:e,read:!1,write:!1};switch(d){case "read":f.read=!0;break;case "write":f.write=!0;break;default:throw Error("Invalid "+b+" operation "+d);}return f}(function(b){Z.__access_dom_element_property=b;Z.__access_dom_element_property.o="access_dom_element_property";Z.__access_dom_element_property.isVendorTemplate=!0;Z.__access_dom_element_property.priorityOverride=0;Z.__access_dom_element_property.isInfrastructure=
!1})(function(b){for(var c=b.vtp_properties||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],l=h.property;h.read&&e.push(l);h.write&&f.push(l)}return{assert:function(n,p,q,r){if(!k(r))throw d(n,{},"Property must be a string.");if("read"===q){if(-1<e.indexOf(r))return}else if("write"===q){if(-1<f.indexOf(r))return}else throw d(n,{},"Operation must be either 'read' or 'write', was "+q);throw d(n,{},"Prohibited "+q+" on "+p.tagName+" property "+r+".");},ba:a}})}();


var wE={};wE.dataLayer=oi;wE.callback=function(a){ei.hasOwnProperty(a)&&Ea(ei[a])&&ei[a]();delete ei[a]};wE.bootstrap=0;wE._spx=!1;
function xE(){Oh[L.H]=Oh[L.H]||wE;L.fb&&(Oh["ctid_"+L.fb]=wE);El();Gl()||m(Hl(),function(a,b){Or(a,b.transportUrl,b.context);Q(92)});Ya(fi,Z.m);Ge=Ue}
(function(a){function b(){l=I.documentElement.getAttribute("data-tag-assistant-present");Yu(l)&&(h=g.tj)}if(!z["__TAGGY_INSTALLED"]){var c=!1;if(I.referrer){var d=Ff(I.referrer);c="cct.google"===Cf(d,"host")}if(!c){var e=Uj("googTaggyReferrer");c=e.length&&e[0].length}c&&(z["__TAGGY_INSTALLED"]=!0,cc("https://cct.google/taggy/agent.js"))}if($h)a();else{var f=function(t){var v="GTM",w="GTM";Uh?(v="OGT",w="GTAG"):$h&&(w=v="OPT");var y=z["google.tagmanager.debugui2.queue"];y||(y=[],
z["google.tagmanager.debugui2.queue"]=y,cc("https://"+Gh.Md+"/debug/bootstrap?id="+L.H+"&src="+w+"&cond="+t+"&gtm="+Kl()));var x={messageType:"CONTAINER_STARTING",data:{scriptSource:Xb,containerProduct:v,debug:!1,id:L.H,isGte:Th}};x.data.resume=function(){a()};Gh.Mi&&(x.data.initialPublish=!0);y.push(x)},g={Bl:1,uj:2,Gj:3,Oi:4,tj:5},h=void 0,l=void 0,n=Df(z.location,"query",!1,void 0,"gtm_debug");Yu(n)&&(h=g.uj);if(!h&&I.referrer){var p=Ff(I.referrer);"tagassistant.google.com"===Cf(p,"host")&&(h=g.Gj)}if(!h){var q=
Uj("__TAG_ASSISTANT");q.length&&q[0].length&&(h=g.Oi)}h||b();if(!h&&Zu(l)){var r=function(){if(u)return!0;u=!0;b();h&&Xb?f(h):a()},u=!1;gc(I,"TADebugSignal",function(){r()},!1);z.setTimeout(function(){r()},200)}else h&&Xb?f(h):a()}})(function(){var a=!1;a&&kr("INIT");if(V(70)){var b=
dr(Dq.I.Ue,L.H);er(b)}kj().B();qm();if(L.fb?Oh["ctid_"+L.fb]:Oh[L.H]){var c=Oh.zones;c&&c.unregisterChild(jl());}else{(V(11)||V(13)||V(55)||V(48))&&Jn();for(var d=data.resource||{},e=d.macros||[],f=0;f<e.length;f++)ve.push(e[f]);for(var g=d.tags||[],h=0;h<g.length;h++)ye.push(g[h]);for(var l=d.predicates||[],n=0;n<l.length;n++)xe.push(l[n]);
for(var p=d.rules||[],q=0;q<p.length;q++){for(var r=p[q],u={},t=0;t<r.length;t++)u[r[t][0]]=Array.prototype.slice.call(r[t],1);we.push(u)}Ae=Z;Be=lw;af=new $e;var v=data.sandboxed_scripts,w=data.security_groups,y=data.infra,x=data.runtime||[],A=data.runtime_lines;XC=new Wd;$C();ue=YC();var B=XC,C=UC();nb(B.h,"require",C);for(var D=0;D<x.length;D++){var H=x[D];if(!Ia(H)||3>H.length){if(0===H.length)continue;break}A&&A[D]&&A[D].length&&Ne(H,A[D]);XC.execute(H)}if(void 0!==v)for(var G=["sandboxedScripts"],
N=0;N<v.length;N++){var R=v[N].replace(/^_*/,"");fi[R]=G}aD(w);if(void 0!==y)for(var aa=0;aa<y.length;aa++)gi[y[aa]]=!0;xE();Xu();cs=!1;ds=0;if("interactive"==I.readyState&&!I.createEventObject||"complete"==I.readyState)fs();else{gc(I,"DOMContentLoaded",fs);gc(I,"readystatechange",fs);if(I.createEventObject&&I.documentElement.doScroll){var oa=!0;try{oa=!z.frameElement}catch(Wa){}oa&&gs()}gc(z,"load",fs)}lu=!1;"complete"===I.readyState?nu():gc(z,"load",nu);Cm&&z.setInterval(Hm,864E5);V(46)&&(Q(111),wb("HEALTH",1));V(47)&&(Q(112),wb("HEALTH",2));di=
Ua();wE.bootstrap=di;if(a){var ca=lr("INIT");}if(V(70)){var ba=dr(Dq.I.Dg,L.H);if(er(ba)){var Fa=dr(Dq.I.Ue,L.H);fr(ba,Fa)}}}});

})()
