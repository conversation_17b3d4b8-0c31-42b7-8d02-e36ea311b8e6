﻿namespace Account.Web
{
    public static class UserConst
    {
        public const string StrValidateCodeEmail = "您的验证码为：{0},有效期1小时!";
        public const string StrValidateCodeEmailExt = "如非本人操作，请忽略此邮件！";
        public const string StrValidateCodeSMS = "您的验证码为：{0}，有效期1小时！如非本人操作，请忽略本短信。";
        public const string StrSMSSign = "【识文助手电子商务工作室】OCR文字识别助手，";
        public const string StrRegSuccessSMS = "恭喜您注册成功！初始密码为：{0}，请及时修改！如非本人操作，请忽略。";

        public const string StrServerError = "服务器异常，请稍后重试！";
        public const string StrSendValidateCode = "发送验证码";

        #region 注册
        public const string StrAccountFormatError = "账号格式不正确，必须为手机号或者邮箱！";
        public const string StrHasReged = "当前账号已注册，请返回重新登录！";
        public const string StrRegSuccess = "注册成功，请使用注册账号登录！";
        public const string StrValidateCodeError = "验证码错误或已过期，请重新获取！";
        public const string StrValidateCodeEmpty = "验证码不能为空！";
        public const string StrPwdFormatError = "密码必须为6-15位的大小写字母、数字或符号！";
        public const string StrNickNameFormatError = "昵称为2-15位的中英文数字字母或下划线！";
        public const string StrWelcomeToReg = "欢迎注册OCR助手";

        #endregion

        #region 登录
        public const string StrAccountNotExsits = "当前账号不存在，请先注册账号！";
        public const string StrAccountEmpty = "用户名或密码不能为空！";

        #endregion

        #region 修改密码
        public const string StrFindPwd = "密码找回服务";
        public const string StrNewPwdEmpty = "新密码不能为空！";
        public const string StrResetPwdSuccess = "操作成功，请使用新密码登录！";
        #endregion

        #region 设备管理
        public const string StrConfirmToDisableMac = "确认要禁用该设备吗？禁用后该设备将无法正常使用！";
        public const string StrConfirmToEnableMac = "确认要启用该设备？";

        #endregion

        #region 系统常量
        /// <summary>
        /// 密码加密盐值
        /// </summary>
        public const string PASSWORD_SALT = "OCRREG";

        /// <summary>
        /// 注册验证码类型
        /// </summary>
        public const string REGISTER_VERIFY_TYPE = "OCRREG";

        /// <summary>
        /// 密码重置验证码类型
        /// </summary>
        public const string RESET_PASSWORD_VERIFY_TYPE = "OCRREGForgetPwd";

        /// <summary>
        /// 账号Cookie名称
        /// </summary>
        public const string COOKIE_ACCOUNT = "account";

        /// <summary>
        /// Token Cookie名称
        /// </summary>
        public const string COOKIE_TOKEN = "token";

        /// <summary>
        /// Token过期时间（小时）
        /// </summary>
        public const int TOKEN_EXPIRE_HOURS = 1;

        #endregion
    }
}