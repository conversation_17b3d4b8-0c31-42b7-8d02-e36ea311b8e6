﻿namespace Account.Web
{
    public class SyncTask
    {
        private string _name;
        private string _localPath;
        //private string _serverPath = "/";
        //private string _uRL;
        private string _serverFileURL;
        //private string _UserName;
        //private string _Password;
        //private int _Port = 21;
        //private TaskType _Type = TaskType.每日;
        private System.Collections.Generic.List<int> _lstDay = new System.Collections.Generic.List<int>();
        private System.Collections.Generic.List<int> _lstWeek = new System.Collections.Generic.List<int>();
        private System.DateTime _dtValue = System.DateTime.MinValue;
        //private DayTaskType _dayType = DayTaskType.小时;
        //private int _dayValue = 1;
        public string Name
        {
            get
            {
                return this._name;
            }
            set
            {
                this._name = value;
            }
        }
        public string LocalPath
        {
            get
            {
                return this._localPath;
            }
            set
            {
                this._localPath = value;
            }
        }
        //public string ServerPath
        //{
        //    get
        //    {
        //        return this._serverPath;
        //    }
        //    set
        //    {
        //        this._serverPath = value;
        //    }
        //}
        //public string URL
        //{
        //    get
        //    {
        //        return this._uRL;
        //    }
        //    set
        //    {
        //        this._uRL = value;
        //    }
        //}
        public string ServerFileURL
        {
            get
            {
                return this._serverFileURL;
            }
            set
            {
                this._serverFileURL = value;
            }
        }
        //public string UserName
        //{
        //    get
        //    {
        //        return this._UserName;
        //    }
        //    set
        //    {
        //        this._UserName = value;
        //    }
        //}
        //public string Password
        //{
        //    get
        //    {
        //        return this._Password;
        //    }
        //    set
        //    {
        //        this._Password = value;
        //    }
        //}
        //public int Port
        //{
        //    get
        //    {
        //        return this._Port;
        //    }
        //    set
        //    {
        //        this._Port = value;
        //    }
        //}
        //public TaskType Type
        //{
        //    get
        //    {
        //        return this._Type;
        //    }
        //    set
        //    {
        //        this._Type = value;
        //    }
        //}
        public System.Collections.Generic.List<int> LstDay
        {
            get
            {
                return this._lstDay;
            }
            set
            {
                this._lstDay = value;
            }
        }
        public System.Collections.Generic.List<int> LstWeek
        {
            get
            {
                return this._lstWeek;
            }
            set
            {
                this._lstWeek = value;
            }
        }
        public System.DateTime DtValue
        {
            get
            {
                return this._dtValue;
            }
            set
            {
                this._dtValue = value;
            }
        }
        //public DayTaskType DayType
        //{
        //    get
        //    {
        //        return this._dayType;
        //    }
        //    set
        //    {
        //        this._dayType = value;
        //    }
        //}
        //public int DayValue
        //{
        //    get
        //    {
        //        return this._dayValue;
        //    }
        //    set
        //    {
        //        this._dayValue = value;
        //    }
        //}

        //public string LocalFile { get; set; }
    }
    public enum TaskType
    {
        每月 = 2,
        每周 = 1,
        每日 = 0
    }
    public enum DayTaskType
    {
        小时 = 0,
        分钟 = 1,
        秒 = 2,
        定时 = 3
    }
}
