<?xml version="1.0"?>
<doc>
    <assembly>
        <name><PERSON><PERSON>.OSS</name>
    </assembly>
    <members>
        <member name="T:Aliyun.OSS.Commands.DeleteBucketTaggingCommand">
            <summary>
            Delete bucket tagging command.
            </summary>
        </member>
        <member name="T:<PERSON>yun.OSS.Commands.DeleteLiveChannelCommand">
            <summary>
            Delete LiveChannel command.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Commands.DeleteObjectTaggingCommand">
            <summary>
            Delete object tagging command.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Commands.SetBucketRefererCommand">
            <summary>
            Description of SetBucketRefererCommand.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Commands.DeleteBucketLifecycleCommand">
            <summary>
            Delete bucket lifecycle command.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Commands.GetBucketInfoCommand">
            <summary>
            Get bucket info command.
            </summary>
        </member>
        <member name="T:<PERSON>yun.OSS.Commands.GetBucketStatCommand">
            <summary>
            Get bucket info command.
            </summary>
        </member>
        <member name="T:<PERSON>yun.OSS.Common.ClientConfiguration">
            <summary>
            The client configuration that specifies the network parameters.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ConnectionLimit">
            <summary>
            Max Http connection connection count. By default it's 512.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.UserAgent">
            <summary>
            User-Agent in requests to OSS
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProxyHost">
            <summary>
            Proxy host
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProxyPort">
            <summary>
            Proxy port
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProxyUserName">
            <summary>
            Proxy user name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProxyPassword">
            <summary>
            Proxy user password
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProxyDomain">
            <summary>
            The proxy user name's domain for authentication
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ConnectionTimeout">
            <summary>
            Connection timeout in milliseconds
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.MaxErrorRetry">
            <summary>
            Max error retry count
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.Protocol">
            <summary>
            Protocols used to access OSS (HTTP or HTTPS)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.IsCname">
            <summary>
            If the endpoint is the CName.
            If it's CName, ListBuckets is not supported.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.ProgressUpdateInterval">
            <summary>
            The progress update interval in terms of data upload/download's delta in bytes. By default it's 4096 bytes.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.EnalbeMD5Check">
            <summary>
            Flag of enabling MD5 checksum.
            When EnalbeMD5Check is set true, MD5 will be checked and CRC check will be skipped whenever MD5 is applicable.  
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.EnableCrcCheck">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Aliyun.OSS.Common.ClientConfiguration"/> enable
            crc check.
            When EnalbeMD5Check is set true, MD5 will be checked and CRC check will be skipped whenever MD5 is applicable.  
            </summary>
            <value><c>true</c> if enable crc check; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Aliyun.OSS.Common.ClientConfiguration.SetCustomEpochTicks(System.Int64)">
            <summary>
            <para>Sets the custom base time</para>
            <para>
            OSS's token validation logic depends on the time. It requires that there's no more than 15 min time difference between client and OSS server.
            This API calculates the difference between local time to epoch time. Later one other APIs use this difference to offset the local time before sending request to OSS. 
            </para>
            </summary>
            <param name="epochTicks">Custom Epoch ticks (in seconds)</param>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.TickOffset">
            <summary>
            Gets the difference between customized epoch time and local time, in seconds
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.DirectWriteStreamThreshold">
            <summary>
            Gets or sets the direct write stream threshold.
            The theshold is the file size threshold that when the uploading file size is more than this value, the HttpWebRequest will not use write buffer to save the memory.
            </summary>
            <value>The direct write stream threshold.</value>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.MaxPartCachingSize">
            <summary>
            Gets or sets the size of the max part caching size
            In multipart upload (resumable upload), if the part size is no bigger than MaxPartCachingSize, it will cache the whole part data before sending the data out. 
            </summary>
            <value>The size of the max part caching.</value>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.PreReadBufferCount">
            <summary>
            Gets or sets the pre read buffer count in resumable upload.
            The max value could be the same size of MaxResumableUploadThreads;
            </summary>
            <value>The pre read buffer count.</value>
        </member>
        <member name="P:Aliyun.OSS.Common.ClientConfiguration.UseSingleThreadReadInResumableUpload">
            <summary>
            When uploading a file with resumable upload, the default behavior is to read the source file in multiple-threading.
            But in old HDD, single thread read may be faster. And when the read speed is the bottleneck, try to change this parameter to compare the result. 
            </summary>
            <value><c>true</c> if use single thread read in resumable upload; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Aliyun.OSS.Common.ClientConfiguration.GetDefaultUserAgent">
            <summary>
            Gets the default user agent
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Authentication.DefaultCredentials">
            <summary>
            Default Credential class
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.DefaultCredentials.AccessKeyId">
            <inheritdoc/>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.DefaultCredentials.AccessKeySecret">
            <inheritdoc/>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.DefaultCredentials.SecurityToken">
            <inheritdoc/>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.DefaultCredentials.UseToken">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.DefaultCredentials.#ctor(System.String,System.String,System.String)">
            <summary>
            creates a instance of <see cref="T:Aliyun.OSS.Common.Authentication.DefaultCredentials"/>
            </summary>
            <param name="accessKeyId">OSS access key Id</param>
            <param name="accessKeySecret">OSS access secret</param>
            <param name="securityToken">STS security token</param>
        </member>
        <member name="T:Aliyun.OSS.Common.Authentication.DefaultCredentialsProvider">
            <summary>
            Default ICredentialProvider implementation
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.DefaultCredentialsProvider.#ctor(Aliyun.OSS.Common.Authentication.ICredentials)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.Common.Authentication.DefaultCredentialsProvider"/>
            </summary>
            <param name="creds"><see cref="T:Aliyun.OSS.Common.Authentication.ICredentials"/>ICredentials instance</param>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.DefaultCredentialsProvider.SetCredentials(Aliyun.OSS.Common.Authentication.ICredentials)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.DefaultCredentialsProvider.GetCredentials">
            <inheritdoc/>
        </member>
        <member name="T:Aliyun.OSS.Common.Authentication.ICredentialsProvider">
            <summary>
            ICredentialsProvider Interface
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.ICredentialsProvider.SetCredentials(Aliyun.OSS.Common.Authentication.ICredentials)">
            <summary>
            Sets the <see cref="T:Aliyun.OSS.Common.Authentication.ICredentials"/> instance
            </summary>
            <param name="creds">An instance of <see cref="T:Aliyun.OSS.Common.Authentication.ICredentials"/></param>
        </member>
        <member name="M:Aliyun.OSS.Common.Authentication.ICredentialsProvider.GetCredentials">
            <summary>
            Gets an instance of <see cref="T:Aliyun.OSS.Common.Authentication.ICredentials"/>
            </summary>
            <returns><see cref="T:Aliyun.OSS.Common.Authentication.ICredentials"/>ICredential instance</returns>
        </member>
        <member name="T:Aliyun.OSS.Common.Authentication.ICredentials">
            <summary>
            ICredential interface
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.ICredentials.AccessKeyId">
            <summary>
            OSS access key Id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.ICredentials.AccessKeySecret">
            <summary>
            OSS access key secret
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.ICredentials.SecurityToken">
            <summary>
            STS security token
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Authentication.ICredentials.UseToken">
            <summary>
            FLag of using STS's SecurityToken
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.ExecutionContext._responseHandlers">
            <summary>
            List of HTTP response handlers. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ExecutionContext.Signer">
            <summary>
            Gets or sets the request signer.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ExecutionContext.Credentials">
            <summary>
            Gets or sets the credentials.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ExecutionContext.ResponseHandlers">
            <summary>
            Gets the list of <see cref="T:Aliyun.OSS.Common.Handlers.IResponseHandler" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ExecutionContext.Command">
            <summary>
            Gets or sets a concrete command associate with this context.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceMessage.Headers">
            <summary>
            Gets the dictionary of HTTP headers.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceMessage.Content">
            <summary>
            Gets or sets the content stream.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.HttpMethod">
            <summary>
            Represents a HTTP method.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Get">
            <summary>
            Represents HTTP GET. Default value.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Delete">
            <summary>
            Represents HTTP DELETE.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Head">
            <summary>
            Represents HTTP HEAD.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Post">
            <summary>
            Represents HTTP POST.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Put">
            <summary>
            Represents HTTP PUT.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.Communication.HttpMethod.Options">
            <summary>
            Represents HTTP OPTIONS.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.IServiceClient">
            <summary>
            Represent the channel that communicates with an Aliyun Open Service.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Communication.IServiceClient.Send(Aliyun.OSS.Common.Communication.ServiceRequest,Aliyun.OSS.Common.Communication.ExecutionContext)">
            <summary>
            Sends a request to the service.
            </summary>
            <param name="request">The request data.</param>
            <param name="context">The execution context.</param>
            <returns>The response data.</returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Communication.IServiceClient.BeginSend(Aliyun.OSS.Common.Communication.ServiceRequest,Aliyun.OSS.Common.Communication.ExecutionContext,System.AsyncCallback,System.Object)">
            <summary>
            Begins to send a request to the service asynchronously.
            </summary>
            <param name="request">The request data.</param>
            <param name="context">The execution context.</param>
            <param name="callback">User callback.</param>
            <param name="state">User state.</param>
            <returns>An instance of <see cref="T:System.IAsyncResult"/>.</returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Communication.IServiceClient.EndSend(System.IAsyncResult)">
            <summary>
            Ends the asynchronous operation.
            </summary>
            <param name="asyncResult">An instance of <see cref="T:System.IAsyncResult"/>.</param>
            <returns>The response data.</returns>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.Endpoint">
            <summary>
            Gets or sets the endpoint.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.ResourcePath">
            <summary>
            Gets or sets the resource path of the request URI.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.Method">
            <summary>
            Gets or sets the HTTP method.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.Parameters">
            <summary>
            Gets the dictionary of the request parameters.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.IsRepeatable">
            <summary>
            Gets whether the request can be repeated.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.UseChunkedEncoding">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Aliyun.OSS.Common.Communication.ServiceRequest"/>
            use chunked encoding.
            </summary>
            <value><c>true</c> if use chunked encoding; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Aliyun.OSS.Common.Communication.ServiceRequest.ParametersInUri">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Aliyun.OSS.Common.Communication.ServiceRequest"/>
            parameters in URL.
            </summary>
            <value><c>true</c> if parameters in URL; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Aliyun.OSS.Common.Communication.ServiceRequest.BuildRequestUri">
            <summary>
            Build the request URI from the request message.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Communication.ServiceResponse.EnsureSuccessful">
            <summary>
            Throws the exception from communication if the status code is not 2xx.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.RetryableServiceClient">
            <summary>
            Implementation of <see cref="T:Aliyun.OSS.Common.Communication.IServiceClient"/> that will auto-retry HTTP requests 
            when encountering some specific exceptions or failures.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.ServiceClientImpl">
            <summary>
            The default  implementation of <see cref="T:Aliyun.OSS.Common.Communication.ServiceClient"/> that
            used to communicate with Aliyun OSS via HTTP protocol.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.ServiceClientImpl.HttpAsyncResult">
            <summary>
            Represents the async operation of requests in <see cref="T:Aliyun.OSS.Common.Communication.ServiceClientImpl"/>.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.ServiceClientImpl.ResponseImpl">
            <summary>
            Represents the response data of <see cref="T:Aliyun.OSS.Common.Communication.ServiceClientImpl"/> requests.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Communication.ServiceClient">
            <summary>
            The default implementation of <see cref="T:Aliyun.OSS.Common.Communication.IServiceClient" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.ClientException">
            <summary>
            Exception thrown by the SDK for errors that occur within the SDK.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.ClientException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Constructs a new instance of the ClientException class with serialized data.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="info" /> parameter is null. </exception>
            <exception cref="T:System.Runtime.Serialization.SerializationException">The class name is null or <see cref="P:System.Exception.HResult" /> is zero (0). </exception>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashingWrapper.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashingWrapper.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Internal.HashStream">
            <summary>
            A wrapper stream that calculates a hash of the base stream as it
            is being read.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an ClientException.
            If CalculatedHash is calculated for only the portion of the stream that
            is read.
            </summary>
            <exception cref="T:Aliyun.OSS.Common.ClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.Algorithm">
            <summary>
            Algorithm to use to calculate hash.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.FinishedHashing">
            <summary>
            True if hashing is finished and no more hashing should be done;
            otherwise false.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.CurrentPosition">
            <summary>
            Current position in the stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.CalculatedHash">
            <summary>
            Calculated hash for the stream.
            This value is set only after the stream is closed.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.ExpectedHash">
            <summary>
            Expected hash value. Compared against CalculatedHash upon Close().
            If the hashes are different, an ClientException is thrown.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.ExpectedLength">
            <summary>
            Expected length of stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.#ctor(System.IO.Stream,System.Int64)">
            <summary>
            Initializes an HashStream with a hash algorithm and a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an HashStream with a hash algorithm and a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position
            within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified
            byte array with the values between offset and (offset + count - 1) replaced
            by the bytes read from the current source.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin storing the data read
            from the current stream.
            </param>
            <param name="count">
            The maximum number of bytes to be read from the current stream.
            </param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the
            number of bytes requested if that many bytes are not currently available,
            or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write the specified buffer, offset and count.
            </summary>
            <returns>The write.</returns>
            <param name="buffer">Buffer.</param>
            <param name="offset">Offset.</param>
            <param name="count">Count.</param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.Close">
            <summary>
            Closes the underlying stream and finishes calculating the hash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            this method will throw an ClientException.
            </summary>
            <exception cref="T:Aliyun.OSS.Common.ClientException">
            If ExpectedHash is set and is different from CalculateHash that the stream calculates.
            </exception>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            HashStream does not support seeking, this will always be false.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            HashStream does not support seeking, attempting to set Position
            will throw NotSupportedException.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            HashStream does not support seeking, attempting to call Seek
            will throw NotSupportedException.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">
            A value of type System.IO.SeekOrigin indicating the reference point used
            to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.HashStream.Length">
            <summary>
            Gets the overridden length used to construct the HashStream
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.CalculateHash">
            <summary>
            Calculates the hash for the stream so far and disables any further
            hashing.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.Reset">
            <summary>
            Resets the hash stream to starting state.
            Use this if the underlying stream has been modified and needs
            to be rehashed without reconstructing the hierarchy.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.ValidateBaseStream">
            <summary>
            Validates the underlying stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream.CompareHashes(System.Byte[],System.Byte[])">
            <summary>
            Compares two hashes (arrays of bytes).
            </summary>
            <param name="expected">Expected hash.</param>
            <param name="actual">Actual hash.</param>
            <returns>
            True if the hashes are identical; otherwise false.
            </returns>
        </member>
        <member name="T:Aliyun.OSS.Common.Internal.HashStream`1">
            <summary>
            A wrapper stream that calculates a hash of the base stream as it
            is being read or written.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an ClientException.
            If base stream's position is not 0 or HashOnReads is true and the entire stream is
            not read, the CalculatedHash will be set to an empty byte array and
            comparison to ExpectedHash will not be made.
            </summary>
            <exception cref="T:Aliyun.OSS.Common.ClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.HashStream`1.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an HashStream with a hash algorithm and a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="T:Aliyun.OSS.Common.Internal.MD5Stream">
            <summary>
            A wrapper stream that calculates an MD5 hash of the base stream as it
            is being read or written.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an ClientException.
            If base stream's position is not 0 or HashOnReads is true and the entire stream is
            not read, the CalculatedHash will be set to an empty byte array and
            comparison to ExpectedHash will not be made.
            </summary>
            <exception cref="T:Aliyun.OSS.Common.ClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.MD5Stream.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an MD5Stream with a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="T:Aliyun.OSS.Common.Internal.PartialWrapperStream">
            <summary>
            This class is used to wrap a stream for a particular segment of a stream.  It 
            makes that segment look like you are reading from beginning to end of the stream.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.Internal.WrapperStream">
            <summary>
            A wrapper stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.BaseStream">
            <summary>
            Base stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes WrapperStream with a base stream.
            </summary>
            <param name="baseStream"></param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.GetNonWrapperBaseStream">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <returns>First base stream that is non-WrapperStream.</returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.GetSeekableBaseStream">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <returns>First base stream that is non-WrapperStream.</returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.GetNonWrapperBaseStream(System.IO.Stream)">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <param name="stream">Potential WrapperStream</param>
            <returns>Base non-WrapperStream.</returns>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            True if the stream supports reading; otherwise, false.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            True if the stream supports seeking; otherwise, false.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            True if the stream supports writing; otherwise, false.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.Close">
            <summary>
            Closes the current stream and releases any resources (such as sockets and
            file handles) associated with the current stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.ReadTimeout">
            <summary>
            Gets or sets a value, in miliseconds, that determines how long the stream
            will attempt to read before timing out.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.Internal.WrapperStream.WriteTimeout">
            <summary>
            Gets or sets a value, in miliseconds, that determines how long the stream
            will attempt to write before timing out.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be written
            to the underlying device.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position
            within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified
            byte array with the values between offset and (offset + count - 1) replaced
            by the bytes read from the current source.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin storing the data read
            from the current stream.
            </param>
            <param name="count">
            The maximum number of bytes to be read from the current stream.
            </param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the
            number of bytes requested if that many bytes are not currently available,
            or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">
            A value of type System.IO.SeekOrigin indicating the reference point used
            to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
        </member>
        <member name="M:Aliyun.OSS.Common.Internal.WrapperStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current
            position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">
            An array of bytes. This method copies count bytes from buffer to the current stream.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin copying bytes to the
            current stream.
            </param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="T:Aliyun.OSS.Common.OssErrorCode">
            <summary>
            The OSS (Object Storage Service) Erro code definitions
            </summary>
            <seealso cref="P:OssException.ErrorCode" />。
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.AccessDenied">
            <summary>
            Access Denied
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.BucketAlreadyExists">
            <summary>
            Bucket already exists
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.BucketNotEmtpy">
            <summary>
            Bucket is not empty (so that deletion will not work)
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.EntityTooLarge">
            <summary>
            Entity is too large
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.EntityTooSmall">
            <summary>
            Entity is too small (this could happen when trying to use multipart upload for a small file.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.FileGroupTooLarge">
            <summary>
            File group is too large.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidLinkName">
            <summary>
            Object Link has the same name of the object it points to.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.LinkPartNotExist">
            <summary>
            Object Link points to a non-existing object.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.ObjectLinkTooLarge">
            <summary>
            Object Link's object count is more than 1. One symlink could only point to one object.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.FieldItemTooLong">
            <summary>
            The item is too long in the post request.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.FilePartInterity">
            <summary>
            File part has been changed.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.FilePartNotExist">
            <summary>
            File part does not exist
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.FilePartStale">
            <summary>
            File part has been expired.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.IncorrectNumberOfFilesInPOSTRequest">
            <summary>
            File count is invalid in the post.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidArgument">
            <summary>
            Invalid argument
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidAccessKeyId">
            <summary>
            Access ID does not exist
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidBucketName">
            <summary>
            Invalid bucket name
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidDigest">
            <summary>
            Invalid digest
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidTargetBucketForLogging">
            <summary>
            Invalid target bucket for logginbg
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidObjectName">
            <summary>
            Invalid object name
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidPart">
            <summary>
            Invalid part
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidPartOrder">
            <summary>
            Invalid part order (the part Ids must be in ascending order)
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidPolicyDocument">
            <summary>
            Invalid policy document
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InternalError">
            <summary>
            OSS internal error (possibly OSS bug)
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MalformedXML">
            <summary>
            Malformed XML
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MalformedPOSTRequest">
            <summary>
            Malformed body in the post request.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MaxPOSTPreDataLengthExceededError">
            <summary>
            The non-content body size in a file upload request is too big
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MethodNotAllowed">
            <summary>
            HTTP Method is not allowed.(for example some CORS rules could define allowed methods)
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MissingArgument">
            <summary>
            Missing argument
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.MissingContentLength">
            <summary>
            Missing content length--in HTTP post/put requests, the content length is needed.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NoSuchBucket">
            <summary>
            Bucket does not exist.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NoSuchKey">
            <summary>
            Object does not exist in OSS
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NoSuchUpload">
            <summary>
            Multipart Upload ID does not exist
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NotImplemented">
            <summary>
            Not implemented methods
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.PreconditionFailed">
            <summary>
            Precondition failed.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.RequestTimeTooSkewed">
            <summary>
            The time skew is too big (more than 15 minutes)
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.RequestTimeout">
            <summary>
            Request timeout
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.RequestIsNotMultiPartContent">
            <summary>
            Invalid content-type in the post request.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.SignatureDoesNotMatch">
            <summary>
            Signature does not match
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.TooManyBuckets">
            <summary>
            Bucket counts exceeds the limit
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.InvalidEncryptionAlgorithmError">
            <summary>
            Invalid Encryption Algorithems error
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NoSuchWebsiteConfiguration">
            <summary>
            The source bucket is not enabled with static website
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NoSuchCORSConfiguration">
            <summary>
            CORS rules do not exist
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.NotModified">
            <summary>
            304 Not modified
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Common.OssErrorCode.CallbackFailed">
            <summary>
            203 callback call failed
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Common.OssException">
            <summary>
            This is the expected exception that is thrown when accessing OSS.
            </summary>
            <seealso cref="T:Aliyun.OSS.Common.ServiceException" />
        </member>
        <member name="M:Aliyun.OSS.Common.OssException.#ctor">
            <summary>
            Initializes a new <see cref="T:Aliyun.OSS.Common.OssException"/> instance
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.OssException.#ctor(System.String)">
            <summary>
            Initializes a new <see cref="T:Aliyun.OSS.Common.OssException"/>instance
            </summary>
            <param name="message">Error message for the exception</param>
        </member>
        <member name="M:Aliyun.OSS.Common.OssException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a <see cref="T:Aliyun.OSS.Common.OssException"/> instance
            </summary>
            <param name="info">Serialization information</param>
            <param name="context">The context information</param>
        </member>
        <member name="M:Aliyun.OSS.Common.OssException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new <see cref="T:Aliyun.OSS.Common.OssException"/> instance
            </summary>
            <param name="message">Error Message</param>
            <param name="innerException">Inner exceptions</param>
        </member>
        <member name="M:Aliyun.OSS.Common.OssException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Override the<see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)"/>methods
            </summary>
            <param name="info"><see cref="T:System.Runtime.Serialization.SerializationInfo"/>，Serialization information about the object</param>
            <param name="context"><see cref="T:System.Runtime.Serialization.StreamingContext"/> Context information</param>
        </member>
        <member name="T:Aliyun.OSS.Common.ServiceException">
            <summary>
            <para>
            The exception returned from OSS server side.
            </para>
            <para>  
            <see cref="T:Aliyun.OSS.Common.ServiceException" />is used for wrap the error messages from OSS server side.
            For example, if Access key Id does not exist, it will throw <see cref="T:Aliyun.OSS.Common.ServiceException" />.
            The exception has the error codes for caller to handle.
            </para>
            <para>
            <see cref="T:System.Net.WebException" /> means there's network issue when OSS client sends request to OSS server.
            For example, if the network is not available, it will throw <see cref="T:System.Net.WebException" />.
            </para>
            <para>
            <see cref="T:System.InvalidOperationException" /> means the client code handnle parse or handle the response. In this case it might means the response is incomplete or the SDK 
            does not match the OSS's response, in which case the SDK needs the upgrade.
            </para>
            Generally speaking, caller only needs to handle <see cref="T:Aliyun.OSS.Common.ServiceException" />. It means the request has been processed by OSS (so network is not an issue),
            but the request could not be processed by OSS correctly. The error code of ServiceException could help to understand the issue and the caller could handle it properly.
            <para>
            
            </para>
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ServiceException.ErrorCode">
            <summary>
            The error code getter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ServiceException.RequestId">
            <summary>
            The requestId getter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Common.ServiceException.HostId">
            <summary>
            Host ID getter
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.ServiceException.#ctor">
            <summary>
            Creates a <see cref="T:Aliyun.OSS.Common.ServiceException"/> instance.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Common.ServiceException.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Aliyun.OSS.Common.ServiceException"/> instance.
            </summary>
            <param name="message">The error messag</param>
        </member>
        <member name="M:Aliyun.OSS.Common.ServiceException.#ctor(System.String,System.Exception)">
            <summary>
            Creates a new <see cref="T:Aliyun.OSS.Common.ServiceException"/>instance.
            </summary>
            <param name="message">Error messag</param>
            <param name="innerException">internal exception</param>
        </member>
        <member name="M:Aliyun.OSS.Common.ServiceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Creates a new <see cref="T:Aliyun.OSS.Common.ServiceException"/> instance.
            </summary>
            <param name="info">serialization information</param>
            <param name="context">context information</param>
        </member>
        <member name="M:Aliyun.OSS.Common.ServiceException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Overrides <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)"/> method
            </summary>
            <param name="info"><see cref="T:System.Runtime.Serialization.SerializationInfo"/>serialization information instance</param>
            <param name="context"><see cref="T:System.Runtime.Serialization.StreamingContext"/>context information</param>
        </member>
        <member name="T:Aliyun.OSS.ProcessObjectResult">
            <summary>
            The result class of the operation to process the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ProcessObjectResult.Content">
            <summary>
            Gets the content of result
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ProcessObjectRequest">
            <summary>
            The request class of the operation to process the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ProcessObjectRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ProcessObjectRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ProcessObjectRequest.Process">
            <summary>
            Gets or sets the process
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ProcessObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.ProcessObjectRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.CreateLiveChannelRequest">
            <summary>
            The request class of the operation to create a live channel.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.Description">
            <summary>
            Gets or sets the description
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.Status">
            <summary>
            Gets or sets the status
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.Type">
            <summary>
            Gets or sets the type
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.FragDuration">
            <summary>
            Gets or sets the frag duration
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.FragCount">
            <summary>
            Gets or sets the frag count
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.PlaylistName">
            <summary>
            Gets or sets playlist name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.RoleName">
            <summary>
            Gets or sets role name of snapshot
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.DestBucket">
            <summary>
            Gets or sets destination bucket of snapshot
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.NotifyTopic">
            <summary>
            Gets or sets notify topic of snapshot
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelRequest.Interval">
            <summary>
            Gets or sets interval of snapshot
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CreateLiveChannelRequest.#ctor(System.String,System.String)">
            <summary>
            Set a new instance of <see cref="T:Aliyun.OSS.CreateLiveChannelRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.CreateLiveChannelResult">
            <summary>
            The result class of the operation to create live channel.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelResult.PublishUrl">
            <summary>
            The publish url.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateLiveChannelResult.PlayUrl">
            <summary>
            The play url.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaInputFormat.CompressionType">
            <summary>
            Specifies the compression type of the object. Valid values: None, GZIP.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateSelectObjectMetaCSVInputFormat">
            <summary>
            Describes how a CSV-formatted input object is formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaCSVInputFormat.RecordDelimiter">
            <summary>
            Specifies the value used to separate individual records.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaCSVInputFormat.FieldDelimiter">
            <summary>
            Specifies the value used to separate individual fields in a record.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaCSVInputFormat.QuoteCharacter">
            <summary>
            Specifies the value used for escaping where the field delimiter is part of the value.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateSelectObjectMetaJSONInputFormat">
            <summary>
            Describes how a JSON-formatted input object is formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaJSONInputFormat.Type">
            <summary>
            Specifies the type of the input JSON object. Valid values: DOCUMENT, LINES.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateSelectObjectMetaRequest">
            <summary>
            The request class of the operation to create the meta of select object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaRequest.Key">
            <summary>
            Gets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaRequest.InputFormat">
            <summary>
            Gets or sets the input format
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaRequest.OverwriteIfExists">
            <summary>
            Gets or sets the overwrite flag
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CreateSelectObjectMetaRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.CreateSelectObjectMetaRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">key</param>
        </member>
        <member name="T:Aliyun.OSS.CreateSelectObjectMetaResult">
            <summary>
            The result class of the operation to create the meta of select object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSelectObjectMetaResult.ErrorMessage">
            <summary>
            Gets or sets the ErrorMessage.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CompleteBucketWormRequest">
            <summary>
            The request class of the operation to CompleteBucketWorm.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteBucketWormRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteBucketWormRequest.WormId">
            <summary>
            Gets the worm id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CompleteBucketWormRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.CompleteBucketWormRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="id">wormId</param>
        </member>
        <member name="T:Aliyun.OSS.InitiateBucketWormResult">
            <summary>
            The result class of the operation to initiate bucket worm.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateBucketWormResult.WormId">
            <summary>
            Set or Gets the worm id
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ListBucketInventoryConfigurationRequest">
            <summary>
            The request class of the operation to list the bucket's inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketInventoryConfigurationRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketInventoryConfigurationRequest.ContinuationToken">
            <summary>
            Gets the inventory continuation token
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListBucketInventoryConfigurationRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.ListBucketInventoryConfigurationRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="token">continuation token</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest">
            <summary>
            The request class of the operation to delete the bucket's inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest.Id">
            <summary>
            Gets the inventory configuration id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="id">inventory configuration id</param>
        </member>
        <member name="T:Aliyun.OSS.GenerateRtmpPresignedUriRequest">
            <summary>
            The request class of the operation to sign the rtmp URL
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GenerateRtmpPresignedUriRequest.BucketName">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GenerateRtmpPresignedUriRequest.ChannelName">
            <summary>
            Channel name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GenerateRtmpPresignedUriRequest.PlaylistName">
            <summary>
            Playlist name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GenerateRtmpPresignedUriRequest.Expiration">
            <summary>
            Getter/setter of the expiration time of the signed URL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GenerateRtmpPresignedUriRequest.QueryParams">
            <summary>
            Gets or sets query parameters
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GenerateRtmpPresignedUriRequest.AddQueryParam(System.String,System.String)">
            <summary>
            Add a query parameter
            </summary>
            <param name="param">param name</param>
            <param name="value">param value</param>
        </member>
        <member name="M:Aliyun.OSS.GenerateRtmpPresignedUriRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.GenerateRtmpPresignedUriRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetBucketInventoryConfigurationRequest">
            <summary>
            The request class of the operation to get the bucket's inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketInventoryConfigurationRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketInventoryConfigurationRequest.Id">
            <summary>
            Gets the inventory configuration id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetBucketInventoryConfigurationRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.GetBucketInventoryConfigurationRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="id">inventory configuration id</param>
        </member>
        <member name="T:Aliyun.OSS.GetVodPlaylistResult">
            <summary>
            The result class of the operation to get vod's playlist.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetVodPlaylistResult.Playlist">
            <summary>
            The vod's playlist.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelHistoryRequest">
            <summary>
            The request class of the operation to get live channel history.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelHistoryRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelHistoryRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetLiveChannelHistoryRequest.#ctor(System.String,System.String)">
            <summary>
            Gets a new instance of <see cref="T:Aliyun.OSS.GetLiveChannelHistoryRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelHistoryResult">
            <summary>
            The result class of the operation to get live channel history.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelHistoryResult.LiveRecords">
            <summary>
            The iterator of <see cref="T:Aliyun.OSS.LiveRecord" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelStatResult">
            <summary>
            The result class of the operation to get live channel stat.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.Status">
            <summary>
            Gets or sets the Status
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.ConnectedTime">
            <summary>
            Gets or sets the connected time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.RemoteAddr">
            <summary>
            Gets or sets the remote address
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.Width">
            <summary>
            Gets or sets the video width
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.Height">
            <summary>
            Gets or sets the video height
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.FrameRate">
            <summary>
            Gets or sets the video frame rate
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.VideoBandwidth">
            <summary>
            Gets or sets the video bandwidth
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.VideoCodec">
            <summary>
            Gets or sets the video codec
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.SampleRate">
            <summary>
            Gets or sets the audio sample rate
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.AudioBandwidth">
            <summary>
            Gets or sets the audio bandwidth
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatResult.AudioCodec">
            <summary>
            Gets or sets the audio codec
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelStatRequest">
            <summary>
            The request class of the operation to get live channel stat.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelStatRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetLiveChannelStatRequest.#ctor(System.String,System.String)">
            <summary>
            Gets a new instance of <see cref="T:Aliyun.OSS.GetLiveChannelStatRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelInfoRequest">
            <summary>
            The request class of the operation to get live channel info.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetLiveChannelInfoRequest.#ctor(System.String,System.String)">
            <summary>
            Gets a new instance of <see cref="T:Aliyun.OSS.GetLiveChannelInfoRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.GetLiveChannelInfoResult">
            <summary>
            The result class of the operation to get live channel info.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.Description">
            <summary>
            Gets or sets the description
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.Status">
            <summary>
            Gets or sets the status
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.Type">
            <summary>
            Gets or sets the type
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.FragDuration">
            <summary>
            Gets or sets the frag duration
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.FragCount">
            <summary>
            Gets or sets the frag count
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetLiveChannelInfoResult.PlaylistName">
            <summary>
            Gets or sets playlist name
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetVodPlaylistRequest">
            <summary>
            The request class of the operation to get a vod playlist.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetVodPlaylistRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetVodPlaylistRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetVodPlaylistRequest.StartTime">
            <summary>
            Gets or sets the start time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetVodPlaylistRequest.EndTime">
            <summary>
            Gets or sets the end time
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetVodPlaylistRequest.#ctor(System.String,System.String)">
            <summary>
            Sets a new instance of <see cref="T:Aliyun.OSS.GetVodPlaylistRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.ListLiveChannelResult">
            <summary>
            The result class of the operation to list live channel.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.Prefix">
            <summary>
            Gets or sets the live channel name prefix(optional).
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.Marker">
            <summary>
            Gets or sets the live channel name marker.Its value should be same as the ListLiveChannelRequest.Marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.MaxKeys">
            <summary>
            Gets or sets the max entries to return.
            By default it's 100.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.IsTruncated">
            <summary>
            Gets or sets the flag of truncated. If it's true, means not all live channels have been returned.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.NextMarker">
            <summary>
            Gets the next marker's value. Assign this value to the next call's ListLiveChannelRequest.marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelResult.LiveChannels">
            <summary>
            Gets the live channel iterator.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ListLiveChannelRequest">
            <summary>
            The request class of the operation to list the live channel.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelRequest.Prefix">
            <summary>
            Gets or sets the live channel name prefix to list (optional)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelRequest.Marker">
            <summary>
            Gets or sets the marker of the live channel name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListLiveChannelRequest.MaxKeys">
            <summary>
            Gets or sets the max entries to return. By default is 100.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListLiveChannelRequest.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.ListLiveChannelRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.Name">
            <summary>
            Gets or sets the name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.Description">
            <summary>
            Gets or sets the name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.Status">
            <summary>
            Gets or sets the status
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.LastModified">
            <summary>
            Gets or sets the last modified time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.PublishUrl">
            <summary>
            Gets or sets the publish url
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveChannel.PlayUrl">
            <summary>
            Gets or sets the play url
            </summary>
        </member>
        <member name="T:Aliyun.OSS.PostVodPlaylistRequest">
            <summary>
            The request class of the operation to create a vod playlist.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PostVodPlaylistRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PostVodPlaylistRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PostVodPlaylistRequest.PlaylistName">
            <summary>
            Gets or sets the playlist name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PostVodPlaylistRequest.StartTime">
            <summary>
            Gets or sets the start time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PostVodPlaylistRequest.EndTime">
            <summary>
            Gets or sets the end time
            </summary>
        </member>
        <member name="M:Aliyun.OSS.PostVodPlaylistRequest.#ctor(System.String,System.String)">
            <summary>
            Sets a new instance of <see cref="T:Aliyun.OSS.PostVodPlaylistRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.SetLiveChannelStatusRequest">
            <summary>
            The request class of the operation to set the live channel stauts.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetLiveChannelStatusRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetLiveChannelStatusRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetLiveChannelStatusRequest.Status">
            <summary>
            Gets or sets the status
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetLiveChannelStatusRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Set a new instance of <see cref="T:Aliyun.OSS.SetLiveChannelStatusRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
            <param name="status">status</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteBucketTaggingRequest">
            <summary>
            The request class of the operation to delete the bucket's tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteBucketTaggingRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteBucketTaggingRequest.Tags">
            <summary>
            Gets or sets the tags.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteBucketTaggingRequest.#ctor(System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.DeleteBucketTaggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.DeleteBucketTaggingRequest.#ctor(System.String,System.Collections.Generic.IList{Aliyun.OSS.Tag})">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.DeleteBucketTaggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="tags">tag list</param>
        </member>
        <member name="M:Aliyun.OSS.DeleteBucketTaggingRequest.AddTag(Aliyun.OSS.Tag)">
            <summary>
            Adds a tag
            </summary>
            <param name="tag"></param>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectResult">
            <summary>
            The result class for delete object operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectResult.DeleteMarker">
            <summary>
            Gets or sets the delete marker.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateSymlinkResult">
            <summary>
            The result class for create symlink operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSymlinkResult.ETag">
            <summary>
            ETag getter/setter. ETag is calculated in the OSS server side by using the 128bit MD5 result on the object content. It's the hex string.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSymlinkResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.BucketEncryptionResult">
            <summary>
            The result class of the operation to get bucket encryption config
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketEncryptionResult.SSEAlgorithm">
            <summary>
            Server-side encryption method.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketEncryptionResult.KMSMasterKeyID">
            <summary>
            The CMK id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.BucketMetadata">
            <summary>
            OSS bucket's metadata, which is the collection of 'key,value' pair.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketMetadata.HttpMetadata">
            <summary>
            Gets HTTP standard headers and their values.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketMetadata.BucketRegion">
            <summary>
            Gets or sets the bucket region(location)
            </summary>
        </member>
        <member name="M:Aliyun.OSS.BucketMetadata.AddHeader(System.String,System.String)">
            <summary>
            Adds one HTTP header and its value.
            </summary>
            <param name="key">header name</param>
            <param name="value">header value</param>
        </member>
        <member name="T:Aliyun.OSS.BucketLocationResult">
            <summary>
            The result class of the operation to get bucket's location.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketLocationResult.Location">
            <summary>
            The bucket location.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateBucketRequest">
            <summary>
            The request class of the operation to set the bucket's lifecycle configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateBucketRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateBucketRequest.StorageClass">
            <summary>
            Gets the bucket StorageClass
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateBucketRequest.ACL">
            <summary>
            Gets the bucket ACL
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateBucketRequest.DataRedundancyType">
            <summary>
            Gets the bucket DataRedundancyType
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CreateBucketRequest.#ctor(System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.CreateBucketRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.CreateBucketRequest.#ctor(System.String,Aliyun.OSS.StorageClass,Aliyun.OSS.CannedAccessControlList)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.CreateBucketRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="storageClass">the bucket storage class</param>
            <param name="acl">the bucket acl</param>
        </member>
        <member name="T:Aliyun.OSS.DeletedObjectSummary">
            <summary>
            a deleted object summary information.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeletedObjectSummary.Key">
            <summary>
            Gets or sets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeletedObjectSummary.VersionId">
            <summary>
            Gets or sets the version of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeletedObjectSummary.DeleteMarker">
            <summary>
            Gets or sets if it is a delete marker of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeletedObjectSummary.DeleteMarkerVersionId">
            <summary>
            Gets or sets the version of a delete marker.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DeleteLiveChannelRequest">
            <summary>
            The request class of the operation to delete the live channel.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteLiveChannelRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteLiveChannelRequest.ChannelName">
            <summary>
            Gets or sets the channel name
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteLiveChannelRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.DeleteLiveChannelRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="channelName">live channel name</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectVersionsRequest">
            <summary>
            The request class of the operation to delete multiple objects with version id in OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsRequest.Quiet">
            <summary>
            Gets quiet mode flag. By default it's true;
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsRequest.Objects">
            <summary>
            Returns the object list where the caller could add or remove key 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsRequest.EncodingType">
            <summary>
            Gets or sets encoding-type value. By default it's HttpUtils.UrlEncodingType.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectVersionsRequest.#ctor(System.String,System.Collections.Generic.IList{Aliyun.OSS.ObjectIdentifier})">
            <summary>
            Creates an instance with bucket name and keys. Quiet mode is true by default.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="objects">object lists to delete</param>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectVersionsRequest.#ctor(System.String,System.Collections.Generic.IList{Aliyun.OSS.ObjectIdentifier},System.Boolean)">
            <summary>
            Creates an instance with bucket name, objects and quiet flag.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="objects">object keys to delete</param>
            <param name="quiet">true: quiet mode; false: detail mode</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectVersionsResult">
            <summary>
            Description of DeleteObjectVersionsResult.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsResult.DeletedObjectSummaries">
            <summary>
            The iterator of <see cref="T:Aliyun.OSS.DeletedObjectSummary" /> that meet the requirements in the DeleteObjectVersionsRequest.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectVersionsResult.EncodingType">
            <summary>
            gets or sets EncodingType
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ExtendBucketWormRequest">
            <summary>
            The request class of the operation to ExtendBucketWorm.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ExtendBucketWormRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ExtendBucketWormRequest.RetentionPeriodInDays">
            <summary>
            Gets the RetentionPeriodInDays
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ExtendBucketWormRequest.WormId">
            <summary>
            Gets the worm id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ExtendBucketWormRequest.#ctor(System.String,System.Int32,System.String)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.ExtendBucketWormRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="days">RetentionPeriodInDays</param>
            <param name="id">wormId</param>
        </member>
        <member name="T:Aliyun.OSS.GetBucketVersioningResult">
            <summary>
            The result class of the operation to get bucket's versioning configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketVersioningResult.Status">
            <summary>
            Gets the versioning status
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectTaggingRequest">
            <summary>
            The request class of the operation to delete the object tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectTaggingRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectTaggingRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectTaggingRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectTaggingRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectTaggingRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.DeleteObjectTaggingRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetBucketWormResult">
            <summary>
            The result class of the operation to get bucket worm.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketWormResult.WormId">
            <summary>
            Set or Gets the worm id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketWormResult.State">
            <summary>
            Set or Gets the bucket worm state
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketWormResult.RetentionPeriodInDays">
            <summary>
            Set or Gets the retention period in days
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketWormResult.CreationDate">
            <summary>
            Set or Gets the creation date
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetObjectTaggingRequest">
            <summary>
            The request class of the operation to get the object tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectTaggingRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectTaggingRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectTaggingRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectTaggingRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetObjectTaggingRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.GetObjectTaggingRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetSymlinkRequest">
            <summary>
            The request class of the operation to get the object symlink.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetSymlinkRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetSymlinkRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetSymlinkRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetSymlinkRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetSymlinkRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.GetSymlinkRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetObjectMetadataRequest">
            <summary>
            The request class of the operation to get the object meta.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectMetadataRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectMetadataRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectMetadataRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectMetadataRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetObjectMetadataRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.GetObjectAclRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetObjectAclRequest">
            <summary>
            The request class of the operation to get the object ACL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectAclRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectAclRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectAclRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectAclRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetObjectAclRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.GetObjectAclRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.InitiateBucketWormRequest">
            <summary>
            The request class of the operation to initiate bucket worm.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateBucketWormRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateBucketWormRequest.RetentionPeriodInDays">
            <summary>
            Gets the retention period in days
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InitiateBucketWormRequest.#ctor(System.String,System.Int32)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.InitiateBucketWormRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="days">RetentionPeriodInDays</param>
        </member>
        <member name="T:Aliyun.OSS.ObjectVersionList">
            <summary>
            The result class of the operation to list object versions.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.BucketName">
            <summary>
            Gets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.NextKeyMarker">
            <summary>
            Gets the next key maker value for the value of <see cref="P:ListObjectVersionsRequest.KeyMarker" /> in the next call.
            If the result is not truncated, this value is null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.NextVersionIdMarker">
            <summary>
            Gets the next version id maker value for the value of <see cref="P:ListObjectVersionsRequest.VersionIdMarker" /> in the next call.
            If the result is not truncated, this value is null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.IsTruncated">
            <summary>
            Flag of truncated result.
            True: the result is truncated (there's more data to list).
            False: no more data in server side to return.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.KeyMarker">
            <summary>
            The object key's marker. The value comes from <see cref="P:ListObjectVersionsRequest.KeyMarker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.VersionIdMarker">
            <summary>
            The version id's marker. The value comes from <see cref="P:ListObjectVersionsRequest.VersionIdMarker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.MaxKeys">
            <summary>
            The max keys to list. The value comes from <see cref="P:ListObjectVersionsRequest.MaxKeys" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.Prefix">
            <summary>
            The object key's prefix. The value comes from <see cref="P:ListObjectVersionsRequest.Prefix" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.Delimiter">
            <summary>
            The delimiter for grouping object. The value comes from <see cref="P:ListObjectVersionsRequest.Delimiter" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.ObjectVersionSummaries">
            <summary>
            The iterator of <see cref="T:Aliyun.OSS.ObjectVersionSummary" /> that meet the requirements in the ListObjectVersionsRequest.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.DeleteMarkerSummaries">
            <summary>
            The iterator of <see cref="T:Aliyun.OSS.DeleteMarkerSummary" /> that meet the requirements in the ListObjectVersionsRequest.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionList.CommonPrefixes">
            <summary>
            The common prefixes in the result. The objects returned do not include the objects under these common prefixes (folders).
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectVersionList.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.ObjectVersionList" />.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
        </member>
        <member name="T:Aliyun.OSS.ListObjectVersionsRequest">
            <summary>
            The request class of the operation to list the summary about the versions(<see cref="T:Aliyun.OSS.ObjectVersionSummary" />)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.BucketName">
            <summary>
            Gets or sets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.Prefix">
            <summary>
            Gets or sets the object name prefix. The names of the returned object must be prefixed by this value.
            It's optional. If it's not set, then there's no requirement on the object name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.KeyMarker">
            <summary>
            Gets or sets the marker value. The name of returned objects must be greater than this value in lexicographic order.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.VersionIdMarker">
            <summary>
            Gets or sets the version id marker value.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.MaxKeys">
            <summary>
            Gets or sets the max entries to return.
            By default it's 100.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.Delimiter">
            <summary>
            Gets or sets the delimiter for grouping the returned objects based on their keys.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.EncodingType">
            <summary>
            Gets or sets encoding-type.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectVersionsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListObjectVersionsRequest.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.ListObjectVersionsRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteMarkerSummary">
            <summary>
            <see cref="T:Aliyun.OSS.OssObject" /> of a delete marker summary information.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.Key">
            <summary>
            Gets or sets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.VersionId">
            <summary>
            Gets or sets the version of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.IsLatest">
            <summary>
            Gets or sets if it is the latest version of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.LastModified">
            <summary>
            Gets the last modified time.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteMarkerSummary.Owner">
            <summary>
            Get's the object's <see cref="P:Aliyun.OSS.DeleteMarkerSummary.Owner" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteMarkerSummary.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.DeleteMarkerSummary" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteMarkerSummary.ToString">
            <summary>
            Gets the serialization result in string.
            </summary>
            <returns>serialization result in string</returns>
        </member>
        <member name="T:Aliyun.OSS.ObjectVersionSummary">
            <summary>
            <see cref="T:Aliyun.OSS.OssObject" /> of a version summary information, no object data.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.Key">
            <summary>
            Gets or sets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.VersionId">
            <summary>
            Gets or sets the version of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.IsLatest">
            <summary>
            Gets or sets if it is the latest version of a object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.ETag">
            <summary>
            Gets or sets the ETag which is the MD5 summry in hex string of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.Size">
            <summary>
            Gets or sets the size of the object in bytes.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.LastModified">
            <summary>
            Gets or sets the last modified time.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.StorageClass">
            <summary>
            Gets or sets the object's storage class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.Owner">
            <summary>
            Gets or sets the object's <see cref="P:Aliyun.OSS.ObjectVersionSummary.Owner" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectVersionSummary.Type">
            <summary>
            Gets or sets the object's type.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectVersionSummary.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.ObjectVersionSummary" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectVersionSummary.ToString">
            <summary>
            Gets the serialization result in string.
            </summary>
            <returns>serialization result in string</returns>
        </member>
        <member name="T:Aliyun.OSS.ObjectIdentifier">
            <summary>
            The object's identifier.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectIdentifier.Key">
            <summary>
            the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectIdentifier.VersionId">
            <summary>
            the object version id.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectIdentifier.#ctor(System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.ObjectIdentifier" />.
            </summary>
            <param name="key">object name</param>
        </member>
        <member name="M:Aliyun.OSS.ObjectIdentifier.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.ObjectIdentifier" />.
            </summary>
            <param name="key">object name</param>
            <param name="versionId">the object version id</param>
        </member>
        <member name="T:Aliyun.OSS.RestoreObjectRequest">
            <summary>
            The request class of the operation to restore an object from OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.Days">
            <summary>
            Gets or sets the Days
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RestoreObjectRequest.Tier">
            <summary>
            Gets or sets the TierType
            </summary>
        </member>
        <member name="M:Aliyun.OSS.RestoreObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.RestoreObjectRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="M:Aliyun.OSS.RestoreObjectRequest.IsUseDefaultParameter">
            <summary>
            Flag of using default parameters.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectRequest">
            <summary>
            The request class of the operation to delete an object from OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Delete a new instance of <see cref="T:Aliyun.OSS.DeleteObjectRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.GetBucketRequestPaymentResult">
            <summary>
            The result class of the operation to get bucket's request payment.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketRequestPaymentResult.Payer">
            <summary>
            Gets the request payment
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetBucketStorageCapacityResult">
            <summary>
            The result class of the operation to get bucket's storage capacity.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketStorageCapacityResult.StorageCapacity">
            <summary>
            The bucket storage capacity.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetObjectTaggingResult">
            <summary>
            The result class of the operation to get bucket's tagging.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.GetObjectTaggingResult.tags">
            <summary>
            The bucket tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectTaggingResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.GetBucketTaggingResult">
            <summary>
            The result class of the operation to get bucket's tagging.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.GetBucketTaggingResult._tags">
            <summary>
            The bucket tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectInputFormat.CompressionType">
            <summary>
            Specifies the compression type of the object. Valid values: None, GZIP.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SelectObjectCSVInputFormat">
            <summary>
            Describes how a CSV-formatted input object is formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.RecordDelimiter">
            <summary>
            Specifies the value used to separate individual records.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.FieldDelimiter">
            <summary>
            Specifies the value used to separate individual fields in a record.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.QuoteCharacter">
            <summary>
            Specifies the value used for escaping where the field delimiter is part of the value.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.CommentCharacter">
            <summary>
            Specifies the comment character used in the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.Range">
            <summary>
            Specifies the query range. The following two query methods are supported: 
            Query by row: line-range=start-end 
            Query by split: split-range=start-end 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.FileHeaderInfo">
            <summary>
            Specifies the first line of input. Valid values: None, Ignore, Use.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVInputFormat.AllowQuotedRecordDelimiter">
            <summary>
            Specifies whether the CSV object contains line breaks in quotation marks (")
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SelectObjectJSONInputFormat">
            <summary>
            Describes how a JSON-formatted input object is formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectJSONInputFormat.Type">
            <summary>
            Specifies the type of the input JSON object. Valid values: DOCUMENT, LINES.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectJSONInputFormat.Range">
            <summary>
            Specifies the query range. The following two query methods are supported: 
            Query by row: line-range=start-end 
            Query by split: split-range=start-end 
            This parameter can only be used when the JSON Type is LINES.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectJSONInputFormat.ParseJsonNumberAsString">
            <summary>
            Specifies whether to parse integers and floating-point numbers in a JSON object into strings.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectOutputFormat.OutputRawData">
            <summary>
            Specifies whether to output in raw format. Default value is fasle.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectOutputFormat.EnablePayloadCrc">
            <summary>
            Specifies whether to include  a CRC-32 value for each frame. 
            This value is used to verify frame data.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SelectObjectCSVOutputFormat">
            <summary>
            Describes how CSV-formatted results are formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVOutputFormat.RecordDelimiter">
            <summary>
            Specifies the value used to separate individual records.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVOutputFormat.FieldDelimiter">
            <summary>
            Specifies the value used to separate individual fields in a record.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVOutputFormat.KeepAllColumns">
            <summary>
            Specifies whether to include that all columns in the CSV object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectCSVOutputFormat.OutputHeader">
            <summary>
            Specifies whether to include the header information of the CSV object in the beginning of the returned.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SelectObjectJSONOutputFormat">
            <summary>
            Describes how JSON-formatted results are formatted.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectJSONOutputFormat.RecordDelimiter">
            <summary>
            Specifies the value used to separate individual records in the output.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectOptions.SkipPartialDataRecord">
            <summary>
            Specifies whether to ignore rows without data.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectOptions.MaxSkippedRecordsAllowed">
            <summary>
            Specifies the maximum allowed number of skipped rows.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SelectObjectRequest">
            <summary>
            The request class of the operation to select object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.Key">
            <summary>
            Gets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.Expression">
            <summary>
            Gets or sets The SQL Expression.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.InputFormat">
            <summary>
            Gets or sets the format of the data in the object that is being queried.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.OutputFormat">
            <summary>
            Gets or sets the format of the data that you want the server to return in response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SelectObjectRequest.Options">
            <summary>
            Gets or sets the options when quering the data.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SelectObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.SelectObjectRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">key</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketEncryptionRequest">
            <summary>
            The request class of the operation to set bucket encryption configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketEncryptionRequest.BucketName">
            <summary>
            Gets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketEncryptionRequest.SSEAlgorithm">
            <summary>
            Gets server-side encryption method.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketEncryptionRequest.KMSMasterKeyID">
            <summary>
            Gets the CMK id.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketEncryptionRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketEncryptionRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="algorithm">server-side encryption method</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketEncryptionRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketEncryptionRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="algorithm">server-side encryption method</param>
            <param name="id">the CMK id</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketVersioningRequest">
            <summary>
            The request class of the operation to set the bucket versioning configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketVersioningRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketVersioningRequest.Status">
            <summary>
            Gets the versioning status
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketVersioningRequest.#ctor(System.String,Aliyun.OSS.VersioningStatus)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.SetBucketVersioningRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="status">versioning status</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketRequestPaymentRequest">
            <summary>
            The request class of the operation to set the bucket request payment.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketRequestPaymentRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketRequestPaymentRequest.Payer">
            <summary>
            Gets the request payment
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketRequestPaymentRequest.#ctor(System.String,Aliyun.OSS.RequestPayer)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.SetBucketRequestPaymentRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="payer">request payer</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketStorageCapacityRequest">
            <summary>
            The request class of the operation to set the bucket storage capacity
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketStorageCapacityRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketStorageCapacityRequest.StorageCapacity">
            <summary>
            The bucket storage capacity
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketStorageCapacityRequest.#ctor(System.String,System.Int64)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketStorageCapacityRequest" />.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.OssObject" />bucket name</param>
            <param name="storageCapacity">storage capacity</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketTaggingRequest">
            <summary>
            The request class of the operation to set the bucket's tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketTaggingRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketTaggingRequest.Tags">
            <summary>
            Gets or sets the tags.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketTaggingRequest.#ctor(System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.SetBucketTaggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketTaggingRequest.#ctor(System.String,System.Collections.Generic.IList{Aliyun.OSS.Tag})">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.SetBucketTaggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="tags">tag list</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketTaggingRequest.AddTag(Aliyun.OSS.Tag)">
            <summary>
            Adds a tag
            </summary>
            <param name="tag"></param>
        </member>
        <member name="T:Aliyun.OSS.SetObjectTaggingRequest">
            <summary>
            The request class of the operation to set the object's tagging.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectTaggingRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectTaggingRequest.Key">
            <summary>
            Gets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectTaggingRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectTaggingRequest.Tags">
            <summary>
            Gets or sets the tagging.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetObjectTaggingRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.SetObjectTaggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.SetObjectTaggingRequest.AddTag(Aliyun.OSS.Tag)">
            <summary>
            Adds a tag
            </summary>
            <param name="tag">tag</param>
        </member>
        <member name="P:Aliyun.OSS.LiveRecord.StartTime">
            <summary>
            Gets or sets the start time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveRecord.EndTime">
            <summary>
            Gets or sets the end time
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LiveRecord.RemoteAddr">
            <summary>
            Gets or sets the remote address
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryFilter">
            <summary>
            The inventory filter class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryFilter.Prefix">
            <summary>
            Gets or sets the prefix value
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventoryFilter.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryFilter" />.
            </summary>
            <param name="prefix">the prefix that an object must have to be included in the inventory results.</param>
        </member>
        <member name="M:Aliyun.OSS.InventoryFilter.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryFilter" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventorySchedule">
            <summary>
            The inventory schedule class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventorySchedule.Frequency">
            <summary>
            Gets or sets the frequency value.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventorySchedule.#ctor(Aliyun.OSS.InventoryFrequency)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventorySchedule" />.
            </summary>
            <param name="frequency"> how frequently inventory results are produced.</param>
        </member>
        <member name="M:Aliyun.OSS.InventorySchedule.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventorySchedule" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventorySSEOSS">
            <summary>
            The inventory SSE-OSS class definition
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventorySSEOSS.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventorySSEOSS" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventorySSEKMS">
            <summary>
            The inventory SSE-KMS class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventorySSEKMS.KeyId">
            <summary>
            Gets or sets the KMS key id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventorySSEKMS.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventorySSEKMS" />.
            </summary>
            <param name="keyId">the KMS key id used to encrypt the inventory contents.</param>
        </member>
        <member name="M:Aliyun.OSS.InventorySSEKMS.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventorySSEKMS" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryEncryption">
            <summary>
            The inventory encryption class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryEncryption.SSEOSS">
            <summary>
            Gets or sets the SSE-OSS encryption.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryEncryption.SSEKMS">
            <summary>
            Gets or sets the SSE-KMS encryption.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventoryEncryption.#ctor(Aliyun.OSS.InventorySSEOSS)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryEncryption" />.
            </summary>
            <param name="sSEOSS">specifies the use of SSE-OSS to encrypt delivered inventory results.</param>
        </member>
        <member name="M:Aliyun.OSS.InventoryEncryption.#ctor(Aliyun.OSS.InventorySSEKMS)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryEncryption" />.
            </summary>
            <param name="sSEKMS">specifies the use of SSE-KMS to encrypt delivered inventory results.</param>
        </member>
        <member name="M:Aliyun.OSS.InventoryEncryption.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryEncryption" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryOSSBucketDestination">
            <summary>
            The inventory destination for OSS bucket class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.Format">
            <summary>
            Gets or sets the output format of the inventory results.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.AccountId">
            <summary>
            Gets or sets the account ID that owns the destination bucket.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.RoleArn">
            <summary>
            Gets or sets the name of the role arn.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.Bucket">
            <summary>
            Gets or sets the bucket where inventory results will be published.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.Prefix">
            <summary>
            Gets or sets the prefix that is prepended to all inventory results.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryOSSBucketDestination.Encryption">
            <summary>
            Gets or sets the type of server-side encryption used to encrypt the inventory results.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventoryOSSBucketDestination.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryOSSBucketDestination" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryDestination">
            <summary>
            The inventory destination class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryDestination.OSSBucketDestination">
            <summary>
            Gets or sets the OSS bucket information.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InventoryDestination.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InventoryDestination" />.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryConfiguration">
            <summary>
            The inventory configuration class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.Id">
            <summary>
            Gets or sets the ID used to identify the inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.IsEnabled">
            <summary>
            Gets or sets the status of the inventory.
            If set to true, an inventory list is generated.
            If set to false, no inventory list is generated.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.Filter">
            <summary>
            Gets or sets the inventory filter.
            The inventory only includes objects that meet the filter's criteria.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.Destination">
            <summary>
            Gets or sets information about where to publish the inventory results.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.Schedule">
            <summary>
            Gets or sets the schedule for generating inventory results.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.IncludedObjectVersions">
            <summary>
            Gets or sets object versions to include in the inventory list.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InventoryConfiguration.OptionalFields">
            <summary>
            Gets or sets the optional fields that are included in the inventory result.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Tag.Key">
            <summary>
            Gets or sets the tag key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Tag.Value">
            <summary>
            Gets or sets the tag value
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Model.GetBucketInventoryConfigurationResult">
            <summary>
            The result class of the operation to get bucket's inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.GetBucketInventoryConfigurationResult.Configuration">
            <summary>
            The bucket inventory configuration.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Model.ListBucketInventoryConfigurationResult">
            <summary>
            The result class of the operation to list bucket's inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketInventoryConfigurationResult.IsTruncated">
            <summary>
            Gets or sets the flag of truncated.
            If it's true, means not all configurations have been returned.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketInventoryConfigurationResult.NextContinuationToken">
            <summary>
            Gets the next continuation token.
            Assign this value to the next call's ListBucketInventoryConfigurationRequest.ContinuationToken.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketInventoryConfigurationResult.Configurations">
            <summary>
            Gets the inventory configuration iterator.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Model.StreamResult">
            <summary>
            Base class for responses that return a stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Model.StreamResult.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.StreamResult.ResponseStream">
            <summary>
            An open stream read from to get the data from OSS. In order to
            use this stream without leaking the underlying resource, please
            wrap access to the stream within a using block.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Model.StreamResult.IsSetResponseStream">
            <summary>
            Check to see if Body property is set
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Model.GenericResult">
            <summary>
            Abstract class for Response objects, contains only metadata, 
            and no result information.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.GenericResult.HttpStatusCode">
            <summary>
            Returns the status code of the HTTP response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.GenericResult.RequestId">
            <summary>
            Gets and sets the RequestId property.
            ID that uniquely identifies a request. Aliyun keeps track of request IDs. If you have a question about a request, include the request ID in your correspondence.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.GenericResult.ContentLength">
            <summary>
            Returns the content length of the HTTP response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Model.GenericResult.ResponseMetadata">
            <summary>
            Contains additional information about the request, such as the md5 value of the object.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Model.ListBucketResult">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.Name">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.Prefix">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.EncodingType">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.Marker">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.MaxKeys">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.Delimiter">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.IsTruncated">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.NextMarker">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.Contents">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResult.CommonPrefixes">
            <remarks/>
        </member>
        <member name="T:Aliyun.OSS.Model.ListBucketResultContents">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.Key">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.LastModified">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.ETag">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.Type">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.Size">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.StorageClass">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultContents.Owner">
            <remarks/>
        </member>
        <member name="T:Aliyun.OSS.Model.ListBucketResultCommonPrefixes">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.ListBucketResultCommonPrefixes.Prefix">
            <remarks/>
        </member>
        <member name="P:Aliyun.OSS.Model.RestoreObjectResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SetBucketInventoryConfigurationRequest">
            <summary>
            The request class of the operation to set the bucket inventory configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketInventoryConfigurationRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketInventoryConfigurationRequest.Configuration">
            <summary>
            Gets the inventory configuration
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketInventoryConfigurationRequest.#ctor(System.String,Aliyun.OSS.InventoryConfiguration)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.SetBucketInventoryConfigurationRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="configuration">inventory configuration</param>
        </member>
        <member name="T:Aliyun.OSS.InventoryFormat">
            <summary>
            The output format of the inventory results
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryFrequency">
            <summary>
            How frequently inventory results are produced
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryIncludedObjectVersions">
            <summary>
            Object versions to include in the inventory list
            All, the list includes all the object versions, which adds the version-related fields VersionId , IsLatest , and DeleteMarker to the list
            Current, the list does not contain these version-related fields.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InventoryOptionalField">
            <summary>
            The optional fields that are included in the inventory results
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DataRedundancyType">
            <summary>
            Disaster recovery of OSS Bucket
            </summary>
        </member>
        <member name="T:Aliyun.OSS.BucketWormState">
            <summary>
            The worm state of a bucket
            </summary>
        </member>
        <member name="T:Aliyun.OSS.TierType">
            <summary>
            The mode of restoring an object
            </summary>
        </member>
        <member name="T:Aliyun.OSS.VersioningStatus">
            <summary>
            The enum of versioning status
            </summary>
        </member>
        <member name="F:Aliyun.OSS.VersioningStatus.Off">
            <summary>
            OSS bucket versioning status indicating that versioning is off for a
            bucket. By default, all buckets start off with versioning off. Once you
            enable versioning for a bucket, you can never set the status back to
            Off". You can only suspend versioning on a bucket once you've enabled.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.VersioningStatus.Enabled">
            <summary>
            OSS bucket versioning status indicating that versioning is enabled for a
            bucket.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.VersioningStatus.Suspended">
            <summary>
            OSS bucket versioning status indicating that versioning is suspended for a
            bucket. Use the "Suspended" status when you want to disable versioning on
            a bucket that has versioning enabled.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.RequestPayer">
            <summary>
            The enum of who pays the request fees
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Transform.XmlStreamSerializer`1">
            <summary>
            Serialize an object of type TRequest to XML stream.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Transform.XmlStreamDeserializer`1">
            <summary>
            Deserialize an object of type T from XML stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Transform.XmlStreamDeserializer`1.Deserialize(System.IO.Stream)">
            <summary>
            Deserialize an object of type T, then close the underlying stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Transform.GetBucketLifecycleDeserializer.TryParseEnum(System.String,Aliyun.OSS.RuleStatus@)">
            <summary>
            TryParseEnum does not exist in .net 2.0. But we need to support .net 2.0
            </summary>
            <param name="value">The string value to parse from.</param>
            <param name="status">The parsed value </param>
            <returns>True: the parse succeeds; False: the parse fails</returns>
        </member>
        <member name="T:Aliyun.OSS.GetBucketPolicyResult">
            <summary>
            The result class of the operation to get bucket's policy.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetBucketPolicyResult.Policy">
            <summary>
            The bucket's policy.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.StreamTransferProgressArgs.#ctor(System.Int64,System.Int64,System.Int64)">
            <summary>
            The constructor takes the number of
            currently transferred bytes and the
            total number of bytes to be transferred
            </summary>
            <param name="incrementTransferred">The number of bytes transferred since last event</param>
            <param name="transferred">The number of bytes transferred</param>
            <param name="total">The total number of bytes to be transferred</param>
        </member>
        <member name="P:Aliyun.OSS.StreamTransferProgressArgs.PercentDone">
            <summary>
            Gets the percentage of transfer completed
            </summary>
        </member>
        <member name="P:Aliyun.OSS.StreamTransferProgressArgs.IncrementTransferred">
            <summary>
            Gets the number of bytes transferred since last event
            </summary>
        </member>
        <member name="P:Aliyun.OSS.StreamTransferProgressArgs.TransferredBytes">
            <summary>
            Gets the number of bytes transferred
            </summary>
        </member>
        <member name="P:Aliyun.OSS.StreamTransferProgressArgs.TotalBytes">
            <summary>
            Gets the total number of bytes to be transferred
            </summary>
        </member>
        <member name="M:Aliyun.OSS.StreamTransferProgressArgs.ToString">
            <summary>
            Returns a string representation of this object
            </summary>
            <returns></returns>
        </member>
        <member name="T:Aliyun.OSS.AppendObjectRequest">
            <summary>
            The request which is used to append data into an object (existing or non-existing)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.BucketName">
            <summary>
            Bucket name getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.Key">
            <summary>
            Object key getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.ObjectMetadata">
            <summary>
            Object metadata getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.Position">
            <summary>
            Position getter/setter. The position is the start index for the appending. 
            Initially it could be the length of the object (length could be got from the GetObjectmeta). Then it could be got from the previous result of AppendObjectRequest.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.InitCrc">
            <summary>
            Gets or sets the init crc.
            </summary>
            <value>The init crc.</value>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.Content">
            <summary>
            The content to append
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.StreamTransferProgress">
            <summary>
            Progress callback getter and setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="M:Aliyun.OSS.AppendObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.AppendObjectRequest" />
            </summary>
            <param name="bucketName"> bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="T:Aliyun.OSS.AppendObjectResult">
            <summary>
            The result class for appending operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectResult.ETag">
            <summary>
            ETag getter/setter. ETag is calculated in the OSS server side by using the 128bit MD5 result on the object content. It's the hex string.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectResult.NextAppendPosition">
            <summary>
            The next append position
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectResult.HashCrc64Ecma">
            <summary>
            The CRC value of the object. It's calculated by ECMA-182.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AppendObjectResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.PutObjectRequest">
            <summary>
            The request class of the operation to put an object to OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.Content">
            <summary>
            Gets or sets object content stream
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.UseChunkedEncoding">
            <summary>
            Gets or sets a value indicating whether this <see cref="T:Aliyun.OSS.PutObjectRequest"/> use chunked encoding.
            </summary>
            <value><c>true</c> if use chunked encoding; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.StreamTransferProgress">
            <summary>
            Gets or sets the transfer progress callback
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.Metadata">
            <summary>
            Gets or sets the object metadata.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.Process">
            <summary>
            Gets or sets the process method.The result will be in <see cref="P:PutObjectResult.ResponseStream" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="M:Aliyun.OSS.PutObjectRequest.#ctor(System.String,System.String,System.IO.Stream)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.PutObjectRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="content">content to upload</param>
        </member>
        <member name="M:Aliyun.OSS.PutObjectRequest.#ctor(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.PutObjectRequest" />
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="content">content to upload</param>
            <param name="metadata">metadata to set</param>
        </member>
        <member name="M:Aliyun.OSS.PutObjectRequest.#ctor(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.Boolean)">
            <summary>
            Puts the object result.
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
            <param name="content">Content.</param>
            <param name="metadata">Metadata.</param>
            <param name="useChunkedEncoding">If set to <c>true</c> use chunked encoding.</param>
        </member>
        <member name="M:Aliyun.OSS.PutObjectRequest.IsNeedResponseStream">
            <summary>
            Returns true if the request has the Process property or has the callback in metadata.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.PutObjectRequest.IsCallbackRequest">
            <summary>
            Returns true if the request has the callback in Metadata property.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SetObjectAclRequest">
            <summary>
            The request class of the operation to set the object ACL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectAclRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectAclRequest.Key">
            <summary>
            Gets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectAclRequest.ACL">
            <summary>
            Gets the ACL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectAclRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetObjectAclRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetObjectAclRequest.#ctor(System.String,System.String,Aliyun.OSS.CannedAccessControlList)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetObjectAclRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="acl">access control list</param>
        </member>
        <member name="T:Aliyun.OSS.ObjectMetadata">
            <summary>
            OSS object's metadata, which is the collection of 'key,value' pair.
            <para>
            It includes user's custom metadata, as well as standard HTTP headers such as Content-Length, ETag, etc.
            </para>
            </summary>
        </member>
        <member name="F:Aliyun.OSS.ObjectMetadata.Aes256ServerSideEncryption">
            <summary>
            256 bit ASE encryption algorithm. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.UserMetadata">
            <summary>
            Gets the user's custom metadata.
            </summary>
            <remarks>
            In OSS server side, it will add "x-oss-meta-" as the prefix for the keys of custom metadata. 
            However, here the key in UserMetadata should not include "x-oss-meta-".
            And the key is case insensitive--in fact all the keys returned from server will be in lowercase anyway.
            For example, for a key MyUserMeta, it will be myusermeta from the result of GetObjectMetadata().
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.HttpMetadata">
            <summary>
            Gets HTTP standard headers and their values.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.LastModified">
            <summary>
            Gets or sets the last modified timestamp of the OSS object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ExpirationTime">
            <summary>
            Gets or sets the expiration time of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ContentLength">
            <summary>
            Gets or sets the content length of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ContentType">
            <summary>
            Gets or sets the content type of the objeft. It's the standard MIME type.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ContentEncoding">
            <summary>
            Gets or sets the content encoding of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.CacheControl">
            <summary>
            Gets or sets the value of HTTP Cache-Control header.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ContentDisposition">
            <summary>
            Gets or sets the value of HTTP Content-Disposition header.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ETag">
            <summary>
            Gets or sets the value of HTTP ETag header. Note that this is set by OSS server. 
            To set the Content-MD5 value, use HTTP COntent-MD5 header instead.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ContentMd5">
            <summary>
            Gets or sets the HTTP Content-MD5 header, which is the MD5 summary in Hex string of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.Crc64">
            <summary>
            Gets or sets the crc64.
            </summary>
            <value>The crc64.</value>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ServerSideEncryption">
            <summary>
            Gets or sets the server side encryption algorithm. Only AES256 is support for now.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.ObjectType">
            <summary>
            Gets the object type (Normal or Appendable)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectMetadata.VersionId">
            <summary>
            Gets the object version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectMetadata.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.ObjectMetadata" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectMetadata.AddHeader(System.String,System.Object)">
            <summary>
            Adds one HTTP header and its value.
            </summary>
            <param name="key">header name</param>
            <param name="value">header value</param>
        </member>
        <member name="M:Aliyun.OSS.ObjectMetadata.Populate(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Populates the request header dictionary with the metdata and user metadata.
            </summary>
            <param name="requestHeaders"></param>
        </member>
        <member name="M:Aliyun.OSS.ObjectMetadata.Populate(System.Net.HttpWebRequest)">
            <summary>
            Populates the request header dictionary with the metdata and user metadata.
            </summary>
            <param name="webRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.ObjectMetadata.HasCallbackHeader(Aliyun.OSS.ObjectMetadata)">
            <summary>
            Get the flag which indicates if the metadata specifies the callback.
            </summary>
            <param name="metadata">The metadata object to check</param>
            <returns></returns>
        </member>
        <member name="T:Aliyun.OSS.SetBucketPolicyRequest">
            <summary>
            The request class of the operation to set the bucket Policy.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketPolicyRequest.Policy">
            <summary>
            Gets the bucket policy
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketPolicyRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketPolicyRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.SetBucketPolicyRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="policy">policy text</param>
        </member>
        <member name="T:Aliyun.OSS.Util.AsyncResult">
            <summary>
            The implementation of <see cref="T:System.IAsyncResult"/>
            that represents the status of an async operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.AsyncResult.AsyncState">
            <summary>
            Gets a user-defined object that qualifies or contains information about an asynchronous operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.AsyncResult.AsyncWaitHandle">
            <summary>
            Gets a <see cref="T:System.Threading.WaitHandle"/> that is used to wait for an asynchronous operation to complete. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.AsyncResult.CompletedSynchronously">
            <summary>
            Gets a value that indicates whether the asynchronous operation completed synchronously.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.AsyncResult.IsCompleted">
            <summary>
            Gets a value that indicates whether the asynchronous operation has completed.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.#ctor(System.AsyncCallback,System.Object)">
            <summary>
            Initializes an instance of <see cref="T:Aliyun.OSS.Util.AsyncResult"/>.
            </summary>
            <param name="callback">The callback method when the async operation completes.</param>
            <param name="state">A user-defined object that qualifies or contains information about an asynchronous operation.</param>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.Complete(System.Exception)">
            <summary>
            Completes the async operation with an exception.
            </summary>
            <param name="ex">Exception from the async operation.</param>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.WaitForCompletion">
            <summary>
            When called in the dervied classes, wait for completion.
            It throws exception if the async operation ends with an exception.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.NotifyCompletion">
            <summary>
            When called in the derived classes, notify operation completion
            by setting <see cref="P:AsyncWaitHandle"/> and calling the user callback.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.Dispose">
            <summary>
            Disposes the object and release resource.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult.Dispose(System.Boolean)">
            <summary>
            When overrided in the derived classes, release resources.
            </summary>
            <param name="disposing">Whether the method is called <see cref="M:Dispose"/></param>
        </member>
        <member name="T:Aliyun.OSS.Util.AsyncResult`1">
            <summary>
            Represents the status of an async operation.
            It also holds the result of the operation.
            </summary>
            <typeparam name="T">Type of the operation result.</typeparam>
        </member>
        <member name="F:Aliyun.OSS.Util.AsyncResult`1._result">
            <summary>
            The result of the async operation.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult`1.#ctor(System.AsyncCallback,System.Object)">
            <summary>
            Initializes an instance of <see cref="T:Aliyun.OSS.Util.AsyncResult`1"/>.
            </summary>
            <param name="callback">The callback method when the async operation completes.</param>
            <param name="state">A user-defined object that qualifies or contains information about an asynchronous operation.</param>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult`1.GetResult">
            <summary>
            Gets result and release resources.
            </summary>
            <returns>The instance of result.</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.AsyncResult`1.Complete(`0)">
            <summary>
            Sets result and notify completion.
            </summary>
            <param name="result">The instance of result.</param>
        </member>
        <member name="T:Aliyun.OSS.Util.CallbackBodyType">
            <summary>
            Callback body's format type. The OSS will issue a post request to the callback url with the data specified in the request's callbackbody header.
            <para>
            OSS does not validate the data sent to callback url.
            </para>
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.CallbackBodyType.Url">
            <summary>
            Url encoded. 
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.CallbackBodyType.Json">
            <summary>
            Json encoded 
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Util.CallbackHeaderBuilder">
            <summary>
            The callback header's builder
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.CallbackHeaderBuilder.CallbackUrl">
            <summary>
            Gets or sets the callback url such as “http://callback.oss.demo.com:9000”
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.CallbackHeaderBuilder.CallbackHost">
            <summary>
            Gets or sets the callback host.By default it's <see cref="P:Aliyun.OSS.Util.CallbackHeaderBuilder.CallbackUrl"/>
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.CallbackHeaderBuilder.CallbackBody">
            <summary>
            Gets or sets the callback body.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.CallbackHeaderBuilder.CallbackBodyType">
            <summary>
            Gets or sets the callback body type.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.CallbackHeaderBuilder.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.Util.CallbackHeaderBuilder" />
            </summary>
            <param name="callbackUrl">callback url</param>
            <param name="callbackBody">callback body</param>
        </member>
        <member name="M:Aliyun.OSS.Util.CallbackHeaderBuilder.#ctor(System.String,System.String,System.String,Aliyun.OSS.Util.CallbackBodyType)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.Util.CallbackHeaderBuilder" />
            </summary>
            <param name="callbackUrl">callback url</param>
            <param name="callbackHost"> callback host</param>
            <param name="callbackBody">callback body</param>
            <param name="callbackBodyType">callback body type</param>
        </member>
        <member name="M:Aliyun.OSS.Util.CallbackHeaderBuilder.Build">
            <summary>
            Builds the callback header.
            </summary>
            <returns>the callback header</returns>
        </member>
        <member name="T:Aliyun.OSS.Util.CallbackVariableHeaderBuilder">
            <summary>
            The callback variable header builder.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Util.CallbackVariableHeaderBuilder.CallbackVariable">
            <summary>
            Gets the callback variable dictionary.
            </summary>
            <remarks>
            The custom parameter's key must start with "x:" and be in lowercase.
            </remarks>
        </member>
        <member name="M:Aliyun.OSS.Util.CallbackVariableHeaderBuilder.AddCallbackVariable(System.String,System.String)">
            <summary>
            Adds the callback variable
            </summary>
            <param name="key">the custom variable, must start with "x:"</param>
            <param name="value">the value of the custom variable.</param>
        </member>
        <member name="M:Aliyun.OSS.Util.CallbackVariableHeaderBuilder.Build">
            <summary>
            Builds the callback variables' header value
            </summary>
            <returns>The callback variables' header value</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.DateUtils.FormatRfc822Date(System.DateTime)">
            <summary>
            Format an instance of <see cref="T:System.DateTime" /> to a GMT format string.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.DateUtils.ParseRfc822Date(System.String)">
            <summary>
            Format a GMT format string to an instance of <see cref="T:System.DateTime" />.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Aliyun.OSS.Util.DateUtils.FormatIso8601Date(System.DateTime)">
            <summary>
            Format an instance of <see cref="T:System.DateTime" /> to string in iso-8601 format.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.DateUtils.ParseIso8601Date(System.String)">
            <summary>
            Format a iso-8601 format string to an instance of <see cref="T:System.DateTime" />.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Aliyun.OSS.Util.DateUtils.FormatUnixTime(System.DateTime)">
            <summary>
            Format an instance of <see cref="T:System.DateTime" /> to string in unix time format.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Util.OssFunc`2">
            <summary>
            The equvalent delegate of .Net4.0's System.Func. This is to make this code compatible with .Net 2.0
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Util.OssAction">
            <summary>
            The equvalent delegate of .Net 4.0's System.Action.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Util.OssUtils">
            <summary>
            Some common utility methods and constants
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxFileSize">
            <summary>
            Max normal file size: 5G
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxPrefixStringSize">
            <summary>
            Max prefix length
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxMarkerStringSize">
            <summary>
            Marker's max length.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxDelimiterStringSize">
            <summary>
            Max delimiter length.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxReturnedKeys">
            <summary>
            Max keys to return in one call.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.DeleteObjectsUpperLimit">
            <summary>
            Max objects to delete in multiple object deletion call.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.BucketCorsRuleLimit">
            <summary>
            Max CORS rule count per bucket
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.LifecycleRuleLimit">
            <summary>
            Max lifecycle rule count per bucket.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.ObjectNameLengthLimit">
            <summary>
            Max object key's length.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.PartNumberUpperLimit">
            <summary>
            Max part number's upper limit.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize">
            <summary>
            Default part size.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit">
            <summary>
            Minimal part size in multipart upload or copy.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MaxPathLength">
            <summary>
            Max file path length.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Util.OssUtils.MinPathLength">
            <summary>
            Min file path
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.IsBucketNameValid(System.String)">
            <summary>
            Check if the bucket name is valid,.
            </summary>
            <param name="bucketName">bucket name</param>
            <returns>true:valid bucket name</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.IsObjectKeyValid(System.String)">
            <summary>
            validates the object key
            </summary>
            <param name="key">object key</param>
            <returns>true:valid object key</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.IsIp(System.Uri)">
            <summary>
            checks if the endpoint is in IP format.
            </summary>
            <param name="endpoint">endpoint to check</param>
            <returns>true: the endpoint is ip.</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.UrlEncodeKey(System.String)">
            <summary>
            Applies the Url encoding on the key
            </summary>
            <param name="key">the object key to encode</param>
            <returns>The encoded key</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.TrimQuotes(System.String)">
            <summary>
            Trims quotes in the ETag
            </summary>
            <param name="eTag">The Etag to trim</param>
            <returns>The Etag without the quotes</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.ComputeContentMd5(System.IO.Stream,System.Int64)">
            <summary>
            Compute the MD5 on the input stream with the given size.
            </summary>
            <param name="input">The input stream</param>
            <param name="partSize">the part size---it could be less than the stream size</param>
            <returns>MD5 digest value</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.ComputeContentCrc64(System.IO.Stream,System.Int64)">
            <summary>
            Computes the content crc64.
            </summary>
            <returns>The content crc64.</returns>
            <param name="input">Input.</param>
            <param name="length">stream length</param>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.IsWebpageValid(System.String)">
            <summary>
            Checks if the webpage url is valid.
            </summary>
            <param name="webpage">The wenpage url to check</param>
            <returns>true: the url is valid.</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.IsLoggingPrefixValid(System.String)">
            <summary>
            Checks if the logging prefix is valid.
            </summary>
            <param name="loggingPrefix">The logging prefix to check</param>
            <returns>true:valid logging prefix</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.SetupProgressListeners(System.IO.Stream,System.Int64,System.Int64,System.Int64,System.Object,System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <summary>
            Sets up the progress listeners
            </summary>
            <param name="originalStream">The content stream</param>
            <param name="contentLength">The length of originalStream</param>
            <param name="totalBytesRead">The length which has read</param>
            <param name="progressUpdateInterval">The interval at which progress needs to be published</param>
            <param name="sender">The objects which is trigerring the progress changes</param>
            <param name="callback">The callback which will be invoked when the progress changed event is trigerred</param>
            <returns>an <see cref="T:Aliyun.OSS.Common.Internal.EventStream"/> object, incase the progress is setup, else returns the original stream</returns>
        </member>
        <member name="M:Aliyun.OSS.Util.OssUtils.InvokeInBackground``1(System.EventHandler{``0},``0,System.Object)">
            <summary>
            Calls a specific EventHandler in a background thread
            </summary>
            <param name="handler"></param>
            <param name="args"></param>
            <param name="sender"></param>
        </member>
        <member name="M:Aliyun.OSS.Util.Crc64.Combine(System.UInt64,System.UInt64,System.Int64)">
            <summary>
            Return the CRC-64 of two sequential blocks, where summ1 is the CRC-64 of the 
            first block, summ2 is the CRC-64 of the second block, and len2 is the length
            of the second block.
            </summary>
            <returns>The combined crc</returns>
            <param name="crc1">Crc1.</param>
            <param name="crc2">Crc2.</param>
            <param name="len2">Len2.</param>
        </member>
        <member name="T:Aliyun.OSS.RuleStatus">
            <summary>
            Lifecycle rule status
            </summary>
        </member>
        <member name="F:Aliyun.OSS.RuleStatus.Enabled">
            <summary>
            Enable the rule
            </summary>
        </member>
        <member name="F:Aliyun.OSS.RuleStatus.Disabled">
            <summary>
            Disable the rule
            </summary>
        </member>
        <member name="T:Aliyun.OSS.LifecycleRule">
            <summary>
            Lifecycle rule definition class, which represents one rule of Lifecycle
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.ID">
            <summary>
            Gets or sets the rule Id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.Prefix">
            <summary>
            Gets or sets the prefix of the files the rule applied to. 
            If it's null, then the rule is applied to the whole bucket.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.Status">
            <summary>
            The rule status
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.ExpriationDays">
            <summary>
            The expiration days.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.ExpirationTime">
            <summary>
            The expiration time.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.CreatedBeforeDate">
            <summary>
            Gets or sets the created before date.
            </summary>
            <value>The created before date.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.ExpiredObjectDeleteMarker">
            <summary>
            Gets or sets the expired object delete marker.
            </summary>
            <value>The expired object delete marker.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.Transitions">
            <summary>
            Gets or sets the transition.
            </summary>
            <value>The transition.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.AbortMultipartUpload">
            <summary>
            Gets or sets the abort multipart upload.
            </summary>
            <value>The abort multipart upload.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.Tags">
            <summary>
            Gets or sets the object tags.
            </summary>
            <value>The object tags.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.NoncurrentVersionExpiration">
            <summary>
            Gets or sets the noncurrent version expiration.
            </summary>
            <value>The noncurrent version expiration.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.NoncurrentVersionTransitions">
            <summary>
            Gets or sets the noncurrent version transition.
            </summary>
            <value>The noncurrent version transition.</value>
        </member>
        <member name="M:Aliyun.OSS.LifecycleRule.Equals(Aliyun.OSS.LifecycleRule)">
            <summary>
            Determines whether the specified <see cref="T:Aliyun.OSS.LifecycleRule"/> is equal to the current <see cref="T:Aliyun.OSS.LifecycleRule"/>.
            </summary>
            <param name="obj">The <see cref="T:Aliyun.OSS.LifecycleRule"/> to compare with the current <see cref="T:Aliyun.OSS.LifecycleRule"/>.</param>
            <returns><c>true</c> if the specified <see cref="T:Aliyun.OSS.LifecycleRule"/> is equal to the current
            <see cref="T:Aliyun.OSS.LifecycleRule"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Aliyun.OSS.LifecycleRule.Validate">
            <summary>
            Validate this instance.
            </summary>
            <returns>The validate result.</returns>
        </member>
        <member name="T:Aliyun.OSS.LifecycleRule.LifeCycleExpiration">
            <summary>
            Life cycle expiration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleExpiration.Days">
            <summary>
            Gets or sets the days.
            </summary>
            <value>The days.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleExpiration.CreatedBeforeDate">
            <summary>
            Gets or sets the expiration time.
            </summary>
            <value>The expiration time.</value>
        </member>
        <member name="M:Aliyun.OSS.LifecycleRule.LifeCycleExpiration.Validate">
            <summary>
            Validate this instance.
            </summary>
            <returns>The validate result.</returns>
        </member>
        <member name="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition">
            <summary>
            Life cycle transition.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleTransition.LifeCycleExpiration">
            <summary>
            Gets or sets the life cycle expiration.
            </summary>
            <value>The life cycle expiration.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleTransition.StorageClass">
            <summary>
            Gets or sets the storage class.
            </summary>
            <value>The storage class.</value>
        </member>
        <member name="M:Aliyun.OSS.LifecycleRule.LifeCycleTransition.Equals(Aliyun.OSS.LifecycleRule.LifeCycleTransition)">
            <summary>
            Determines whether the specified <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/> is equal to
            the current <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/>.
            </summary>
            <param name="transition">The <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/> to compare with the current <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/>.</param>
            <returns><c>true</c> if the specified <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/> is equal to the
            current <see cref="T:Aliyun.OSS.LifecycleRule.LifeCycleTransition"/>; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Aliyun.OSS.LifecycleRule.LifeCycleNoncurrentVersionExpiration">
            <summary>
            Life cycle noncurrent version expiration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleNoncurrentVersionExpiration.NoncurrentDays">
            <summary>
            Gets or sets the noncurrent days.
            </summary>
            <value>The noncurrent days.</value>
        </member>
        <member name="T:Aliyun.OSS.LifecycleRule.LifeCycleNoncurrentVersionTransition">
            <summary>
            Life cycle noncurrent version transition.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleNoncurrentVersionTransition.NoncurrentDays">
            <summary>
            Gets or sets the noncurrent days.
            </summary>
            <value>The noncurrent days.</value>
        </member>
        <member name="P:Aliyun.OSS.LifecycleRule.LifeCycleNoncurrentVersionTransition.StorageClass">
            <summary>
            Gets or sets the storage class.
            </summary>
            <value>The storage class.</value>
        </member>
        <member name="T:Aliyun.OSS.ListBucketsResult">
            <summary>
            The result class of the operation to list buckets.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.Prefix">
            <summary>
            Gets or sets the bucket name prefix(optional).
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.Marker">
            <summary>
            Gets or sets the bucket name marker.Its value should be same as the ListBucketsRequest.Marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.MaxKeys">
            <summary>
            Gets or sets the max entries to return.
            By default it's 100.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.IsTruncated">
            <summary>
            Gets or sets the flag of truncated. If it's true, means not all buckets have been returned.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.NextMaker">
            <summary>
            Gets the next marker's value. Assign this value to the next call's ListBucketRequest.marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsResult.Buckets">
            <summary>
            Gets the bucket iterator.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ListBucketsRequest">
            <summary>
            The request class of the operation to list <see cref="T:Aliyun.OSS.Bucket" /> of the current account.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsRequest.Prefix">
            <summary>
            Gets or sets the bucket name prefix to list (optional)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsRequest.Marker">
            <summary>
            Gets or sets the marker of the bucket name. The buckets to return whose names are greater than this value in lexicographic order.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsRequest.MaxKeys">
            <summary>
            Gets or sets the max entries to return. By default is 100.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListBucketsRequest.Tag">
            <summary>
            Gets or sets the bucket tag
            </summary>
        </member>
        <member name="T:Aliyun.OSS.MatchMode">
            <summary>
            The match mode enum
            </summary>
        </member>
        <member name="F:Aliyun.OSS.MatchMode.Unknown">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="F:Aliyun.OSS.MatchMode.Exact">
            <summary>
            Exactly match
            </summary>
        </member>
        <member name="F:Aliyun.OSS.MatchMode.StartWith">
            <summary>
            Match the prefix only
            </summary>
        </member>
        <member name="F:Aliyun.OSS.MatchMode.Range">
            <summary>
            Match the size range. For example, the policy could be applied the files of size between 1KB to 4KB.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.TupleType">
            <summary>
            Tuplre type enum.!-- Currently only two tuple {key:value} and three tuple type (tuple1,tuple2,tuple3) are supported.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.AbstractConditionItem">
            <summary>
            The abstract Condition Item.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.EqualConditionItem">
            <summary>
            EqualConditionItem definition
            </summary>
        </member>
        <member name="T:Aliyun.OSS.StartWithConditionItem">
            <summary>
            StartwithConditionItem definition.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.RangeConditionItem">
            <summary>
            Content size's RangeConditionItem definition.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.PolicyConditions">
            <summary>
            Conditions list. It specifies all valid fields in the post form.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondContentLengthRange">
            <summary>
            Content length range
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondCacheControl">
            <summary>
            The cache control behavior for downloading files
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondContentType">
            <summary>
            Content types defined in RFC2616
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondContentDisposition">
            <summary>
            Content disposition behavior 
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondContentEncoding">
            <summary>
            The content encoding
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondExpires">
            <summary>
            Expiration time
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondKey">
            <summary>
            object key
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondSuccessActionRedirect">
            <summary>
            redirect upon success
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondSuccessActionStatus">
            <summary>
            The action status upon success
            </summary>
        </member>
        <member name="F:Aliyun.OSS.PolicyConditions.CondXOssMetaPrefix">
            <summary>
            The custom metadata prefix
            </summary>
        </member>
        <member name="M:Aliyun.OSS.PolicyConditions.AddConditionItem(System.String,System.String)">
            <summary>
            Adds a condition item with exact MatchMode
            </summary>
            <param name="name">Condition name</param>
            <param name="value">Condition value</param>
        </member>
        <member name="M:Aliyun.OSS.PolicyConditions.AddConditionItem(Aliyun.OSS.MatchMode,System.String,System.String)">
            <summary>
            Adds a condition item with specified MatchMode
            </summary>
            <param name="matchMode">Conditions match mode</param>
            <param name="name">Condition name</param>
            <param name="value">Condition value</param>
        </member>
        <member name="M:Aliyun.OSS.PolicyConditions.AddConditionItem(System.String,System.Int64,System.Int64)">
            <summary>
            Adds a condition with range match mode.
            </summary>
            <param name="name">Condition name</param>
            <param name="min">Range's low end</param>
            <param name="max">Range's high end</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketLifecycleRequest">
            <summary>
            The request class of the operation to set the bucket's lifecycle configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketLifecycleRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketLifecycleRequest.LifecycleRules">
            <summary>
            Gets or sets the Lifecycle rule list.Each bucket can have up to 1000 rules.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketLifecycleRequest.#ctor(System.String)">
            <summary>
            Creates a new intance of <see cref="T:Aliyun.OSS.SetBucketLifecycleRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketLifecycleRequest.AddLifecycleRule(Aliyun.OSS.LifecycleRule)">
            <summary>
            Adds a LifeCycle rule
            </summary>
            <param name="lifecycleRule"></param>
        </member>
        <member name="T:Aliyun.OSS.Protocol">
            <summary>
            supported protocol definition. HTTP is the default one.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Protocol.Http">
            <summary>
            HTTP
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Protocol.Https">
            <summary>
            HTTPs
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SetBucketRefererRequest">
            <summary>
            The request of the operation to set bucket referer.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketRefererRequest.BucketName">
            <summary>
            Gets the bucket name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketRefererRequest.AllowEmptyReferer">
            <summary>
            Gets the flag of allowing empty referer.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketRefererRequest.RefererList">
            <summary>
            Gets the referer list.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketRefererRequest.#ctor(System.String)">
            <summary>
            Creates the instance of SetBucketRefererRequest
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketRefererRequest.#ctor(System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates the instance of <see cref="T:Aliyun.OSS.SetBucketRefererRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="refererList">referer list </param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketRefererRequest.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            Creates the instance of <see cref="T:Aliyun.OSS.SetBucketRefererRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="refererList">referer list</param>
            <param name="allowEmptyReferer">allowEmptyReferer flag</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketRefererRequest.ClearRefererList">
            <summary>
            Clears the referer list.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectsRequest">
            <summary>
            The request class of the operation to delete multiple objects in OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsRequest.Quiet">
            <summary>
            Gets quiet mode flag. By default it's true;
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsRequest.Keys">
            <summary>
            Returns the keys list where the caller could add or remove key 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsRequest.EncodingType">
            <summary>
            Gets or sets encoding-type value. By default it's HttpUtils.UrlEncodingType.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectsRequest.#ctor(System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates an instance with bucket name and keys. Quiet mode is true by default.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="keys">object lists to delete</param>
        </member>
        <member name="M:Aliyun.OSS.DeleteObjectsRequest.#ctor(System.String,System.Collections.Generic.IList{System.String},System.Boolean)">
            <summary>
            Creates an instance with bucket name, keys and quiet flag.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="keys">object keys to delete</param>
            <param name="quiet">true: quiet mode; false: detail mode</param>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectsResult">
            <summary>
            Description of DeleteObjectsResult.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsResult.Keys">
            <summary>
            gets or sets deleted keys
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsResult.EncodingType">
            <summary>
            gets or sets EncodingType
            </summary>
        </member>
        <member name="T:Aliyun.OSS.DeleteObjectsResult.DeletedObject">
            <summary>
            Deleted object class. Key is its only property.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DeleteObjectsResult.DeletedObject.Key">
            <summary>
            Gets or sets deleted key
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SetBucketAclRequest">
            <summary>
            The request class of the operation to set the bucket ACL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketAclRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketAclRequest.ACL">
            <summary>
            Gets the ACL
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketAclRequest.#ctor(System.String,Aliyun.OSS.CannedAccessControlList)">
            <summary>
            Creates a instance of <see cref="T:Aliyun.OSS.SetBucketAclRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="acl">user acl</param>
        </member>
        <member name="T:Aliyun.OSS.UploadPartCopyRequest">
            <summary>
            The request class of the operation to upload the source object as a part of the target object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.TargetBucket">
            <summary>
            Gets the target bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.TargetKey">
            <summary>
            Gets the target key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.UploadId">
            <summary>
            Gets the upload Id.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.PartNumber">
            <summary>
            Gets or sets the part number.
            Every part upload will have a part number (from 1 to 10000).
            For a given upload id, the part number is the Id of the part and determine the position of the part in the whole file.
            If the same part number is uploaded with new data, the original data will be overwritten.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.PartSize">
            <summary>
            Gets or sets the part size
            Except the last part, other parts' size should be at least 5MB.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.Md5Digest">
            <summary>
            Gets or sets the MD5 checksum for the part's data.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.SourceKey">
            <summary>
            Gets or sets the source object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.SourceBucket">
            <summary>
            Gets or sets the source bucket
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.BeginIndex">
            <summary>
            Gets or sets the beginning index of the source object to copy from.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.MatchingETagConstraints">
            <summary>
            Gets the constraints of matching ETag. If the source object's ETag matches any one in the list, the copy will be proceeded.
            Otherwise returns error code 412 (precondition failed).
            </summary>        
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.NonmatchingETagConstraints">
            <summary>
            Gets the constraints of non-matching ETag. If the source object's ETag does not match any one in the list, the copy will be proceeded.
            Otherwise returns error code 412 (precondition failed).
            </summary>       
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.UnmodifiedSinceConstraint">
            <summary>
            Gets or sets the constraints of unmodified timestamp threshold. If the value is same or greater than the actual last modified time, proceed the copy.
            Otherwise returns 412 (precondition failed).
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.ModifiedSinceConstraint">
            <summary>
            Gets or sets the constraints of modified timestamp threshold. If the value is smaller than the actual last modified time,  proceed the copy.
            Otherwise returns 412 (precondition failed).
            </summary>   
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="T:Aliyun.OSS.UploadPartCopyResult">
            <summary>
            The result class of the operation to upload a source file as the target object's one part.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.ETag">
            <summary>
            The ETag of the source object
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.PartNumber">
            <summary>
            The part number of the target object
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.Crc64">
            <summary>
            Gets or sets the crc64.
            </summary>
            <value>The crc64.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.Length">
            <summary>
            Gets or sets the length.
            </summary>
            <value>The length.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.PartETag">
            <summary>
            Gets the wrapper class of the part number and ETag.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartCopyResult.CopySourceVersionId">
            <summary>
            Gets or sets the copy source version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.AbortMultipartUploadRequest">
            <summary>
            The request to abort a multipart upload. It specifies all parameters needed for the operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AbortMultipartUploadRequest.BucketName">
            <summary>
            Gets <see cref="T:Aliyun.OSS.OssObject" />'s <see cref="T:Aliyun.OSS.Bucket" /> name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AbortMultipartUploadRequest.Key">
             <summary>
            <see cref="T:Aliyun.OSS.OssObject" /> getter
             </summary>
        </member>
        <member name="P:Aliyun.OSS.AbortMultipartUploadRequest.UploadId">
            <summary>
            UploadId getter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AbortMultipartUploadRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.AbortMultipartUploadRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new intance <see cref="T:Aliyun.OSS.AbortMultipartUploadRequest" /> with bucket name, object key and upload Id.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object name</param>
            <param name="uploadId">Upload Id to cancel. It could be got from<see cref="T:Aliyun.OSS.InitiateMultipartUploadResult"/></param>
        </member>
        <member name="T:Aliyun.OSS.AccessControlList">
            <summary>
            The class defines "Access control list"(ACL).
            It contains a list of <see cref="T:Aliyun.OSS.Grant"/> instances, each specifies a <see cref="T:Aliyun.OSS.IGrantee" /> and
            a <see cref="T:Aliyun.OSS.Permission" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AccessControlList.Grants">
            <summary>
            Gets the iterator of <see cref="T:Aliyun.OSS.Grant" /> list.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AccessControlList.Owner">
            <summary>
            Owner getter and setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.AccessControlList.ACL">
            <summary>
            ACL getter or setter
            </summary>
        </member>
        <member name="M:Aliyun.OSS.AccessControlList.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.AccessControlList.GrantPermission(Aliyun.OSS.IGrantee,Aliyun.OSS.Permission)">
            <summary>
            Grants permission to a <see cref="T:Aliyun.OSS.IGrantee" /> instance with specified <see cref="T:Aliyun.OSS.Permission" />.
            Currently the supported grantee is <see cref="P:Aliyun.OSS.GroupGrantee.AllUsers" />.
            </summary>
            <param name="grantee">The grantee</param>
            <param name="permission">The permission</param>
        </member>
        <member name="M:Aliyun.OSS.AccessControlList.RevokeAllPermissions(Aliyun.OSS.IGrantee)">
            Revoke all permissions on a specific grantee.
            @param grantee
                      The grantee, currently only <see cref="P:Aliyun.OSS.GroupGrantee.AllUsers" /> is supported.
            <summary>
            Invoke the <see cref="T:Aliyun.OSS.IGrantee" /> instance's all permissions.
            </summary>
            <param name="grantee">The grantee instanc</param>
        </member>
        <member name="M:Aliyun.OSS.AccessControlList.ToString">
            <summary>
            Return the string that has the owner and ACL list information.
            </summary>
            <returns>The serialized information in a string</returns>
        </member>
        <member name="T:Aliyun.OSS.Bucket">
            <summary>
            Bucket is the OSS namespace, which could be thought as storage space.
            </summary>
             <remarks>
            <para>
            Bucket is globally unique across the whole OSS and is immutable. Every object must be stored at one and only one bucket.
            An application, such as picture sharing website, could have one or more bucket. And each account could only create up to 10 buckets.
            But in every bucket, there's no limit in terms of data size and object count.
            </para>
            <para>
            Bucket naming rules
            <list type="">
             <item>Can only have lowercase letter, number or dash (-)</item>
             <item>Can only start with lowercase letter or number</item>
             <item>The length must be between 3 and 63</item>
            </list>
            </para>
             </remarks>
        </member>
        <member name="P:Aliyun.OSS.Bucket.Location">
            <summary>
            Bucket location getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Bucket.Name">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Bucket.Owner">
            <summary>
            Bucket <see cref="P:Aliyun.OSS.Bucket.Owner" /> getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Bucket.CreationDate">
            <summary>
            Bucket creation time getter/setter
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Bucket.#ctor(System.String)">
            <summary>
            Creats a new <see cref="T:Aliyun.OSS.Bucket" /> instance with the specified name.
            </summary>
            <param name="name">Bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.Bucket.ToString">
            <summary>
            Returns the bucket's serialization information in string.
            </summary>
            <returns>The serialization information in string</returns>
        </member>
        <member name="T:Aliyun.OSS.BucketLoggingResult">
            <summary>
            The result class of the operation to get bucket logging config
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketLoggingResult.TargetBucket">
            <summary>
            Target bucket.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketLoggingResult.TargetPrefix">
            <summary>
            Target logging file's prefix. If it's empty, the OSS system will name the file instead.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.BucketWebsiteResult">
            <summary>
            The result class of the operation to get bucket's static website config.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketWebsiteResult.IndexDocument">
            <summary>
            The index page for the static website.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketWebsiteResult.ErrorDocument">
            <summary>
            The error page for the static website.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CannedAccessControlList">
            <summary>
            The enum of canned access control list.
            <para>
            This is a quick way to grant permissions to all users
            </para>
            </summary>
        </member>
        <member name="F:Aliyun.OSS.CannedAccessControlList.Private">
            <summary>
            Private read and write.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.CannedAccessControlList.PublicRead">
            <summary>
            Public read, private write.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.CannedAccessControlList.PublicReadWrite">
            <summary>
            public read or write---everyone can read and write the data.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.CannedAccessControlList.Default">
            <summary>
            Default permission, inherits from the bucket.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CompleteMultipartUploadRequest">
            <summary>
            The request class of operation to complete a multipart upload
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.BucketName">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.Key">
            <summary>
            Object key getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.UploadId">
            <summary>
            Upload Id's getter/setter. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.PartETags">
            <summary>
            <see cref="T:Aliyun.OSS.PartETag" /> list getter. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.Metadata">
            <summary>
            <see cref="T:Aliyun.OSS.ObjectMetadata" /> getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CompleteMultipartUploadRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a <see cref="T:Aliyun.OSS.CompleteMultipartUploadRequest" /> instance by bucket name, object key and upload Id.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="uploadId">Upload Id, which is got from <see cref="T:Aliyun.OSS.InitiateMultipartUploadResult"/></param>
        </member>
        <member name="M:Aliyun.OSS.CompleteMultipartUploadRequest.IsNeedResponseStream">
            <summary>
            Flag of containing the http body in the response.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CompleteMultipartUploadRequest.IsCallbackRequest">
            <summary>
            Flag of containing the callback parameters in the request.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CompleteMultipartUploadResult">
            <summary>
            The result class of operation to complete a multipart upload.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadResult.BucketName">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadResult.Key">
            <summary>
            Object key's getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CompleteMultipartUploadResult.Location">
            <summary>
            The new object' URL
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CopyObjectRequest">
            <summary>
            The request class of the operation to copy an existing object to another one. The destination object could be a non-existing or existing object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.SourceBucketName">
            <summary>
            Source bucket name's getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.SourceKey">
            <summary>
            Source object key's getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.DestinationBucketName">
            <summary>
            Destination bucket name's getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.DestinationKey">
            <summary>
            Destination object key's getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.NewObjectMetadata">
            <summary>
            Destination object's metadata getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.MatchingETagConstraints">
            <summary>
            ETag maching contraints---that is for the copy operation to execute, the source object's ETag must match one of the ETags in this property. 
            If not, return 412 as HTTP code (precondition failed)
            </summary>        
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.NonmatchingETagConstraints">
            <summary>
            ETag non-matching contraints---that is for the copy operation to execute, the source object's ETag must not match any of the ETags in this property. 
            If matches any, return 412 as HTTP code (precondition failed)
            </summary>       
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.UnmodifiedSinceConstraint">
            <summary>
            Unmodified timestamp threshold----that is for the copy operation to execute, the file's last modified time must be smaller than this property;
            Otherwise return 412 as HTTP code (precondition failed)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.ModifiedSinceConstraint">
            <summary>
            Modified timestamp threshold----that is for the copy operation to execute, the file's last modified time must be same or greater than this property;
            Otherwise return 412 as HTTP code (precondition failed)
            </summary>   
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectRequest.SourceVersionId">
            <summary>
            Gets or sets the source key version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CopyObjectRequest.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Aliyun.OSS.CopyObjectRequest" /> instance
            </summary>
            <param name="sourceBucketName">source object's bucket name</param>
            <param name="sourceKey">source object key</param>
            <param name="destinationBucketName">destination object's bucket name</param>
            <param name="destinationKey">destination object key</param>
        </member>
        <member name="T:Aliyun.OSS.CopyObjectResult">
            <summary>
            Result class for the copy object operation.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectResult.LastModified">
            <summary>
            Last modified timestamp getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectResult.ETag">
            <summary>
            New object's ETag
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CopyObjectResult.CopySourceVersionId">
            <summary>
            Gets or sets the copy source version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CORSRule">
            <summary>
            Defining a cross origin resource sharing rule
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CORSRule.AllowedOrigins">
            <summary>
            Allowed origins. One origin could contain at most one wildcard (*).
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CORSRule.AllowedMethods">
            <summary>
            Allowed HTTP Method. Valid values are GET,PUT,DELETE,POST,HEAD.
            This property is to specify the value of Access-Control-Allow-Methods header in the preflight response.
            It means the allowed methods in the actual CORS request. 
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CORSRule.AllowedHeaders">
            <summary>
            Get or set Allowed Headers.
            This property is to specify the value of Access-Control-Allowed-Headers in the preflight response.
            It defines the allowed headers in the actual CORS request.
            Each allowed header can have up to one wildcard (*).
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CORSRule.ExposeHeaders">
            <summary>
            Get or set exposed headers in the CORS response. Wildcard(*) is not allowed.
            This property is to specify the value of Access-Control-Expose-Headers in the preflight response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CORSRule.MaxAgeSeconds">
            <summary>
            HTTP Access-Control-Max-Age's getter and setter, in seconds.
            The Access-Control-Max-Age header indicates how long the results of a preflight request (OPTIONS) can be cached in a preflight result cache.
            The max value is 999999999.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.AddAllowedOrigin(System.String)">
            <summary>
            Adds one allowed origin.
            </summary>
            <param name="allowedOrigin">Allowed origin </param>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.AddAllowedMethod(System.String)">
            <summary>
            Adds one allowed HTTP method
            </summary>
            <param name="allowedMethod">allowed http method, such as GET,PUT,DELETE,POST,HEAD</param>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.AddAllowedHeader(System.String)">
            <summary>
            Adds a allowed header.
            </summary>
            <param name="allowedHeader">allowed header</param>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.AddExposeHeader(System.String)">
            <summary>
            adds an expose header.
            </summary>
            <param name="exposedHeader">an expose-header</param>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.CountOfAsterisk(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Gets the wildcard count from the parameter items.
            </summary>
            <param name="items">items to count wildcard from</param>
            <returns>wildcard count</returns>
        </member>
        <member name="M:Aliyun.OSS.CORSRule.InAllowedMethods(System.String)">
            <summary>
            Checks if a method is allowed.
            </summary>
            <param name="allowedMethod">the http method to check</param>
            <returns>True:the method is allowed; False: The method is not allowed</returns>
        </member>
        <member name="T:Aliyun.OSS.GeneratePresignedUriRequest">
            <summary>
            The request class of the operation to sign the URL
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.Method">
            <summary>
            HTTP method getter/setter.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.BucketName">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.Key">
            <summary>
            Object key getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.ContentType">
            <summary>
            Getter/setter of the target file's content-type header.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.ContentMd5">
            <summary>
            Getter/setter of the target file's MD5.
            Note that the setter should only be called by the SDK internally.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.Expiration">
            <summary>
            Getter/setter of the expiration time of the signed URL.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.Process">
            <summary>
            Process getter/setter.
            Process is specific to image files on which a specific operation (such as resize, sharpen,etc ) could be applied.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.Callback">
            <summary>
            Callback getter/setter, encoded in base64
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.CallbackVar">
            <summary>
            Callback parameters, in base64
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.ResponseHeaders">
            <summary>
            ResponseHeaders getter/setter
            Response headers is to ask OSS service to return these headers (and their values) in the response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.UserMetadata">
            <summary>
            Gets or sets the UserMetadata dictionary. 
            The SDK will automatically add the x-oss-meta- as the prefix of the metadata. 
            So the key in this property should not include x-oss-meta- prefix anymore.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GeneratePresignedUriRequest.QueryParams">
            <summary>
            Gets or sets query parameters
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GeneratePresignedUriRequest.AddUserMetadata(System.String,System.String)">
            <summary>
            Add a user metadata
            The metaItem should not start with 'x-oss-meta-'.
            </summary>
            <param name="metaItem">meta name</param>
            <param name="value">value of the metaItem</param>
        </member>
        <member name="M:Aliyun.OSS.GeneratePresignedUriRequest.AddQueryParam(System.String,System.String)">
            <summary>
            Add a query parameter
            </summary>
            <param name="param">param name</param>
            <param name="value">param value</param>
        </member>
        <member name="M:Aliyun.OSS.GeneratePresignedUriRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.GeneratePresignedUriRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="M:Aliyun.OSS.GeneratePresignedUriRequest.#ctor(System.String,System.String,Aliyun.OSS.SignHttpMethod)">
            <summary>
            Creates a <see cref="T:Aliyun.OSS.GeneratePresignedUriRequest" /> instance.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="httpMethod">http method</param>
        </member>
        <member name="T:Aliyun.OSS.GetObjectRequest">
            <summary>
            The request class for getting object from OSS.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.BucketName">
            <summary>
            Gets or sets <see cref="T:Aliyun.OSS.Bucket" /> name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.Key">
            <summary>
            Gets or sets <see cref="T:Aliyun.OSS.OssObject" /> key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.Range">
            <summary>
            Gets <see cref="T:Aliyun.OSS.OssObject" /> range to read
            </summary>
            <remarks>
            Calls <see cref="M:Aliyun.OSS.GetObjectRequest.SetRange(System.Int64,System.Int64)" /> to set. If it's not set, returns null.
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.Process">
            <summary>
            Gets or sets <see cref="T:Aliyun.OSS.OssObject" />'s process method (such as resize, sharpen, etc)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.UnmodifiedSinceConstraint">
            <summary>
            Gets or sets "If-Unmodified-Since" parameter
            </summary>
            <remarks>
            It means if its value is same or later than the actual last modified time, the file will be downloaded. 
            Otherwise, return precondition failed (412).
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.ModifiedSinceConstraint">
            <summary>
            Gets or sets "If-Modified-Since".
            </summary>
            <remarks>
            It means if its value is smaller the actual last modified time, the file will be downloaded. 
            Otherwise, return precondition failed (412).
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.StreamTransferProgress">
            <summary>
            Gets or sets the progress callback
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.MatchingETagConstraints">
            <summary>
            Gets the ETag matching constraint list. If the actual ETag matches any one in the constraint list, the file will be downloaded.
            Otherwise, returns precondition failed.
            The corresponding http header is "If-Match".
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.NonmatchingETagConstraints">
            <summary>
            Gets the ETag non-matching constraint list. If the actual ETag does not match any one in the constraint list, the file will be downloaded.
            Otherwise, returns precondition failed.
            The corresponding http header is "If-None-Match".
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.ResponseHeaders">
            <summary>
            Gets the overrided response headers.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GetObjectRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GetObjectRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.GetObjectRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="M:Aliyun.OSS.GetObjectRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.GetObjectRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key name</param>
            <param name="process">The process method for image file in OSS</param>
        </member>
        <member name="M:Aliyun.OSS.GetObjectRequest.SetRange(System.Int64,System.Int64)">
            <summary>
            Sets the read range of the target object (optional).
            It follows the HTTP header "Range"'s semantic 
            </summary>
            <param name="start">
            The start value in the range.
            <para>
            If the value is non-negative, it means the start index of the object to read. 
            If the value is -1, it means the start index is determined by end parameter and thus the end parameter must not be -1.
            For example, if the end is 100, then the start is bytes=-100 (bytes is the total length of the object). It means to read the last 100 bytes of the object.
            </para>
            </param>
            <param name="end">
            The end value of the range. And it must be smaller than the total length of the object.
            <para>
            If the value is non-negative, it means the end index of the object to read.
            If the value is -1, it means the end is the object's last byte and start must not be -1.
            For example, if the start is 99 and end is -1, it means to read the whole object except the first 99 bytes.
            </para>
            </param>
        </member>
        <member name="M:Aliyun.OSS.GetObjectRequest.Populate(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Populate the http headers according to the properties of this object.
            </summary>
            <param name="headers">The generated http headers</param>
        </member>
        <member name="T:Aliyun.OSS.Grant">
            <summary>
            The access control grant class definition
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Grant.Grantee">
            <summary>
            The grantee instance
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Grant.Permission">
            <summary>
            The granted permission
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Grant.#ctor(Aliyun.OSS.IGrantee,Aliyun.OSS.Permission)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.Grant" />.
            </summary>
            <param name="grantee">the grantee instance----cannot be null</param>
            <param name="permission">the permission instance</param>
        </member>
        <member name="M:Aliyun.OSS.Grant.Equals(System.Object)">
            <summary>
            Checks if two <see cref="T:Aliyun.OSS.Grant" /> instances equal.
            </summary>
            <param name="obj">The other grant instance to compare with</param>
            <returns></returns>
        </member>
        <member name="M:Aliyun.OSS.Grant.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>hash code</returns>
        </member>
        <member name="T:Aliyun.OSS.GroupGrantee">
            <summary>
            It defines a group of user that could be granted with permission.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.GroupGrantee.Identifier">
            <summary>
            The grantee's identifier.
            </summary>
            <remarks>
            Only supports gets operation. Calling the setter will trigger <see cref="T:System.NotSupportedException" />.
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.GroupGrantee.AllUsers">
            <summary>
            AllUsers means the <see cref="T:Aliyun.OSS.Bucket" /> or <see cref="T:Aliyun.OSS.OssObject" /> could be accessed by anonymous users.
            That is all users could access the resource.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.GroupGrantee.#ctor(System.String)">
            <summary>
            Sets the identifier.
            </summary>
            <param name="identifier">the grantee's Id</param>
        </member>
        <member name="M:Aliyun.OSS.GroupGrantee.Equals(System.Object)">
            <summary>
            Checks if two <see cref="T:Aliyun.OSS.GroupGrantee"/> instances equal
            </summary>
            <param name="obj">The other instance to compare with</param>
            <returns></returns>
        </member>
        <member name="M:Aliyun.OSS.GroupGrantee.GetHashCode">
            <summary>
            Gets the hash code.
            </summary>
            <returns>hash code</returns>
        </member>
        <member name="T:Aliyun.OSS.IGrantee">
            <summary>
            The interface for the grantee entity
            </summary>
        </member>
        <member name="P:Aliyun.OSS.IGrantee.Identifier">
            <summary>
            Gets or sets the grantee entity's identifier.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.InitiateMultipartUploadRequest">
            <summary>
            The request class of the operation to initiate a multipart upload
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadRequest.BucketName">
            <summary>
            Gets or sets the bucket name to upload files to.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadRequest.Key">
            <summary>
            Gets or sets the target <see cref="T:Aliyun.OSS.OssObject" /> key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadRequest.EncodingType">
            <summary>
            Gets or sets the encoding-type value
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadRequest.ObjectMetadata">
            <summary>
            Gets or sets <see cref="P:Aliyun.OSS.InitiateMultipartUploadRequest.ObjectMetadata" />
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.InitiateMultipartUploadRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InitiateMultipartUploadRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
        </member>
        <member name="M:Aliyun.OSS.InitiateMultipartUploadRequest.#ctor(System.String,System.String,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.InitiateMultipartUploadRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="objectMetadata">Object's <see cref="P:Aliyun.OSS.InitiateMultipartUploadRequest.ObjectMetadata"/></param>
        </member>
        <member name="T:Aliyun.OSS.InitiateMultipartUploadResult">
            <summary>
            The result class of the operation to initiate a multipart upload.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadResult.BucketName">
            <summary>
            Gets or sets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadResult.Key">
            <summary>
            Gets or sets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.InitiateMultipartUploadResult.UploadId">
            <summary>
            Gets or sets the upload Id
            </summary>
        </member>
        <member name="T:Aliyun.OSS.IOss">
            <summary>
            The Object Storage Service (OSS) entry point interface.
            </summary>
            <remarks>
            <para>
            OSS is the highly scalable, secure, inexpensive and reliable cloud storage service.
            This interface is to access all the functionality OSS provides.
            The same functionality could be done in web console.
            Multimedia sharing web app, network disk, or enterprise data backup app could be easily built based on OSS.
            </para>
            <para>
            OSS website：http://www.aliyun.com/product/oss
            </para>
            </remarks>
        </member>
        <member name="M:Aliyun.OSS.IOss.SwitchCredentials(Aliyun.OSS.Common.Authentication.ICredentials)">
            <summary>
            Switches the user credentials
            </summary>
            <param name="creds">The credential instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetEndpoint(System.Uri)">
            <summary>
            Sets the endpoint
            </summary>
            <param name="endpoint">Endpoint value</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateBucket(System.String)">
            <summary>
            Creates a new bucket
            </summary>
            <param name="bucketName">The bucket name. It must be globably unique.</param>
            <returns><see cref="T:Aliyun.OSS.Bucket" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateBucket(System.String,System.Nullable{Aliyun.OSS.StorageClass})">
            <summary>
            Creates the bucket with specified storage class.
            </summary>
            <returns>The bucket.</returns>
            <param name="bucketName">Bucket name.</param>
            <param name="storageClass">Storage class.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateBucket(Aliyun.OSS.CreateBucketRequest)">
            <summary>
            Creates a bucket
            </summary>
            <returns>The bucket.</returns>
            <param name="createBucketRequest"><see cref="T:Aliyun.OSS.CreateBucketRequest"/></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucket(System.String)">
            <summary>
            Deletes a empty bucket.If the bucket is not empty, this will fail.
            </summary>
            <param name="bucketName">The bucket name to delete</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListBuckets">
            <summary>
            List all buckets under the current account.
            </summary>
            <returns>All <see cref="T:Aliyun.OSS.Bucket" /> instances</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListBuckets(Aliyun.OSS.ListBucketsRequest)">
            <summary>
            Lists all buckets according to the ListBucketsRequest, which could have filters by prefix, marker, etc.
            </summary>
            <param name="listBucketsRequest"><see cref="T:Aliyun.OSS.ListBucketsRequest"/> instance</param>
            <returns><see cref="T:Aliyun.OSS.ListBucketsResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketInfo(System.String)">
            <summary>
            Gets the bucket information.
            </summary>
            <returns>The bucket information.</returns>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketStat(System.String)">
            <summary>
            Gets the bucket stat.
            </summary>
            <returns>The bucket stat.</returns>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketAcl(System.String,Aliyun.OSS.CannedAccessControlList)">
            <summary>
            Sets the bucket ACL
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="acl"><see cref="T:Aliyun.OSS.CannedAccessControlList" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketAcl(Aliyun.OSS.SetBucketAclRequest)">
            <summary>
            Sets the bucket ACL
            </summary>
            <param name="setBucketAclRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketAcl(System.String)">
            <summary>
            Gets the bucket ACL
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns>Bucket ACL<see cref="T:Aliyun.OSS.AccessControlList" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketLocation(System.String)">
            <summary>
            Gets the bucket location
            </summary>
            <param name="bucketName">bucket name</param>
            <returns>bucket location</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketMetadata(System.String)">
            <summary>
            Gets the bucket metadata
            </summary>
            <param name="bucketName">bucket name</param>
            <returns><see cref="T:Aliyun.OSS.BucketMetadata" />metadata</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketCors(Aliyun.OSS.SetBucketCorsRequest)">
            <summary>
            Sets the CORS rules for the <see cref="T:Aliyun.OSS.Bucket" />
            </summary>
            <param name="setBucketCorsRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketCors(System.String)">
            <summary>
            Gets the <see cref="T:Aliyun.OSS.Bucket" /> CORS rules.
            </summary>
            <param name="bucketName">bucket name</param>
            <returns>CORS rules</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketCors(System.String)">
            <summary>
            Deletes the CORS rules on the <see cref="T:Aliyun.OSS.Bucket" />
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketLogging(Aliyun.OSS.SetBucketLoggingRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> logging config
            OSS will log the access information on this bucket, according to the logging config
            The hourly log file will be stored in the target bucket.
            </summary>
            <param name="setBucketLoggingRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketLogging(System.String)">
            <summary>
            Gets the bucket logging config
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns>The logging config result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketLogging(System.String)">
            <summary>
            Deletes the <see cref="T:Aliyun.OSS.Bucket" /> logging config
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketWebsite(Aliyun.OSS.SetBucketWebsiteRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> static website config
            </summary>
            <param name="setBucketWebSiteRequest"><see cref="T:Aliyun.OSS.SetBucketWebsiteRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketWebsite(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> static website config
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.BucketWebsiteResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketWebsite(System.String)">
            <summary>
            Deletes the <see cref="T:Aliyun.OSS.Bucket" /> static website config
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" />的名称。</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketReferer(Aliyun.OSS.SetBucketRefererRequest)">
            <summary>
            Sets the <see cref="T:Aliyun.OSS.Bucket" /> referer config
            </summary>
            <param name="setBucketRefererRequest">The requests that contains the Referer whitelist</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketReferer(System.String)">
            <summary>
            Gets the <see cref="T:Aliyun.OSS.Bucket" /> referer config
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns>Referer config</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketLifecycle(Aliyun.OSS.SetBucketLifecycleRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> lifecycle rule
            </summary>
            <param name="setBucketLifecycleRequest">the <see cref="T:Aliyun.OSS.SetBucketLifecycleRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketLifecycle(System.String)">
            <summary>
            Deletes the bucket's all lifecycle rules.
            </summary>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketLifecycle(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> lifecycle instance. 
            </summary>
            <param name="bucketName">bucket name</param>
            <returns>Lifecycle list</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketStorageCapacity(Aliyun.OSS.SetBucketStorageCapacityRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> storage capacity
            </summary>
            <param name="setBucketStorageCapacityRequest"><see cref="T:Aliyun.OSS.SetBucketStorageCapacityRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketStorageCapacity(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> storage capacity
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketStorageCapacityResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DoesBucketExist(System.String)">
            <summary>
            Checks if the bucket exists
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns>
            True when the bucket exists under the current user;
            Otherwise returns false.
            </returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketPolicy(Aliyun.OSS.SetBucketPolicyRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> policy
            </summary>
            <param name="setBucketPolicyRequest"><see cref="T:Aliyun.OSS.SetBucketPolicyRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketPolicy(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> policy
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketPolicyResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketPolicy(System.String)">
            <summary>
            Deletes <see cref="T:Aliyun.OSS.Bucket" /> policy.
            </summary>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketTagging(Aliyun.OSS.SetBucketTaggingRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> bucket tagging
            </summary>
            <param name="setBucketTaggingRequest"><see cref="T:Aliyun.OSS.SetBucketTaggingRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketTagging(System.String)">
            <summary>
            Deletes the bucket's tagging.
            </summary>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketTagging(Aliyun.OSS.DeleteBucketTaggingRequest)">
            <summary>
            Deletes the bucket's tagging.
            </summary>
            <param name="deleteBucketTaggingRequest">DeleteBucketTaggingRequest.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketTagging(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket tagging
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketTaggingResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketRequestPayment(Aliyun.OSS.SetBucketRequestPaymentRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> bucket request payment
            </summary>
            <param name="setBucketRequestPaymentRequest"><see cref="T:Aliyun.OSS.SetBucketRequestPaymentRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketRequestPayment(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket request payment
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketRequestPaymentResult"/></returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketEncryption(Aliyun.OSS.SetBucketEncryptionRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> bucket encryption rule
            </summary>
            <param name="setBucketEncryptionRequest"><see cref="T:Aliyun.OSS.SetBucketEncryptionRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketEncryption(System.String)">
            <summary>
            Deletes bucket encryption rule
            </summary>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketEncryption(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket encryption rule
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.BucketEncryptionResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketVersioning(Aliyun.OSS.SetBucketVersioningRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> bucket versioning
            </summary>
            <param name="setBucketVersioningRequest"><see cref="T:Aliyun.OSS.SetBucketEncryptionRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketVersioning(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket versioning
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketVersioningResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetBucketInventoryConfiguration(Aliyun.OSS.SetBucketInventoryConfigurationRequest)">
            <summary>
            Sets <see cref="T:Aliyun.OSS.Bucket" /> bucket inventory configuration
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.SetBucketInventoryConfigurationRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteBucketInventoryConfiguration(Aliyun.OSS.DeleteBucketInventoryConfigurationRequest)">
            <summary>
            Deletes <see cref="T:Aliyun.OSS.Bucket" /> bucket inventory configuration
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.DeleteBucketInventoryConfigurationRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketInventoryConfiguration(Aliyun.OSS.GetBucketInventoryConfigurationRequest)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket inventory configuration
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetBucketInventoryConfigurationRequest"/> instance</param>
            <returns><see cref="T:Aliyun.OSS.Model.GetBucketInventoryConfigurationResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListBucketInventoryConfiguration(Aliyun.OSS.ListBucketInventoryConfigurationRequest)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> bucket inventory configuration
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.ListBucketInventoryConfigurationRequest"/> instance</param>
            <returns><see cref="T:Aliyun.OSS.Model.ListBucketInventoryConfigurationResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.InitiateBucketWorm(Aliyun.OSS.InitiateBucketWormRequest)">
            <summary>
            InitiateBucketWorm
            </summary>
            <returns><see cref="T:Aliyun.OSS.InitiateBucketWormResult"/> instance</returns>
            <param name="request"><see cref="T:Aliyun.OSS.InitiateBucketWormRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.AbortBucketWorm(System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Bucket" /> AbortBucketWorm
            </summary>
            <param name="bucketName">Bucket name.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.CompleteBucketWorm(Aliyun.OSS.CompleteBucketWormRequest)">
            <summary>
            CompleteBucketWorm
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.CompleteBucketWormRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.ExtendBucketWorm(Aliyun.OSS.ExtendBucketWormRequest)">
            <summary>
            ExtendBucketWorm
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.ExtendBucketWormRequest"/> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetBucketWorm(System.String)">
            <summary>
            GetBucketWormResult
            </summary>
            <param name="bucketName">Bucket name.</param>
            <returns><see cref="T:Aliyun.OSS.GetBucketWormResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListObjects(System.String)">
            <summary>
            Lists all objects under the <see cref="T:Aliyun.OSS.Bucket" />
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> list</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginListObjects(System.String,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to list objects.The returned object is type of OssObjectSummary.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" />list</returns>
            <param name="callback">callback when the list is done</param>
            <param name="state">state object in the callback</param>
            <returns>IAsyncResult instance.</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListObjects(System.String,System.String)">
            <summary>
            Lists object with specified prefix
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="prefix"><see cref="P:Aliyun.OSS.OssObject.Key" /> prefix</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> instances list</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginListObjects(System.String,System.String,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to list objects under the specified bucket and prefix
            The returned object is type of OssObjectSummary.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="prefix"><see cref="P:Aliyun.OSS.OssObject.Key" /> prefix</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> list</returns>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListObjects(Aliyun.OSS.ListObjectsRequest)">
            <summary>
            Lists objects according to the ListObjectsRequest.
            The returned object is type of OssObjectSummary.
            </summary>
            <param name="listObjectsRequest"><see cref="T:Aliyun.OSS.ListObjectsRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> list</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginListObjects(Aliyun.OSS.ListObjectsRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to list objects under the specified <see cref="T:Aliyun.OSS.Bucket" /> with specified filters in <see cref="T:Aliyun.OSS.ListObjectsRequest" />
            </summary>
            <param name="listObjectsRequest"><see cref="T:Aliyun.OSS.ListObjectsRequest"/> instance</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> list</returns>
            <param name="callback">callback instance</param>
            <param name="state">callback state object</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndListObjects(System.IAsyncResult)">
            <summary>
            Ends the async call of listing objects.
            </summary>
            <param name="asyncResult">The asyncResult instance returned by BeginListObjects call</param>
            <returns><see cref="T:Aliyun.OSS.ObjectListing"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListObjectVersions(Aliyun.OSS.ListObjectVersionsRequest)">
            <summary>
            Lists object vesions according to the ListObjectVersionsRequest.
            The returned object is type of OssObjectSummary.
            </summary>
            <param name="listObjectVersionsRequest"><see cref="T:Aliyun.OSS.ListObjectVersionsRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> list</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.String,System.String,System.IO.Stream)">
            <summary>
            Puts object to the specified bucket with specified object key.
            </summary>
            <param name="bucketName">specified bucket name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" /></param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginPutObject(System.String,System.String,System.IO.Stream,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call of uploading object to specified bucket.
            </summary>
            <param name="bucketName">target <see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" /></param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>The IAsyncResult instance for EndPutObject()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Uploads the content to object under the specified bucket and object key.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" /></param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(Aliyun.OSS.PutObjectRequest)">
            <summary>
            Upload a <see cref="T:Aliyun.OSS.OssObject" /> according to <see cref="T:Aliyun.OSS.PutObjectRequest" />.
            </summary>
            <param name="putObjectRequest"><see cref="T:Aliyun.OSS.PutObjectRequest" />instance</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" />instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginPutObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to upload object
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" /></param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance for EndPutObject()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginPutObject(Aliyun.OSS.PutObjectRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to upload object
            </summary>
            <param name="putObjectRequest"><see cref="T:Aliyun.OSS.PutObjectRequest" /> instance</param>
            <param name="callback">callback object</param>
            <param name="state">state object</param>
            <returns>IAsyncResult instance for EndPutObject()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.String,System.String,System.String)">
            <summary>
            Uploads a local file to OSS under the specified bucket
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="fileToUpload">local file path to upload</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginPutObject(System.String,System.String,System.String,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to upload local file to OSS under the specified bucket.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="fileToUpload">local file path to upload</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAyncResult instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Uploads a local file with specified metadata to OSS.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="fileToUpload">local file path</param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" />metadata</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginPutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to upload object with specified metadata.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="fileToUpload">local file to upload</param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance for EndPutObject</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndPutObject(System.IAsyncResult)">
            <summary>
             Ends the async call to upload the object.
             When it's called, the actual upload has already been done.
            </summary>
            <param name="asyncResult">IAsyncResult instance</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutBigObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64})">
            <summary>
            Deprecated method.Please use ResumableUploadObject.
            Uploads the specified file with optional part size.
            If the file size is not bigger than the part size, then use normal file upload.
            Otherwise use multipart upload.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">target object key</param>
            <param name="fileToUpload">local file path to upload</param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="partSize">Part size.If it's not specified, then use <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/>.
            If the part size is less than <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/>, it will be changed to <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/> automatically.
            </param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutBigObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64})">
            <summary>
            Deprecated method. Use ResumableUploadObject instead.
            Upload the specified file to OSS.
            If the file size is same or less than the part size, use normal file upload instead.
            Otherwise it will use multipart file upload.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" /></param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="partSize">Part size. If it's not specified or the value is less than <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/>, 
            then use <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/> instead.
            </param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.Uri,System.String)">
            <summary>
            Uploads the file via the signed url.
            </summary>
            <param name="signedUrl">Signed url</param>
            <param name="fileToUpload">File to upload</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.Uri,System.IO.Stream)">
            <summary>
            Uploads the instream via the signed url.
            </summary>
            <param name="signedUrl">Signed url</param>
            <param name="content">content stream</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.Uri,System.String,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Uploads the file via the signed url with the metadata.
            </summary>
            <param name="signedUrl">The signed url</param>
            <param name="fileToUpload">Local file path</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.PutObject(System.Uri,System.IO.Stream,Aliyun.OSS.ObjectMetadata)">
            <summary>
            Uploads the stream via the signed url with the metadata.
            </summary>
            <param name="signedUrl">Signed url</param>
            <param name="content">content stream</param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.ResumableUploadObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable{System.Int64},System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <summary>
            Resumable file upload. It automaticlly uses multipart upload upon big file and also support resume upload after a failed upload.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> instance</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /> instance</param>
            <param name="fileToUpload">file to upload</param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="checkpointDir">Check point dir. If it's not specified, then no checkpoint file is saved and thus resumable file upload is not supported.</param>
            <param name="partSize">Part size. If it's not specified, or the size is smaller than <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/>
            then <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/> is used instead.
            </param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance </returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ResumableUploadObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable{System.Int64},System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <summary>
            Resumable file upload. It automaticlly uses multipart upload upon big file and also support resume upload after a failed upload.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="content"><see cref="P:Aliyun.OSS.OssObject.Content" />. Content is disposed after the call finishes.</param>
            <param name="metadata"><see cref="T:Aliyun.OSS.OssObject" /> metadata</param>
            <param name="checkpointDir">Check point dir. If it's not specified, then no checkpoint file is saved and thus resumable file upload is not supported.</param>
            <param name="partSize">Part size. If it's not specified, or the size is smaller than <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/>
            then <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/> is used instead.
            </param>
            <returns><see cref="T:Aliyun.OSS.PutObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ResumableUploadObject(Aliyun.OSS.UploadObjectRequest)">
            <summary>
            Resumables the upload object.
            The request.UploadStream will be disposed once the call finishes.
            </summary>
            <returns>The upload object.</returns>
            <param name="request">Upload Request.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.AppendObject(Aliyun.OSS.AppendObjectRequest)">
            <summary>
            Appends object to OSS according to the <see cref="T:Aliyun.OSS.AppendObjectRequest" />
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.AppendObjectRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.AppendObjectResult" /> result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginAppendObject(Aliyun.OSS.AppendObjectRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to append object to OSS.
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.AppendObjectRequest" /> instance</param>
            <param name="callback">callback instance</param>
            <param name="state">state object</param>
            <returns>IAsyncResut instance for EndAppendObject call</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndAppendObject(System.IAsyncResult)">
            <summary>
            Ends the async call to append object to OSS. WHen it's called, the actual upload has been done.
            </summary>
            <param name="asyncResult">The IAsyncResult instance returned from BeginAppendObjet</param>
            <returns><see cref="T:Aliyun.OSS.AppendObjectResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateSymlink(System.String,System.String,System.String)">
            <summary>
            Creates the symlink of the target object
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="symlink">Symlink.</param>
            <param name="target">Target.</param>
            <returns><see cref="T:Aliyun.OSS.CreateSymlinkResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateSymlink(Aliyun.OSS.CreateSymlinkRequest)">
            <summary>
            Creates the symlink of the target object
            </summary>
            <param name="createSymlinkRequest">Create symlink request.</param>
            <returns><see cref="T:Aliyun.OSS.CreateSymlinkResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetSymlink(System.String,System.String)">
            <summary>
            Gets the target file of the symlink.
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="symlink">Symlink </param>
            <returns>OssSymlink object</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetSymlink(Aliyun.OSS.GetSymlinkRequest)">
            <summary>
            Gets the target file of the symlink.
            </summary>
            <param name="getSymlinkRequest">Get symlink request.</param>
            <returns>OssSymlink object</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObject(System.String,System.String)">
            <summary>
            Gets object
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key"/></param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObject(System.Uri)">
            <summary>
            Gets object via signed url
            </summary>
            <param name="signedUrl">The signed url of HTTP GET method</param>
            <returns><see cref="T:Aliyun.OSS.OssObject"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObject(Aliyun.OSS.GetObjectRequest)">
            <summary>
            Gets object via the bucket name and key name in the <see cref="T:Aliyun.OSS.GetObjectRequest" /> instance.
            </summary>
            <param name="getObjectRequest"> The request parameter</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> instance. The caller needs to dispose the object.</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginGetObject(Aliyun.OSS.GetObjectRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to get object according to the <see cref="T:Aliyun.OSS.GetObjectRequest"/> instance.
            </summary>
            <param name="getObjectRequest"> request parameter</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance for EndGetObject()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginGetObject(System.String,System.String,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to get object by the bucket and key information.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">object key</param>
            <param name="callback">callback instance</param>
            <param name="state">state instance</param>
            <returns>ISyncResult instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndGetObject(System.IAsyncResult)">
            <summary>
            Ends the async call to get the object.
            </summary>
            <param name="asyncResult">The AsyncResult instance returned from BeginGetObject()</param>
            <returns><see cref="T:Aliyun.OSS.OssObject"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObject(Aliyun.OSS.GetObjectRequest,System.IO.Stream)">
            <summary>
            Gets the object and assign the data to the stream.
            </summary>
            <param name="getObjectRequest">request parameter</param>
            <param name="output">output stream</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" /> metadata</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ResumableDownloadObject(Aliyun.OSS.DownloadObjectRequest)">
            <summary>
            Download a file.
            Internally it may use multipart download in case the file is big
            </summary>
            <returns>The metadata object</returns>
            <param name="request">DownloadObjectRequest instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectMetadata(System.String,System.String)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.OssObject" /> metadata.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <returns><see cref="T:Aliyun.OSS.OssObject" />metadata</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectMetadata(Aliyun.OSS.GetObjectMetadataRequest)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.OssObject" /> metadata.
            </summary>
            <param name="request">GetObjectMetadataRequest instance</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" />metadata</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetSimplifiedObjectMetadata(Aliyun.OSS.GetObjectMetadataRequest)">
            <summary>
            Gets <see cref="T:Aliyun.OSS.OssObject" /> metadata.
            </summary>
            <param name="request">GetObjectMetadataRequest instance</param>
            <returns><see cref="T:Aliyun.OSS.OssObject" />metadata</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObject(System.String,System.String)">
            <summary>
            Deletes <see cref="T:Aliyun.OSS.OssObject" />
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <returns><see cref="T:Aliyun.OSS.DeleteObjectResult" />instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObject(Aliyun.OSS.DeleteObjectRequest)">
            <summary>
            Deletes <see cref="T:Aliyun.OSS.OssObject" />
            </summary>
            <param name="deleteObjectRequest">the request parameter</param>
            <returns><see cref="T:Aliyun.OSS.DeleteObjectResult" />instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObjects(Aliyun.OSS.DeleteObjectsRequest)">
            <summary>
            Deletes multiple objects
            </summary>
            <param name="deleteObjectsRequest">the request parameter</param>
            <returns>delete object result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObjectVersions(Aliyun.OSS.DeleteObjectVersionsRequest)">
            <summary>
            Deletes multiple objects with version id
            </summary>
            <param name="deleteObjectVersionsRequest">the request parameter</param>
            <returns>delete object result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CopyObject(Aliyun.OSS.CopyObjectRequest)">
            <summary>
            copy an object to another one in OSS.
            </summary>
            <param name="copyObjectRequst">The request parameter</param>
            <returns>copy object result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginCopyObject(Aliyun.OSS.CopyObjectRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to copy an object
            </summary>
            <param name="copyObjectRequst">the request parameter</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>The IAsyncResult instance for EndCopyObject()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndCopyResult(System.IAsyncResult)">
            <summary>
            Ends the async call to copy an object.
            </summary>
            <param name="asyncResult">The IAsyncResult instance returned from BeginCopyObject()</param>
            <returns><see cref="T:Aliyun.OSS.CopyObjectResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CopyBigObject(Aliyun.OSS.CopyObjectRequest,System.Nullable{System.Int64},System.String)">
            <summary>
            Deprecated. Use ResumableCopyObject instead.
            Copy the specified file with optional checkpoint support.
            </summary>
            <param name="copyObjectRequest">the request parameter</param>
            <param name="partSize">part size. If the part size is not specified, or less than <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/>,
            <see cref="F:Aliyun.OSS.Util.OssUtils.PartSizeLowerLimit"/> will be used instead.
            </param>
            <param name="checkpointDir">The checkpoint file folder. If it's not specified, checkpoint information is not stored and resumnable upload will not be supported in this case.</param>
            <returns><see cref="T:Aliyun.OSS.CopyObjectResult" /> instance.</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ResumableCopyObject(Aliyun.OSS.CopyObjectRequest,System.String,System.Nullable{System.Int64})">
            <summary>
            Resumable object copy.
            If the file size is less than part size, normal file upload is used; otherwise multipart upload is used.
            </summary>
            <param name="copyObjectRequest">request parameter</param>
            <param name="checkpointDir">checkpoint file folder </param>
            <param name="partSize">The part size. 
            </param>
            <returns><see cref="T:Aliyun.OSS.CopyObjectResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ModifyObjectMeta(System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64},System.String)">
            <summary>
            Modify the object metadata. 
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <param name="newMeta">new metadata</param>
            <param name="checkpointDir">check point folder. It must be specified to store the checkpoint information</param>
            <param name="partSize">Part size, it's no less than <see cref="F:Aliyun.OSS.Util.OssUtils.DefaultPartSize"/>
            </param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DoesObjectExist(System.String,System.String)">
            <summary>
            Checks if the object exists
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <returns>true:object exists;false:otherwise</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetObjectAcl(System.String,System.String,Aliyun.OSS.CannedAccessControlList)">
            <summary>
            Sets the object ACL
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /> key</param>
            <param name="acl"><see cref="T:Aliyun.OSS.CannedAccessControlList" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetObjectAcl(Aliyun.OSS.SetObjectAclRequest)">
            <summary>
            Sets the object ACL
            </summary>
            <param name="setObjectAclRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectAcl(System.String,System.String)">
            <summary>
            Gets the object ACL 
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <returns><see cref="T:Aliyun.OSS.AccessControlList" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectAcl(Aliyun.OSS.GetObjectAclRequest)">
            <summary>
            Gets the object ACL
            </summary>
            <param name="getObjectAclRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.RestoreObject(System.String,System.String)">
            <summary>
            Restores the object.
            </summary>
            <returns>The object.</returns>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.RestoreObject(Aliyun.OSS.RestoreObjectRequest)">
            <summary>
            Restores the object.
            </summary>
            <returns>The object.</returns>
            <param name="restoreObjectRequest"></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetObjectTagging(Aliyun.OSS.SetObjectTaggingRequest)">
            <summary>
            Sets the object tagging
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.SetObjectTaggingRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectTagging(System.String,System.String)">
            <summary>
            Gets the object tagging 
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
            <returns><see cref="T:Aliyun.OSS.GetObjectTaggingResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetObjectTagging(Aliyun.OSS.GetObjectTaggingRequest)">
            <summary>
            Gets the object tagging
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetObjectTaggingRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.GetObjectTaggingResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObjectTagging(System.String,System.String)">
            <summary>
            Deletes object tagging
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
            <param name="key"><see cref="P:Aliyun.OSS.OssObject.Key" /></param>
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteObjectTagging(Aliyun.OSS.DeleteObjectTaggingRequest)">
            <summary>
            Deletes the object tagging
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.DeleteObjectTaggingRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SelectObject(Aliyun.OSS.SelectObjectRequest)">
            <summary>
            Gets the contents of a object based on a SQL statement. 
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.SelectObjectRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateSelectObjectMeta(Aliyun.OSS.CreateSelectObjectMetaRequest)">
            <summary>
            Creates the meta of a select object
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.CreateSelectObjectMetaRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.ProcessObject(Aliyun.OSS.ProcessObjectRequest)">
            <summary>
            Processes the object
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.ProcessObjectRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.ProcessObjectRequest" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePresignedUri(Aliyun.OSS.GeneratePresignedUriRequest)">
            <summary>
            Generates a signed url
            </summary>
            <param name="generatePresignedUriRequest">request parameter</param>
            <returns>The signed url. The user could use this url to access the object directly</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePresignedUri(System.String,System.String)">
            <summary>
            Generates the signed url with default expiration time (15 min) that supports HTTP GET method.
            </summary>
            <param name="bucketName">Bucket name</param>
            <param name="key">Object key</param>
            <returns>Signed uri</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePresignedUri(System.String,System.String,System.DateTime)">
            <summary>
            Generates the pre-signed GET url with specified expiration time
            </summary>
            <param name="bucketName">Bucket name</param>
            <param name="key">Object key</param>
            <param name="expiration">Uri expiration time</param>
            <returns>signed url</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePresignedUri(System.String,System.String,Aliyun.OSS.SignHttpMethod)">
            <summary>
            Generates the pre-signed url with specified expiration time that supports the specified HTTP method
            </summary>
            <param name="bucketName">Bucket name</param>
            <param name="key">Object key</param>
            <param name="method">HTTP method</param>
            <returns>signed url</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePresignedUri(System.String,System.String,System.DateTime,Aliyun.OSS.SignHttpMethod)">
            <summary>
            Generates the presigned url with specified method and specified expiration time.
            </summary>
            <param name="bucketName">Bucket name</param>
            <param name="key">Object key</param>
            <param name="expiration">Uri expiration time</param>
            <param name="method">HTTP method</param>
            <returns>signed url</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.GeneratePostPolicy(System.DateTime,Aliyun.OSS.PolicyConditions)">
            <summary>
            Generates the post policy
            </summary>
            <param name="expiration">policy expiration time</param>
            <param name="conds">policy conditions</param>
            <returns>policy string</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListMultipartUploads(Aliyun.OSS.ListMultipartUploadsRequest)">
            <summary>
            Lists ongoing multipart uploads 
            </summary>
            <param name="listMultipartUploadsRequest">request parameter</param>
            <returns><see cref="T:Aliyun.OSS.MultipartUploadListing" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.InitiateMultipartUpload(Aliyun.OSS.InitiateMultipartUploadRequest)">
            <summary>
            Initiate a multipart upload
            </summary>
            <param name="initiateMultipartUploadRequest">request parameter</param>
            <returns><see cref="T:Aliyun.OSS.InitiateMultipartUploadResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.AbortMultipartUpload(Aliyun.OSS.AbortMultipartUploadRequest)">
            <summary>
            Aborts a multipart upload
            </summary>
            <param name="abortMultipartUploadRequest">request parameter</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.UploadPart(Aliyun.OSS.UploadPartRequest)">
            <summary>
            Uploads a part
            </summary>
            <param name="uploadPartRequest">request parameter</param>
            <returns><see cref="T:Aliyun.OSS.UploadPartResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginUploadPart(Aliyun.OSS.UploadPartRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to upload a part
            </summary>
            <param name="uploadPartRequest">request parameter</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance for EndUploadPart()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndUploadPart(System.IAsyncResult)">
            <summary>
            Ends the async call to upload a part.
            </summary>
            <param name="asyncResult">IAsyncResult instance returned from BeginUploadPart()</param>
            <returns><see cref="T:Aliyun.OSS.UploadPartResult" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.UploadPartCopy(Aliyun.OSS.UploadPartCopyRequest)">
            <summary>
            Copy an existing object as one part of a multipart upload.
            </summary>
            <param name="uploadPartCopyRequest">request parameter</param>
            <returns><see cref="T:Aliyun.OSS.UploadPartCopyResult"/> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.BeginUploadPartCopy(Aliyun.OSS.UploadPartCopyRequest,System.AsyncCallback,System.Object)">
            <summary>
            Begins the async call to copy an existing object as one part of a multipart upload.
            </summary>
            <param name="uploadPartCopyRequest">request parameter</param>
            <param name="callback">callback instance</param>
            <param name="state">callback state</param>
            <returns>IAsyncResult instance for EndUploadPartCopy()</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.EndUploadPartCopy(System.IAsyncResult)">
            <summary>
            Ends the async call to copy an existing object as one part of a multipart upload.
            </summary>
            <param name="asyncResult">IAsyncResult instance</param>
            <returns>The upload result</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.ListParts(Aliyun.OSS.ListPartsRequest)">
            <summary>
            Lists successfully uploaded parts of a specific upload id
            </summary>
            <param name="listPartsRequest">request parameter</param>
            <returns><see cref="T:Aliyun.OSS.PartListing" /> instance</returns>
        </member>
        <member name="M:Aliyun.OSS.IOss.CompleteMultipartUpload(Aliyun.OSS.CompleteMultipartUploadRequest)">
            <summary>
            Completes a multipart upload. 
            </summary>
            <param name="completeMultipartUploadRequest">the request parameter</param>
            <returns><see cref="T:Aliyun.OSS.CompleteMultipartUploadResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.CreateLiveChannel(Aliyun.OSS.CreateLiveChannelRequest)">
            <summary>
            Creates a live channel
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.CreateLiveChannelRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.CreateLiveChannelResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.ListLiveChannel(Aliyun.OSS.ListLiveChannelRequest)">
            <summary>
            Lists live channels
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.ListLiveChannelRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.ListLiveChannelResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.DeleteLiveChannel(Aliyun.OSS.DeleteLiveChannelRequest)">
            <summary>
            Deletes a live channel
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.DeleteLiveChannelRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.SetLiveChannelStatus(Aliyun.OSS.SetLiveChannelStatusRequest)">
            <summary>
            Sets the live channel status
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.SetLiveChannelStatusRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetLiveChannelInfo(Aliyun.OSS.GetLiveChannelInfoRequest)">
            <summary>
            Gets the live channel information
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetLiveChannelInfoRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.GetLiveChannelInfoResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.GetLiveChannelStat(Aliyun.OSS.GetLiveChannelStatRequest)">
            <summary>
            Gets the live channel status
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetLiveChannelStatRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.GetLiveChannelStatResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.GetLiveChannelHistory(Aliyun.OSS.GetLiveChannelHistoryRequest)">
            <summary>
            Gets the live channel history
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetLiveChannelHistoryRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.GetLiveChannelHistoryResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.PostVodPlaylist(Aliyun.OSS.PostVodPlaylistRequest)">
            <summary>
            Creates a vod playlist
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.PostVodPlaylistRequest" /> instance</param>
        </member>
        <member name="M:Aliyun.OSS.IOss.GetVodPlaylist(Aliyun.OSS.GetVodPlaylistRequest)">
            <summary>
            Gets a vod playlist
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GetVodPlaylistRequest" /> instance</param>
            <returns><see cref="T:Aliyun.OSS.GetVodPlaylistResult" /> instance</returns>        
        </member>
        <member name="M:Aliyun.OSS.IOss.GenerateRtmpPresignedUri(Aliyun.OSS.GenerateRtmpPresignedUriRequest)">
            <summary>
            Generates a rtmp signed url
            </summary>
            <param name="request"><see cref="T:Aliyun.OSS.GenerateRtmpPresignedUriRequest" /> instance</param>
            <returns>The signed url. The user could use this url to push stream directly</returns>
        </member>
        <member name="T:Aliyun.OSS.ListMultipartUploadsRequest">
            <summary>
            The request class of the operation to list ongoing multipart uploads.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.BucketName">
            <summary>
            Gets the bucket name that these multipart uploads belong to.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.Delimiter">
            <summary>
            Gets or sets the delimiter for grouping the result.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.MaxUploads">
            <summary>
            Gets or sets the max entries to list.
            By default it's 1000. The max value is 1000.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.KeyMarker">
            <summary>
            Gets or sets the key marker.
            The key marker and upload id marker filter the multipart uploads to return.
            If the upload-id-marker is not set, then the returned uploads whose target object name are greater than key-marker.
            If the uploader-id-marker is set, then beside the target object's requirement above, the returned uploads Ids must be greater than the upliad-id-marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.Prefix">
            <summary>
            Gets or sets the target object's prefix of these multipart uploads.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.UploadIdMarker">
            <summary>
            Gets or sets upload-id-marker.
            The key marker and upload id marker filter the multipart uploads to return.
            If the key-marker is not set, the upload-id-marker is ignored by OSS.
            If the key marker is set, then:
                All target objects' name must be greater than key-marker value in lexicographic order.
                And all the Upload IDs returned must be greater than upload-id-marker.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.EncodingType">
            <summary>
            Gets or sets encoding-type value.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListMultipartUploadsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListMultipartUploadsRequest.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.ListMultipartUploadsRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="T:Aliyun.OSS.ListObjectsRequest">
            <summary>
            The request class of the operation to list objects' summary(<see cref="T:Aliyun.OSS.OssObjectSummary" />)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.BucketName">
            <summary>
            Gets or sets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.Prefix">
            <summary>
            Gets or sets the object name prefix. The names of the returned object must be prefixed by this value.
            It's optional. If it's not set, then there's no requirement on the object name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.Marker">
            <summary>
            Gets or sets the marker value. The name of returned objects must be greater than this value in lexicographic order.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.MaxKeys">
            <summary>
            Gets or sets the max entries to return.
            By default it's 100.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.Delimiter">
            <summary>
            Gets or sets the delimiter for grouping the returned objects based on their keys.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.EncodingType">
            <summary>
            Gets or sets encoding-type.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListObjectsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListObjectsRequest.#ctor(System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.ListObjectsRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="T:Aliyun.OSS.ListPartsRequest">
            <summary>
            The request class of operation to list parts of a ongoing multipart upload.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.Key">
            <summary>
            Gets or sets the target object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.MaxParts">
            <summary>
            Gets or sets the max parts to return.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.PartNumberMarker">
            <summary>
            Gets or sets the part number marker. It will only list the parts whose numbers are greater than the property.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.EncodingType">
            <summary>
            Gets encoding-type.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.UploadId">
            <summary>
            Gets UploadId.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ListPartsRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ListPartsRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.ListPartsRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="key">target object key</param>
            <param name="uploadId">upload Id</param>
        </member>
        <member name="T:Aliyun.OSS.RefererConfiguration">
            <summary>
            Referer Configuration
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RefererConfiguration.AllowEmptyReferer">
            <summary>
            Flag of allowing empty referer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RefererConfiguration.RefererList">
            <summary>
            Gets or sets the referer list
            </summary>
        </member>
        <member name="T:Aliyun.OSS.RefererConfiguration.RefererListModel">
            <summary>
            referer list model
            </summary>
        </member>
        <member name="P:Aliyun.OSS.RefererConfiguration.RefererListModel.Referers">
            <summary>
            referer list
            </summary>
        </member>
        <member name="T:Aliyun.OSS.MultipartUpload">
            <summary>
            The mutipart upload class definition.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUpload.Key">
            <summary>
            Gets or sets the target object's key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUpload.UploadId">
            <summary>
            Gets or sets the upload Id.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUpload.StorageClass">
            <summary>
            Gets or sets the target object's storage class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUpload.Initiated">
            <summary>
            The initiated timestamp of the multipart upload.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.MultipartUpload.ToString">
            <summary>
            Gets the serialization string
            </summary>
            <returns>the serilization string</returns>
        </member>
        <member name="T:Aliyun.OSS.MultipartUploadListing">
            <summary>
            The result class of the operation to list ongoing multipart uploads.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.BucketName">
            <summary>
            bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.KeyMarker">
            <summary>
            The key marker from <see cref="P:ListMultipartUploadsRequest.KeyMarker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.Delimiter">
            <summary>
            The delimiter from <see cref="P:ListMultipartUploadsRequest.Delimiter" />
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.Prefix">
            <summary>
            The prefix from <see cref="P:ListMultipartUploadsRequest.Prefix" />
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.UploadIdMarker">
            <summary>
            The upload Id marker from <see cref="P:ListMultipartUploadsRequest.UploadIdMarker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.MaxUploads">
            <summary>
            The max upload count from <see cref="P:ListMultipartUploadsRequest.MaxUploads" />
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.IsTruncated">
            <summary>
            The flag which indciates if there's more data to return in OSS server side.
            “true” means there's more data to return.
            “false” means no more data to return.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.NextKeyMarker">
            <summary>
            Gets the next key marker value. If the IsTruncated is true, this could be the next list call's KeyMarker value.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.NextUploadIdMarker">
            <summary>
            Gets the next upload id marker value. If the IsTruncated is true, this value could be the next list call's UploadIdMarker value.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.MultipartUploads">
            <summary>
            The iterator of all multipart upload returned.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.MultipartUploadListing.CommonPrefixes">
            <summary>
            Gets all the common prefixes (which could be thought as virtual 'folder').
            </summary>
        </member>
        <member name="M:Aliyun.OSS.MultipartUploadListing.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.MultipartUploadListing" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.MultipartUploadListing.AddMultipartUpload(Aliyun.OSS.MultipartUpload)">
            <summary>
            Adds a <see cref="T:Aliyun.OSS.MultipartUpload"/> instance---internal only.
            </summary>
            <param name="multipartUpload">a multipart upload instance</param>
        </member>
        <member name="M:Aliyun.OSS.MultipartUploadListing.AddCommonPrefix(System.String)">
            <summary>
            Adds a prefix---internal only.
            </summary>
            <param name="prefix">The prefix</param>
        </member>
        <member name="T:Aliyun.OSS.ObjectListing">
            <summary>
            The result class of the operation to list objects.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.BucketName">
            <summary>
            Gets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.NextMarker">
            <summary>
            Gets the next maker value for the value of <see cref="P:ListObjectRequest.Marker" /> in the next call.
            If the result is not truncated, this value is null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.IsTrunked">
            <summary>
            Obsolete property.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.IsTruncated">
            <summary>
            Flag of truncated result.
            True: the result is truncated (there's more data to list).
            False: no more data in server side to return.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.Marker">
            <summary>
            The object key's marker. The value comes from <see cref="P:ListObjectRequest.Marker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.MaxKeys">
            <summary>
            The max keys to list. The value comes from <see cref="P:ListObjectRequest.MaxKeys" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.Prefix">
            <summary>
            The object key's prefix. The value comes from <see cref="P:ListObjectRequest.Prefix" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.Delimiter">
            <summary>
            The delimiter for grouping object. The value comes from <see cref="P:ListObjectRequest.Delimiter" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.ObjectSummaries">
            <summary>
            The iterator of <see cref="T:Aliyun.OSS.OssObjectSummary" /> that meet the requirements in the ListOjectRequest.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ObjectListing.CommonPrefixes">
            <summary>
            The common prefixes in the result. The objects returned do not include the objects under these common prefixes (folders).
            </summary>
        </member>
        <member name="M:Aliyun.OSS.ObjectListing.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.ObjectListing" />.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.Bucket" /> name</param>
        </member>
        <member name="T:Aliyun.OSS.OssClient">
            <summary>
            The OSS's access entry point interface's implementation.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.OssClient" /> with OSS endpoint, access key Id, access key secret (cound be found from web console).
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">OSS access key Id</param>
            <param name="accessKeySecret">OSS key secret</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,System.String,System.String,System.String)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.OssClient" /> with OSS endpoint, access key Id, access key secret (cound be found from web console) and STS token.
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">STS's temp access key Id</param>
            <param name="accessKeySecret">STS's temp access key secret</param>
            <param name="securityToken">STS security token</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.OssClient" /> with OSS endpoint, access key Id, access key secret and client configuration. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">OSS access key Id</param>
            <param name="accessKeySecret">OSS access key secret</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,System.String,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance of <see cref="T:Aliyun.OSS.OssClient" /> with OSS endpoint, access key Id, access key secret (cound be found from web console) and STS token.
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">STS's temp access key Id</param>
            <param name="accessKeySecret">STS's temp access key secret</param>
            <param name="securityToken">STS security token</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,Aliyun.OSS.Common.Authentication.ICredentialsProvider)">
            <summary>
            Creates an instance with specified credential information.
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="credsProvider">Credentials information</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.String,Aliyun.OSS.Common.Authentication.ICredentialsProvider,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance with specified credential information and client side configuration.
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="credsProvider">Credentials provider</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,System.String,System.String)">
            <summary>
            Creates an instance with specified endpoint, access key Id and access key secret. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">OSS access key Id</param>
            <param name="accessKeySecret">OSS access key secret</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,System.String,System.String,System.String)">
            <summary>
            Creates an instance with specified endpoint, access key Id and access key secret and STS token. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">STS access key Id</param>
            <param name="accessKeySecret">STS security token</param>
            <param name="securityToken">STS security token</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance with specified endpoint, access key Id and access key secret and configuration. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">OSS access key id</param>
            <param name="accessKeySecret">OSS access key secret</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,System.String,System.String,System.String,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance with specified endpoint, access key Id, access key secret, STS security token and configuration. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="accessKeyId">STS access key</param>
            <param name="accessKeySecret">STS access key secret</param>
            <param name="securityToken">STS security token</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,Aliyun.OSS.Common.Authentication.ICredentialsProvider)">
            <summary>
            Creates an instance with specified endpoint and credential information. 
            </summary>
            <param name="endpoint">OSS的访问地址。</param>
            <param name="credsProvider">Credentials提供者。</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.#ctor(System.Uri,Aliyun.OSS.Common.Authentication.ICredentialsProvider,Aliyun.OSS.Common.ClientConfiguration)">
            <summary>
            Creates an instance with specified endpoint, credential information and credential information. 
            </summary>
            <param name="endpoint">OSS endpoint</param>
            <param name="credsProvider">Credentials information</param>
            <param name="configuration">client side configuration</param>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SwitchCredentials(Aliyun.OSS.Common.Authentication.ICredentials)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetEndpoint(System.Uri)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateBucket(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateBucket(System.String,System.Nullable{Aliyun.OSS.StorageClass})">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateBucket(Aliyun.OSS.CreateBucketRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucket(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListBuckets">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListBuckets(Aliyun.OSS.ListBucketsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketInfo(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketStat(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketAcl(System.String,Aliyun.OSS.CannedAccessControlList)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketAcl(Aliyun.OSS.SetBucketAclRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketAcl(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketLocation(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketMetadata(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketCors(Aliyun.OSS.SetBucketCorsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketCors(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketCors(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketLogging(Aliyun.OSS.SetBucketLoggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketLogging(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketLogging(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketWebsite(Aliyun.OSS.SetBucketWebsiteRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketWebsite(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketWebsite(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketReferer(Aliyun.OSS.SetBucketRefererRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketReferer(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketLifecycle(Aliyun.OSS.SetBucketLifecycleRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketLifecycle(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketLifecycle(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketStorageCapacity(Aliyun.OSS.SetBucketStorageCapacityRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketStorageCapacity(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketTagging(Aliyun.OSS.SetBucketTaggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketTagging(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketTagging(Aliyun.OSS.DeleteBucketTaggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketTagging(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DoesBucketExist(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketPolicy(Aliyun.OSS.SetBucketPolicyRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketPolicy(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketPolicy(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketRequestPayment(Aliyun.OSS.SetBucketRequestPaymentRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketRequestPayment(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketEncryption(Aliyun.OSS.SetBucketEncryptionRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketEncryption(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketEncryption(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketVersioning(Aliyun.OSS.SetBucketVersioningRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketVersioning(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetBucketInventoryConfiguration(Aliyun.OSS.SetBucketInventoryConfigurationRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteBucketInventoryConfiguration(Aliyun.OSS.DeleteBucketInventoryConfigurationRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketInventoryConfiguration(Aliyun.OSS.GetBucketInventoryConfigurationRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListBucketInventoryConfiguration(Aliyun.OSS.ListBucketInventoryConfigurationRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.InitiateBucketWorm(Aliyun.OSS.InitiateBucketWormRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.AbortBucketWorm(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CompleteBucketWorm(Aliyun.OSS.CompleteBucketWormRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ExtendBucketWorm(Aliyun.OSS.ExtendBucketWormRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetBucketWorm(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListObjects(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginListObjects(System.String,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListObjects(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginListObjects(System.String,System.String,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListObjects(Aliyun.OSS.ListObjectsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginListObjects(Aliyun.OSS.ListObjectsRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndListObjects(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.String,System.String,System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginPutObject(System.String,System.String,System.IO.Stream,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginPutObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(Aliyun.OSS.PutObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginPutObject(Aliyun.OSS.PutObjectRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.String,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginPutObject(System.String,System.String,System.String,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginPutObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndPutObject(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.Uri,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.Uri,System.String,Aliyun.OSS.ObjectMetadata)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.Uri,System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutObject(System.Uri,System.IO.Stream,Aliyun.OSS.ObjectMetadata)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutBigObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64})">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PutBigObject(System.String,System.String,System.IO.Stream,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64})">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ResumableUploadObject(System.String,System.String,System.String,Aliyun.OSS.ObjectMetadata,System.String,System.Nullable{System.Int64},System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.AppendObject(Aliyun.OSS.AppendObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginAppendObject(Aliyun.OSS.AppendObjectRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndAppendObject(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateSymlink(System.String,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateSymlink(Aliyun.OSS.CreateSymlinkRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetSymlink(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetSymlink(Aliyun.OSS.GetSymlinkRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObject(System.Uri)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObject(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginGetObject(System.String,System.String,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObject(Aliyun.OSS.GetObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginGetObject(Aliyun.OSS.GetObjectRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndGetObject(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObject(Aliyun.OSS.GetObjectRequest,System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObjectMetadata(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObjectMetadata(Aliyun.OSS.GetObjectMetadataRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetSimplifiedObjectMetadata(Aliyun.OSS.GetObjectMetadataRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ResumableDownloadObject(Aliyun.OSS.DownloadObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObject(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObject(Aliyun.OSS.DeleteObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObjects(Aliyun.OSS.DeleteObjectsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObjectVersions(Aliyun.OSS.DeleteObjectVersionsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CopyObject(Aliyun.OSS.CopyObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginCopyObject(Aliyun.OSS.CopyObjectRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndCopyResult(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CopyBigObject(Aliyun.OSS.CopyObjectRequest,System.Nullable{System.Int64},System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ResumableCopyObject(Aliyun.OSS.CopyObjectRequest,System.String,System.Nullable{System.Int64})">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ModifyObjectMeta(System.String,System.String,Aliyun.OSS.ObjectMetadata,System.Nullable{System.Int64},System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DoesObjectExist(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetObjectAcl(System.String,System.String,Aliyun.OSS.CannedAccessControlList)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetObjectAcl(Aliyun.OSS.SetObjectAclRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObjectAcl(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.RestoreObject(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.RestoreObject(Aliyun.OSS.RestoreObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetObjectTagging(Aliyun.OSS.SetObjectTaggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObjectTagging(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteObjectTagging(Aliyun.OSS.DeleteObjectTaggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObjectTagging(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetObjectTagging(Aliyun.OSS.GetObjectTaggingRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SelectObject(Aliyun.OSS.SelectObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateSelectObjectMeta(Aliyun.OSS.CreateSelectObjectMetaRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ProcessObject(Aliyun.OSS.ProcessObjectRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePresignedUri(System.String,System.String)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePresignedUri(System.String,System.String,System.DateTime)">
            <inheritdoc/> 
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePresignedUri(System.String,System.String,Aliyun.OSS.SignHttpMethod)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePresignedUri(System.String,System.String,System.DateTime,Aliyun.OSS.SignHttpMethod)">
            <inheritdoc/>  
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePresignedUri(Aliyun.OSS.GeneratePresignedUriRequest)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.GeneratePostPolicy(System.DateTime,Aliyun.OSS.PolicyConditions)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListMultipartUploads(Aliyun.OSS.ListMultipartUploadsRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.InitiateMultipartUpload(Aliyun.OSS.InitiateMultipartUploadRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.AbortMultipartUpload(Aliyun.OSS.AbortMultipartUploadRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.UploadPart(Aliyun.OSS.UploadPartRequest)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginUploadPart(Aliyun.OSS.UploadPartRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndUploadPart(System.IAsyncResult)">
            <inheritdoc/>        
        </member>
        <member name="M:Aliyun.OSS.OssClient.UploadPartCopy(Aliyun.OSS.UploadPartCopyRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.BeginUploadPartCopy(Aliyun.OSS.UploadPartCopyRequest,System.AsyncCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.EndUploadPartCopy(System.IAsyncResult)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListParts(Aliyun.OSS.ListPartsRequest)">
            <inheritdoc/>                
        </member>
        <member name="M:Aliyun.OSS.OssClient.CompleteMultipartUpload(Aliyun.OSS.CompleteMultipartUploadRequest)">
            <inheritdoc/>                
        </member>
        <member name="M:Aliyun.OSS.OssClient.CreateLiveChannel(Aliyun.OSS.CreateLiveChannelRequest)">
            <inheritdoc/>                
        </member>
        <member name="M:Aliyun.OSS.OssClient.ListLiveChannel(Aliyun.OSS.ListLiveChannelRequest)">
            <inheritdoc/>                
        </member>
        <member name="M:Aliyun.OSS.OssClient.DeleteLiveChannel(Aliyun.OSS.DeleteLiveChannelRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.SetLiveChannelStatus(Aliyun.OSS.SetLiveChannelStatusRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetLiveChannelInfo(Aliyun.OSS.GetLiveChannelInfoRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetLiveChannelStat(Aliyun.OSS.GetLiveChannelStatRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetLiveChannelHistory(Aliyun.OSS.GetLiveChannelHistoryRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.PostVodPlaylist(Aliyun.OSS.PostVodPlaylistRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GetVodPlaylist(Aliyun.OSS.GetVodPlaylistRequest)">
            <inheritdoc/>
        </member>
        <member name="M:Aliyun.OSS.OssClient.GenerateRtmpPresignedUri(Aliyun.OSS.GenerateRtmpPresignedUriRequest)">
            <inheritdoc/>        
        </member>
        <member name="T:Aliyun.OSS.OssObject">
            <summary>
            Base class for OSS's object.
            </summary>
            <remarks>
            <para>
            In OSS, every file is an object.
            Object consists of key, data and metadata. Key is the object name which must be unique under the bucket.
            Data is the object's content. And user metadata is the key-value pair collection that has the object's additional description.
            </para>
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.OssObject.Key">
            <summary>
            Gets or sets object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObject.BucketName">
            <summary>
            Gets or sets object's bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObject.Metadata">
            <summary>
            Gets or sets object's metadata.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObject.Content">
            <summary>
            Gets or sets object's content stream.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.OssObject.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.OssObject" />---internal only.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.OssObject.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.OssObject" /> with the key name.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.OssObjectSummary">
            <summary>
            <see cref="T:Aliyun.OSS.OssObject" />'s summary information, no object data.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.BucketName">
            <summary>
            Gets or sets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.Key">
            <summary>
            Gets or sets the object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.ETag">
            <summary>
            Gets or sets the ETag which is the MD5 summry in hex string of the object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.Size">
            <summary>
            Gets or sets the size of the object in bytes.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.LastModified">
            <summary>
            Gets the last modified time.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.StorageClass">
            <summary>
            Gets the object's storage class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssObjectSummary.Owner">
            <summary>
            Get's the object's <see cref="P:Aliyun.OSS.OssObjectSummary.Owner" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.OssObjectSummary.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.OssObjectSummary" />.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.OssObjectSummary.ToString">
            <summary>
            Gets the serialization result in string.
            </summary>
            <returns>serialization result in string</returns>
        </member>
        <member name="T:Aliyun.OSS.Domain.OssResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Domain.OssResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Domain.OssResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Domain.OssResources.BucketNameInvalid">
             <summary>
               Looks up a localized string similar to Invalid bucket name. The bucket naming rules:
            1) Can only contain lowercase letter, number or dash(-);
            2) Starts and ends with lowercase letter or number;
            3) The length must be between 3 to 63 bytes..
             </summary>
        </member>
        <member name="P:Aliyun.OSS.Domain.OssResources.EndpointNotSupportedProtocal">
            <summary>
              Looks up a localized string similar to Not supported protocol in the endpoint. The supported protocols are HTTP or HTTPS. So the endpoint must start with &quot;http://&quot; or &quot;https://&quot;..
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Domain.OssResources.ObjectKeyInvalid">
            <summary>
              Looks up a localized string similar to Invalid Object Key. Its length must be between 1 to 1023..
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Owner">
            <summary>
            The owner of the OSS object.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Owner.Id">
            <summary>
            Gets or sets the owner Id.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Owner.DisplayName">
            <summary>
            Gets or sets the owner name.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Owner.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.Owner" />---internal only,.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Owner.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.Owner" /> with owner id and name.
            </summary>
            <param name="id">Owner id.</param>
            <param name="displayName">Owner display name</param>
        </member>
        <member name="M:Aliyun.OSS.Owner.ToString">
            <summary>
            Gets <see cref="T:Aliyun.OSS.Owner"/> serialization result in string.
            </summary>
            <returns><see cref="T:Aliyun.OSS.Owner"/> serialization result in string</returns>
        </member>
        <member name="T:Aliyun.OSS.Part">
            <summary>
            The part's summary information in a multipart upload. It does not have the actual content data.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Part.PartNumber">
            <summary>
            Parts number.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Part.LastModified">
            <summary>
            Part's last updated time (typically it's just the upload time)
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Part.ETag">
            <summary>
            The Etag of the part content.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Part.Size">
            <summary>
            Size of the part content, in bytes.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.Part.ToString">
            <summary>
            The serialization string
            </summary>
            <returns>the serialization string</returns>
        </member>
        <member name="P:Aliyun.OSS.Part.PartETag">
            <summary>
            Gets the <see cref="P:Aliyun.OSS.Part.PartETag" /> instance which consists of the part number and the ETag.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.PartETag">
            <summary>
            The class consists of part ETag and Part number. It's used in the request to complete the multipart upload.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartETag.PartNumber">
            <summary>
            Gets or sets the part number.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartETag.ETag">
            <summary>
            Gets or sets the ETag, which is the 128 bit MD5 digest in hex string.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartETag.Crc64">
            <summary>
            Gets or sets the crc64.
            </summary>
            <value>The crc64.</value>
        </member>
        <member name="P:Aliyun.OSS.PartETag.Length">
            <summary>
            Gets or sets the length.
            </summary>
            <value>The length.</value>
        </member>
        <member name="M:Aliyun.OSS.PartETag.#ctor(System.Int32,System.String,System.String,System.Int64)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.PartETag" />.
            </summary>
            <param name="partNumber">Part number</param>
            <param name="eTag">Etag</param>
            <param name="crc64">crc64</param>
            <param name="length">length</param>
        </member>
        <member name="T:Aliyun.OSS.PartListing">
            <summary>
            The result class of the operation to list the parts of a multipart upload.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.BucketName">
            <summary>
            Gets bucket name.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.Key">
            <summary>
            Gets target object key.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.UploadId">
            <summary>
            Gets the value from <see cref="P:ListPartsRequest.UploadId" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.PartNumberMarker">
            <summary>
            Gets the value from <see cref="P:ListPartsRequest.PartNumberMarker" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.NextPartNumberMarker">
            <summary>
            If the result does not have all data, the response will have the value of this property for the next call to start with
            That is assign this value to the PartNumberMarker property in the next call.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.MaxParts">
            <summary>
            The max parts to return. The value comes from <see cref="P:ListPartsRequest.MaxParts" />.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.IsTruncated">
            <summary>
            Flag if the result is truncated.
            “true” means it's truncated;“false” means the result is complete.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PartListing.Parts">
            <summary>
            Gets the parts iterator.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.PartListing.AddPart(Aliyun.OSS.Part)">
            <summary>
            Adds a <see cref="T:Aliyun.OSS.Part"/> information---internal only
            </summary>
            <param name="part">one part instance</param>
        </member>
        <member name="M:Aliyun.OSS.PartListing.#ctor">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.PartListing" />---internal only.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Permission">
            <summary>
            Permission enum definition
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Permission.Read">
            <summary>
            read only
            </summary>
        </member>
        <member name="F:Aliyun.OSS.Permission.FullControl">
            <summary>
            ful control
            </summary>
        </member>
        <member name="T:Aliyun.OSS.PutObjectResult">
            <summary>
            The request class of the operatoin to upload an object
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectResult.ETag">
            <summary>
            Gets or sets the Etag.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.PutObjectResult.VersionId">
            <summary>
            Gets or sets the version id.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.ResponseHeaderOverrides">
            <summary>
            The class to contains the headers the caller hopes to get from the OSS response.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.ContentType">
            <summary>
            Gets or sets content-type. If it's not specified, returns null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.ContentLanguage">
            <summary>
            Gets or sets content-language.If it's not specified, returns null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.Expires">
            <summary>
            Gets or sets the expires header. If it's not specified, returns null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.CacheControl">
            <summary>
            Gets or sets the cache-control header.If it's not specified, returns null.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.ContentDisposition">
            <summary>
            Gets or sets the Content-Disposition header.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.ResponseHeaderOverrides.ContentEncoding">
            <summary>
            Gets or sets the Content-Encoding header.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.SetBucketCorsRequest">
            <summary>
            The request class of the operation to set the bucket CORS
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketCorsRequest.BucketName">
            <summary>
            Gets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketCorsRequest.CORSRules">
            <summary>
            Gets or sets the CORS list. Each bucket can have up to 10 rules.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketCorsRequest.#ctor(System.String)">
            <summary>
            Creates a new instance of<see cref="T:Aliyun.OSS.SetBucketCorsRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketCorsRequest.AddCORSRule(Aliyun.OSS.CORSRule)">
            <summary>
            Add a CORRule instance.
            </summary>
            <param name="corsRule"></param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketLoggingRequest">
            <summary>
            The request class of the operation to set bucket logging configuration.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketLoggingRequest.BucketName">
            <summary>
            Gets bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketLoggingRequest.TargetBucket">
            <summary>
            Gets the target bucket name of the logging file
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketLoggingRequest.TargetPrefix">
            <summary>
            Gets the target prefix.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketLoggingRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketLoggingRequest" />.
            </summary>
            <param name="bucketName">bucket name</param>
            <param name="targetBucket">target bucket</param>
            <param name="targetPrefix">target prefix</param>
        </member>
        <member name="T:Aliyun.OSS.SetBucketWebsiteRequest">
            <summary>
            The request class of the operation to set the bucket static website configuration
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketWebsiteRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketWebsiteRequest.IndexDocument">
            <summary>
            Index page
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketWebsiteRequest.ErrorDocument">
            <summary>
            Error page
            </summary>
        </member>
        <member name="P:Aliyun.OSS.SetBucketWebsiteRequest.Configuration">
            <summary>
            Website configuration in xml format
            </summary>
        </member>
        <member name="M:Aliyun.OSS.SetBucketWebsiteRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketWebsiteRequest" />.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.OssObject" />bucket name</param>
            <param name="indexDocument">index page</param>
            <param name="errorDocument">error page</param>
        </member>
        <member name="M:Aliyun.OSS.SetBucketWebsiteRequest.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Aliyun.OSS.SetBucketWebsiteRequest" />.
            </summary>
            <param name="bucketName"><see cref="T:Aliyun.OSS.OssObject" />bucket name</param>
            <param name="configuration">website configuration in xml format</param>
        </member>
        <member name="T:Aliyun.OSS.SignHttpMethod">
            <summary>
            Sign HTTP method enum definition
            </summary>
        </member>
        <member name="F:Aliyun.OSS.SignHttpMethod.Get">
            <summary>
            Represents HTTP GET. Default value.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.SignHttpMethod.Delete">
            <summary>
            Represents HTTP DELETE.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.SignHttpMethod.Head">
            <summary>
            Represents HTTP HEAD.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.SignHttpMethod.Post">
            <summary>
            Represents HTTP POST.
            </summary>
        </member>
        <member name="F:Aliyun.OSS.SignHttpMethod.Put">
            <summary>
            Represents HTTP PUT.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.UploadPartRequest">
            <summary>
            The request class of the operation to upload part
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.BucketName">
            <summary>
            Gets the bucket name
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.Key">
            <summary>
            Gets the object key
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.UploadId">
            <summary>
            Gets the upload Id
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.PartNumber">
            <summary>
            Gets the part number which is between 1 to 10000.
            Each part has the Part number as its Id and for a given upload Id, the part number determine the part's position in the whole file.
            If there's another part upload with the same part number under the same upload Id, the existing data will be overwritten.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.PartSize">
            <summary>
            Gets or sets the part size.
            Except the last part, all other parts size are at least 5MB.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.Md5Digest">
            <summary>
            Gets or sets the part data's MD5.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.InputStream">
            <summary>
            Gets or sets the part's input stream.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.StreamTransferProgress">
            <summary>
            Gets or sets the progress callback.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="T:Aliyun.OSS.UploadPartResult">
            <summary>
            The result class of the operation to upload part.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartResult.ETag">
            <summary>
            Gets or sets Object ETag
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartResult.PartNumber">
            <summary>
            Gets or sets Part number
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadPartResult.Crc64">
            <summary>
            Gets or sets the crc64.
            </summary>
            <value>The crc64.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadPartResult.Length">
            <summary>
            Gets or sets the length.
            </summary>
            <value>The length.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadPartResult.PartETag">
            <summary>
            Gets the PartEtag instance which consists of a part number and the part's ETag
            </summary>
        </member>
        <member name="T:Aliyun.OSS.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.ExceptionEndOperationHasBeenCalled">
            <summary>
              Looks up a localized string similar to The EndOperation has been called on this asyncResult..
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.ExceptionIfArgumentStringIsNullOrEmpty">
            <summary>
              Looks up a localized string similar to The parameter is empty or null..
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.ExceptionInvalidResponse">
            <summary>
              Looks up a localized string similar to The response is invalid or unable to parse..
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.ExceptionUnknownError">
            <summary>
              Looks up a localized string similar to Unknown error is returned from Server..
            </summary>
        </member>
        <member name="P:Aliyun.OSS.Properties.Resources.MimeData">
             <summary>
               Looks up a localized string similar to xlsx    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
            xltx    application/vnd.openxmlformats-officedocument.spreadsheetml.template
            potx    application/vnd.openxmlformats-officedocument.presentationml.template
            ppsx    application/vnd.openxmlformats-officedocument.presentationml.slideshow
            pptx    application/vnd.openxmlformats-officedocument.presentationml.presentation
            sldx    application/vnd.openxmlformats-officedocument.presentationml.slide
            docx    application/vnd.openxmlformats-of [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="T:Aliyun.OSS.StorageClass">
            <summary>
            Storage class of OSS Bucket
            </summary>
        </member>
        <member name="T:Aliyun.OSS.BucketInfo">
            <summary>
            The bucket information class
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketInfo.Bucket">
            this is to map the XML structure like below: 
            <BucketInfo>
                <Bucket>
                     ..
                </Bucket>
            </BucketInfo>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.Location">
            <summary>
            Bucket location getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.Name">
            <summary>
            Bucket name getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.Owner">
            <summary>
            Bucket <see cref="P:Aliyun.OSS.BucketInfoEntry.Owner" /> getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.CreationDate">
            <summary>
            Bucket creation time getter/setter
            </summary>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.ExtranetEndpoint">
            <summary>
            Gets or sets the extranet endpoint.
            </summary>
            <value>The extranet endpoint.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.IntranetEndpoint">
            <summary>
            Gets or sets the intranet endpoint.
            </summary>
            <value>The intranet endpoint.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.StorageClass">
            <summary>
            Gets or sets the storage class.
            </summary>
            <value>The storage class.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.AccessControlList">
            <summary>
            Gets or sets the access control list.
            </summary>
            <value>The access control list.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.DataRedundancyType">
            <summary>
            Gets or sets the disaster recovery.
            </summary>
            <value>The access control list.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.ServerSideEncryptionRule">
            <summary>
            Gets or sets server-side encryption rule.
            </summary>
            <value>The access control list.</value>
        </member>
        <member name="P:Aliyun.OSS.BucketInfoEntry.Versioning">
            <summary>
            Gets or sets versioning status.
            </summary>
            <value>bucket versioning status.</value>
        </member>
        <member name="M:Aliyun.OSS.BucketInfoEntry.#ctor(System.String)">
            <summary>
            Creats a new <see cref="T:Aliyun.OSS.Bucket" /> instance with the specified name.
            </summary>
            <param name="name">Bucket name</param>
        </member>
        <member name="M:Aliyun.OSS.BucketInfoEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Aliyun.OSS.Bucket"/> class.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.BucketInfoEntry.ToString">
            <summary>
            Returns the bucket's serialization information in string.
            </summary>
            <returns>The serialization information in string</returns>
        </member>
        <member name="T:Aliyun.OSS.BucketStat">
            <summary>
            Bucket state.
            </summary>
        </member>
        <member name="T:Aliyun.OSS.CreateSymlinkRequest">
            <summary>
            Create symlink request.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.CreateSymlinkRequest.ObjectMetadata">
            <summary>
            Gets or sets the object metadata of the Symlink (not the target object).
            </summary>
            <value>The object metadata.</value>
        </member>
        <member name="P:Aliyun.OSS.CreateSymlinkRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="T:Aliyun.OSS.OssSymlink">
            <summary>
            The result object of GetSymlink
            </summary>
        </member>
        <member name="P:Aliyun.OSS.OssSymlink.ObjectMetadata">
            <summary>
            Gets or sets the symlink's metadata.
            </summary>
            <value>The symlink's metadata.</value>
        </member>
        <member name="P:Aliyun.OSS.OssSymlink.Target">
            <summary>
            Gets or sets the target.
            </summary>
            <value>The target.</value>
        </member>
        <member name="P:Aliyun.OSS.OssSymlink.Symlink">
            <summary>
            Gets or sets the symlink.
            </summary>
            <value>The symlink.</value>
        </member>
        <member name="M:Aliyun.OSS.ResumableUploadManager.DoResumableUploadFileMultiThread(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.FileStream,System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <summary>
            Do the resumable upload with multithread from file stream.
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
            <param name="resumableContext">Resumable context.</param>
            <param name="fs">Fs.</param>
            <param name="uploadProgressCallback">Upload progress callback.</param>
        </member>
        <member name="M:Aliyun.OSS.ResumableUploadManager.DoResumableUploadPreReadMultiThread(System.String,System.String,Aliyun.OSS.ResumableContext,System.IO.Stream,System.EventHandler{Aliyun.OSS.StreamTransferProgressArgs})">
            <summary>
            Do the resumable upload with multithread from non file stream
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
            <param name="resumableContext">Resumable context.</param>
            <param name="fs">Fs.</param>
            <param name="uploadProgressCallback">Upload progress callback.</param>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.BucketName">
            <summary>
            Gets or sets the name of the bucket.
            </summary>
            <value>The name of the bucket.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.Key">
            <summary>
            Gets or sets the key.
            </summary>
            <value>The key.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.DownloadFile">
            <summary>
            Gets or sets the download file.
            </summary>
            <value>The download file.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.PartSize">
            <summary>
            Gets or sets the size of the part.
            </summary>
            <value>The size of the part.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.ParallelThreadCount">
            <summary>
            Gets or sets the parallel thread count.
            </summary>
            <value>The parallel thread count.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.CheckpointDir">
            <summary>
            Gets or sets the checkpoint file.
            </summary>
            <value>The checkpoint file.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.UnmodifiedSinceConstraint">
            <summary>
            Gets or sets "If-Unmodified-Since" parameter
            </summary>
            <remarks>
            It means if its value is same or later than the actual last modified time, the file will be downloaded. 
            Otherwise, return precondition failed (412).
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.ModifiedSinceConstraint">
            <summary>
            Gets or sets "If-Modified-Since".
            </summary>
            <remarks>
            It means if its value is smaller the actual last modified time, the file will be downloaded. 
            Otherwise, return precondition failed (412).
            </remarks>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.StreamTransferProgress">
            <summary>
            Gets or sets the stream transfer progress.
            </summary>
            <value>The stream transfer progress.</value>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.MatchingETagConstraints">
            <summary>
            Gets the ETag matching constraint list. If the actual ETag matches any one in the constraint list, the file will be downloaded.
            Otherwise, returns precondition failed.
            The corresponding http header is "If-Match".
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.NonmatchingETagConstraints">
            <summary>
            Gets the ETag non-matching constraint list. If the actual ETag does not match any one in the constraint list, the file will be downloaded.
            Otherwise, returns precondition failed.
            The corresponding http header is "If-None-Match".
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.ResponseHeaders">
            <summary>
            Gets the overrided response headers.
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
        <member name="P:Aliyun.OSS.DownloadObjectRequest.VersionId">
            <summary>
            Gets or sets the version id
            </summary>
        </member>
        <member name="M:Aliyun.OSS.DownloadObjectRequest.Populate(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Populate the http headers according to the properties of this object.
            </summary>
            <param name="headers">The generated http headers</param>
        </member>
        <member name="T:Aliyun.OSS.UploadObjectRequest">
            <summary>
            Upload object request.
            </summary>
        </member>
        <member name="M:Aliyun.OSS.UploadObjectRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Aliyun.OSS.UploadObjectRequest"/> class.
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
            <param name="uploadFile">Upload file.</param>
        </member>
        <member name="M:Aliyun.OSS.UploadObjectRequest.#ctor(System.String,System.String,System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Aliyun.OSS.UploadObjectRequest"/> class.
            </summary>
            <param name="bucketName">Bucket name.</param>
            <param name="key">Key.</param>
            <param name="uploadStream">Upload stream.</param>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.BucketName">
            <summary>
            Gets or sets the name of the bucket.
            </summary>
            <value>The name of the bucket.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.Key">
            <summary>
            Gets or sets the key.
            </summary>
            <value>The key.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.UploadFile">
            <summary>
            Gets or sets the upload file.
            </summary>
            <value>The upload file.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.UploadStream">
            <summary>
            Gets or sets the upload stream.
            Note: when both UploadStream and UploadFile properties are set, the UploadStream will be used.
            It will be disposed once the ResumableUploadFile finishes.
            </summary>
            <value>The upload stream.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.PartSize">
            <summary>
            Gets or sets the size of the part.
            </summary>
            <value>The size of the part.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.ParallelThreadCount">
            <summary>
            Gets or sets the parallel thread count.
            </summary>
            <value>The parallel thread count.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.CheckpointDir">
            <summary>
            Gets or sets the checkpoint dir.
            </summary>
            <value>The checkpoint dir.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.StreamTransferProgress">
            <summary>
            Gets or sets the stream transfer progress.
            </summary>
            <value>The stream transfer progress.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.Metadata">
            <summary>
            Gets or sets the metadata.
            </summary>
            <value>The metadata.</value>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.RequestPayer">
            <summary>
            Gets or sets the reqeust payer
            </summary>
        </member>
        <member name="P:Aliyun.OSS.UploadObjectRequest.TrafficLimit">
            <summary>
            Gets or sets the traffic limit, the unit is bit/s
            </summary>
        </member>
    </members>
</doc>
