﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;
using CompressDataSet;

namespace ToolCommon
{
    public class DataCommon
    {

        public static DataSet GetDataSetByByte(byte[] bty)
        {
            DataSet ds = null;
            try
            {
                byte[] buffer = new CompressionHelper().DecompressToBytes(bty);
                BinaryFormatter ser = new BinaryFormatter();
                DataSetSurrogate dss = ser.Deserialize(new MemoryStream(buffer)) as DataSetSurrogate;
                ds = dss.ConvertToDataSet();
            }
            catch
            {
            }
            return ds;
        }

        public static byte[] GetByteByDataSet(DataSet dsTemp)
        {
            DataSetSurrogate dss = new DataSetSurrogate(dsTemp);
            BinaryFormatter ser = new BinaryFormatter();
            MemoryStream ms = new MemoryStream();
            ser.Serialize(ms, dss);
            byte[] buffer = ms.ToArray();
            byte[] zipBuffer = new CompressionHelper(CompressionLevel.BestSpeed).CompressToBytes(buffer);
            return zipBuffer;
        }
    }
}
