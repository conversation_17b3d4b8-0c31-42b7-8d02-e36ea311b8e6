﻿2025-05-10 09:56:40,934 [40] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 09:56:40
2025-05-10 11:14:40,361 [10] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 11:14:40
2025-05-10 11:48:18,090 [8] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多 ---> System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResult(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean ensureIsUpToDate)
   在 System.Web.UI.BaseTemplateParser.GetReferencedType(VirtualPath virtualPath, Boolean allowNoCompile)
   在 System.Web.UI.PageParser.ProcessMainDirectiveAttribute(String deviceName, String name, String value, IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessMainDirective(IDictionary mainDirective)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:48:25,347 [8] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多 ---> System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResult(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean ensureIsUpToDate)
   在 System.Web.UI.BaseTemplateParser.GetReferencedType(VirtualPath virtualPath, Boolean allowNoCompile)
   在 System.Web.UI.PageParser.ProcessMainDirectiveAttribute(String deviceName, String name, String value, IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessMainDirective(IDictionary mainDirective)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:49:18,980 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 11:49:18
2025-05-10 11:49:24,373 [8] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): 标记包含重复的“rel”特性。 ---> System.Web.HttpParseException (0x80004005): 标记包含重复的“rel”特性。 ---> System.Web.HttpException (0x80004005): 标记包含重复的“rel”特性。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:50:39,136 [12] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多 ---> System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResult(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean ensureIsUpToDate)
   在 System.Web.UI.BaseTemplateParser.GetReferencedType(VirtualPath virtualPath, Boolean allowNoCompile)
   在 System.Web.UI.PageParser.ProcessMainDirectiveAttribute(String deviceName, String name, String value, IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessMainDirective(IDictionary mainDirective)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:51:14,377 [18] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多 ---> System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Site.Master(45): error CS1012: 字符文本中的字符太多
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResult(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean ensureIsUpToDate)
   在 System.Web.UI.BaseTemplateParser.GetReferencedType(VirtualPath virtualPath, Boolean allowNoCompile)
   在 System.Web.UI.PageParser.ProcessMainDirectiveAttribute(String deviceName, String name, String value, IDictionary parseData)
   在 System.Web.UI.TemplateParser.ProcessMainDirective(IDictionary mainDirective)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:51:23,075 [10] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpParseException (0x80004005): 标记包含重复的“rel”特性。 ---> System.Web.HttpParseException (0x80004005): 标记包含重复的“rel”特性。 ---> System.Web.HttpException (0x80004005): 标记包含重复的“rel”特性。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ProcessBeginTag(Match match, String inputText)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 11:56:25,926 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 11:56:25
2025-05-10 11:57:26,267 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 11:57:26
2025-05-10 12:09:31,516 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:09:39,392 [107] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:09:49,393 [107] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:10:00,498 [107] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:12:11,901 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:12:21,494 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/or/Default.aspx,lang:or,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:12:39,454 [107] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Default.aspx,lang:ja,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:12:49,156 [105] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Default.aspx,lang:ja,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:12:58,958 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Default.aspx,lang:ja,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:13:08,990 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Default.aspx,lang:ja,newUrl:http://localhost:19225/Default.aspx
2025-05-10 12:14:55,673 [107] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Detail.aspx,lang:ja,newUrl:http://localhost:19225/Detail.aspx
2025-05-10 12:14:55,915 [104] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Product.aspx,lang:ja,newUrl:http://localhost:19225/Product.aspx
2025-05-10 12:15:04,762 [105] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/Ocr.aspx?type=index,lang:ja,newUrl:http://localhost:19225/default.aspx
2025-05-10 12:15:05,337 [117] ERROR CommonLog 
 - oldUrl:http://localhost:19225/ja/ocr/index.html,lang:ja,newUrl:http://localhost:19225/ocr/index.html
2025-05-10 14:01:38,447 [106] ERROR Application_Error 
 -  URL:http://localhost:19225/Default.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Default.aspx(332): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 14:01:39,527 [106] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:no-cache
HTTP_CONNECTION:keep-alive
HTTP_PRAGMA:no-cache
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9,en;q=0.8
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:none
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: no-cache
Connection: keep-alive
Pragma: no-cache
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
Cookie: lang=zh-Hans
Host: localhost:19225
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: none
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Default.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Default.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->63986|REQUEST_METHOD->GET|SCRIPT_NAME->/Default.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Default.aspx|HTTP_CACHE_CONTROL->no-cache|HTTP_CONNECTION->keep-alive|HTTP_PRAGMA->no-cache|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9,en;q=0.8|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->none|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Default.aspx
Header:Cache-Control->no-cache|Connection->keep-alive|Pragma->no-cache|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9,en;q=0.8|Cookie->lang=zh-Hans|Host->localhost:19225|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->none|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document

2025-05-10 14:01:39,722 [106] ERROR CommonLog 
 - IIS异常:/Default.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Default.aspx
Method: GET
Referer: 
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 16:32:47,333 [9] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 16:32:47
2025-05-10 17:19:02,451 [111] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(160): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:19:49,974 [29] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(148): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:20:27,355 [31] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(163): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:20:42,777 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(153): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:20:57,963 [29] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(153): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:23:41,145 [35] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(160): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:26:08,262 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(160): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:29:23,832 [53] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(160): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:34:48,859 [29] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 17:34:48
2025-05-10 17:40:17,296 [29] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(207): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:44:07,123 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(267): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 17:51:24,226 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/desc.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Desc.aspx(160): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 18:15:45,228 [31] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 18:15:45
2025-05-10 18:34:39,873 [29] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 18:34:39
2025-05-10 18:37:45,428 [31] ERROR Application_Error 
 -  URL:http://localhost:19225/Status.aspx
System.Web.HttpParseException (0x80004005): 服务器块的格式不正确。 ---> System.Web.HttpException (0x80004005): 服务器块的格式不正确。
   在 System.Web.UI.TemplateParser.ProcessError(String message)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 18:38:04,059 [29] ERROR Application_Error 
 -  URL:http://localhost:19225/Server.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Server.aspx(702): error CS1010: 常量中有换行符
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 18:39:44,778 [34] ERROR Application_Error 
 -  URL:http://localhost:19225/Server.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Server.aspx(495): error CS0128: 已在此范围定义了名为“lstCode”的局部变量
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-05-10 22:07:21,868 [31] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 22:07:21
2025-05-10 22:12:11,681 [52] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:no-cache
HTTP_CONNECTION:keep-alive
HTTP_PRAGMA:no-cache
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Cache-Control: no-cache
Connection: keep-alive
Pragma: no-cache
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53711|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CACHE_CONTROL->no-cache|HTTP_CONNECTION->keep-alive|HTTP_PRAGMA->no-cache|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Cache-Control->no-cache|Connection->keep-alive|Pragma->no-cache|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:12:11,838 [52] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:12:19,031 [108] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Connection: keep-alive
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53708|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Connection->keep-alive|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:12:19,033 [108] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:12:33,024 [108] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Connection: keep-alive
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53708|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Connection->keep-alive|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:12:33,026 [108] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:13:45,827 [74] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:no-cache
HTTP_CONNECTION:keep-alive
HTTP_PRAGMA:no-cache
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Cache-Control: no-cache
Connection: keep-alive
Pragma: no-cache
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53706|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CACHE_CONTROL->no-cache|HTTP_CONNECTION->keep-alive|HTTP_PRAGMA->no-cache|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Cache-Control->no-cache|Connection->keep-alive|Pragma->no-cache|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:13:45,842 [74] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:14:21,024 [108] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:no-cache
HTTP_CONNECTION:keep-alive
HTTP_PRAGMA:no-cache
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Cache-Control: no-cache
Connection: keep-alive
Pragma: no-cache
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53708|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CACHE_CONTROL->no-cache|HTTP_CONNECTION->keep-alive|HTTP_PRAGMA->no-cache|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Cache-Control->no-cache|Connection->keep-alive|Pragma->no-cache|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:14:21,053 [108] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:14:26,104 [109] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/Default.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\Default.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53707|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/Default.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/Default.aspx|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/ocr/Default.aspx
Header:Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document

2025-05-10 22:14:26,112 [109] ERROR CommonLog 
 - 路径不存在:ocr/default.aspx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/Default.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:14:40,729 [108] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:no-cache
HTTP_CONNECTION:keep-alive
HTTP_PRAGMA:no-cache
HTTP_ACCEPT:*/*
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/ocr/index
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_SEC_CH_UA:"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:cors
HTTP_SEC_FETCH_DEST:empty
|ALL_RAW->Cache-Control: no-cache
Connection: keep-alive
Pragma: no-cache
Accept: */*
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/ocr/index
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua-platform: "Windows"
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/ocr/code.ashx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\ocr\code.ashx|QUERY_STRING->op=userinfo|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->53708|REQUEST_METHOD->GET|SCRIPT_NAME->/ocr/code.ashx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/ocr/code.ashx|HTTP_CACHE_CONTROL->no-cache|HTTP_CONNECTION->keep-alive|HTTP_PRAGMA->no-cache|HTTP_ACCEPT->*/*|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/ocr/index|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_SEC_CH_UA->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->cors|HTTP_SEC_FETCH_DEST->empty
GET:http://localhost:19225/ocr/code.ashx?op=userinfo
Header:Cache-Control->no-cache|Connection->keep-alive|Pragma->no-cache|Accept->*/*|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/ocr/index|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua-platform->"Windows"|sec-ch-ua->"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"|sec-ch-ua-mobile->?0|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->cors|Sec-Fetch-Dest->empty

2025-05-10 22:14:40,746 [108] ERROR CommonLog 
 - 路径不存在:ocr/code.ashx被拦截！原因：ResourceNotFound
IP: 
URL: http://localhost:19225/ocr/code.ashx?op=userinfo
Method: GET
Referer: http://localhost:19225/zh-Hans/ocr/index
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-05-10 22:16:32,807 [99] ERROR Application_End 
 - 【POD挂了】 时间:2025-05-10 22:16:32
