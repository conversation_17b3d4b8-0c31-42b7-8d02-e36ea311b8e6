$(document).ready((function(){nav={init:function(){var t=this;"/events"==window.location.pathname&&($(".v4_header_pc").addClass("whiteHeader"),$(".v4_header_mob").addClass("whiteHeader")),$(window).scrollTop()>60&&this.setScrollActive(),$(window).on("scroll",(function(e){$(this).scrollTop()>60?t.setScrollActive():t.setScrollunActive()}))},setScrollActive:function(){$(".v4_header_pc").addClass("scrollActive"),$(".v4_header_mob").addClass("scrollActive")},setScrollunActive:function(){$(".v4_header_pc").removeClass("scrollActive"),$(".v4_header_mob").removeClass("scrollActive")}},nav_pc={init:function(){$(".v4_header_pc").on("mouseenter",(function(){$(".v4_header_pc").addClass("active")})),$(".v4_header_pc").on("mouseleave",(function(){$(".v4_header_pc").removeClass("active")})),$("ul.top-nav>li").on("mouseenter",(function(){var t=this;$(t).find("a").addClass("active"),$(t).find(".triangle").addClass("active");var e=$(this).find("a").data("sub");e&&($("#"+e).attr("style","").show().siblings().hide(),$("#"+e).find(".nav-drown-con").attr("style","height: 100%; opacity: 1; transform: translate(0px, 0px);"),$("#"+e).on("mouseenter",(function(){$(t).find("a").addClass("active"),$(t).find(".triangle").addClass("active"),$("#"+e).attr("style","").show().siblings().hide(),$("#"+e).find(".nav-drown-con").attr("style","height: 100%; opacity: 1; transform: translate(0px, 0px);")})),$("#"+e).on("mouseleave",(function(){$("#"+e).attr("style","visibility: hidden; height: 0px"),$("#"+e).find(".nav-drown-con").attr("style","height: 0; opacity: 0; transform: translate(0px, -100%);"),$(t).find("a").removeClass("active"),$(t).find(".triangle").removeClass("active")})))})),$("ul.top-nav>li").on("mouseleave",(function(){var t=$(this).find("a").data("sub");$(this).find("a").removeClass("active"),$(this).find(".triangle").removeClass("active"),t&&($("#"+t).attr("style","visibility: hidden; height: 0px"),$("#"+t).find(".nav-drown-con").attr("style","height: 0; opacity: 0; transform: translate(0px, -100%);"))}))}},nav_mob={init:function(){$(".v4_header_mob .right-menu").on("click",(function(){$(this).toggleClass("active"),$(".v4_header_mob").toggleClass("active"),$("#nav_products").toggleClass("active1"),$(".mob-nav-content .sidebar-fix").toggleClass("fixedActiveBg"),$(".mob-nav-content .sidebar-fix").toggleClass("show"),$(".mob-nav-content .sidebar-fix").hasClass("show")?$("body").attr("style","overflow:hidden"):$("body").attr("style","overflow-y: scroll;")}))}},nav.init(),nav_pc.init(),nav_mob.init(),$(document).on("click",".sidebar-fix-left .mob-nav-item",(function(){$(this).addClass("active").siblings().removeClass("active");let t=$(this).attr("data");$(".sidebar-fix-rigth .sub-nav").eq(t).show().siblings().hide()}))}));