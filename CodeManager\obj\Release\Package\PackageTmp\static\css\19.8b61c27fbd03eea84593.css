@media screen and (min-width: 501px) {
  .a-PWeummh-C5NElZzyGwH .es-menu {
    width: 267px;
    right: -107px !important;
  }
}


.subscriber-heading .border {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  text-align: center;
  padding: 25.6px;
  padding: 1.6rem;
  margin-bottom: 48px;
  margin-bottom: 3rem;
}

.subscriber-heading .mode {
  text-align: center;
  width: 100%;
  font-size: 12.8px;
  font-size: 0.8rem;
  line-height: 16px;
  line-height: 1rem;
  margin-bottom: 0;
}

.subscriber-heading .identifier {
  text-align: center;
  width: 100%;
  font-size: 22.4px !important;
  font-size: 1.4rem !important;
  margin-bottom: 6.4px !important;
  margin-bottom: 0.4rem !important;
  word-wrap: break-word;
}

.subscriber-heading .unsubscribe-button {
  width: auto;
  margin: 0 auto;
  border: none;
  padding: 0;
  background: none;
  font-size: 12.8px;
  font-size: 0.8rem;
}

.subscriber-heading .unsubscribe-button:focus {
  outline: none;
}

.subscriber-heading .unsubscribe-button:hover {
  text-decoration: underline;
}

.subscriber-heading .unsubscribe-button:active {
  color: #cb5757;
}

.new-subscription .heading .title {
  margin-bottom: 4.8px;
  margin-bottom: 0.3rem;
  font-size: 24px;
  font-size: 1.5rem;
  font-weight: 500;
}

.new-subscription .heading .subscriber {
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 500;
}

.new-subscription .heading .instructions {
  margin-top: 18px;
  margin-top: 1.125rem;
  margin-bottom: 23.04px;
  margin-bottom: 1.44rem;
  font-size: 14px;
  font-size: 0.875rem;
}

.new-subscription .global-toggle-btn {
  cursor: pointer;
  margin-left: 11.2px;
  margin-left: 0.7rem;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 500;
}

.component-form .header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.component-form #component-selector-toggle {
  cursor: pointer;
}

.component-form .form-actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.component-form .cancel-btn {
  padding-right: 10px;
  padding-top: 5px;
}

.component-form .flat-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}


.status-embed {
  margin-bottom: 20px;
}

.status-embed .config-display-columns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.status-embed .config-display-columns .config-column {
  -webkit-box-flex: 2;
      -ms-flex: 2;
          flex: 2;
}

.status-embed .config-display-columns .display-column {
  margin-left: 40px;
  -webkit-box-flex: 3;
      -ms-flex: 3;
          flex: 3;
}

.status-embed .config-display-columns .color-examples {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: left;
      -ms-flex-pack: left;
          justify-content: left;
}

@media only screen and (max-width: 600px) {
  .status-embed .config-display-columns {
    display: inline-block;
  }

  .status-embed .config-display-columns .display-column {
    margin-left: 0;
    width: 100%;
  }

  .status-embed .color-examples {
    padding: 10px 0 !important;
  }
}

.status-embed .description {
  margin-bottom: 11px;
}

.status-embed .status-message {
  background-color: #e3fcef;
  padding: 16px;
  border-radius: 2px;
  position: relative;
}

.status-embed .status-message p {
  margin-bottom: 0;
}

.status-embed .status-message.offline {
  background-color: #deebff;
}

.status-embed .status-message .icon-container {
  color: #006644;
  position: absolute;
  top: 16px;
}

.status-embed .status-message .icon-container.offline {
  color: #0747a6;
}

.status-embed .status-message .message-container {
  margin-left: 40px;
  display: inline-block;
}

.status-embed .position-setup {
  margin-top: 27px;
}

@media only screen and (max-width: 600px) {
  .status-embed .position-setup {
    margin-top: 36px;
  }
}

.status-embed .position-setup .position-config h1 {
  font-size: 16px;
  color: #253858;
  margin: 0;
  line-height: 16px;
}

.status-embed .position-setup .position-config .status-radio-group {
  margin-top: 20px;
}

@media only screen and (max-width: 600px) {
  .status-embed .position-setup .position-config .status-radio-group {
    margin-top: 11px;
  }
}

@media only screen and (max-width: 600px) {
  .status-embed .position-setup .position-preview {
    margin-top: 15px;
  }
}

.status-embed .position-setup .position-preview .img-container > img {
  width: 100%;
}

.status-embed .position-setup .position-preview .preview-app-position {
  position: relative;
}

.status-embed .position-setup .position-preview .preview-status-embed-position {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 122px;
  -webkit-transition: bottom 0.5s ease, right 0.5s ease;
  transition: bottom 0.5s ease, right 0.5s ease;
}

.status-embed .position-setup .position-preview .preview-status-embed-position.bottom-left {
  bottom: 16px;
  right: calc(100% - 140px);
}

.status-embed .position-setup .position-preview .preview-status-embed-position.bottom-right {
  bottom: 16px;
  right: 18px;
}

.status-embed .position-setup .position-preview .preview-status-embed-position.top-right {
  bottom: calc(100% - 76px);
  right: 18px;
}

.status-embed .position-setup .position-preview .app-preview-note {
  color: #5e6c84;
  font-size: 12px;
  margin-top: 10px;
  margin-right: 8px;
  line-height: 16px;
}

.status-embed .color-setup .title {
  margin-top: 2px;
  margin-bottom: 9px;
  font-weight: 600;
  font-size: 16px;
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .title {
    margin-top: 27px;
  }
}

.status-embed .color-setup .control-group {
  margin-top: 0px;
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .control-group {
    margin-top: 0px;
    margin-bottom: 0px;
  }
}

.status-embed .color-setup .color-group {
  margin-top: 20px;
  margin-bottom: 48px;
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .color-group {
    width: 100%;
    margin-top: 0;
    margin-bottom: 0;
  }
}

.status-embed .color-setup .color-input-container {
  position: flex;
}

.status-embed .color-setup .color-input-container::before {
  content: '#';
  position: absolute;
  display: inline-block;
  font-size: 16px;
  line-height: 40px;
  margin-left: 10px;
  color: #666666;
}

.status-embed .color-setup .color-input-container input {
  padding-left: 19px;
}

.status-embed .color-setup .color-input-container .invalid-color {
  position: absolute;
  color: #de350b;
  font-size: 12px;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.status-embed .color-setup .color-input-container .invalid-color .error-text {
  margin: 1px 0 0 0;
}

.status-embed .color-setup iframe {
  margin-top: 0;
  width: 320px;
  height: 115px;
  border-radius: 4px;
  -webkit-box-shadow: rgba(9, 30, 66, .31) 0px 0px 1px, rgba(9, 30, 66, .25) 0px 20px 32px -8px;
          box-shadow: rgba(9, 30, 66, .31) 0px 0px 1px, rgba(9, 30, 66, .25) 0px 20px 32px -8px;
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup iframe {
    margin-top: 16px;
    width: inherit;
  }
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .maintenance .color-choices {
    margin-top: 45px;
    margin-bottom: 48px;
  }
}

@media only screen and (max-width: 600px) and (max-width: 600px) {
  .status-embed .color-setup .maintenance .color-choices {
    margin-bottom: 0;
  }
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .control-group {
    margin-top: 11px;
  }
}

.status-embed .color-setup .control-group input {
  width: 197px;
}

@media only screen and (max-width: 600px) {
  .status-embed .color-setup .control-group input {
    width: 100%;
  }
}

.status-embed .color-setup .control-group:nth-child(2) {
  margin-top: 37px;
}

.status-embed .embed-form-controls {
  float: right;
  margin-bottom: 118px;
}

@media only screen and (max-width: 600px) {
  .status-embed .embed-form-controls {
    margin-top: 43px;
    margin-bottom: 64px;
  }
}

@media only screen and (max-width: 600px) {
  .status-embed .embed-form-controls {
    margin-top: 43px;
    margin-bottom: 64px;
  }
}

.status-embed .embed-form-controls button {
  margin-left: 8px;
  margin-left: 0.5rem;
}

.status-embed .embed-form-controls .copy-code-btn {
  color: #0052cc;
}

.status-embed .embed-form-controls .copy-code-btn.not-adg3 {
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  font-size: 0.875rem;
  display: inline-block;
  margin-right: 8px;
  margin-right: 0.5rem;
}

.status-embed .embed-code-modal .title {
  font-size: 20px;
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.status-embed .embed-code-modal .code {
  border-radius: 3px;
  background: #f4f5f7;
  color: #172b4d;
  padding: 2px 4px;
}

.status-embed .embed-code-modal #copy-icon {
  margin-right: 40px;
}

.status-embed .embed-code-modal #copy-icon a {
  margin-left: 10px;
  text-decoration: none;
  color: #172b4d;
  position: absolute;
  cursor: pointer;
}

.status-embed .embed-code-modal #copy-icon a i {
  position: absolute;
  font-size: 24px;
}

.status-embed .embed-code-modal #code-copied {
  display: inline-block;
  text-align: center;
  font-size: 12px;
  height: 22px;
  width: 88px;
  color: white;
  background-color: #172b4d;
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .2);
          box-shadow: 0 1px 1px 0 rgba(0, 0, 0, .2);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.status-embed .embed-code-modal #code-copied.visible {
  visibility: visible;
  opacity: 1;
}

.status-embed .embed-code-modal #code-copied.faded {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 1s linear, visibility 0s linear 1s;
  transition: opacity 1s linear, visibility 0s linear 1s;
}

.status-embed .embed-code-modal .embed-code-block {
  width: 100%;
  background: #253858;
  overflow: scroll;
  color: white;
  padding: 8px 20px;
  margin-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
}

.status-embed .embed-code-modal .confirm-btn {
  float: right;
}

.status-embed .embed-code-modal .modal-header {
  padding-bottom: 0px;
}

@media only screen and (max-width: 600px) {
  .status-embed .embed-code-modal .modal-body {
    padding-top: 0;
    margin-top: 16px;
    -webkit-box-flex: 0;
        -ms-flex-positive: 0;
            flex-grow: 0;
  }

  .status-embed .embed-code-modal .embed-code-block {
    margin-top: 12px;
    word-break: break-all;
  }
}


@media screen and (max-width: 485px) {
  .activate-audience-specific .price-explanation-group {
    margin: 0 68px;
  }
}

.activate-audience-specific .description {
  padding-bottom: 54px;
}

.activate-audience-specific .description ul {
  padding-top: 15px;
}

.activate-audience-specific .description li {
  list-style: disc inside;
  font-size: 25px;
}

.activate-audience-specific .description li span {
  font-size: 17px;
  position: relative;
  margin-left: -19px;
}

.activate-audience-specific .description h4 {
  font-size: 22px;
}

.activate-audience-specific .fa-warning {
  color: #ff8b00;
}

.activate-audience-specific .fa-info-circle {
  color: #42526e;
}

.activate-audience-specific .pricing-tier-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 17px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.activate-audience-specific .pricing-tier-row .pricing-tier-label {
  color: #333333;
  font-size: 26px;
  font-weight: 500;
  padding-right: 14px;
  text-align: center;
  padding-top: 16px;
}

.activate-audience-specific .pricing-tier-row .select-container {
  width: 69px;
  height: 40px;
}

.activate-audience-specific .warning-container {
  max-width: 350px;
  background-color: #fffae6;
  margin: 0 auto 11px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.activate-audience-specific .warning-container .icon {
  padding: 16px 16px 37px;
}

.activate-audience-specific .warning-container .message {
  text-align: left;
}

.activate-audience-specific .plan-detail-selector {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: center;
}

.activate-audience-specific .plan-detail {
  -ms-flex-preferred-size: 350px;
      flex-basis: 350px;
  height: 354px;
  float: left;
  border: 1px solid #dfe1e6;
  border-radius: 2px;
}

.activate-audience-specific .plan-detail .lozenge {
  margin: 16px auto 8px;
}

.activate-audience-specific .plan-detail .lozenge.invisible {
  visibility: hidden;
}

.activate-audience-specific .plan-detail.with-lozenge {
  height: 370px;
}

.activate-audience-specific .plan-detail .header {
  margin: 33.6px 24px 19.2px;
}

.activate-audience-specific .plan-detail .header.with-lozenge {
  margin-top: 0;
}

.activate-audience-specific .plan-detail .header .price {
  font-size: 54.4px;
  line-height: 55.488px;
}

.activate-audience-specific .plan-detail .header .price .sup,
.activate-audience-specific .plan-detail .header .price .sub {
  font-size: 16px;
  line-height: 24px;
  position: relative;
}

.activate-audience-specific .plan-detail .header .price .sup {
  top: -26px;
}

.activate-audience-specific .plan-detail .header .price .amount {
  font-weight: 500;
}

.activate-audience-specific .plan-detail .header .name {
  margin-top: 8px;
  text-transform: uppercase;
  font-size: 20px;
  line-height: 29px;
  letter-spacing: 2px;
}

.activate-audience-specific .plan-detail .table-row {
  margin-bottom: 8px;
  color: #172b4d;
}

.activate-audience-specific .plan-detail .table-row.bigger {
  font-size: 21.2px;
  line-height: 30.422px;
}

.activate-audience-specific .plan-detail .table-row.bigger .more-info {
  margin-top: 1px;
}

.activate-audience-specific .plan-detail .table-row.smaller {
  font-size: 15.6px;
  line-height: 23.478px;
}

.activate-audience-specific .plan-detail .table-row .more-info {
  position: absolute;
}

.activate-audience-specific .plan-detail .table-row .info-tooltip {
  z-index: -1;
}

.activate-audience-specific .plan-detail .button-container {
  margin-bottom: 40px;
}

.activate-audience-specific .plan-detail ul {
  display: inline-block;
  margin: auto;
  text-align: left;
  padding-top: 10px;
}

.activate-audience-specific .plan-detail ul.with-lozenge {
  padding-bottom: 32px;
}

.activate-audience-specific .price-explanation {
  text-align: center;
  cursor: pointer;
  padding: 32px;
}

.activate-audience-specific .price-explanation .chevron {
  position: absolute;
  margin-top: -5px;
}

.activate-audience-specific .price-explanation.invisible {
  visibility: hidden;
}

.activate-audience-specific .pricing-table {
  border-collapse: collapse;
  margin-top: 30px;
}

.activate-audience-specific .pricing-table th {
  border-bottom: 1px solid #ddd;
  color: #6b778c;
  font-size: 13px;
  font-weight: 500;
  text-align: left;
}

.activate-audience-specific .pricing-table th,
.activate-audience-specific .pricing-table td {
  padding-right: 92px;
}

.activate-audience-specific .pricing-table td {
  padding-top: 8px;
  font-size: 17px;
}


.page-authentication .heading .title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 50px;
  margin-bottom: 35px;
}

.page-authentication .config-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 25px 0;
  border-bottom: 1px solid #dfe1e6;
}

.page-authentication .config-row .description .main {
  font-size: 16px;
  font-weight: 600;
}

.page-authentication .configure-step.step-2 {
  margin-top: 32px;
}

.page-authentication .configure-step .title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 25px;
}

.page-authentication .configure-step .field-container {
  margin-bottom: 15px;
}

.page-authentication .configure-step .field-container .label {
  color: #6b778c;
  font-size: 12px;
  font-weight: 600;
}

.page-authentication .configure-step .field-container .label .tooltip-base {
  top: 1px;
}

.page-authentication .configure-step .subsection {
  margin-top: 10px;
}

.page-authentication .subtext,
.page-authentication .subtext a {
  font-size: 11px;
}

.page-authentication .subtext {
  color: #6b778c;
}

.page-authentication textarea {
  height: 145px;
}

.page-authentication .footer {
  margin-top: 20px;
}

.page-authentication .footer.two-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.page-authentication .footer .form-actions-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.page-authentication .footer .form-actions-container button {
  margin-left: 15px;
}

.page-authentication .previous-page-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
  color: #172b4d;
}

.page-authentication .previous-page-link:hover {
  text-decoration: none;
}

.page-authentication .previous-page-link .chevron-container {
  margin-right: 2px;
}

.page-authentication .previous-page-link .text {
  margin-top: -2px;
}

.page-authentication .btn-delete-config,
.page-authentication .btn-delete-config:hover {
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  color: #42525e;
}

.page-authentication .legacy-options {
  margin-bottom: 32px;
}

.page-authentication .legacy-option-container {
  padding: 25px 0;
}

.page-authentication .disable-message {
  color: #6b778c;
  font-size: 11px;
}


.modal-import-users {
  width: 536px;
  padding: 32px 24px 24px;
}

.modal-import-users .content h3 {
  margin: 0;
  font-size: 21px;
}

.modal-import-users .content .header {
  margin-bottom: 30px;
}

.modal-import-users .content .header h3.modal-title {
  margin-bottom: 8px;
}

.modal-import-users .content .body {
  font-weight: 500;
  margin-bottom: 30px;
}

.modal-import-users .content .body ol {
  margin-left: 17px;
}

.modal-import-users .content .body li {
  margin-bottom: 8px;
}

.modal-import-users .content .body li small.footnote {
  color: #6b778c;
  margin-top: 2px;
}

.modal-import-users .content .body a {
  font-weight: 500;
}

.modal-import-users .content .modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 4px 0;
}

.modal-import-users .content .modal-footer div {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.modal-import-users .content .modal-footer a,
.modal-import-users .content .modal-footer button {
  font-weight: 500;
}

.modal-import-users .content .modal-footer .learn-more {
  text-align: left;
}

.modal-import-users .content .modal-footer .learn-more a {
  margin-top: 4px;
}

.modal-import-users .disabled-warning {
  margin: -6px 0 24px;
}

.modal-import-users .file-upload-container {
  margin-bottom: 40px;
  height: 62px;
  width: 100%;
  border: 1px solid #c1c7d0;
  border-radius: 3px;
  /*hide the input, but not from the browser*/
}

.modal-import-users .file-upload-container input[type='file'] {
  opacity: 0;
  position: absolute;
  pointer-events: none;
}

.modal-import-users .file-upload-container label,
.modal-import-users .file-upload-container button.disabled {
  display: inline-block;
  position: relative;
  margin: 14px;
}

.modal-import-users .file-upload-container label {
  background-color: #f5f6f8;
  border-radius: 3px;
  line-height: 31px;
  height: 34px;
  width: 97px;
  text-align: center;
  cursor: pointer;
}



/*# sourceMappingURL=19.8b61c27fbd03eea84593.css.map*/