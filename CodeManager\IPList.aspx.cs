﻿using CommonLib;
using System;
using System.Collections.Generic;
using System.Data;

namespace Account.Web
{
    public partial class IPList : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (CommonLib.BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
            {
                Response.End();
                return;
            }
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            DataTable dtTmp = CodeHelper.GetAllIp(txtDate.Text.Trim(), BoxUtil.GetInt32FromObject(txtType.Text, -1));
            lblCount.Text = dtTmp.Rows.Count.ToString();
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }

        protected void btnSpeed_Click(object sender, EventArgs e)
        {
            if (BoxUtil.GetStringFromObject(Request.QueryString["pwd"]) != CommonHelper.StrPWD)
                return;
            DataTable dtTmp = CodeHelper.GetFastIp(null);
            lblCount.Text = dtTmp.Rows.Count.ToString();
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
        }
    }

    [Serializable]
    public class SiteMain
    {
        public string update { get; set; }

        public List<WebInfo> web { get; set; }

        public string defaultHost { get; set; }
    }

    [Serializable]
    public class WebInfo
    {
        public string Ip { get; set; }

        public string Host { get; set; }

        public bool IsDefault { get; set; }
    }
}