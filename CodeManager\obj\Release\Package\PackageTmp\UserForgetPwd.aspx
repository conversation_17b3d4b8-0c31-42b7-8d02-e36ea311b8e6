﻿<%@ Page Title="" Language="C#" CodeBehind="UserForgetPwd.aspx.cs" Inherits="Account.Web.UserForgetPwd" %>

<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="robots" content="nofollow,noarchive">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>忘记密码<%=PageTitleConst.Default_Ext %></title>
    <link rel="stylesheet" href="./CSS/bootstrap.min.css">
</head>
<body>
    <div class="card-body">
        <div class="main">
            <form role="form" class="form-layout" method="post" action="UserForgetPwd.aspx" runat="server">
                <div class="text-center m-b">
                    <h4 class="text-uppercase">找回密码</h4>
                    <p>AI智能一站式平台，专注提升生产力！</p>
                </div>
                <div style="display: flex">
                    <style>
                        .mod-btn-disabled-gray {
                            color: #444 !important;
                            background-color: #d9d9d9 !important;
                            cursor: default;
                        }
                    </style>
                    <input type="text" class="form-control input-lg" name="account" id="account" placeholder="请输入登录账号(手机号/邮箱)" required="" style="flex: 1">
                    <a id="btn_vcode" class="btn btn-default" style="line-height: 25px; flex-basis: 48px; margin: 0 0 12px 12px; display: inline-block">发送验证码</a>
                </div>
                <div class="form-group">
                    <input type="text" class="form-control input-lg" name="verify" placeholder="请输入接收到的验证码" required="">
                </div>
                <div class="form-group">
                    <input type="password" class="form-control input-lg" name="password" placeholder="请输入6-15位密码，大小写字母、数字" required="">
                </div>
                <button class="btn btn-primary btn-block btn-lg m-b" style="background-color: #034FD8; border-color: #034FD8;" type="submit">找回密码</button>
                <p class="text-center" style="margin-top: 10px">
                    已有账号？<a href="UserLogin.aspx">点击登陆</a>
                </p>
            </form>
        </div>
    </div>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script src="https://static.geetest.com/v4/gt4.js"></script>
    <script>
        var phoneReg = /^1[3456789]\d{9}$/;
        var emailReg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        function send_vcode() {
            if ($('#btn_vcode').hasClass('mod-btn-disabled-gray')) {
                return;
            }
            var txtAccount = $.trim($("#account").val());
            var isMobile = phoneReg.test(txtAccount);
            var isEmail = emailReg.test(txtAccount);
            if (!isMobile && !isEmail) {
                alert("<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrAccountFormatError,Request)%>");
                return;
            }
            $.ajax({
                url: "/mail.aspx?en=1&op=forgetpwd&" + (isEmail ? "email" : "mobile") + "=" + txtAccount,
                type: "GET",
                success: function success(result) {
                    if (result !== "True") {
                        alert(result);
                    }
                }
            });
            $('#btn_vcode').addClass('mod-btn-disabled-gray');
            var val = 60;
            var timer = function timer() {
                val--;
                if (val == 0) {
                    $('#btn_vcode').removeClass('mod-btn-disabled-gray');
                } else {
                    setTimeout(timer, 1e3);
                }
                var html = "<%=Account.Web.CommonTranslate.GetTrans(Account.Web.UserConst.StrSendValidateCode,Request)%>" + (val ? "(".concat(val, ")") : '');
                $('#btn_vcode').html(html);
            };
            timer();
        }
        initGeetest4({
            captchaId: '1946e559ec0dd0b1102aeb4d8b58fe19',
            product: 'bind',
            riskType: 'nine'
        }, function (captchaObj) {
            captchaObj.onSuccess(function () {
                var result = captchaObj.getValidate();
                if (result) {
                    send_vcode();
                }
            });
            $("#btn_vcode").click(function () {
                captchaObj.showCaptcha();
            });
        });
    </script>
</body>
</html>
