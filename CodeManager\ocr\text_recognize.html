<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <title>智能文字识别 | 高精度OCR识别工具 | 98%+识别率</title>
    <meta name="description" content="免费高精度OCR文字识别工具，准确率高达98%，支持100+种语言识别。智能识别排版，精准提取图片、PDF和扫描文档中的文字内容，并支持多种格式导出，保留原始排版。">
    <meta name="keywords" content="文字识别,OCR识别,图片转文字,高精度文字识别,智能图像分析,多语言OCR,PDF文字提取,文本识别工具,字符识别,文档数字化">
    <meta name="next-head-count" content="13">
    <link rel="preload" href="static/css/bf406b4dfe4a88f7.css" as="style">
    <link rel="stylesheet" href="static/css/bf406b4dfe4a88f7.css" data-n-g="">
    <link rel="preload" href="static/css/97e00c3eababac2c.css" as="style">
    <link rel="stylesheet" href="static/css/97e00c3eababac2c.css" data-n-p="">
    <link rel="preload" href="static/css/09a1ad1c616d180a.css" as="style">
    <link rel="stylesheet" href="static/css/09a1ad1c616d180a.css" data-n-p="">
    <link rel="preload" href="static/css/048266ae42f362cd.css" as="style">
    <link rel="stylesheet" href="static/css/048266ae42f362cd.css" data-n-p="">
    <link rel="preload" href="static/css/71899eb9acb65263.css" as="style">
    <link rel="stylesheet" href="static/css/71899eb9acb65263.css" data-n-p="">
    <noscript data-n-css=""></noscript>
    <script defer="" nomodule="" src="static/js/polyfills-c67a75d1b6f99dc8.js"></script>
    <script src="static/js/leads.js" defer="" data-nscript="beforeInteractive"></script>
    <script src="static/js/webpack-9e2dac2c71e11463.js" defer=""></script>
    <script src="static/js/framework-bb5c596eafb42b22.js" defer=""></script>
    <script src="static/js/main-ab39c4ec2bce69ad.js" defer=""></script>
    <script src="static/js/_app-d736fe5cc417ac5c.js" defer=""></script>
    <script src="static/js/75fc9c18-2e9ae03a475db518.js" defer=""></script>
    <script src="static/js/676-58ab10dc70b27a01.js" defer=""></script>
    <script src="static/js/937-f867bcf63c8b973c.js" defer=""></script>
    <script src="static/js/121-b6d40ccfcd609a3d.js" defer=""></script>
    <script src="static/js/188-3e55a14c02731598.js" defer=""></script>
    <script src="static/js/387-5665b26f1097c66c.js" defer=""></script>
    <script src="static/js/97-f3427726e942417a.js" defer=""></script>
    <script src="static/js/text_recognize-c0949d51319d3d2e.js" defer=""></script>
</head>
<body>
    <div id="__next" data-reactroot="">
        <main class="y1fU5arT">
            <div>
                <main>
                    <div class="J08R4Y7Q">
                        <div class="_BEBSCQe">
                            <div class="_2iFAvTh"></div>
                            <div class="C_f2FiQb">OCR助手 <!-- -->免费通用文字识别(OCR)工具</div>
                        </div>
                        <div class="PPCKtJf0">
                            <div class="ub_V7Qrd">
                                <div class="ant-steps ant-steps-horizontal ant-steps-label-horizontal">
                                    <div class="ant-steps-item ant-steps-item-process ant-steps-item-active">
                                        <div class="ant-steps-item-container">
                                            <div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">1</span></div>
                                            <div class="ant-steps-item-content"><div class="ant-steps-item-title">上传文件</div></div>
                                        </div>
                                    </div><div class="ant-steps-item ant-steps-item-wait"><div class="ant-steps-item-container"><div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">2</span></div><div class="ant-steps-item-content"><div class="ant-steps-item-title">在线识别</div></div></div></div><div class="ant-steps-item ant-steps-item-wait"><div class="ant-steps-item-container"><div class="ant-steps-item-tail"></div><div class="ant-steps-item-icon"><span class="ant-steps-icon">3</span></div><div class="ant-steps-item-content"><div class="ant-steps-item-title">导出结果</div></div></div></div>
                                </div>
                                <div class="e_PClGCQ">
                                    <span>
                                        <div class="ant-upload ant-upload-drag">
                                            <span tabindex="0" class="ant-upload ant-upload-btn" role="button">
                                                <input type="file" style="display:none" accept="image/png,image/jpeg,image/bmp" multiple="">
                                                <div class="ant-upload-drag-container">
                                                    <div>
                                                        <div class="J75OY4C_">
                                                            <div class="nMnmvFKj">
                                                                <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                                    <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%">
                                                                        <img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2772%27%20height=%2772%27/%3e">
                                                                    </span>
                                                                    <img alt="upload" src="static/image/nodata.svg" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" srcset="static/image/nodata.svg 1x, static/image/nodata.svg 2x">
                                                                    <noscript>
                                                                        <img alt="upload" srcSet="static/image/nodata.svg 1x, static/image/nodata.svg 2x" src="static/image/nodata.svg" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" loading="lazy" />
                                                                    </noscript>
                                                                </span>
                                                            </div><div class="x4UZHQKD fr-bold-1324e8e2">点击上传文件 / 拖拽文件到此处 / 截图后ctrl+v</div><div class="aaeTkeUt">可支持<!-- -->png、jpg、jpeg、bmp<!-- -->格式文件</div><div class="aaeTkeUt">上传单个文件大小不超过<!-- -->10<!-- -->M</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </span>
                                        </div>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </main>
    </div>
    <script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/text_recognize","query":{},"buildId":"fmSVWroe3Il4vJrWJlNDc","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script>
</body>
</html>