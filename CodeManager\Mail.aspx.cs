﻿using Account.Web.Common;
using CommonLib;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Web;

namespace Account.Web
{
    public partial class Mail : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
                if (!string.IsNullOrEmpty(strOp))
                {
                    DoOperate(strOp);
                }
            }
            Response.End();
        }

        private void DoOperate(string strOP)
        {
            var notice = new NoticeQueueEntity() { Lang = Request.GetValue("lang") };
            try
            {
                string strEncrypt = "";
                //OPEntity opp = new OPEntity();
                var isEncrypt = CommonHelper.IsEncrypt;
                if (Equals(Request.QueryString["en"], "1"))
                {
                    isEncrypt = false;
                }
                if (isEncrypt)
                {
                    strEncrypt = BoxUtil.GetStringFromObject(Request.QueryString["con"]).Replace(" ", "+");
                    strEncrypt = CommonEncryptHelper.DESDecrypt(strEncrypt, CommonHelper.StrEncrypt);
                    if (!string.IsNullOrEmpty(strEncrypt))
                    {
                        //opp.StrApp = CommonHelper.SubString(strEncrypt, "app=", "&");
                        notice.MacCode = CommonHelper.SubString(strEncrypt, "app=", "&");
                    }
                }
                else
                {
                    notice.MacCode = Request.GetValue("app");
                }
                //op=regaccount&con=+4AwFpPdZnNU67HlSCmNSavTEV7EMBGG&mac=DESKTOP-U0SQIL9-li&uid=5d8dc868d9ab09eedd3156de38fb2672&ver=2024-07-09 00:00:00&tick=638562216300931629&lang=SimplifiedChinese
                if (string.IsNullOrEmpty(notice.MacCode))
                {
                    notice.MacCode = Request.GetValue("mac");
                }
                var uid = Request.GetValue("uid");
                if (string.IsNullOrEmpty(uid))
                {
                    //排除Web请求
                    if (isEncrypt)
                        Request.Log("空Header请求！");
                }
                switch (strOP)
                {
                    case "forgetpwd":
                        if (isEncrypt)
                        {
                            notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                            notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                        }
                        else
                        {
                            notice.To = BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                            notice.MobileNo = BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                        }
                        var isEmail = BoxUtil.IsEmail(notice.To);
                        var isMobile = BoxUtil.IsMobile(notice.MobileNo);
                        if (isMobile || isEmail)
                        {
                            if ((isMobile && !CodeHelper.IsExitsCode(notice.MobileNo))
                                || (isEmail && !CodeHelper.IsExitsCode(notice.To)))
                            {
                                Response.Write(UserConst.StrAccountNotExsits.GetTrans(notice.Lang));
                                return;
                            }
                            notice.Subject = "密码找回服务".GetTrans(notice.Lang);
                            var validateCode = CommonValidateCode.GetValidateCode(isMobile ? notice.MobileNo : notice.To, "OCRREGForgetPwd", true);
                            if (isEmail)
                            {
                                notice.NoticeType = NoticeType.邮件;
                                notice.Body = validateCode;
                            }
                            else if (isMobile)
                            {
                                notice.NoticeType = NoticeType.短信;
                                notice.Body = UserConst.StrSMSSign + string.Format(UserConst.StrValidateCodeSMS, validateCode);
                            }
                        }
                        if (!string.IsNullOrEmpty(notice.Body))
                        {
                            //SendMail.Send(notice.To, notice.Body, notice.Subject);
                            Response.Write("True");
                        }
                        else
                        {
                            Response.Write(UserConst.StrAccountFormatError.GetTrans(notice.Lang));
                        }
                        break;
                    case "regaccount":
                        //mail.aspx?email=<EMAIL>&op=regaccount
                        if (isEncrypt)
                        {
                            notice.To = CommonHelper.SubString(strEncrypt, "email=", "&");
                            notice.MobileNo = CommonHelper.SubString(strEncrypt, "mobile=", "&");
                        }
                        else
                        {
                            notice.To = BoxUtil.GetStringFromObject(Request.QueryString["email"]);
                            notice.MobileNo = BoxUtil.GetStringFromObject(Request.QueryString["mobile"]);
                        }
                        //格式验证
                        isMobile = BoxUtil.IsMobile(notice.MobileNo);
                        isEmail = BoxUtil.IsEmail(notice.To);
                        if (!isMobile && !isEmail)
                        {
                            Response.Write(UserConst.StrAccountFormatError.GetTrans(notice.Lang));
                            return;
                        }

                        //已注册验证
                        var account = isMobile ? notice.MobileNo : notice.To;
                        if (CodeHelper.IsExitsCode(account))
                        {
                            Response.Write(UserConst.StrHasReged.GetTrans(notice.Lang));
                            return;
                        }

                        //同一机器注册次数限制
                        if (string.IsNullOrEmpty(uid) || CodeHelper.IsCanReg(uid, ""))
                        {
                            notice.Subject = "欢迎注册OCR助手".GetTrans(notice.Lang);
                            notice.Body = CommonValidateCode.GetValidateCode(account, "OCRREG", true);
                            if (isEmail)
                            {
                                notice.NoticeType = NoticeType.邮件;
                            }
                            else if (isMobile)
                            {
                                notice.Body = UserConst.StrSMSSign + string.Format(UserConst.StrValidateCodeSMS, notice.Body);
                                notice.NoticeType = NoticeType.短信;
                            }
                            //SendMail.Send(notice.To, notice.Body, notice.Subject);
                            Response.Write("True");
                        }
                        else
                        {
                            CommonHelper._Log.Debug("注册次数超限！token：" + uid + "，账号：" + notice.To + notice.MobileNo);
                            Response.Write("True");
                        }
                        break;
                }
                if (notice != null && notice.NoticeType.GetHashCode() > 0)
                {
                    CommonHelper._Log.Debug(notice.ToString());
                    MsgProcessHelper.SendMsg(notice);
                }
            }
            catch (Exception oe)
            {
                Response.Write(UserConst.StrServerError.GetTrans(notice.Lang));
                CommonHelper._Log.Error(oe);
            }
        }
    }
}