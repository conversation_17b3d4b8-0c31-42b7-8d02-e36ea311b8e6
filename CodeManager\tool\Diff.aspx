﻿<%@ Page Title="在线文本差异比对工具 | 代码文本对比器 | 差异分析" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="static/css/index-b078aaba.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="keywords" content="文本对比,代码差异分析,文件比较工具,在线差异比对,代码对比,差异检查,比较文件差异,diff工具,两个文本比较,版本差异" />
    <meta name="description" content="免费在线文本差异比对工具，可简单快速地对比两个文本或代码的差异，直观地显示添加和删除的内容。适用于开发者对比代码变更、版本差异检查等多种场景。" />
    <script type="text/javascript" src="static/js/vue.js"></script>
    <script src="static/js/require.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="panel panel-default" style="margin-bottom: 0px;">
        <div class="panel-heading">
            <h3 class="panel-title">在线文本差异比对工具</h3>
        </div>
    </div>
    <div class="col-md-12">
        <link type="text/css" rel="stylesheet" href="static/css/codemirror.css" />
        <link type="text/css" rel="stylesheet" href="static/css/mergely.css" />
        <div class="content">
            <div class="panel panel-default panel-beforef px-4">
                <div class="panel-body px-0">
                    <div class="row clearfix">
                        <div id="wbdb" class="col-md-12">
                            <div id="mergely-resizer">
                                <div id="compare"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="static/js/jquery-3.3.1.min.js"></script>
    <script type="text/javascript" src="static/js/codemirror.js"></script>
    <script type="text/javascript" src="static/js/mergely.js"></script>
    <script type="text/javascript">
        function getWinHeight() {
            var winHeight = 0;
            if (window.innerHeight)
                winHeight = window.innerHeight;
            else if ((document.body) && (document.body.clientHeight))
                winHeight = document.body.clientHeight; return winHeight
        }
        function getWinWidth() {
            var o = document.getElementById("wbdb");
            var w = o.offsetWidth; return w
        }
        $(document).ready(
            function () {
                $('#compare').mergely({
                    width: 'auto', height: '400',
                    cmsettings: { readOnly: false, lineWrapping: true, },
                    //lhs: function (setValue) { setValue('/*	welcome here, .com	*/') },
                    //rhs: function (setValue) { setValue('/*	welcome here, .com	*/') }
                });
                $(window).resize(
                    function () {
                        $('#compare').mergely('options', { height: getWinHeight() - 120, width: getWinWidth() - 30 });
                        $('#compare').mergely('update')
                    });
                $(window).resize()
            });
    </script>
</asp:Content>
