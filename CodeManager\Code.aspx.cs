﻿using Account.Web.Common;
using CommonLib;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text.RegularExpressions;
using System.Web;

namespace Account.Web
{
    public partial class Code : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
            if (!string.IsNullOrEmpty(strOp))
            {
                DoOperate(strOp);
            }
            Response.End();
        }

        private static List<string> lstMsg = new List<string>();
        private static object objLock = "";

        private void ResponseWriteByIsWeb(string strMsg, bool isWeb = false, string location = "")
        {
            Response.Write(string.Format("{0}{1}{2}"
                , isWeb ? "<script>alert('" : ""
                , strMsg
                , isWeb ? "');" + (string.IsNullOrEmpty(location) ? "" : "window.location='" + location + "'") + "</script>" : ""));
        }

        private void DoOperate(string strOP)
        {
            var code = new CodeEntity
            {
                StrAppCode = Request.GetValue("app")
            };
            var lang = Request.GetValue("lang");
            if (strOP == "pay")
            {
                //http://ocr.oldfish.cn:9090/testOrder?isHtml=1&type=0&price=0.01&return_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.aspx&notify_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.ashx&remark=OCR%E5%8A%A9%E6%89%8B-%E4%B8%80%E5%B9%B4%E4%B8%93%E4%B8%9A%E7%89%88&param=***********
                var account = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                var orderNo = BoxUtil.GetStringFromObject(Request.QueryString["orderNo"]);
                var remark = HttpUtility.UrlDecode(BoxUtil.GetStringFromObject(Request.QueryString["remark"]));
                var from = (OrderFrom)BoxUtil.GetInt32FromObject(Request.QueryString["from"], 0);
                string price = "";

                if (!string.IsNullOrEmpty(remark))
                {
                    var userType = GetCanRegUserTypes().FirstOrDefault(p => remark.EndsWith(p.Type.ToString()));
                    if (userType != null && userType.UserChargeType.Count > 0)
                    {
                        var priceType = userType.UserChargeType.FirstOrDefault(p => remark.StartsWith(p.Name)) ??
                                        userType.UserChargeType.FirstOrDefault();
                        if (priceType != null)
                        {
                            price = priceType.Price.ToString("F0");
                            remark = string.Format("{0}{1}", priceType.Name, userType.Type);
                        }
                    }
                    if (string.IsNullOrEmpty(price))
                    {
                        userType = GetCanRegUserTypes(true).FirstOrDefault(p => remark.EndsWith(p.Type.ToString()));
                        if (userType != null && userType.UserChargeType.Count > 0)
                        {
                            var priceType = userType.UserChargeType.FirstOrDefault(p => remark.StartsWith(p.Name)) ??
                                            userType.UserChargeType.FirstOrDefault();
                            if (priceType != null)
                            {
                                price = priceType.Price.ToString("F0");
                                remark = string.Format("{0}{1}", priceType.Name, userType.Type);
                            }
                        }
                    }
                }
                LogHelper.Log.Error("发起支付入参：" + string.Format("price:{0}, orderNo:{1}, remark:{2}, account:{3}", price, orderNo, Request.QueryString["remark"], account));

                if (!BoxUtil.IsEmail(account) && !BoxUtil.IsMobile(account))
                {
                    Response.Write(UserConst.StrAccountFormatError.GetTrans(lang));
                }
                else
                {
                    var result = PayUtil.GetPayUrl(price, orderNo, remark, account, from);
                    Response.Write(result);
                }
            }
            else if (strOP == "pay_BuDan")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.BuDan(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "pay_Del")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.DelOrder(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "getOrder")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.GetPayOrderInfo(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "checkOrder")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.CheckPayOrderState(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "changePayType")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var payType = BoxUtil.GetInt32FromObject(Request.Form["payType"]);
                var result = PayUtil.ChangePayType(orderId, payType);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "msg")
            {
                var msg = "";
                lock (objLock)
                {
                    if (lstMsg.Count > 0)
                    {
                        msg = lstMsg[0];
                        lstMsg.Remove(msg);
                    }
                }
                if (!string.IsNullOrEmpty(msg))
                {
                    Response.Write(msg);
                }
                else
                {
                    Response.Write("no");
                }
            }
            else if (strOP == "reg")
            {
                var account = Request.GetValue("account");
                if (string.IsNullOrEmpty(account))
                    account = Request.GetValue("app");

                var password = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                var nickname = BoxUtil.GetStringFromObject(Request.QueryString["nick"]);
                var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);

                // 使用统一的注册服务
                var result = Account.Web.Common.UnifiedUserService.RegisterUser(account, password, nickname, validateCode, lang);
                Response.Write(result.IsSuccess ? "True" : result.Message);
            }
            else if (strOP == "heart")
            {
                //if (!Request.UserAgent.StartsWith("OcrAgent"))
                //{
                //    Request.Log("疑似爬虫！");
                //    Response.Write("True");
                //    return;
                //}
                var uid = Request.GetValue("uid");
                var token = Request.GetValue("token");
                if (string.IsNullOrEmpty(token))
                {
                    if (string.IsNullOrEmpty(uid))
                    {
                        Request.Log("UID为空，验证失败！");
                        Response.Write("True");
                        return;
                    }
                    token = string.IsNullOrEmpty(token) ? uid : token;
                }
                var account = Request.GetValue("app");
                if (string.IsNullOrEmpty(account))
                {
                    account = Request.GetValue("account");
                }
                account = account.ToLower();
                var success = true;
                //检查设备是否被禁用
                if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
                {
                    if (CodeHelper.CheckIsForbid(account, token))
                    {
                        success = false;
                    }
                }
                if (success)
                {
                    var isTest = string.IsNullOrEmpty(account);
                    var testAccount = RdsCacheHelper.LstAccountCache.GetTestUserName(token);
                    account = isTest ? testAccount : account;

                    success = RdsCacheHelper.LstAccountCache.HeartBeat(account, token);
                    if (!isTest)
                    {
                        RdsCacheHelper.LstAccountCache.Remove(testAccount);
                    }
                    try
                    {
                        if (!string.IsNullOrEmpty(uid))
                        {
                            var version = Request.GetValue("version");
                            if (string.IsNullOrEmpty(version))
                            {
                                version = Request.GetValue("ver");
                            }
                            UserData data = new UserData()
                            {
                                Account = account,
                                Token = uid,
                                DtLast = ServerTime.DateTime,
                                Ip = HttpContext.Current.Items["nowIP"]?.ToString(),
                                Mac = Request.GetValue("mac"),
                                lang = Request.GetValue("lang"),
                                version = version,
                            };
                            if (!string.IsNullOrEmpty(data.Mac))
                            {
                                data.Mac = HttpUtility.UrlDecode(data.Mac);
                            }
                            if (string.IsNullOrEmpty(data.Mac))
                            {
                                var old = CodeHelper.GetUserMac(uid);
                                if (old != null && old.Rows.Count > 0)
                                {
                                    if (!string.IsNullOrEmpty(old.Rows[0]["Ip"]?.ToString()))
                                        data.Ip = old.Rows[0]["Ip"]?.ToString();
                                    data.Mac = old.Rows[0]["Mac"]?.ToString();
                                    if (string.IsNullOrEmpty(data.version) && !string.IsNullOrEmpty(old.Rows[0]["version"]?.ToString()))
                                        data.version = old.Rows[0]["version"]?.ToString();
                                    if (string.IsNullOrEmpty(data.lang) && !string.IsNullOrEmpty(old.Rows[0]["lang"]?.ToString()))
                                        data.lang = old.Rows[0]["lang"]?.ToString();
                                }
                                if (string.IsNullOrEmpty(data.Mac))
                                    Request.Log("Mac为空！");
                            }
                            CodeHelper.AddUserData(data);
                        }
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("Heart失败", oe);
                    }
                }
                Response.Write(success.ToString());
            }
            else if (strOP == "frpc")
            {
                try
                {
                    var strData = Request.Form["frpc"];
                    if (!string.IsNullOrEmpty(strData))
                    {
                        var data = JsonConvert.DeserializeObject<List<FrpcEntity>>(strData);
                        CodeHelper.AddOrUpdateFrpc(data);
                    }
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("Frpc保存失败", oe);
                }
                try
                {
                    var dtTmp = new DataTable();
                    var mainSite = CodeHelper.GetFastSite(ref dtTmp);
                    var strTmp = JsonConvert.SerializeObject(mainSite).Replace(",\"IsDefault\":false", "").Replace(",\"Host\":null", "");
                    QiNiuUpload.UploadFile(strTmp, "newSite.json");
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("生成Site失败:" + oe.Message);
                }
                Response.Write("True");
            }
            else if (strOP == "count")
            {
                var account = Request.GetValue("app");
                if (string.IsNullOrEmpty(account))
                {
                    account = Request.GetValue("account");
                }
                if (string.IsNullOrEmpty(account))
                {
                    var identity = Request.GetValue("uid");
                    if (!string.IsNullOrEmpty(identity))
                    {
                        account = RdsCacheHelper.LstAccountCache.GetTestUserName(identity);
                    }
                }

                var codeCount = new UserCodeCount { Account = account };
                if (!string.IsNullOrEmpty(account))
                {
                    codeCount.TodayCount = RdsCacheHelper.CodeRecordCache.GetUserCodeInfo(account).TodayCount;

                    var userLoginInfo = RdsCacheHelper.LstAccountCache.GetUserInfo(account);
                    if (userLoginInfo != null)
                    {
                        var userType = UserTypeHelper.GetUserType(userLoginInfo.UserType);
                        codeCount.LimitCount = userType.LimitPerDayCount;
                    }
                }
                Response.Write(JsonConvert.SerializeObject(codeCount));
            }
            else if (strOP == "regtype")
            {
                var lstUserType = GetCanRegUserTypes();
                Response.Write(JsonConvert.SerializeObject(lstUserType));
            }
            else if (strOP == "login")
            {
                var isWeb = !string.IsNullOrEmpty(Request.QueryString["isweb"]);
                if (!isWeb && !CommonHelper.IsValidateVersion(Request))
                {
                    ResponseWriteByIsWeb(ConfigHelper.MinVersionStr);
                    return;
                }

                var identity = isWeb ? Guid.NewGuid().ToString().Replace("-", "") : Request.GetValue("uid");
                var account = Request.GetValue("account");
                var password = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                var strWebLoginUrl = "Login.aspx";

                // 使用统一的登录服务
                var result = Account.Web.Common.UnifiedUserService.LoginUser(account, password, identity, isWeb, lang);

                if (result.IsSuccess)
                {
                    var loginInfo = result.Data;
                    if (isWeb)
                    {
                        Response.Cookies.Add(new HttpCookie("token", loginInfo.Token) { Expires = ServerTime.DateTime.AddHours(3) });
                        Response.Cookies.Add(new HttpCookie("account", loginInfo.Account) { Expires = ServerTime.DateTime.AddHours(3) });
                        Response.Redirect("User.aspx");
                    }
                    else
                    {
                        // 客户端不需要返回敏感信息
                        loginInfo.LstToken = null;
                        Response.Write("True|" + JsonConvert.SerializeObject(loginInfo));
                    }
                }
                else
                {
                    ResponseWriteByIsWeb(result.Message, isWeb, strWebLoginUrl);
                }
            }
            else if (strOP == "resetpwd")
            {
                var account = Request.GetValue("account");
                if (string.IsNullOrEmpty(account))
                    account = Request.GetValue("app");

                var password = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);

                // 使用统一的密码重置服务
                var result = Account.Web.Common.UnifiedUserService.ResetPassword(account, password, validateCode, lang);
                Response.Write(result.IsSuccess ? "True" : result.Message);
            }
            else if (strOP == "resetnickname")
            {
                var account = Request.GetValue("account");
                if (string.IsNullOrEmpty(account))
                    account = Request.GetValue("app");

                var nickname = Request.QueryString["nick"];

                // 使用统一的昵称修改服务
                var result = Account.Web.Common.UnifiedUserService.UpdateNickname(account, nickname, lang);
                Response.Write(result.IsSuccess ? "True" : result.Message);
            }
            else
            {
                switch (strOP)
                {
                    case "del":
                        if (CodeHelper.DelByAppCode(code))
                        {
                            Response.Write("删除成功！");
                        }
                        else
                        {
                            Response.Write("删除失败！");
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        internal class UserCodeCount
        {
            public string Account { get; set; }

            public long TodayCount { get; set; }

            public long LimitCount { get; set; }
        }

        public static List<UserType> GetCanRegUserTypes(bool isApi = false)
        {
            var lstUserType = isApi ? UserTypeHelper.GetCanRegApiTypes() : UserTypeHelper.GetCanRegUserTypes();
            lstUserType.ForEach(p =>
            {
                var lstChargeType = new List<ChargeViewToUser>();
                p.ChargeTypes.ForEach(q =>
                {
                    string strDesc = "";
                    var price = q.GetPrice(p.PerPrice, ref strDesc);
                    var charge = new ChargeViewToUser()
                    {
                        Name = q.Name,
                        Desc = strDesc,
                        Price = (double)price,
                        OriPrice = (double)q.OriPrice,
                        IsDefault = q.IsDefault,
                        Tag = q.Tag,
                    };
                    lstChargeType.Add(charge);
                });
                p.UserChargeType = lstChargeType;
            });
            return lstUserType;
        }

        private void Add(CodeEntity code)
        {
            if (CodeHelper.AddOrUpdateCode(code))
            {
                RdsCacheHelper.LstAccountCache.Remove(code.StrAppCode);
            }
        }
    }
}