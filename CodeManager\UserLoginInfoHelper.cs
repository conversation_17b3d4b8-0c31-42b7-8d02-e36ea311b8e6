﻿using CommonLib;
using System;
using System.Linq;
using System.Web;

namespace Account.Web
{
    public class UserLoginInfoHelper
    {
        public static UserLoginInfo GetUserInfo(HttpRequest Request, HttpResponse Response = null)
        {
            UserLoginInfo user = null;
            var strUser = Request.Cookies["account"]?.Value ?? "";
            var strToken = Request.Cookies["token"]?.Value ?? "";
            if (!string.IsNullOrEmpty(strUser) && !string.IsNullOrEmpty(strToken))
            {
                user = RdsCacheHelper.LstAccountCache.GetUserInfo(strUser);
                if (user != null)
                {
                    user = user.LstToken?.Any(p => Equals(p.Token, strToken)) == true ? user : null;
                }
            }
            if (Response != null && (user == null || string.IsNullOrEmpty(user.Account)))
            {
                Response.Redirect("Login.aspx");
            }
            return user;
        }

        /// <summary>
        /// 清空所有cookie
        /// </summary>
        /// <param name="contenxt">The contenxt.</param>
        public static void LoginOut(HttpContext contenxt)
        {
            var user = GetUserInfo(contenxt.Request);
            if (user != null && !string.IsNullOrEmpty(user.Account))
            {
                RdsCacheHelper.LstAccountCache.Remove(user.Account);
            }
            //清除cookies
            for (int i = 0; i < contenxt.Request.Cookies.Count; i++)
            {
                contenxt.Response.Cookies[contenxt.Request.Cookies[i].Name].Expires = DateTime.Now.AddDays(-1);
            }
        }
    }
}