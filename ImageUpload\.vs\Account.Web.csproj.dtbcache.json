{"RootPath": "D:\\Code\\Code\\CodeManager\\CodeManager", "ProjectFileName": "Account.Web.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AllUser.aspx.cs"}, {"SourceFile": "AllUser.aspx.designer.cs"}, {"SourceFile": "BLL\\CommonHelper.cs"}, {"SourceFile": "Code.ashx.cs"}, {"SourceFile": "Code.aspx.cs"}, {"SourceFile": "Code.aspx.designer.cs"}, {"SourceFile": "Common\\CommonValidateCode.cs"}, {"SourceFile": "Common\\QiNiuUpload.cs"}, {"SourceFile": "Common\\SqlChecker.cs"}, {"SourceFile": "Default.aspx.cs"}, {"SourceFile": "Site.Master.cs"}, {"SourceFile": "Site.Master.designer.cs"}, {"SourceFile": "ToPay.aspx.cs"}, {"SourceFile": "ToPay.aspx.designer.cs"}, {"SourceFile": "UserUpgrade.aspx.cs"}, {"SourceFile": "UserUpgrade.aspx.designer.cs"}, {"SourceFile": "UserMac.aspx.cs"}, {"SourceFile": "UserMac.aspx.designer.cs"}, {"SourceFile": "UserMaster.Master.cs"}, {"SourceFile": "UserMaster.Master.designer.cs"}, {"SourceFile": "UserIndex.aspx.cs"}, {"SourceFile": "UserIndex.aspx.designer.cs"}, {"SourceFile": "UserReg.aspx.cs"}, {"SourceFile": "UserReg.aspx.designer.cs"}, {"SourceFile": "UserForgetPwd.aspx.cs"}, {"SourceFile": "UserForgetPwd.aspx.designer.cs"}, {"SourceFile": "UserLogin.aspx.cs"}, {"SourceFile": "UserLogin.aspx.designer.cs"}, {"SourceFile": "FrpcEntity.cs"}, {"SourceFile": "CodeEntity.cs"}, {"SourceFile": "BLL\\CodeHelper.cs"}, {"SourceFile": "CommonEncryptHelper.cs"}, {"SourceFile": "Common\\SyncTask.cs"}, {"SourceFile": "Common\\WebClientExt.cs"}, {"SourceFile": "Default.Master.cs"}, {"SourceFile": "Default.Master.designer.cs"}, {"SourceFile": "Detail.aspx.cs"}, {"SourceFile": "Detail.aspx.designer.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "IPList.aspx.cs"}, {"SourceFile": "IPList.aspx.designer.cs"}, {"SourceFile": "Mail.aspx.cs"}, {"SourceFile": "Mail.aspx.designer.cs"}, {"SourceFile": "MD5Helper.cs"}, {"SourceFile": "OPEntity.cs"}, {"SourceFile": "Pay.ashx.cs"}, {"SourceFile": "Pay.aspx.cs"}, {"SourceFile": "Pay.aspx.designer.cs"}, {"SourceFile": "PayUtil.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "SendMail.cs"}, {"SourceFile": "Server.aspx.cs"}, {"SourceFile": "Server.aspx.designer.cs"}, {"SourceFile": "SmtpEmailSend.cs"}, {"SourceFile": "Status.aspx.cs"}, {"SourceFile": "Status.aspx.designer.cs"}, {"SourceFile": "View.aspx.cs"}, {"SourceFile": "View.aspx.designer.cs"}, {"SourceFile": "RCode.aspx.cs"}, {"SourceFile": "RCode.aspx.designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\Code\\Code\\CommonLib\\bin\\Debug\\CommonLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\FreeSql.3.2.680\\lib\\net451\\FreeSql.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\FreeSql.Provider.Sqlite.3.2.680\\lib\\net45\\FreeSql.Provider.Sqlite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\ImageLib\\bin\\Debug\\ImageLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\K4os.Compression.LZ4.1.2.16\\lib\\net46\\K4os.Compression.LZ4.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\K4os.Hash.xxHash.1.0.7\\lib\\net46\\K4os.Hash.xxHash.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\DLL\\log4net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\Newtonsoft.Json.13.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\packages\\Qiniu.SDK.8.0.0\\lib\\net45\\Qiniu.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\Code\\Code\\RegLib\\bin\\Debug\\RegLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Code\\Code\\CodeManager\\CodeManager\\bin\\Account.Web.dll", "OutputItemRelativePath": "Account.Web.dll"}, {"OutputItemFullPath": "D:\\Code\\Code\\CodeManager\\CodeManager\\bin\\Account.Web.pdb", "OutputItemRelativePath": "Account.Web.pdb"}], "CopyToOutputEntries": []}