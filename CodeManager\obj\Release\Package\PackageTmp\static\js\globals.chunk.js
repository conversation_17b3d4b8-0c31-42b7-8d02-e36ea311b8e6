(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{1100:function(e,t){!function(){var t,n={days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","<PERSON>e","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],AM:"AM",PM:"PM",am:"am",pm:"pm",formats:{D:"%m/%d/%y",F:"%Y-%m-%d",R:"%H:%M",T:"%H:%M:%S",X:"%T",c:"%a %b %d %X %Y",r:"%I:%M:%S %p",v:"%e-%b-%Y",x:"%D"}},r=new function e(t,r,i){var o,s=t||n,a=r||0,c=i||!1,u=0;var l=function(e,t){var n;if(t)n=t.getTime(),c&&(t=new Date(t.getTime()+v(t)+a));else{var r=Date.now();r>u?(u=r,o=new Date(u),n=u,c&&(o=new Date(u+v(o)+a))):n=u,t=o}return function e(t,n,r,i){for(var o="",s=null,u=!1,l=t.length,f=!1,v=0;v<l;v++){var y=t.charCodeAt(v);if(!0!==u)37!==y?o+=t[v]:u=!0;else{if(45===y){s="";continue}if(95===y){s=" ";continue}if(48===y){s="0";continue}if(58===y){f&&"undefined"!=typeof console&&"function"==typeof console.warn&&console.warn("[WARNING] detected use of unsupported %:: or %::: modifiers to strftime"),f=!0;continue}switch(y){case 65:o+=r.days[n.getDay()];break;case 66:o+=r.months[n.getMonth()];break;case 67:o+=d(Math.floor(n.getFullYear()/100),s);break;case 68:o+=e(r.formats.D,n,r,i);break;case 70:o+=e(r.formats.F,n,r,i);break;case 72:o+=d(n.getHours(),s);break;case 73:o+=d(h(n.getHours()),s);break;case 76:o+=p(Math.floor(i%1e3));break;case 77:o+=d(n.getMinutes(),s);break;case 80:o+=n.getHours()<12?r.am:r.pm;break;case 82:o+=e(r.formats.R,n,r,i);break;case 83:o+=d(n.getSeconds(),s);break;case 84:o+=e(r.formats.T,n,r,i);break;case 85:o+=d(m(n,"sunday"),s);break;case 87:o+=d(m(n,"monday"),s);break;case 88:o+=e(r.formats.X,n,r,i);break;case 89:o+=n.getFullYear();break;case 90:if(c&&0===a)o+="GMT";else{var _=n.toString().match(/\(([\w\s]+)\)/);o+=_&&_[1]||""}break;case 97:o+=r.shortDays[n.getDay()];break;case 98:o+=r.shortMonths[n.getMonth()];break;case 99:o+=e(r.formats.c,n,r,i);break;case 100:o+=d(n.getDate(),s);break;case 101:o+=d(n.getDate(),null==s?" ":s);break;case 104:o+=r.shortMonths[n.getMonth()];break;case 106:var b=new Date(n.getFullYear(),0,1),w=Math.ceil((n.getTime()-b.getTime())/864e5);o+=p(w);break;case 107:o+=d(n.getHours(),null==s?" ":s);break;case 108:o+=d(h(n.getHours()),null==s?" ":s);break;case 109:o+=d(n.getMonth()+1,s);break;case 110:o+="\n";break;case 111:o+=String(n.getDate())+g(n.getDate());break;case 112:o+=n.getHours()<12?r.AM:r.PM;break;case 114:o+=e(r.formats.r,n,r,i);break;case 115:o+=Math.floor(i/1e3);break;case 116:o+="\t";break;case 117:w=n.getDay();o+=0===w?7:w;break;case 118:o+=e(r.formats.v,n,r,i);break;case 119:o+=n.getDay();break;case 120:o+=e(r.formats.x,n,r,i);break;case 121:o+=(""+n.getFullYear()).slice(2);break;case 122:if(c&&0===a)o+=f?"+00:00":"+0000";else{var I,E=(I=0!==a?a/6e4:-n.getTimezoneOffset())<0?"-":"+",T=f?":":"",k=Math.floor(Math.abs(I/60)),C=Math.abs(I%60);o+=E+d(k)+T+d(C)}break;default:o+=t[v]}s=null,u=!1}}return o}(e,t,s,n)};return l.localize=function(t){return new e(t||s,a,c)},l.timezone=function(t){var n=a,r=c,i=typeof t;"number"!==i&&"string"!==i||(r=!0,"string"===i?n=("-"===t[0]?-1:1)*(60*parseInt(t.slice(1,3),10)+parseInt(t.slice(3,5),10))*60*1e3:"number"===i&&(n=60*t*1e3));return new e(s,n,r)},l.utc=function(){return new e(s,a,!0)},l}(n,0,!1),i=void 0!==e;i?(t=e.exports=u).strftime=l:(t=function(){return this||(0,eval)("this")}()).strftime=u;var o=i?"require('strftime')":"strftime",s={};function a(e,t){s[e]||("undefined"!=typeof console&&"function"==typeof console.warn&&console.warn("[WARNING] "+e+" is deprecated and will be removed in version 1.0. Instead, use `"+t+"`."),s[e]=!0)}function c(e){e.localize=r.localize.bind(r),e.timezone=r.timezone.bind(r),e.utc=r.utc.bind(r)}function u(e,t,n){return t&&t.days&&(n=t,t=void 0),n&&a("`"+o+"(format, [date], [locale])`","var s = "+o+".localize(locale); s(format, [date])"),(n?r.localize(n):r)(e,t)}function l(e,t,n){return n?a("`"+o+".strftime(format, [date], [locale])`","var s = "+o+".localize(locale); s(format, [date])"):a("`"+o+".strftime(format, [date])`",o+"(format, [date])"),(n?r.localize(n):r)(e,t)}t.strftimeTZ=function(e,t,n,i){"number"!=typeof n&&"string"!=typeof n||null!=i||(i=n,n=void 0);n?a("`"+o+".strftimeTZ(format, date, locale, tz)`","var s = "+o+".localize(locale).timezone(tz); s(format, [date])` or `var s = "+o+".localize(locale); s.timezone(tz)(format, [date])"):a("`"+o+".strftimeTZ(format, date, tz)`","var s = "+o+".timezone(tz); s(format, [date])` or `"+o+".timezone(tz)(format, [date])");return(n?r.localize(n):r).timezone(i)(e,t)},t.strftimeUTC=function(e,t,n){n?a("`"+o+".strftimeUTC(format, date, locale)`","var s = "+o+".localize(locale).utc(); s(format, [date])"):a("`"+o+".strftimeUTC(format, [date])`","var s = "+o+".utc(); s(format, [date])");return(n?f.localize(n):f)(e,t)},t.localizedStrftime=function(e){return a("`"+o+".localizedStrftime(locale)`",o+".localize(locale)"),r.localize(e)},c(u),c(l);var f=r.utc();function d(e,t){return""===t||e>9?e:(null==t&&(t="0"),t+e)}function p(e){return e>99?e:e>9?"0"+e:"00"+e}function h(e){return 0===e?12:e>12?e-12:e}function m(e,t){t=t||"sunday";var n=e.getDay();"monday"===t&&(0===n?n=6:n--);var r=Date.UTC(e.getFullYear(),0,1),i=Date.UTC(e.getFullYear(),e.getMonth(),e.getDate()),o=(Math.floor((i-r)/864e5)+7-n)/7;return Math.floor(o)}function g(e){var t=e%10,n=e%100;if(n>=11&&n<=13||0===t||t>=4)return"th";switch(t){case 1:return"st";case 2:return"nd";case 3:return"rd"}}function v(e){return 6e4*(e.getTimezoneOffset()||0)}"function"!=typeof Date.now&&(Date.now=function(){return+new Date})}()},1101:function(e,t,n){"use strict";t.a=function(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){return t.reject(n)}))}))}},1102:function(e,t,n){"use strict";t.a=function(e){return new this((function(t,n){if(!e||void 0===e.length)return n(new TypeError(typeof e+" "+e+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,n){if(n&&("object"==typeof n||"function"==typeof n)){var s=n.then;if("function"==typeof s)return void s.call(n,(function(t){o(e,t)}),(function(n){r[e]={status:"rejected",reason:n},0==--i&&t(r)}))}r[e]={status:"fulfilled",value:n},0==--i&&t(r)}for(var s=0;s<r.length;s++)o(s,r[s])}))}},1103:function(e,t,n){"use strict";(function(t){var r=t.analytics,i=n(310).Alias,o=n(523),s=n(310),a=n(310).Group,c=n(310).Identify,u=n(906).SourceMiddlewareChain,l=n(906).IntegrationMiddlewareChain,f=n(310).Page,d=n(310).Track,p=n(313),h=n(351),m=n(1793),g=n(427),v=n(1800),y=n(314),_=n(352),b=n(209),w=n(676),I=n(1808),E=n(521),T=n(1810),k=n(418),C=n(912),x=n(524),S=n(1811),A=n(1814).bind,O=n(1815),j=n(1817),M=n(1818),D=n(680),R=n(678),N=n(1821),P=n(312);function L(){this._options({}),this.Integrations={},this._sourceMiddlewares=new u,this._integrationMiddlewares=new l,this._integrations={},this._readied=!1,this._timeout=300,this._user=N,this.log=y("analytics.js"),p(this);var e=this;this.on("initialize",(function(t,n){n.initialPageview&&e.page(),e._parseQuery(window.location.search)}))}o(L.prototype),L.prototype.use=function(e){return e(this),this},L.prototype.addIntegration=function(e){var t=e.prototype.name;if(!t)throw new TypeError("attempted to add an invalid integration");return this.Integrations[t]=e,this},L.prototype.addSourceMiddleware=function(e){if(this.initialized)throw new Error("attempted to add a source middleware after initialization");return this._sourceMiddlewares.add(e),this},L.prototype.addIntegrationMiddleware=function(e){if(this.initialized)throw new Error("attempted to add an integration middleware after initialization");return this._integrationMiddlewares.add(e),this},L.prototype.init=L.prototype.initialize=function(e,t){e=e||{},t=t||{},this._options(t),this._readied=!1;var n=this;b((function(t,r){n.Integrations[r]||delete e[r]}),e),b((function(e,r){if(!t.integrations||!1!==t.integrations[r]&&(!1!==t.integrations.All||t.integrations[r])){var i=n.Integrations[r],o={};m(!0,o,e);var s=new i(o);n.log("initialize %o - %o",r,e),n.add(s)}}),e);var r=this._integrations;N.load(),I.load();var i=0,o=k(r).length,s=function(){++i>=o&&(n._readied=!0,n.emit("ready"))};o<=0&&s(),this.failedInitializations=[];var a=!1;return b((function(e){if(t.initialPageview&&!1===e.options.initialPageview){var r=e.page;e.page=function(){if(a)return r.apply(this,arguments);a=!0}}e.analytics=n,e.once("ready",s);try{v.increment("analytics_js.integration.invoke",{method:"initialize",integration_name:e.name}),e.initialize()}catch(t){var i=e.name;v.increment("analytics_js.integration.invoke.error",{method:"initialize",integration_name:e.name}),n.failedInitializations.push(i),n.log("Error initializing %s integration: %o",i,t),e.ready()}}),r),this.initialized=!0,this.emit("initialize",e,t),this},L.prototype.setAnonymousId=function(e){return this.user().anonymousId(e),this},L.prototype.add=function(e){return this._integrations[e.name]=e,this},L.prototype.identify=function(e,t,n,r){E.fn(n)&&(r=n,n=null),E.fn(t)&&(r=t,n=null,t=null),E.object(e)&&(n=t,t=e,e=N.id()),N.identify(e,t);var i=this.normalize({options:n,traits:N.traits(),userId:N.id()});return this.options.integrations&&_(i.integrations,this.options.integrations),this._invoke("identify",new c(i)),this.emit("identify",e,t,n),this._callback(r),this},L.prototype.user=function(){return N},L.prototype.group=function(e,t,n,r){if(!arguments.length)return I;E.fn(n)&&(r=n,n=null),E.fn(t)&&(r=t,n=null,t=null),E.object(e)&&(n=t,t=e,e=I.id()),I.identify(e,t);var i=this.normalize({options:n,traits:I.traits(),groupId:I.id()});return this.options.integrations&&_(i.integrations,this.options.integrations),this._invoke("group",new a(i)),this.emit("group",e,t,n),this._callback(r),this},L.prototype.track=function(e,t,n,r){E.fn(n)&&(r=n,n=null),E.fn(t)&&(r=t,n=null,t=null);var i=this.options.plan||{},o=i.track||{},s={},a=this.normalize({properties:t,options:n,event:e});(i=o[e])?(this.log("plan %o - %o",e,i),s=!1===i.enabled?{All:!1,"Segment.io":!0}:i.integrations||{}):(o.__default||{enabled:!0}).enabled||(s={All:!1,"Segment.io":!0});return _(a.integrations,this._mergeInitializeAndPlanIntegrations(s)),this._invoke("track",new d(a)),this.emit("track",e,t,n),this._callback(r),this},L.prototype.trackClick=L.prototype.trackLink=function(e,t,n){if(!e)return this;"element"===P(e)&&(e=[e]);var r=this;return b((function(e){if("element"!==P(e))throw new TypeError("Must pass HTMLElement to `analytics.trackLink`.");A(e,"click",(function(i){var o=E.fn(t)?t(e):t,s=E.fn(n)?n(e):n,a=e.getAttribute("href")||e.getAttributeNS("http://www.w3.org/1999/xlink","href")||e.getAttribute("xlink:href");r.track(o,s),a&&"_blank"!==e.target&&!T(i)&&(M(i),r._callback((function(){window.location.href=a})))}))}),e),this},L.prototype.trackSubmit=L.prototype.trackForm=function(e,t,n){if(!e)return this;"element"===P(e)&&(e=[e]);var r=this;return b((function(e){if("element"!==P(e))throw new TypeError("Must pass HTMLElement to `analytics.trackForm`.");function i(i){M(i);var o=E.fn(t)?t(e):t,s=E.fn(n)?n(e):n;r.track(o,s),r._callback((function(){e.submit()}))}var o=window.jQuery||window.Zepto;o?o(e).submit(i):A(e,"submit",i)}),e),this},L.prototype.page=function(e,t,n,r,i){E.fn(r)&&(i=r,r=null),E.fn(n)&&(i=n,r=n=null),E.fn(t)&&(i=t,r=n=t=null),"object"===P(e)&&(r=t,n=e,t=e=null),"object"===P(t)&&(r=n,n=t,t=null),"string"===P(e)&&"string"!==P(t)&&(t=e,e=null),n=h(n)||{},t&&(n.name=t),e&&(n.category=e);var o=O();_(n,o);var s=j(k(o),n);E.empty(s)||((r=r||{}).context=r.context||{},r.context.page=s);var a=this.normalize({properties:n,category:e,options:r,name:t});return this.options.integrations&&_(a.integrations,this.options.integrations),this._invoke("page",new f(a)),this.emit("page",e,t,n,r),this._callback(i),this},L.prototype.pageview=function(e){var t={};return e&&(t.path=e),this.page(t),this},L.prototype.alias=function(e,t,n,r){E.fn(n)&&(r=n,n=null),E.fn(t)&&(r=t,n=null,t=null),E.object(t)&&(n=t,t=null);var o=this.normalize({options:n,previousId:t,userId:e});return this.options.integrations&&_(o.integrations,this.options.integrations),this._invoke("alias",new i(o)),this.emit("alias",e,t,n),this._callback(r),this},L.prototype.ready=function(e){return E.fn(e)&&(this._readied?x(e):this.once("ready",e)),this},L.prototype.timeout=function(e){this._timeout=e},L.prototype.debug=function(e){!arguments.length||e?y.enable("analytics:"+(e||"*")):y.disable()},L.prototype._options=function(e){return e=e||{},this.options=e,g.options(e.cookie),v.options(e.metrics),R.options(e.localStorage),N.options(e.user),I.options(e.group),this},L.prototype._callback=function(e){return E.fn(e)&&(this._timeout?setTimeout(e,this._timeout):x(e)),this},L.prototype._invoke=function(e,t){var n=this;try{this._sourceMiddlewares.applyMiddlewares(m(!0,new s({}),t),this._integrations,(function(t){null!==t?(t instanceof s||(t=new s(t)),n.emit("invoke",t),v.increment("analytics_js.invoke",{method:e}),function(t){var r=n.failedInitializations||[];b((function(i,o){var a=m(!0,new s({}),t);if(a.enabled(o))if(r.indexOf(o)>=0)n.log("Skipping invocation of .%s method of %s integration. Integration failed to initialize properly.",e,o);else try{n._integrationMiddlewares.applyMiddlewares(a,i.name,(function(t){null!==t?(t instanceof s||(t=new s(t)),v.increment("analytics_js.integration.invoke",{method:e,integration_name:i.name}),i.invoke.call(i,e,t)):n.log('Payload to integration "%s" was null and dropped by a middleware.',o)}))}catch(t){v.increment("analytics_js.integration.invoke.error",{method:e,integration_name:i.name}),n.log("Error invoking .%s method of %s integration: %o",e,o,t)}}),n._integrations)}(t)):n.log('Payload with method "%s" was null and dropped by source a middleware.',e)}))}catch(t){v.increment("analytics_js.invoke.error",{method:e}),n.log("Error invoking .%s method of %s integration: %o",e,name,t)}return this},L.prototype.push=function(e){var t=e.shift();this[t]&&this[t].apply(this,e)},L.prototype.reset=function(){this.user().logout(),this.group().logout()},L.prototype._parseQuery=function(e){var t=D.parse(e),n=i("ajs_trait_",t),r=i("ajs_prop_",t);return t.ajs_uid&&this.identify(t.ajs_uid,n),t.ajs_event&&this.track(t.ajs_event,r),t.ajs_aid&&N.anonymousId(t.ajs_aid),this;function i(e,t){var n=e.length;return w((function(t,r,i){return i.substr(0,n)===e&&(t[i.substr(n)]=r),t}),{},t)}},L.prototype.normalize=function(e){return(e=S(e,k(this._integrations))).anonymousId&&N.anonymousId(e.anonymousId),e.anonymousId=N.anonymousId(),e.context.page=_(e.context.page||{},O()),e},L.prototype._mergeInitializeAndPlanIntegrations=function(e){if(!this.options.integrations)return e;var t,n=m({},this.options.integrations);for(t in!1===e.All&&(n={All:!1}),e)e.hasOwnProperty(t)&&!1!==this.options.integrations[t]&&(n[t]=e[t]);return n},L.prototype.noConflict=function(){return window.analytics=r,this},e.exports=L,e.exports.cookie=g,e.exports.memory=C,e.exports.store=R,e.exports.metrics=v}).call(this,n(97))},1104:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},i=n(1871),o=n(1872),s=n(1873),a=n(677),c=n(721),u=n(353),l=n(418),f=n(1877),d=n(914).hash,p=n(1879),h=n(910),m=n(908),g=n(1880),v=n(1881).v4,y=n(2104).default,_=n(1886).default,b={maxage:31536e6,secure:"https:"===window.location.protocol,path:"/"},w=c("Segment.io").option("apiKey","").option("apiScheme","https").option("apiHost","api.segment.io/v1").option("crossDomainIdServers",[]).option("retryQueue",!0).option("retryQueuePrefix","awc").option("addBundledMetadata",!1).option("unbundledIntegrations",[]).option("retryQueueOptions",{maxRetryDelay:6e4,minRetryDelay:1e3,backoffFactor:2,maxAttempts:10,maxItems:100}).option("requestTimeout",1e4);function I(e){return e.split(".").splice(-2).join(".")}function E(e,t,n){!function(e,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.withCredentials=!0,n.onreadystatechange=function(){n.readyState===XMLHttpRequest.DONE&&(n.status>=200&&n.status<300?t(null,n.responseText?u.parse(n.responseText):null):t(n.statusText||"Unknown Error",null))},n.send()}("https://"+e+"/v1/id/"+t,(function(t,r){t?n(t,null):n(null,{domain:e,id:r&&r.id||null})}))}t=w,e.exports=w,t.storage=function(){return"file:"===p()||"chrome-extension:"===p()?f:s},t.global=window,t.sendJsonWithTimeout=function(e,t,n,i,o){if("xhr"===h.type){var s=new XMLHttpRequest;s.onerror=o,s.onreadystatechange=function(){4===s.readyState&&(429===s.status||s.status>=500&&s.status<600?o(new Error("HTTP Error "+s.status+" ("+s.statusText+")")):o(null,s))},s.withCredentials=!0,s.open("POST",e,!0),s.timeout=i,s.ontimeout=o,Object.entries(n).forEach((function(e){var t=r(e,2),n=t[0],i=t[1];s.setRequestHeader(n,i)})),s.send(u.stringify(t))}else h(e,t,n,o)},w.prototype.attachXid=function(e,t){if(t&&t instanceof Function){var n;if(e&&e.msg&&e.msg.properties&&["screen","ui","operational","track"].indexOf(e.msg.properties.eventType)>-1)(n=t())&&Array.isArray(n)&&(e.msg.properties.xid=n);if(e&&e.msg&&e.msg.traits&&e.msg.traits.userIdType&&"identify"===e.msg.type)(n=t())&&Array.isArray(n)&&(e.msg.traits.xid=n)}return e},w.prototype.attachXids=function(e){var t=this;return this._xidPromiseCallback?this._xidPromiseCallback.then((function(n){return Array.isArray(e)?e.map((function(e){return t.attachXid(e,n)})):t.attachXid(e,n)})).catch((function(){return e})):e},w.prototype.proccessBatchItems=function(e,t){var n=this,r=this,i=this._lsqueue.getDiscardCounter(),o=this._lsqueue.getOverflowCounter(),s=this._lsqueue.getGlobalRetryCount(),a=this._lsqueue.getDuplicateCounter(),c=function(e){var c=(new Date).toISOString(),u={batch:e.map((function(e){return e.msg.sentAt=c,e.msg})),sentAt:c,metadata:{itemsDiscardedByRetry:i,itemsDiscardedByOverflow:o,httpRetryCount:s,duplicateEventCount:a}},l=n.options.apiScheme+"://"+n.options.apiHost+"/batch";return w.sendJsonWithTimeout(l,u,{"Content-Type":"text/plain"},n.options.requestTimeout,(function(e,n){return r.debug("sent %O, received %O",u,[e,n]),e?t(e):t(null,n)}))};return this._xidPromiseCallback?this.attachXids(e).then((function(e){c(e)})):c(e)},w.prototype.initialize=function(){var e=this;this.options.retryQueue&&(this._lsqueue=new y(this.options.retryQueuePrefix,this.options.retryQueueOptions,this.proccessBatchItems.bind(this)),this._lsqueue.start()),this.options.xidPromiseGetter&&(this._xidPromiseCallback=this.options.xidPromiseGetter()),this.ready(),this.analytics.on("invoke",(function(t){var n=t.action(),r="on"+t.action();e.debug("%s %o",n,t),e[r]&&e[r](t),e.ready()})),this.cookie("segment_cross_domain_id")&&(this.cookie("seg_xid",this.cookie("segment_cross_domain_id")),this.cookie("seg_xid_fd",this.cookie("segment_cross_domain_id_from_domain")),this.cookie("seg_xid_ts",this.cookie("segment_cross_domain_id_timestamp")),this.cookie("segment_cross_domain_id",null),this.cookie("segment_cross_domain_id_from_domain",null),this.cookie("segment_cross_domain_id_timestamp",null)),this.options.crossDomainIdServers&&this.options.crossDomainIdServers.length>0&&this.retrieveCrossDomainId(),_(this.options.retryQueuePrefix)},w.prototype.loaded=function(){return!0},w.prototype.onpage=function(e){this.enqueue("/p",e.json())},w.prototype.onidentify=function(e){this.enqueue("/i",e.json())},w.prototype.ongroup=function(e){this.enqueue("/g",e.json())},w.prototype.ontrack=function(e){var t=e.json();delete t.traits,this.enqueue("/t",t)},w.prototype.onalias=function(e){var t=e.json(),n=this.analytics.user();t.previousId=t.previousId||t.from||n.id()||n.anonymousId(),t.userId=t.userId||t.to,delete t.from,delete t.to,this.enqueue("/a",t)},w.prototype.normalize=function(e){this.debug("normalize %o",e);var n=this.analytics.user(),r=t.global.location.search;e.context=e.context||e.options||{};var i=e.context;delete e.options,e.writeKey=this.options.apiKey,i.userAgent=navigator.userAgent,i.library||(i.library={name:"analytics.js",version:this.analytics.VERSION});var o=this.cookie("seg_xid");o&&(i.traits?i.traits.crossDomainId||(i.traits.crossDomainId=o):i.traits={crossDomainId:o}),r&&!i.campaign&&(i.campaign=g(r)),this.referrerId(r,i),e.userId=e.userId||n.id(),e.anonymousId=n.anonymousId(),e.sentAt=new Date;var s=this.analytics.failedInitializations||[];if(s.length>0&&(e._metadata={failedInitializations:s}),this.options.addBundledMetadata){var a=l(this.analytics.Integrations);e._metadata=e._metadata||{},e._metadata.bundled=a,e._metadata.unbundled=this.options.unbundledIntegrations}return e.messageId="ajs-"+d(u.stringify(e)+v()),this.debug("normalized %o",e),this.ampId(i),e},w.prototype.ampId=function(e){var t=this.cookie("segment_amp_id");t&&(e.amp={id:t})},w.prototype.enqueue=function(e,t,n){var r=this.options.apiScheme+"://"+this.options.apiHost+e,i={"Content-Type":"text/plain"},o=this.normalize(t);u.stringify(o).length>32e3&&this.debug("message must be less than 32kb %O",o),this.debug("enqueueing %O",o);var s=this;this.options.retryQueue?this._lsqueue.addItem({url:r,headers:i,msg:o}):h(r,o,i,(function(e,t){if(s.debug("sent %O, received %O",o,[e,t]),n){if(e)return n(e);n(null,t)}}))},w.prototype.cookie=function(e,n){var r=w.storage();if(1===arguments.length)return r(e);var i=t.global,s=i.location.href,a="."+m(s);"."===a&&(a=""),this.debug("store domain %s -> %s",s,a);var c=o(b);c.domain=a,this.debug("store %s, %s, %o",e,n,c),r(e,n,c),r(e)||(delete c.domain,this.debug("fallback store %s, %s, %o",e,n,c),r(e,n,c))},w.prototype.referrerId=function(e,t){var n,r=this.cookie("s:context.referrer");r&&(r=u.parse(r)),e&&(n=i(e)),(n=n||r)&&(t.referrer=a(t.referrer||{},n),this.cookie("s:context.referrer",u.stringify(n)))},w.prototype.retrieveCrossDomainId=function(e){if(this.options.crossDomainIdServers){if(!this.cookie("seg_xid")){var t=this,n=this.options.apiKey,r=I(window.location.hostname),i=[];this.options.crossDomainIdServers.forEach((function(e){I(e)!==r&&i.push(e)})),function(e,t,n){0===e.length&&n(null,null);var r=!1,i=0,o=null;e.forEach((function(s){E(s,t,(function(t,s){i++,t?o=t:s&&s.id&&!r&&(r=!0,n(null,s)),i!==e.length||r||n(o,null)}))}))}(i,n,(function(n,r){if(n)e&&e(n,null);else{var i=null,o=null;r?(i=r.id,o=r.domain):(i=v(),o=window.location.hostname);var s=(new Date).getTime();t.cookie("seg_xid",i),t.cookie("seg_xid_fd",o),t.cookie("seg_xid_ts",s),t.analytics.identify({crossDomainId:i}),e&&e(null,{crossDomainId:i,fromDomain:o,timestamp:s})}}))}}else e&&e("crossDomainId not enabled",null)}},1105:function(e,t,n){"use strict";var r=n(164).v4,i=n(1883),o=n(209),s=n(1884),a=n(1885)("localstorage-retry");function c(e,t){return function(){return e.apply(t,arguments)}}function u(e,t,n){"function"==typeof t&&(n=t),this.name=e,this.id=r(),this.fn=n,this.maxItems=t.maxItems||1/0,this.maxAttempts=t.maxAttempts||1/0,this.backoff={MIN_RETRY_DELAY:t.minRetryDelay||1e3,MAX_RETRY_DELAY:t.maxRetryDelay||3e4,FACTOR:t.backoffFactor||2,JITTER:t.backoffJitter||0},this.timeouts={ACK_TIMER:1e3,RECLAIM_TIMER:3e3,RECLAIM_TIMEOUT:1e4,RECLAIM_WAIT:500},this.keys={IN_PROGRESS:"inProgress",QUEUE:"queue",RECLAIM_START:"reclaimStart",RECLAIM_END:"reclaimEnd",ACK:"ack"},this._schedule=new s,this._processId=0,this._store=new i(this.name,this.id,this.keys),this._store.set(this.keys.IN_PROGRESS,{}),this._store.set(this.keys.QUEUE,[]),this._ack=c(this._ack,this),this._checkReclaim=c(this._checkReclaim,this),this._processHead=c(this._processHead,this),this._running=!1}n(523)(u.prototype),u.prototype.start=function(){this._running&&this.stop(),this._running=!0,this._ack(),this._checkReclaim(),this._processHead()},u.prototype.stop=function(){this._schedule.cancelAll(),this._running=!1},u.prototype.shouldRetry=function(e,t){return!(t>this.maxAttempts)},u.prototype.getDelay=function(e){var t=this.backoff.MIN_RETRY_DELAY*Math.pow(this.backoff.FACTOR,e);if(this.backoff.JITTER){var n=Math.random(),r=Math.floor(n*this.backoff.JITTER*t);Math.floor(10*n)<5?t-=r:t+=r}return Number(Math.min(t,this.backoff.MAX_RETRY_DELAY).toPrecision(1))},u.prototype.addItem=function(e){this._enqueue({item:e,attemptNumber:0,time:this._schedule.now()})},u.prototype.requeue=function(e,t,n){this.shouldRetry(e,t,n)?this._enqueue({item:e,attemptNumber:t,time:this._schedule.now()+this.getDelay(t)}):this.emit("discard",e,t)},u.prototype._enqueue=function(e){var t=this._store.get(this.keys.QUEUE)||[];(t=t.slice(-(this.maxItems-1))).push(e),t=t.sort((function(e,t){return e.time-t.time})),this._store.set(this.keys.QUEUE,t),this._running&&this._processHead()},u.prototype._processHead=function(){var e=this,t=this._store;this._schedule.cancel(this._processId);var n=t.get(this.keys.QUEUE)||[],i=t.get(this.keys.IN_PROGRESS)||{},s=this._schedule.now(),c=[];function u(n,r){c.push({item:n.item,done:function(i,o){var s=t.get(e.keys.IN_PROGRESS)||{};delete s[r],t.set(e.keys.IN_PROGRESS,s),e.emit("processed",i,o,n.item),i&&e.requeue(n.item,n.attemptNumber+1,i)}})}for(var l=Object.keys(i).length;n.length&&n[0].time<=s&&l++<e.maxItems;){var f=n.shift(),d=r();i[d]={item:f.item,attemptNumber:f.attemptNumber,time:e._schedule.now()},u(f,d)}t.set(this.keys.QUEUE,n),t.set(this.keys.IN_PROGRESS,i),o((function(t){try{e.fn(t.item,t.done)}catch(e){a("Process function threw error: "+e)}}),c),n=t.get(this.keys.QUEUE)||[],this._schedule.cancel(this._processId),n.length>0&&(this._processId=this._schedule.run(this._processHead,n[0].time-s))},u.prototype._ack=function(){this._store.set(this.keys.ACK,this._schedule.now()),this._store.set(this.keys.RECLAIM_START,null),this._store.set(this.keys.RECLAIM_END,null),this._schedule.run(this._ack,this.timeouts.ACK_TIMER)},u.prototype._checkReclaim=function(){var e=this;o((function(t){t.id!==e.id&&(e._schedule.now()-t.get(e.keys.ACK)<e.timeouts.RECLAIM_TIMEOUT||function(t){t.set(e.keys.RECLAIM_START,e.id),t.set(e.keys.ACK,e._schedule.now()),e._schedule.run((function(){t.get(e.keys.RECLAIM_START)===e.id&&(t.set(e.keys.RECLAIM_END,e.id),e._schedule.run((function(){t.get(e.keys.RECLAIM_END)===e.id&&t.get(e.keys.RECLAIM_START)===e.id&&e._reclaim(t.id)}),e.timeouts.RECLAIM_WAIT))}),e.timeouts.RECLAIM_WAIT)}(t))}),function(t){for(var n=[],r=e._store.getOriginalEngine(),o=0;o<r.length;o++){var s=r.key(o).split(".");3===s.length&&(s[0]===t&&"ack"===s[2]&&n.push(new i(t,s[1],e.keys)))}return n}(this.name)),this._schedule.run(this._checkReclaim,this.timeouts.RECLAIM_TIMER)},u.prototype._reclaim=function(e){var t=this,n=new i(this.name,e,this.keys),r={queue:this._store.get(this.keys.QUEUE)||[]},s={inProgress:n.get(this.keys.IN_PROGRESS)||{},queue:n.get(this.keys.QUEUE)||[]};o((function(e){r.queue.push({item:e.item,attemptNumber:e.attemptNumber,time:t._schedule.now()})}),s.queue),o((function(e){r.queue.push({item:e.item,attemptNumber:e.attemptNumber+1,time:t._schedule.now()})}),s.inProgress),r.queue=r.queue.sort((function(e,t){return e.time-t.time})),this._store.set(this.keys.QUEUE,r.queue),n.remove(this.keys.IN_PROGRESS),n.remove(this.keys.QUEUE),n.remove(this.keys.RECLAIM_START),n.remove(this.keys.RECLAIM_END),n.remove(this.keys.ACK),this._processHead()},e.exports=u},1106:function(e,t,n){"use strict";(function(e){var n=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},r="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},i="object"==typeof r&&r&&r.Object===Object&&r,o="object"==typeof self&&self&&self.Object===Object&&self,s=i||o||Function("return this")(),a=function(){return s.Date.now()},c=s.Symbol,u=Object.prototype,l=u.hasOwnProperty,f=u.toString,d=c?c.toStringTag:void 0;var p=function(e){var t=l.call(e,d),n=e[d];try{e[d]=void 0;var r=!0}catch(e){}var i=f.call(e);return r&&(t?e[d]=n:delete e[d]),i},h=Object.prototype.toString;var m=function(e){return h.call(e)},g=c?c.toStringTag:void 0;var v=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":g&&g in Object(e)?p(e):m(e)};var y=function(e){return null!=e&&"object"==typeof e};var _=function(e){return"symbol"==typeof e||y(e)&&"[object Symbol]"==v(e)},b=/^\s+|\s+$/g,w=/^[-+]0x[0-9a-f]+$/i,I=/^0b[01]+$/i,E=/^0o[0-7]+$/i,T=parseInt;var k=function(e){if("number"==typeof e)return e;if(_(e))return NaN;if(n(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=n(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(b,"");var r=I.test(e);return r||E.test(e)?T(e.slice(2),r?2:8):w.test(e)?NaN:+e},C=Math.max,x=Math.min;var S=function(e,t,r){var i,o,s,c,u,l,f=0,d=!1,p=!1,h=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function m(t){var n=i,r=o;return i=o=void 0,f=t,c=e.apply(r,n)}function g(e){return f=e,u=setTimeout(y,t),d?m(e):c}function v(e){var n=e-l;return void 0===l||n>=t||n<0||p&&e-f>=s}function y(){var e=a();if(v(e))return _(e);u=setTimeout(y,function(e){var n=t-(e-l);return p?x(n,s-(e-f)):n}(e))}function _(e){return u=void 0,h&&i?m(e):(i=o=void 0,c)}function b(){var e=a(),n=v(e);if(i=arguments,o=this,l=e,n){if(void 0===u)return g(l);if(p)return u=setTimeout(y,t),m(l)}return void 0===u&&(u=setTimeout(y,t)),c}return t=k(t)||0,n(r)&&(d=!!r.leading,s=(p="maxWait"in r)?C(k(r.maxWait)||0,t):s,h="trailing"in r?!!r.trailing:h),b.cancel=function(){void 0!==u&&clearTimeout(u),f=0,i=l=o=u=void 0},b.flush=function(){return void 0===u?c:_(a())},b};var A=function(e,t,r){var i=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return n(r)&&(i="leading"in r?!!r.leading:i,o="trailing"in r?!!r.trailing:o),S(e,t,{leading:i,maxWait:t,trailing:o})},O=["scroll","resize"],j=["mousemove","keyup","keydown","touchstart","click","contextmenu"],M=function(){function e(e){var t=e.timeIntervalEllapsedCallbacks,n=e.absoluteTimeEllapsedCallbacks,r=e.checkCallbacksIntervalMs,i=e.browserTabInactiveCallbacks,o=e.browserTabActiveCallbacks,s=e.idleTimeoutMs,a=this;this.onBrowserTabInactive=function(e){a.isRunning()&&a.stopTimer(),a.browserTabInactiveCallbacks.forEach((function(e){return e(a.getTimeInMilliseconds())}))},this.onBrowserTabActive=function(e){a.isRunning()||a.startTimer(),a.browserTabActiveCallbacks.forEach((function(e){return e(a.getTimeInMilliseconds())}))},this.onTimePassed=function(){a.absoluteTimeEllapsedCallbacks.forEach((function(e,t){var n=e.callback,r=e.pending,i=e.timeInMilliseconds;r&&i<=a.getTimeInMilliseconds()&&(n(a.getTimeInMilliseconds()),a.absoluteTimeEllapsedCallbacks[t].pending=!1)})),a.timeIntervalEllapsedCallbacks.forEach((function(e,t){var n=e.callback,r=e.timeInMilliseconds,i=e.multiplier;r<=a.getTimeInMilliseconds()&&(n(a.getTimeInMilliseconds()),a.timeIntervalEllapsedCallbacks[t].timeInMilliseconds=i(r))})),a.currentIdleTimeMs>=a.idleTimeoutMs&&a.isRunning()?(a.idle=!0,a.stopTimer()):a.currentIdleTimeMs+=a.checkCallbacksIntervalMs},this.resetIdleTime=function(){a.idle&&a.startTimer(),a.idle=!1,a.currentIdleTimeMs=0},this.registerEventListeners=function(){var e={passive:!0};window.addEventListener("blur",a.onBrowserTabInactive,e),window.addEventListener("focus",a.onBrowserTabActive,e);var t=A(a.resetIdleTime,2e3,{leading:!0,trailing:!1});O.forEach((function(n){window.addEventListener(n,t,e)})),j.forEach((function(n){return document.addEventListener(n,t,e)}))},this.unregisterEventListeners=function(){window.removeEventListener("blur",a.onBrowserTabInactive),window.removeEventListener("focus",a.onBrowserTabActive),O.forEach((function(e){return window.removeEventListener(e,a.resetIdleTime)})),j.forEach((function(e){return document.removeEventListener(e,a.resetIdleTime)}))},this.checkCallbacksOnInterval=function(){a.checkCallbackIntervalId=window.setInterval((function(){a.onTimePassed()}),a.checkCallbacksIntervalMs)},this.startTimer=function(){a.checkCallbackIntervalId||a.checkCallbacksOnInterval();var e=a.times[a.times.length-1];e&&null===e.stop||(a.times.push({start:performance.now(),stop:null}),a.running=!0)},this.stopTimer=function(){a.times.length&&(a.times[a.times.length-1].stop=performance.now(),a.running=!1)},this.addTimeIntervalEllapsedCallback=function(e){a.timeIntervalEllapsedCallbacks.push(e)},this.addAbsoluteTimeEllapsedCallback=function(e){a.absoluteTimeEllapsedCallbacks.push(e)},this.addBrowserTabInactiveCallback=function(e){a.browserTabInactiveCallbacks.push(e)},this.addBrowserTabActiveCallback=function(e){a.browserTabActiveCallbacks.push(e)},this.getTimeInMilliseconds=function(){return a.times.reduce((function(e,t){return t.stop?e+=t.stop-t.start:e+=performance.now()-t.start,e}),0)},this.isRunning=function(){return a.running},this.reset=function(){a.times=[]},this.destroy=function(){a.unregisterEventListeners(),a.checkCallbackIntervalId&&window.clearInterval(a.checkCallbackIntervalId)},this.running=!1,this.times=[],this.idle=!1,this.currentIdleTimeMs=0,this.marks={},this.measures={},this.browserTabActiveCallbacks=o||[],this.browserTabInactiveCallbacks=i||[],this.checkCallbacksIntervalMs=r||100,this.idleTimeoutMs=s||3e3,this.timeIntervalEllapsedCallbacks=t||[],this.absoluteTimeEllapsedCallbacks=n||[],this.registerEventListeners()}return e.prototype.mark=function(e){this.marks[e]||(this.marks[e]=[]),this.marks[e].push({time:this.getTimeInMilliseconds()})},e.prototype.getMarks=function(e){if(!(this.marks[e].length<1))return this.marks[e]},e.prototype.measure=function(e,t,n){var r=this.marks[t],i=r[r.length-1],o=this.marks[n],s=o[o.length-1];this.measures[e]||(this.measures[e]=[]),this.measures[e].push({name:e,startTime:i.time,duration:s.time-i.time})},e.prototype.getMeasures=function(e){if(this.measures[e]||!(this.measures[e].length<1))return this.measures[e]},e}();t.a=M}).call(this,n(97))},124:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.UNAVAILABLE="UNAVAILABLE",t.ERROR="ERROR",t.YES="YES",t.NO="NO"},1781:function(e,t,n){n(2098),e.exports=n(237)},1782:function(e,t,n){"use strict";var r=n(520);e.exports=function(e){function t(e,t){return function(){var n=this.traits(),i=this.properties?this.properties():{};return r(n,"address."+e)||r(n,e)||(t?r(n,"address."+t):null)||(t?r(n,t):null)||r(i,"address."+e)||r(i,e)||(t?r(i,"address."+t):null)||(t?r(i,t):null)}}e.zip=t("postalCode","zip"),e.country=t("country"),e.street=t("street"),e.state=t("state"),e.city=t("city"),e.region=t("region")}},1783:function(e,t){var n=Object.prototype.toString;e.exports=function(e){switch(n.call(e)){case"[object Function]":return"function";case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Array]":return"array"}return null===e?"null":void 0===e?"undefined":e===Object(e)?"object":typeof e}},1784:function(e,t,n){"use strict";var r={Salesforce:!0};e.exports=function(e){return!r[e]}},1785:function(e,t,n){"use strict";var r=/\d{13}/;t.is=function(e){return r.test(e)},t.parse=function(e){return e=parseInt(e,10),new Date(e)}},1786:function(e,t,n){"use strict";var r=/\d{10}/;t.is=function(e){return r.test(e)},t.parse=function(e){var t=1e3*parseInt(e,10);return new Date(t)}},1787:function(e,t,n){var r;try{r=n(903)}catch(e){r=n(903)}function i(e){switch({}.toString.call(e)){case"[object Object]":return function(e){var t={};for(var n in e)t[n]="string"==typeof e[n]?o(e[n]):i(e[n]);return function(e){if("object"!=typeof e)return!1;for(var n in t){if(!(n in e))return!1;if(!t[n](e[n]))return!1}return!0}}(e);case"[object Function]":return e;case"[object String]":return/^ *\W+/.test(n=e)?new Function("_","return _ "+n):new Function("_","return "+function(e){var t,n,i,o=r(e);if(!o.length)return"_."+e;for(n=0;n<o.length;n++)i=o[n],e=s(i,e,t="('function' == typeof "+(t="_."+i)+" ? "+t+"() : "+t+")");return e}(n));case"[object RegExp]":return t=e,function(e){return t.test(e)};default:return o(e)}var t,n}function o(e){return function(t){return e===t}}function s(e,t,n){return t.replace(new RegExp("(\\.)?"+e,"g"),(function(e,t){return t?e:n}))}e.exports=i},1788:function(e,t,n){"use strict";var r=n(208).inherit,i=n(311);function o(e,t){i.call(this,e,t)}r(o,i),o.prototype.action=function(){return"alias"},o.prototype.type=o.prototype.action,o.prototype.previousId=function(){return this.field("previousId")||this.field("from")},o.prototype.from=o.prototype.previousId,o.prototype.userId=function(){return this.field("userId")||this.field("to")},o.prototype.to=o.prototype.userId,e.exports=o},1789:function(e,t,n){"use strict";var r=n(208).inherit,i=n(522),o=n(673),s=n(311);function a(e,t){s.call(this,e,t)}r(a,s),a.prototype.action=function(){return"group"},a.prototype.type=a.prototype.action,a.prototype.groupId=s.field("groupId"),a.prototype.created=function(){var e=this.proxy("traits.createdAt")||this.proxy("traits.created")||this.proxy("properties.createdAt")||this.proxy("properties.created");if(e)return o(e)},a.prototype.email=function(){var e=this.proxy("traits.email");if(e)return e;var t=this.groupId();return i(t)?t:void 0},a.prototype.traits=function(e){var t=this.properties(),n=this.groupId();for(var r in e=e||{},n&&(t.id=n),e){var i=null==this[r]?this.proxy("traits."+r):this[r]();null!=i&&(t[e[r]]=i,delete t[r])}return t},a.prototype.name=s.proxy("traits.name"),a.prototype.industry=s.proxy("traits.industry"),a.prototype.employees=s.proxy("traits.employees"),a.prototype.properties=function(){return this.field("traits")||this.field("properties")||{}},e.exports=a},1790:function(e,t){(t=e.exports=function(e){return e.trim?e.trim():t.right(t.left(e))}).left=function(e){return e.trimLeft?e.trimLeft():e.replace(/^\s\s*/,"")},t.right=function(e){if(e.trimRight)return e.trimRight();for(var t=/\s/,n=e.length;t.test(e.charAt(--n)););return e.slice(0,n+1)}},1791:function(e,t,n){"use strict";var r=n(208).inherit,i=n(905),o=n(674);function s(e,t){i.call(this,e,t)}r(s,i),s.prototype.action=function(){return"screen"},s.prototype.type=s.prototype.action,s.prototype.event=function(e){return e?"Viewed "+e+" Screen":"Loaded a Screen"},s.prototype.track=function(e){var t=this.json();return t.event=this.event(e),t.timestamp=this.timestamp(),t.properties=this.properties(),new o(t,this.opts)},e.exports=s},1792:function(e,t,n){"use strict";var r=n(208).inherit,i=n(311);function o(e,t){i.call(this,e,t)}r(o,i),o.prototype.type=function(){return"delete"},e.exports=o},1793:function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty,i=Object.prototype.toString,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===i.call(e)},c=function(e){if(!e||"[object Object]"!==i.call(e))return!1;var t,n=r.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&r.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!o)return!1;for(t in e);return void 0===t||r.call(e,t)},u=function(e,t){o&&"__proto__"===t.name?o(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,t){if("__proto__"===t){if(!r.call(e,t))return;if(s)return s(e,t).value}return e[t]};e.exports=function e(){var t,n,r,i,o,s,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(t=arguments[d]))for(n in t)r=l(f,n),f!==(i=l(t,n))&&(h&&i&&(c(i)||(o=a(i)))?(o?(o=!1,s=r&&a(r)?r:[]):s=r&&c(r)?r:{},u(f,{name:n,newValue:e(h,s,i)})):void 0!==i&&u(f,{name:n,newValue:i}));return f}},1794:function(e,t,n){function r(){var e;try{e=t.storage.debug}catch(e){}return e}(t=e.exports=n(1795)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(){var e=arguments,n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return e;var r="color: "+this.color;e=[e[0],r,"color: inherit"].concat(Array.prototype.slice.call(e,1));var i=0,o=0;return e[0].replace(/%[a-z%]/g,(function(e){"%%"!==e&&(i++,"%c"===e&&(o=i))})),e.splice(o,0,r),e},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=r,t.useColors=function(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){return JSON.stringify(e)},t.enable(r())},1795:function(e,t,n){(t=e.exports=function(e){function n(){}function i(){var e=i,n=+new Date,s=n-(r||n);e.diff=s,e.prev=r,e.curr=n,r=n,null==e.useColors&&(e.useColors=t.useColors()),null==e.color&&e.useColors&&(e.color=o());var a=Array.prototype.slice.call(arguments);a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&(a=["%o"].concat(a));var c=0;a[0]=a[0].replace(/%([a-z%])/g,(function(n,r){if("%%"===n)return n;c++;var i=t.formatters[r];if("function"==typeof i){var o=a[c];n=i.call(e,o),a.splice(c,1),c--}return n})),"function"==typeof t.formatArgs&&(a=t.formatArgs.apply(e,a));var u=i.log||t.log||console.log.bind(console);u.apply(e,a)}n.enabled=!1,i.enabled=!0;var s=t.enabled(e)?i:n;return s.namespace=e,s}).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e);for(var n=(e||"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(e=n[i].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(1796),t.names=[],t.skips=[],t.formatters={};var r,i=0;function o(){return t.colors[i++%t.colors.length]}},1796:function(e,t){var n=1e3,r=6e4,i=60*r,o=24*i;function s(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){return t=t||{},"string"==typeof e?function(e){if((e=""+e).length>1e4)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*r;case"seconds":case"second":case"secs":case"sec":case"s":return s*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s}}(e):t.long?s(a=e,o,"day")||s(a,i,"hour")||s(a,r,"minute")||s(a,n,"second")||a+" ms":function(e){return e>=o?Math.round(e/o)+"d":e>=i?Math.round(e/i)+"h":e>=r?Math.round(e/r)+"m":e>=n?Math.round(e/n)+"s":e+"ms"}(e);var a}},1797:function(e,t,n){"use strict";var r=Math.max;e.exports=function(e,t){var n=t?t.length:0;if(!n)return[];for(var i=r(Number(e)||0,0),o=r(n-i,0),s=new Array(o),a=0;a<o;a+=1)s[a]=t[a+i];return s}},1798:function(e,t,n){"use strict";var r=Math.max;e.exports=function(e){if(null==e||!e.length)return[];for(var t=new Array(r(e.length-2,0)),n=1;n<e.length;n+=1)t[n-1]=e[n];return t}},1799:function(e,t){(function(t){e.exports=t}).call(this,{})},1800:function(e,t,n){"use strict";var r=n(313),i=n(910),o=n(314)("analytics.js:metrics");function s(e){this.options(e)}s.prototype.options=function(e){if(e=e||{},this.host=e.host||"api.segment.io/v1",this.sampleRate=e.sampleRate||0,this.flushTimer=e.flushTimer||3e4,this.maxQueueSize=e.maxQueueSize||20,this.queue=[],this.sampleRate>0){var t=this;setInterval((function(){t._flush()}),this.flushTimer)}},s.prototype.increment=function(e,t){Math.random()>this.sampleRate||this.queue.length>=this.maxQueueSize||(this.queue.push({type:"Counter",metric:e,value:1,tags:t}),e.indexOf("error")>0&&this._flush())},s.prototype._flush=function(){if(!(this.queue.length<=0)){var e={series:this.queue};this.queue=[],"xhr"===i.type&&i("https://"+this.host+"/m",e,{"Content-Type":"text/plain"},(function(t,n){o("sent %O, received %O",e,[t,n])}))}},e.exports=r(new s),e.exports.Metrics=s},1801:function(e,t,n){var r=n(1802),i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";e.exports=function(e){var t,n,o,s,a,c,u,l="",f=0;e=r(e);for(;f<e.length;)t=e.charCodeAt(f++),n=e.charCodeAt(f++),o=e.charCodeAt(f++),s=t>>2,a=(3&t)<<4|n>>4,c=(15&n)<<2|o>>6,u=63&o,isNaN(n)?c=u=64:isNaN(o)&&(u=64),l=l+i.charAt(s)+i.charAt(a)+i.charAt(c)+i.charAt(u);return l}},1802:function(e,t){e.exports=function(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t}},1803:function(e,t){try{e.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){e.exports=!1}},1804:function(e,t,n){var r=n(1805)("jsonp");e.exports=function(e,t,n){"function"==typeof t&&(n=t,t={});t||(t={});var s,a,c=t.prefix||"__jp",u=t.name||c+i++,l=t.param||"callback",f=null!=t.timeout?t.timeout:6e4,d=encodeURIComponent,p=document.getElementsByTagName("script")[0]||document.head;f&&(a=setTimeout((function(){h(),n&&n(new Error("Timeout"))}),f));function h(){s.parentNode&&s.parentNode.removeChild(s),window[u]=o,a&&clearTimeout(a)}return window[u]=function(e){r("jsonp got",e),h(),n&&n(null,e)},e=(e+=(~e.indexOf("?")?"&":"?")+l+"="+d(u)).replace("?&","?"),r('jsonp req "%s"',e),(s=document.createElement("script")).src=e,p.parentNode.insertBefore(s,p),function(){window[u]&&h()}};var i=0;function o(){}},1805:function(e,t,n){(function(r){function i(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG),e}(t=e.exports=n(1806)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var i=0,o=0;e[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(i++,"%c"===e&&(o=i))})),e.splice(o,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=i,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(i())}).call(this,n(182))},1806:function(e,t,n){var r;function i(e){function n(){if(n.enabled){var e=n,i=+new Date,o=i-(r||i);e.diff=o,e.prev=r,e.curr=i,r=i;for(var s=new Array(arguments.length),a=0;a<s.length;a++)s[a]=arguments[a];s[0]=t.coerce(s[0]),"string"!=typeof s[0]&&s.unshift("%O");var c=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,(function(n,r){if("%%"===n)return n;c++;var i=t.formatters[r];if("function"==typeof i){var o=s[c];n=i.call(e,o),s.splice(c,1),c--}return n})),t.formatArgs.call(e,s);var u=n.log||t.log||console.log.bind(console);u.apply(e,s)}}return n.namespace=e,n.enabled=t.enabled(e),n.useColors=t.useColors(),n.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),"function"==typeof t.init&&t.init(n),n}(t=e.exports=i.debug=i.default=i).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];for(var n=("string"==typeof e?e:"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(e=n[i].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(1807),t.names=[],t.skips=[],t.formatters={}},1807:function(e,t){var n=1e3,r=6e4,i=60*r,o=24*i;function s(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var a,c=typeof e;if("string"===c&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*r;case"seconds":case"second":case"secs":case"sec":case"s":return s*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===c&&!1===isNaN(e))return t.long?s(a=e,o,"day")||s(a,i,"hour")||s(a,r,"minute")||s(a,n,"second")||a+" ms":function(e){if(e>=o)return Math.round(e/o)+"d";if(e>=i)return Math.round(e/i)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1808:function(e,t,n){"use strict";var r=n(911),i=n(313),o=n(314)("analytics:group"),s=n(672);function a(e){this.defaults=a.defaults,this.debug=o,r.call(this,e)}a.defaults={persist:!0,cookie:{key:"ajs_group_id"},localStorage:{key:"ajs_group_properties"}},s(a,r),e.exports=i(new a),e.exports.Group=a},1809:function(e,t,n){"use strict";(function(t){var r=n(353);e.exports=function(){var e,n={},i="undefined"!=typeof window?window:t,o=i.document;if(n.disabled=!1,n.version="1.3.20",n.set=function(e,t){},n.get=function(e,t){},n.has=function(e){return void 0!==n.get(e)},n.remove=function(e){},n.clear=function(){},n.transact=function(e,t,r){null==r&&(r=t,t=null),null==t&&(t={});var i=n.get(e,t);r(i),n.set(e,i)},n.getAll=function(){var e={};return n.forEach((function(t,n){e[t]=n})),e},n.forEach=function(){},n.serialize=function(e){return r.stringify(e)},n.deserialize=function(e){if("string"==typeof e)try{return r.parse(e)}catch(t){return e||void 0}},function(){try{return"localStorage"in i&&i.localStorage}catch(e){return!1}}())e=i.localStorage,n.set=function(t,r){return void 0===r?n.remove(t):(e.setItem(t,n.serialize(r)),r)},n.get=function(t,r){var i=n.deserialize(e.getItem(t));return void 0===i?r:i},n.remove=function(t){e.removeItem(t)},n.clear=function(){e.clear()},n.forEach=function(t){for(var r=0;r<e.length;r++){var i=e.key(r);t(i,n.get(i))}};else if(o&&o.documentElement.addBehavior){var s,a;try{(a=new ActiveXObject("htmlfile")).open(),a.write('<script>document.w=window<\/script><iframe src="/favicon.ico"></iframe>'),a.close(),s=a.w.frames[0].document,e=s.createElement("div")}catch(t){e=o.createElement("div"),s=o.body}var c=function(t){return function(){var r=Array.prototype.slice.call(arguments,0);r.unshift(e),s.appendChild(e),e.addBehavior("#default#userData"),e.load("localStorage");var i=t.apply(n,r);return s.removeChild(e),i}},u=new RegExp("[!\"#$%&'()*+,/\\\\:;<=>?@[\\]^`{|}~]","g"),l=function(e){return e.replace(/^d/,"___$&").replace(u,"___")};n.set=c((function(e,t,r){return t=l(t),void 0===r?n.remove(t):(e.setAttribute(t,n.serialize(r)),e.save("localStorage"),r)})),n.get=c((function(e,t,r){t=l(t);var i=n.deserialize(e.getAttribute(t));return void 0===i?r:i})),n.remove=c((function(e,t){t=l(t),e.removeAttribute(t),e.save("localStorage")})),n.clear=c((function(e){var t=e.XMLDocument.documentElement.attributes;e.load("localStorage");for(var n=t.length-1;n>=0;n--)e.removeAttribute(t[n].name);e.save("localStorage")})),n.forEach=c((function(e,t){for(var r,i=e.XMLDocument.documentElement.attributes,o=0;r=i[o];++o)t(r.name,n.deserialize(e.getAttribute(r.name)))}))}try{var f="__storejs__";n.set(f,f),n.get(f)!=f&&(n.disabled=!0),n.remove(f)}catch(e){n.disabled=!0}return n.enabled=!n.disabled,n}()}).call(this,n(97))},1810:function(e,t,n){"use strict";e.exports=function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return!0;var t=e.which,n=e.button;return t||void 0===n?2===t:1&!n&&2&!n&&4&n}},1811:function(e,t,n){"use strict";var r=n(314)("analytics.js:normalize"),i=n(352),o=n(209),s=n(679),a=n(1812),c=n(312),u=n(913).v4,l=n(353),f=n(914).hash,d=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a((function(e){return e.toLowerCase()}),t),h=e.options||{},m=h.integrations||{},g=h.providers||{},v=h.context||{},y={};return r("<-",e),o((function(e,t){_(t)&&(d.call(m,t)||(m[t]=e),delete h[t])}),h),delete h.providers,o((function(e,t){_(t)&&"object"!==c(m[t])&&(d.call(m,t)&&"boolean"==typeof g[t]||(m[t]=e))}),g),o((function(e,t){s(t,p)?y[t]=h[t]:v[t]=h[t]}),h),e.messageId="ajs-"+f(l.stringify(e)+u()),delete e.options,y.integrations=m,y.context=v,y=i(y,e),r("->",y),y;function _(e){return!(!s(e,t)&&"all"!==e.toLowerCase()&&!s(e.toLowerCase(),n))}};var p=["integrations","anonymousId","timestamp","context"]},1812:function(e,t,n){"use strict";var r=n(209);e.exports=function(e,t){if("function"!=typeof e)throw new TypeError("Expected a function but received a "+typeof e);var n=[];return r((function(t,r,i){n.push(e(t,r,i))}),t),n}},1813:function(e,t,n){(function(t){var n,r=t.crypto||t.msCrypto;if(r&&r.getRandomValues){var i=new Uint8Array(16);n=function(){return r.getRandomValues(i),i}}if(!n){var o=new Array(16);n=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=***********Math.random()),o[t]=e>>>((3&t)<<3)&255;return o}}e.exports=n}).call(this,n(97))},1814:function(e,t){var n=window.addEventListener?"addEventListener":"attachEvent",r=window.removeEventListener?"removeEventListener":"detachEvent",i="addEventListener"!==n?"on":"";t.bind=function(e,t,r,o){return e[n](i+t,r,o||!1),r},t.unbind=function(e,t,n,o){return e[r](i+t,n,o||!1),n}},1815:function(e,t,n){"use strict";var r=n(1816),i=n(679),o=n(909);function s(){var e=r();return e?o.parse(e).pathname:window.location.pathname}function a(e){var t=r();if(t)return i("?",t)?t:t+e;var n=window.location.href,o=n.indexOf("#");return-1===o?n:n.slice(0,o)}e.exports=function(){return{path:s(),referrer:document.referrer,search:location.search,title:document.title,url:a(location.search)}}},1816:function(e,t,n){"use strict";e.exports=function(){for(var e,t=document.getElementsByTagName("link"),n=0;e=t[n];n++)if("canonical"===e.getAttribute("rel"))return e.getAttribute("href")}},1817:function(e,t,n){"use strict";var r=Object.prototype.toString,i=function(e){return"string"==typeof e||"[object String]"===r.call(e)};e.exports=function(e,t){if(null==t||!function(e){return null!=e&&"object"==typeof e}(t))return{};i(e)&&(e=[e]),function(e){return"[object Array]"===r.call(e)}(e)||(e=[]);for(var n={},o=0;o<e.length;o+=1)i(e[o])&&e[o]in t&&(n[e[o]]=t[e[o]]);return n}},1818:function(e,t,n){"use strict";e.exports=function(e){return(e=e||window.event).preventDefault?e.preventDefault():e.returnValue=!1}},1819:function(e,t){(t=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},t.right=function(e){return e.replace(/\s*$/,"")}},1820:function(e,t){var n=Object.prototype.toString;e.exports=function(e){switch(n.call(e)){case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Array]":return"array";case"[object Error]":return"error"}return null===e?"null":void 0===e?"undefined":e!=e?"nan":e&&1===e.nodeType?"element":typeof(e=e.valueOf?e.valueOf():Object.prototype.valueOf.apply(e))}},1821:function(e,t,n){"use strict";var r=n(911),i=n(313),o=n(427),s=n(314)("analytics:user"),a=n(672),c=n(675),u=n(913),l=n(678);function f(e){this.defaults=f.defaults,this.debug=s,r.call(this,e)}f.defaults={persist:!0,cookie:{key:"ajs_user_id",oldKey:"ajs_user"},localStorage:{key:"ajs_user_traits"}},a(f,r),f.prototype.id=function(e){var t=this._getId(),n=r.prototype.id.apply(this,arguments);return null==t||t!=e&&e&&this.anonymousId(null),n},f.prototype.anonymousId=function(e){var t=this.storage();return arguments.length?(t.set("ajs_anonymous_id",e),this._setAnonymousIdInLocalStorage(e),this):(e=t.get("ajs_anonymous_id"))?(this._setAnonymousIdInLocalStorage(e),t.set("ajs_anonymous_id",e),e):!this._options.localStorageFallbackDisabled&&(e=l.get("ajs_anonymous_id"))?(t.set("ajs_anonymous_id",e),e):(e=c("_sio"))?(e=e.split("----")[0],t.set("ajs_anonymous_id",e),this._setAnonymousIdInLocalStorage(e),t.remove("_sio"),e):(e=u.v4(),t.set("ajs_anonymous_id",e),this._setAnonymousIdInLocalStorage(e),t.get("ajs_anonymous_id"))},f.prototype._setAnonymousIdInLocalStorage=function(e){this._options.localStorageFallbackDisabled||l.set("ajs_anonymous_id",e)},f.prototype.logout=function(){r.prototype.logout.call(this),this.anonymousId(null)},f.prototype.load=function(){this._loadOldCookie()||r.prototype.load.call(this)},f.prototype._loadOldCookie=function(){var e=o.get(this._options.cookie.oldKey);return!!e&&(this.id(e.id),this.traits(e.traits),o.remove(this._options.cookie.oldKey),!0)},e.exports=i(new f),e.exports.User=f},1822:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=r.__importDefault(n(539)),o=n(915),s=n(1827),a=n(1854),c=n(1855),u=n(419);t.defaultTimeout=5e3;var l=function(){function e(e,n){if(void 0===e&&(e="local"),void 0===n&&(n=t.defaultTimeout),this.storageEnabled=!0,this.timeoutUid={type:u.XIDItemType.UID,state:"TIMEOUT"},!e)throw new Error("Missing env configuration");this.env=e;var r="prod"===this.env?1e4:3e4;n>=2e3&&n<=r?this.timeout=n:("prod"!==this.env&&console.log("timeout configuration must be in the range of [2000, "+r+"] seconds"),this.timeout=t.defaultTimeout)}return e.prototype.getTimeoutPromise=function(e){var t=this;return new i.default((function(n){setTimeout((function(){n([e])}),(t.timeout-1e3)/2)}))},e.prototype.getXcPromiseAndSave=function(){var e=this;return new i.default((function(t){e.getXc().then((function(n){e.storageEnabled&&o.Storage.saveXid([n]),t([n])}))}))},e.prototype.getUidPromiseAndSave=function(){var e=this;return new i.default((function(t){var n=function(){var n=[];e.storageEnabled&&(n=o.Storage.getUids());var r=s.UIDGenerator.getValue(n);e.storageEnabled&&(o.Storage.saveUids(r),o.Storage.saveXid(r)),t(r)},r=window.requestIdleCallback;r?r(n):setTimeout(n,1e3)}))},e.prototype.getXidPromiseInXcUidOnly=function(){var e=this;return this.getXcPromiseAndSave().then((function(t){return t&&t.length>0&&t[0].createdAt&&t[0].value?t:(e.getTimeoutPromise(e.timeoutUid),e.getUidPromiseAndSave().then((function(e){return r.__spreadArrays(e,t)})))}))},e.prototype.getXidCallbackForPromise=function(e,t){var n;void 0===e&&(e=u.XIDGenerateMode.CACHE_XC_UID),void 0===t&&(t=!0);return this.getXidPromise(e,t).then((function(e){return function(){return t=e,n||(n=t.map((function(e){return"NEW"===e.state||"CHANGED"===e.state?{state:"EXISTING",type:e.type,createdAt:e.createdAt,value:e.value}:e})),t);var t}}))},e.prototype.getXidPromise=function(e,t){var n=this;void 0===e&&(e=u.XIDGenerateMode.CACHE_XC_UID),void 0===t&&(t=!0),this.storageEnabled=t;var r=e===u.XIDGenerateMode.CACHE_ONLY||e===u.XIDGenerateMode.CACHE_XC_UID?o.Storage.getCachedXID():[];switch(e){case u.XIDGenerateMode.XC_ONLY:return c.promiseWithTimer((function(){return n.getXcPromiseAndSave()}));case u.XIDGenerateMode.UID_ONLY:return c.promiseWithTimer((function(){return i.default.race([n.getTimeoutPromise(n.timeoutUid),n.getUidPromiseAndSave()])}));case u.XIDGenerateMode.XC_UID:return c.promiseWithTimer((function(){return n.getXidPromiseInXcUidOnly()}));case u.XIDGenerateMode.CACHE_ONLY:return i.default.resolve(r);case u.XIDGenerateMode.CACHE_XC_UID:default:return r.length>0?i.default.resolve(r):c.promiseWithTimer((function(){return n.getXidPromiseInXcUidOnly()}))}},e.prototype.getXc=function(){return a.XCGenerator.getValue(this.env,(this.timeout-1e3)/2,this.storageEnabled)},e.prototype.getUidDetails=function(){return s.UIDGenerator.getDetails()},e}();t.XID=l},1823:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(681),i=n(1824),o=function(e){try{var t=JSON.parse(e);if(t instanceof Array){for(var n=!0,r=0,o=t;r<o.length;r++){var s=o[r];n=n&&i.checkXidValid(s)}return n?t:void 0}return i.checkXidValid(t)?t:void 0}catch(e){return}};function s(e){var t=r.getCookie(e);return o(t)}function a(e){var t="";try{t=localStorage.getItem(e)||""}catch(e){}return o(t)}t.getXidFromCookie=s,t.getXidFromLocalStorage=a,t.setLocalstorage=function(e,t){try{localStorage.setItem(e,t)}catch(e){}},t.getCache=function(e){return r.getCookie(e)||function(e){try{return localStorage.getItem(e)||""}catch(e){}}(e)},t.getXidItemFromCache=function(e){return s(e)||a(e)}},1824:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(419);t.checkXidValid=function(e){return!(e.type!==r.XIDItemType.UID&&e.type!==r.XIDItemType.XC||!e.value||!Date.parse(e.createdAt))}},1825:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parse=function(e){var n=document.createElement("a");return n.href=e,{href:n.href,host:n.host||location.host,port:"0"===n.port||""===n.port?t.port(n.protocol):n.port,hash:n.hash,hostname:n.hostname||location.hostname,pathname:"/"!==n.pathname.charAt(0)?"/"+n.pathname:n.pathname,protocol:n.protocol&&":"!==n.protocol?n.protocol:location.protocol,search:n.search,query:n.search.slice(1)}},t.isAbsolute=function(e){return 0===e.indexOf("//")||!!~e.indexOf("://")},t.isRelative=function(e){return!t.isAbsolute(e)},t.isCrossDomain=function(e){var n=t.parse(e),r=t.parse(window.location.href);return n.hostname!==r.hostname||n.port!==r.port||n.protocol!==r.protocol},t.port=function(e){switch(e){case"http:":return 80;case"https:":return 443;default:return location.port}},t.getParam=function(e){var t=window.location.href.match("[?&]"+e+"=([^&#]+)");return t?t.pop():""}},1826:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uuidv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}},1827:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=n(1828),o=n(1845),s=n(124),a=n(1847),c=n(1850),u=n(280),l=n(1853),f=function(){function e(){}return e.getDetails=function(){try{return{browser:{browserDefaultEvalLength:i.browserDefaultEvalLength(),canWriteCookie:i.canWriteCookie(),getBrowserVendor:i.getBrowserVendor(),getHardwareConcurrency:i.getHardwareConcurrency(),getLanguage:i.getLanguage(),getPlatform:i.getPlatform(),getPlugins:i.getPlugins(),getProductSub:i.getProductSub(),getTimeZone:i.getTimeZone(),getTimeZoneOffset:i.getTimeZoneOffset(),getUserAgent:i.getUserAgent(),isIndexedDBAvailable:i.isIndexedDBAvailable(),isLocalStorageAvailable:i.isLocalStorageAvailable(),isSessionStorageAvailable:i.isSessionStorageAvailable(),isTestingBot:i.isTestingBot(),isWebDriver:i.isWebDriver()},canvas:{studyPrint:o.studyPrint()},font:{jsFonts:(new a.DetectFonts).detect()},webGL:{getWebGL:c.getWebGL()}}}catch(e){return s.ERROR}},e.getValue=function(e){try{var t=[i.browserDefaultEvalLength(),i.canWriteCookie(),i.getBrowserVendor(),i.getHardwareConcurrency(),i.getLanguage(),i.getPlatform(),i.getPlugins(),i.getProductSub(),i.getTimeZone(),i.getTimeZoneOffset(),i.getUserAgent(),i.isIndexedDBAvailable(),i.isLocalStorageAvailable(),i.isSessionStorageAvailable(),i.isTestingBot(),i.isWebDriver(),o.studyPrint(),(new a.DetectFonts).result(),c.getWebGL()],n=l.x64hash128(t.join("|"),64),s=void 0;return"EXISTING"===(s=e.length?e[0].value===n?"EXISTING":"CHANGED":"NEW")?e:r.__spreadArrays([{type:u.XIDItemType.UID,createdAt:(new Date).toISOString(),state:s,value:n}],e)}catch(e){return[{type:u.XIDItemType.UID,state:"ERROR"}]}},e}();t.UIDGenerator=f},1828:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1829);t.browserDefaultEvalLength=r.browserDefaultEvalLength;var i=n(1830);t.canWriteCookie=i.canWriteCookie;var o=n(1831);t.getBrowserVendor=o.getBrowserVendor;var s=n(1832);t.getHardwareConcurrency=s.getHardwareConcurrency;var a=n(1833);t.getLanguage=a.getLanguage;var c=n(1834);t.getPlatform=c.getPlatform;var u=n(1835);t.getPlugins=u.getPlugins;var l=n(1836);t.getProductSub=l.getProductSub;var f=n(1837);t.getTimeZone=f.getTimeZone;var d=n(1838);t.getTimeZoneOffset=d.getTimeZoneOffset;var p=n(1839);t.getUserAgent=p.getUserAgent;var h=n(1840);t.isIndexedDBAvailable=h.isIndexedDBAvailable;var m=n(1841);t.isLocalStorageAvailable=m.isLocalStorageAvailable;var g=n(1842);t.isSessionStorageAvailable=g.isSessionStorageAvailable;var v=n(1843);t.isTestingBot=v.isTestingBot;var y=n(1844);t.isWebDriver=y.isWebDriver},1829:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.browserDefaultEvalLength=function(){try{return eval.toString().length||0}catch(e){return r.ERROR}}},1830:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.canWriteCookie=function(){try{return document.cookie="cookietest=1",-1!==document.cookie.indexOf("cookietest=")?(document.cookie="cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT",r.YES):r.NO}catch(e){return r.ERROR}}},1831:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getBrowserVendor=function(){try{return navigator.vendor||r.UNAVAILABLE}catch(e){return r.ERROR}}},1832:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getHardwareConcurrency=function(){try{var e=parseInt(navigator.hardwareConcurrency.toString(),10);return isNaN(e)?1:e}catch(e){return 1}}},1833:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getLanguage=function(){try{return navigator.language||r.UNAVAILABLE}catch(e){return r.ERROR}}},1834:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getPlatform=function(){try{return navigator.platform||r.UNAVAILABLE}catch(e){return r.ERROR}}},1835:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getPlugins=function(){try{var e="Microsoft Internet Explorer"===navigator.appName,t="Netscape"===navigator.appName,n=/Trident/.test(navigator.userAgent);if(e||t&&n)return r.UNAVAILABLE;for(var i=navigator.plugins,o="",s=0;s<i.length;s++)for(var a=navigator.plugins[s],c=a.name,u=a.filename,l=a.length,f=0;f<l;f++){var d=a[f];d&&(o=o+c+","+u+","+d.type+"|")}return o}catch(e){return r.ERROR}}},1836:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getProductSub=function(){try{return navigator.productSub||r.UNAVAILABLE}catch(e){return r.ERROR}}},1837:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getTimeZone=function(){try{return window.Intl&&window.Intl.DateTimeFormat?(new window.Intl.DateTimeFormat).resolvedOptions().timeZone:r.UNAVAILABLE}catch(e){return r.ERROR}}},1838:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getTimeZoneOffset=function(){try{return(new Date).getTimezoneOffset()||r.UNAVAILABLE}catch(e){return r.ERROR}}},1839:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getUserAgent=function(){try{return navigator.userAgent||r.UNAVAILABLE}catch(e){return r.ERROR}}},1840:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.isIndexedDBAvailable=function(){try{return window.indexedDB?r.YES:r.NO}catch(e){return r.ERROR}}},1841:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.isLocalStorageAvailable=function(){try{return window.localStorage?r.YES:r.NO}catch(e){return r.ERROR}}},1842:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.isSessionStorageAvailable=function(){try{return window.sessionStorage?r.YES:r.NO}catch(e){return r.ERROR}}},1843:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.isTestingBot=function(){try{return["webdriver"in window,"_Selenium_IDE_Recorder"in window,"callSelenium"in window,"_selenium"in window,"__webdriver_script_fn"in document,"__driver_evaluate"in document,"__webdriver_evaluate"in document,"__selenium_evaluate"in document,"__fxdriver_evaluate"in document,"__driver_unwrapped"in document,"__webdriver_unwrapped"in document,"__selenium_unwrapped"in document,"__fxdriver_unwrapped"in document,"__webdriver_script_func"in document,null!==document.documentElement.getAttribute("selenium"),null!==document.documentElement.getAttribute("webdriver"),null!==document.documentElement.getAttribute("driver")].some((function(e){return!0===e}))?r.YES:r.NO}catch(e){return r.ERROR}}},1844:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.isWebDriver=function(){try{return navigator.webdriver?r.YES:r.NO}catch(e){return r.ERROR}}},1845:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1846);t.studyPrint=r.studyPrint},1846:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.studyPrint=function(){try{var e=document.createElement("canvas");e.width=240,e.height=140,e.style.display="inline";var t=e.getContext("2d");return t?(t.rect(0,0,10,10),t.rect(2,2,6,6),t.isPointInPath(5,5,"evenodd"),t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(125,1,62,20),t.fillStyle="#069",t.font="11pt no-real-font-123",t.fillText("Cwm fjordbank 😃 gly",2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText("Cwm fjordbank 😃 gly",4,45),t.globalCompositeOperation="multiply",t.fillStyle="rgb(255,0,255)",t.beginPath(),t.arc(50,50,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(0,255,255)",t.beginPath(),t.arc(100,50,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(255,255,0)",t.beginPath(),t.arc(75,100,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(255,0,255)",t.arc(75,75,75,0,2*Math.PI,!0),t.arc(75,75,25,0,2*Math.PI,!0),t.fill("evenodd"),e.toDataURL()):r.UNAVAILABLE}catch(e){return r.ERROR}}},1847:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1848);t.DetectFonts=r.DetectFonts},1848:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124),i=n(1849),o=function(){function e(){var e=this;this.baseFonts=["monospace","sans-serif","serif"],this.testString="mmmmmmmmmmlli",this.testSize="72px",this.defaultWidth={},this.defaultHeight={},this.createSpan=function(){var t=document.createElement("span");return t.style.position="absolute",t.style.left="-9999px",t.style.fontSize=e.testSize,t.style.fontStyle="normal",t.style.fontWeight="normal",t.style.letterSpacing="normal",t.style.lineBreak="auto",t.style.lineHeight="normal",t.style.textTransform="none",t.style.textAlign="left",t.style.textDecoration="none",t.style.textShadow="none",t.style.whiteSpace="normal",t.style.wordBreak="normal",t.style.wordSpacing="normal",t.innerHTML=e.testString,t},this.createSpanWithFonts=function(t,n){var r=e.createSpan();return r.style.fontFamily="'"+t+"',"+n,r},this.initializeBaseFontsSpans=function(){for(var t=[],n=0,r=e.baseFonts;n<r.length;n++){var i=r[n],o=e.createSpan();o.style.fontFamily=i,e.baseFontsDiv.appendChild(o),t.push(o)}return t},this.initializeFontsSpans=function(){for(var t={},n=0,r=i.fontsToCheck;n<r.length;n++){for(var o=r[n],s=[],a=0,c=e.baseFonts;a<c.length;a++){var u=c[a],l=e.createSpanWithFonts(o,u);e.fontsDiv.appendChild(l),s.push(l)}t[o]=s}return t},this.isFontAvailable=function(t){for(var n=!1,r=0;r<e.baseFonts.length;r++){var i=t[r].offsetWidth,o=e.defaultWidth[e.baseFonts[r]],s=t[r].offsetHeight,a=e.defaultHeight[e.baseFonts[r]];if(n=i!==o||s!==a)return n}return n},this.bodyElement=document.getElementsByTagName("body")[0],this.baseFontsDiv=document.createElement("div"),this.fontsDiv=document.createElement("div")}return e.prototype.detect=function(){var e=this.initializeBaseFontsSpans();this.bodyElement.appendChild(this.baseFontsDiv);for(var t=0;t<this.baseFonts.length;t++)this.defaultWidth[this.baseFonts[t]]=e[t].offsetWidth,this.defaultHeight[this.baseFonts[t]]=e[t].offsetHeight;var n=this.initializeFontsSpans();this.bodyElement.appendChild(this.fontsDiv);for(var r=[],o=0,s=i.fontsToCheck;o<s.length;o++){var a=s[o],c=0;this.isFontAvailable(n[a])&&(c=1),r.push(c)}return this.bodyElement.removeChild(this.fontsDiv),this.bodyElement.removeChild(this.baseFontsDiv),r.join("|")},e.prototype.result=function(){try{return this.detect()}catch(e){return r.ERROR}},e}();t.DetectFonts=o},1849:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fontsToCheck=["Adobe Garamond","Adobe Hebrew","Agency FB","Airstream","Andale Mono","Aparajita","Arial","BN Manson Nights","Baskerville","BlackChancery","Book Antiqua","Bookman Old Style","CG Times","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Chalkboard","Comic Sans","Comic Sans MS","Consolas","Cooper Black","Corbel","Courier","Courier New","Curlz MT","DS Sans Serif","Degrassi","Didot","Ebrima","GOST Common","Geneva","Georgia","Gulim","Heiti TC","Helvetica","Helvetica Neue","Hoefler Text","Impact","Kalinga","Letter Gothic","Lithos Pro","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","Lucida Handwriting","Lucida Sans","MS Gothic","MS Outlook","MS PGothic","MYRIAD","Malgun Gothic","Mangal","MapInfo Miscellaneous","Meiryo UI","Monaco","Monotype Corsiva","NSimSun","OSAKA","Palatino","Pickwick","Playbill","Pristina","Segoe Print","Segoe Script","Segoe UI","Shonar Bangla","Snell Roundhand","Styllo","Tahoma","Times","Times New Roman","Trebuchet MS","Verdana","Wingdings"]},1850:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1851);t.getWebGL=r.getWebGL},1851:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124),i=n(1852);t.getWebGL=function(){try{var e=i.getContext();if(void 0===e)return r.UNAVAILABLE;if(e&&"object"==typeof e&&e.context){var t=e.context,n=t.getExtension("WEBGL_debug_renderer_info");return null!==n?[t.getParameter(n.UNMASKED_RENDERER_WEBGL),t.getParameter(t.MAX_COMBINED_TEXTURE_IMAGE_UNITS),t.getParameter(t.MAX_CUBE_MAP_TEXTURE_SIZE)].join("|"):r.UNAVAILABLE}}catch(e){return r.ERROR}}},1852:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(124);t.getContext=function(){if(window.WebGLRenderingContext)for(var e=document.createElement("canvas"),t=0,n=["webgl","experimental-webgl","moz-webgl","webkit-3d"];t<n.length;t++){var i=n[t];try{var o=e.getContext(i);if(o&&o instanceof WebGLRenderingContext&&"function"==typeof o.getParameter)return{name:i,context:o}}catch(e){return r.ERROR}}return r.UNAVAILABLE}},1853:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]+t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]+t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]+t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]+t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]},i=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]*t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]*t[3],n[1]+=n[2]>>>16,n[2]&=65535,n[2]+=e[3]*t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]*t[3],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[2]*t[2],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[3]*t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]},o=function(e,t){return 32===(t%=64)?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])},s=function(e,t){return 0===(t%=64)?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]},a=function(e,t){return[e[0]^t[0],e[1]^t[1]]},c=function(e){return e=a(e,[0,e[0]>>>1]),e=i(e,[4283543511,3981806797]),e=a(e,[0,e[0]>>>1]),e=i(e,[3301882366,444984403]),e=a(e,[0,e[0]>>>1])};t.x64hash128=function(e,t){t=t||0;for(var n=(e=e||"").length%16,u=e.length-n,l=[0,t],f=[0,t],d=[0,0],p=[0,0],h=[2277735313,289559509],m=[1291169091,658871167],g=0;g<u;g+=16)d=[255&e.charCodeAt(g+4)|(255&e.charCodeAt(g+5))<<8|(255&e.charCodeAt(g+6))<<16|(255&e.charCodeAt(g+7))<<24,255&e.charCodeAt(g)|(255&e.charCodeAt(g+1))<<8|(255&e.charCodeAt(g+2))<<16|(255&e.charCodeAt(g+3))<<24],p=[255&e.charCodeAt(g+12)|(255&e.charCodeAt(g+13))<<8|(255&e.charCodeAt(g+14))<<16|(255&e.charCodeAt(g+15))<<24,255&e.charCodeAt(g+8)|(255&e.charCodeAt(g+9))<<8|(255&e.charCodeAt(g+10))<<16|(255&e.charCodeAt(g+11))<<24],d=i(d,h),d=o(d,31),d=i(d,m),l=a(l,d),l=o(l,27),l=r(l,f),l=r(i(l,[0,5]),[0,1390208809]),p=i(p,m),p=o(p,33),p=i(p,h),f=a(f,p),f=o(f,31),f=r(f,l),f=r(i(f,[0,5]),[0,944331445]);d=[0,0],p=[0,0];var v=u;switch(n){case 15:p=a(p,s([0,e.charCodeAt(v+14)],48));case 14:p=a(p,s([0,e.charCodeAt(v+13)],40));case 13:p=a(p,s([0,e.charCodeAt(v+12)],32));case 12:p=a(p,s([0,e.charCodeAt(v+11)],24));case 11:p=a(p,s([0,e.charCodeAt(v+10)],16));case 10:p=a(p,s([0,e.charCodeAt(v+9)],8));case 9:p=a(p,[0,e.charCodeAt(v+8)]),p=i(p,m),p=o(p,33),p=i(p,h),f=a(f,p);case 8:d=a(d,s([0,e.charCodeAt(v+7)],56));case 7:d=a(d,s([0,e.charCodeAt(v+6)],48));case 6:d=a(d,s([0,e.charCodeAt(v+5)],40));case 5:d=a(d,s([0,e.charCodeAt(v+4)],32));case 4:d=a(d,s([0,e.charCodeAt(v+3)],24));case 3:d=a(d,s([0,e.charCodeAt(v+2)],16));case 2:d=a(d,s([0,e.charCodeAt(v+1)],8));case 1:d=a(d,[0,e.charCodeAt(v)]),d=i(d,h),d=o(d,31),d=i(d,m),l=a(l,d)}return l=a(l,[0,e.length]),f=a(f,[0,e.length]),l=r(l,f),f=r(f,l),l=c(l),f=c(f),l=r(l,f),f=r(f,l),("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)+("00000000"+(f[0]>>>0).toString(16)).slice(-8)+("00000000"+(f[1]>>>0).toString(16)).slice(-8)}},1854:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1).__importDefault(n(539)),i=n(916),o=n(915),s=n(280),a=function(){function e(){}return e.getValue=function(t,n,i){void 0===n&&(n=3e3),void 0===i&&(i=!0);var a=e.getHost(t),c=a?"https://"+a:"",u=c?c+"?t="+(new Date).getTime():"",l=e.addFrame(u);return new r.default((function(r){var a=function(n){if("xcReady"===n.data.type&&n.origin===c){var s=e.messageToXc(n);i&&(s=o.Storage.updateXc(t,s)),r(s),e.removeFrame(l,a)}};window.addEventListener("message",a),setTimeout((function(){r({type:s.XIDItemType.XC,state:"TIMEOUT"}),e.removeFrame(l,a)}),n)}))},e.messageToXc=function(e){var t=e.data,n=t.xc,r=t.ts,i=t.state;return n&&Date.parse(r)&&i?{type:s.XIDItemType.XC,value:n,createdAt:r,state:i}:{type:s.XIDItemType.XC,state:i||"MALFORMED"}},e.addFrame=function(e){var t=document.createElement("iframe");return t.id="xc-"+(new Date).getTime(),t.src=e,t.style.display="none",t.style.position="fixed",document.body.appendChild(t),t},e.removeFrame=function(e,t){document.body.contains(e)&&(document.body.removeChild(e),window.removeEventListener("message",t))},e.getHost=function(e){switch(e){case"stage":return"xxid.staging.atl-paas.net";case"prod":return i.isAtlassianComDomain()?"xxid.atl-paas.net":"xxid.atlassian.com";case"test":return"";case"local":case"dev":default:return"xxid.dev.atl-paas.net"}},e}();t.XCGenerator=a},1855:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(419);t.promiseWithTimer=function(e){var t=function(e){var t={type:r.XIDItemType.UID,state:"MALFORMED"};return function(e,t){var n=e.state,r=e.type,o=e.value,s=e.createdAt;return e&&n&&r&&o&&s&&("NEW"===n||"CHANGED"===n)?{state:n,type:r,value:o,createdAt:s,timeTaken:i()-t}:null}(e,n)||function(e){var t=e.state,n=e.type,r=e.value,i=e.createdAt;return e&&t&&n&&r&&i&&"EXISTING"===t?{state:t,type:n,value:r,createdAt:i}:null}(e)||function(e){var t=e.state,n=e.type;if(e&&t&&n)switch(t){case"TIMEOUT":case"ERROR":case"UNAVAILABLE":case"UNKNOWN":case"MALFORMED":return{type:n,state:t}}return null}(e)||function(e){var t=e.type;return e&&t?{type:t,state:"MALFORMED"}:null}(e)||t},n=i();return e().then((function(e){return e.map((function(e){return t(e)}))}))};var i=function(){return performance&&performance.now?performance.now():(new Date).getTime()}},1856:function(e,t,n){(function(r){function i(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG),e}(t=e.exports=n(1857)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var i=0,o=0;e[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(i++,"%c"===e&&(o=i))})),e.splice(o,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=i,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(i())}).call(this,n(182))},1857:function(e,t,n){var r;function i(e){function n(){if(n.enabled){var e=n,i=+new Date,o=i-(r||i);e.diff=o,e.prev=r,e.curr=i,r=i;for(var s=new Array(arguments.length),a=0;a<s.length;a++)s[a]=arguments[a];s[0]=t.coerce(s[0]),"string"!=typeof s[0]&&s.unshift("%O");var c=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,(function(n,r){if("%%"===n)return n;c++;var i=t.formatters[r];if("function"==typeof i){var o=s[c];n=i.call(e,o),s.splice(c,1),c--}return n})),t.formatArgs.call(e,s);var u=n.log||t.log||console.log.bind(console);u.apply(e,s)}}return n.namespace=e,n.enabled=t.enabled(e),n.useColors=t.useColors(),n.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),"function"==typeof t.init&&t.init(n),n}(t=e.exports=i.debug=i.default=i).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];for(var n=("string"==typeof e?e:"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(e=n[i].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(1858),t.names=[],t.skips=[],t.formatters={}},1858:function(e,t){var n=1e3,r=6e4,i=60*r,o=24*i;function s(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var a,c=typeof e;if("string"===c&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*r;case"seconds":case"second":case"secs":case"sec":case"s":return s*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===c&&!1===isNaN(e))return t.long?s(a=e,o,"day")||s(a,i,"hour")||s(a,r,"minute")||s(a,n,"second")||a+" ms":function(e){if(e>=o)return Math.round(e/o)+"d";if(e>=i)return Math.round(e/i)+"h";if(e>=r)return Math.round(e/r)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1859:function(e,t){e.exports=function(e,t){return t||(t={}),e.toLowerCase().replace(t.replace||/[^a-z0-9]/g," ").replace(/^ +| +$/g,"").replace(/ +/g,t.separator||"-")}},1860:function(e,t,n){"use strict";var r=n(523),i=n(1861),o=n(209),s=n(1863),a=n(1864),c=n(1865),u=n(676),l=n(521),f=n(1866),d=n(1867),p=n(524),h=n(1868),m=Object.prototype.hasOwnProperty,g=function(){},v=window.onerror;function y(e){return!!l.object(e)&&(!!l.string(e.key)&&!!m.call(e,"value"))}r(t),t.initialize=function(){var e=this.ready;p(e)},t.loaded=function(){return!1},t.page=function(e){},t.track=function(e){},t.map=function(e,t){var n=h(t),r=function(e){if(l.array(e))return a(y,e)?"mixed":"array";return l.object(e)?"map":"unknown"}(e);return"unknown"===r?[]:u((function(e,t,i){var o,s;return"map"===r&&(o=i,s=t),"array"===r&&(o=t,s=t),"mixed"===r&&(o=t.key,s=t.value),h(o)===n&&e.push(s),e}),[],e)},t.invoke=function(e){if(this[e]){var t=Array.prototype.slice.call(arguments,1);return this._ready?(this.debug("%s with %o",e,t),this[e].apply(this,t)):this.queue(e,t)}},t.queue=function(e,t){if("page"===e&&this._assumesPageview&&!this._initialized)return this.page.apply(this,t);this._queue.push({method:e,args:t})},t.flush=function(){this._ready=!0;var e=this;o((function(t){e[t.method].apply(e,t.args)}),this._queue),this._queue.length=0},t.reset=function(){for(var e=0;e<this.globals.length;e++)window[this.globals[e]]=void 0;window.onerror=v,window.onload=null},t.load=function(e,t,n){"function"==typeof e&&(n=e,t=null,e=null),e&&"object"==typeof e&&(n=t,t=e,e=null),"function"==typeof t&&(n=t,t=null),e=e||"library",t=t||{},t=this.locals(t);var r=this.templates[e];if(!r)throw new Error(c('template "%s" not defined.',e));var i=function(e,t){return u((function(e,n,r){return e[r]=n.replace(/\{\{\ *(\w+)\ *\}\}/g,(function(e,n){return t[n]})),e}),{},e.attrs)}(r,t);n=n||g;var s,a=this;switch(r.type){case"img":i.width=1,i.height=1,s=function(e,t){t=t||function(){};var n=new Image;return n.onerror=function(e,t,n){return function(r){r=r||window.event;var i=new Error(t);i.event=r,i.source=n,e(i)}}(t,"failed to load pixel",n),n.onload=function(){t()},n.src=e.src,n.width=1,n.height=1,n}(i,n);break;case"script":s=d(i,(function(e){if(!e)return n();a.debug('error loading "%s" error="%s"',a.name,e)})),delete i.src,o((function(e,t){s.setAttribute(t,e)}),i);break;case"iframe":s=f(i,n)}return s},t.locals=function(e){e=e||{};var t=Math.floor((new Date).getTime()/36e5);return e.hasOwnProperty("cache")||(e.cache=t),o((function(t,n){e.hasOwnProperty(n)||(e[n]=t)}),this.options),e},t.ready=function(){this.emit("ready")},t._wrapInitialize=function(){var e=this.initialize;this.initialize=function(){this.debug("initialize"),this._initialized=!0;var t=e.apply(this,arguments);return this.emit("initialize"),t},this._assumesPageview&&(this.initialize=i(2,this.initialize))},t._wrapPage=function(){var e=this.page;this.page=function(){return this._assumesPageview&&!this._initialized?this.initialize.apply(this,arguments):e.apply(this,arguments)}},t._wrapTrack=function(){var e=this.track;this.track=function(t){var n,r,i=t.event();for(var o in s)if(m.call(s,o)){var a=s[o];if(!this[o])continue;if(!a.test(i))continue;r=this[o].apply(this,arguments),n=!0;break}return n||(r=e.apply(this,arguments)),r}}},1861:function(e,t,n){"use strict";var r=n(1862),i=Object.prototype.toString;e.exports=function(e,t){if("number"!==(o=typeof(n=e))&&("object"!==o||"[object Number]"!==i.call(n)))throw new TypeError("Expected a number but received "+typeof e);var n,o;if(!function(e){return"function"==typeof e}(t))throw new TypeError("Expected a function but received "+typeof t);var s=0;return r(t.length,(function(){if(!((s+=1)<e))return t.apply(this,arguments)}))}},1862:function(e,t,n){"use strict";var r=Object.prototype.toString,i=[function(e){return function(){return e.apply(this,arguments)}},function(e){return function(t){return e.apply(this,arguments)}},function(e){return function(t,n){return e.apply(this,arguments)}},function(e){return function(t,n,r){return e.apply(this,arguments)}},function(e){return function(t,n,r,i){return e.apply(this,arguments)}},function(e){return function(t,n,r,i,o){return e.apply(this,arguments)}}];e.exports=function(e,t){if("function"!=typeof t)throw new TypeError("Expected a function but got "+typeof t);return e=Math.max(function(e){var t=typeof e;return"number"===t||"object"===t&&"[object Number]"===r.call(e)}(e)?e:0,0),i[e]||(i[e]=function(e){var t=function(e){for(var t=[],n=1;n<=e;n+=1)t.push("arg"+n);return t}(e).join(", "),n="".concat("  return function(",t,") {\n","    return func.apply(this, arguments);\n","  };");return new Function("func",n)}(e)),i[e](t)}},1863:function(e,t){e.exports={promotionViewed:/^[ _]?promotion[ _]?viewed?[ _]?$/i,viewedPromotion:/^[ _]?viewed[ _]?promotion?[ _]?$/i,promotionClicked:/^[ _]?promotion[ _]?clicked?[ _]?$/i,clickedPromotion:/^[ _]?clicked[ _]?promotion?[ _]?$/i,productsSearched:/^[ _]?products[ _]?searched[ _]?$/i,productListViewed:/^[ _]?product[ _]?list[ _]?viewed[ _]?$/i,productListFiltered:/^[ _]?product[ _]?list[ _]?filtered[ _]?$/i,viewedProductCategory:/^[ _]?viewed[ _]?product[ _]?category[ _]?$/i,viewedProductDetails:/^[ _]?viewed[ _]?product[ _]?details?[ _]?$/i,productClicked:/^[ _]?product[ _]?clicked[ _]?$/i,clickedProduct:/^[ _]?clicked[ _]?product[ _]?$/i,productViewed:/^[ _]?product[ _]?viewed[ _]?$/i,viewedProduct:/^[ _]?viewed[ _]?product[ _]?$/i,productAdded:/^[ _]?product[ _]?added[ _]?$/i,addedProduct:/^[ _]?added[ _]?product[ _]?$/i,productRemoved:/^[ _]?product[ _]?removed[ _]?$/i,removedProduct:/^[ _]?removed[ _]?product[ _]?$/i,cartViewed:/^[ _]?cart[ _]?viewed[ _]?$/i,orderStarted:/^[ _]?order[ _]?started[ _]?$/i,startedOrder:/^[ _]?started[ _]?order[ _]?$/i,orderUpdated:/^[ _]?order[ _]?updated[ _]?$/i,updatedOrder:/^[ _]?updated[ _]?order[ _]?$/i,orderCompleted:/^[ _]?order[ _]?completed[ _]?$/i,completedOrder:/^[ _]?completed[ _]?order[ _]?$/i,orderRefunded:/^[ _]?order[ _]?refunded[ _]?$/i,refundedOrder:/^[ _]?refunded[ _]?order[ _]?$/i,orderCancelled:/^[ _]?order[ _]?cancelled[ _]?$/i,paymentInfoAdded:/^[ _]?payment[ _]?info[ _]?added[ _]?$/i,checkoutStarted:/^[ _]?checkout[ _]?started[ _]?$/i,checkoutStepViewed:/^[ _]?checkout[ _]?step[ _]?viewed[ _]?$/i,viewedCheckoutStep:/^[ _]?viewed[ _]?checkout[ _]?step[ _]?$/i,checkoutStepCompleted:/^[ _]?checkout[ _]?step[ _]?completed[ _]?$/i,completedCheckoutStep:/^[ _]?completed[ _]?checkout[ _]?step[ _]?$/i,couponEntered:/^[ _]?coupon[ _]?entered[ _]?$/i,couponApplied:/^[ _]?coupon[ _]?applied[ _]?$/i,couponDenied:/^[ _]?coupon[ _]?denied[ _]?$/i,couponRemoved:/^[ _]?coupon[ _]?removed[ _]?$/i,productAddedToWishlist:/^[ _]?product[ _]?added[ _]?to[ _]?wishlist[ _]?$/i,wishlistProductRemoved:/^[ _]?wishlist[ _]?product[ _]?removed[ _]?$/i,wishlistProductAddedToCart:/^[ _]?wishlist[ _]?product[ _]?added[ _]?to[ _]?cart[ _]?$/i,productShared:/^[ _]?product[ _]?shared[ _]?$/i,cartShared:/^[ _]?cart[ _]?shared[ _]?$/i,productRemoved:/^[ _]?product[ _]?removed[ _]?$/i,applicationInstalled:/^[ _]?application[ _]?installed[ _]?$/i,applicationUpdated:/^[ _]?application[ _]?updated[ _]?$/i,applicationOpened:/^[ _]?application[ _]?opened[ _]?$/i,applicationBackgrounded:/^[ _]?application[ _]?backgrounded[ _]?$/i,applicationUninstalled:/^[ _]?application[ _]?uninstalled[ _]?$/i,installAttributed:/^[ _]?install[ _]?attributed[ _]?$/i,deepLinkOpened:/^[ _]?deep[ _]?link[ _]?opened[ _]?$/i,pushNotificationReceived:/^[ _]?push[ _]?notification[ _]?received[ _]?$/i,pushNotificationTapped:/^[ _]?push[ _]?notification[ _]?received[ _]?$/i,pushNotificationBounced:/^[ _]?push[ _]?notification[ _]?bounced[ _]?$/i}},1864:function(e,t,n){"use strict";var r=n(209);e.exports=function(e,t){if("function"!=typeof e)throw new TypeError("`predicate` must be a function but was a "+typeof e);var n=!0;return r((function(t,r,i){if(!(n=!!e(t,r,i)))return!1}),t),n}},1865:function(e,t,n){"use strict";(function(t){var n=t.JSON&&"function"==typeof JSON.stringify?JSON.stringify:String;function r(e){var t=Array.prototype.slice.call(arguments,1),n=0;return e.replace(/%([a-z])/gi,(function(e,i){return r[i]?r[i](t[n++]):e+i}))}r.o=n,r.s=String,r.d=parseInt,e.exports=r}).call(this,n(97))},1866:function(e,t,n){var r=n(521),i=n(917),o=n(524);e.exports=function(e,t){if(!e)throw new Error("Cant load nothing...");r.string(e)&&(e={src:e});var n="https:"===document.location.protocol||"chrome-extension:"===document.location.protocol;e.src&&0===e.src.indexOf("//")&&(e.src=n?"https:"+e.src:"http:"+e.src),n&&e.https?e.src=e.https:!n&&e.http&&(e.src=e.http);var s=document.createElement("iframe");return s.src=e.src,s.width=e.width||1,s.height=e.height||1,s.style.display="none",r.fn(t)&&i(s,t),o((function(){var e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(s,e)})),s}},1867:function(e,t,n){"use strict";var r=n(917),i=n(524),o=n(312);e.exports=function(e,t){if(!e)throw new Error("Can't load nothing...");"string"===o(e)&&(e={src:e});var n="https:"===document.location.protocol||"chrome-extension:"===document.location.protocol;e.src&&0===e.src.indexOf("//")&&(e.src=(n?"https:":"http:")+e.src),n&&e.https?e.src=e.https:!n&&e.http&&(e.src=e.http);var s=document.createElement("script");return s.type="text/javascript",s.async=!0,s.src=e.src,"function"===o(t)&&r(s,t),i((function(){var e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(s,e)})),s}},1868:function(e,t){e.exports=function(e){return n.test(e)?e.toLowerCase():r.test(e)?(function(e){return e.replace(i,(function(e,t){return t?" "+t:""}))}(e)||e).toLowerCase():function(e){return e.replace(o,(function(e,t,n){return t+" "+n.toLowerCase().split("").join(" ")}))}(e).toLowerCase()};var n=/\s/,r=/[\W_]/;var i=/[\W_]+(.|$)/g;var o=/(.)([A-Z]+)/g},1869:function(e,t,n){"use strict";var r=n(523),i=n(1870),o=n(209),s=n(679);r(t),t.option=function(e,t){return this.prototype.defaults[e]=t,this},t.mapping=function(e){return this.option(e,[]),this.prototype[e]=function(t){return this.map(this.options[e],t)},this},t.global=function(e){return this.prototype.globals.push(e),this},t.assumesPageview=function(){return this.prototype._assumesPageview=!0,this},t.readyOnLoad=function(){return this.prototype._readyOnLoad=!0,this},t.readyOnInitialize=function(){return this.prototype._readyOnInitialize=!0,this},t.tag=function(e,t){return null==t&&(t=e,e="library"),this.prototype.templates[e]=function(e){e=e.replace(' src="',' data-src="');var t=i(e),n={};return o((function(t){var r="data-src"===t.name?"src":t.name;s(t.name+"=",e)&&(n[r]=t.value)}),t.attributes),{type:t.tagName.toLowerCase(),attrs:n}}(t),this}},1870:function(e,t){e.exports=function(e,t){if("string"!=typeof e)throw new TypeError("String expected");t||(t=document);var n=/<([\w:]+)/.exec(e);if(!n)return t.createTextNode(e);e=e.replace(/^\s+|\s+$/g,"");var r=n[1];if("body"==r){return(o=t.createElement("html")).innerHTML=e,o.removeChild(o.lastChild)}var o,s=i[r]||i._default,a=s[0],c=s[1],u=s[2];(o=t.createElement("div")).innerHTML=c+e+u;for(;a--;)o=o.lastChild;if(o.firstChild==o.lastChild)return o.removeChild(o.firstChild);var l=t.createDocumentFragment();for(;o.firstChild;)l.appendChild(o.removeChild(o.firstChild));return l};var n,r=!1;"undefined"!=typeof document&&((n=document.createElement("div")).innerHTML='  <link/><table></table><a href="/a">a</a><input type="checkbox"/>',r=!n.getElementsByTagName("link").length,n=void 0);var i={legend:[1,"<fieldset>","</fieldset>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],_default:r?[1,"X<div>","</div>"]:[0,"",""]};i.td=i.th=[3,"<table><tbody><tr>","</tr></tbody></table>"],i.option=i.optgroup=[1,'<select multiple="multiple">',"</select>"],i.thead=i.tbody=i.colgroup=i.caption=i.tfoot=[1,"<table>","</table>"],i.polyline=i.ellipse=i.polygon=i.circle=i.text=i.line=i.path=i.rect=i.g=[1,'<svg xmlns="http://www.w3.org/2000/svg" version="1.1">',"</svg>"]},1871:function(e,t,n){"use strict";var r=n(680).parse,i={btid:"dataxu",urid:"millennial-media"};e.exports=function(e){var t=r(e);for(var n in t)if(t.hasOwnProperty(n))for(var o in i)if(i.hasOwnProperty(o)&&n===o)return{id:t[n],type:i[o]}}},1872:function(e,t,n){var r;try{r=n(312)}catch(e){r=n(312)}e.exports=function e(t){switch(r(t)){case"object":var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e(t[i]));return n;case"array":n=new Array(t.length);for(var o=0,s=t.length;o<s;o++)n[o]=e(t[o]);return n;case"regexp":var a="";return a+=t.multiline?"m":"",a+=t.global?"g":"",a+=t.ignoreCase?"i":"",new RegExp(t.source,a);case"date":return new Date(t.getTime());default:return t}}},1873:function(e,t,n){var r=n(1874)("cookie");function i(e,t,n){n=n||{};var r=a(e)+"="+a(t);null==t&&(n.maxage=-1),n.maxage&&(n.expires=new Date(+new Date+n.maxage)),n.path&&(r+="; path="+n.path),n.domain&&(r+="; domain="+n.domain),n.expires&&(r+="; expires="+n.expires.toUTCString()),n.secure&&(r+="; secure"),document.cookie=r}function o(){var e;try{e=document.cookie}catch(e){return"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e.stack||e),{}}return function(e){var t,n={},r=e.split(/ *; */);if(""==r[0])return n;for(var i=0;i<r.length;++i)t=r[i].split("="),n[c(t[0])]=c(t[1]);return n}(e)}function s(e){return o()[e]}function a(e){try{return encodeURIComponent(e)}catch(t){r("error `encode(%o)` - %o",e,t)}}function c(e){try{return decodeURIComponent(e)}catch(t){r("error `decode(%o)` - %o",e,t)}}e.exports=function(e,t,n){switch(arguments.length){case 3:case 2:return i(e,t,n);case 1:return s(e);default:return o()}}},1874:function(e,t,n){(function(r){t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(1875)(t);const{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this,n(182))},1875:function(e,t,n){e.exports=function(e){function t(e){let n,i=null;function o(...e){if(!o.enabled)return;const r=o,i=Number(new Date),s=i-(n||i);r.diff=s,r.prev=n,r.curr=i,n=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(n,i)=>{if("%%"===n)return"%";a++;const o=t.formatters[i];if("function"==typeof o){const t=e[a];n=o.call(r,t),e.splice(a,1),a--}return n}),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=r,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null===i?t.enabled(e):i,set:e=>{i=e}}),"function"==typeof t.init&&t.init(o),o}function r(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let n;t.save(e),t.names=[],t.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(n=0;n<i;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(1876),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(n=>{t[n]=e[n]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},1876:function(e,t){var n=1e3,r=6e4,i=60*r,o=24*i;function s(e,t,n,r){var i=t>=1.5*n;return Math.round(e/n)+" "+r+(i?"s":"")}e.exports=function(e,t){t=t||{};var a=typeof e;if("string"===a&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*r;case"seconds":case"second":case"secs":case"sec":case"s":return s*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===a&&isFinite(e))return t.long?function(e){var t=Math.abs(e);if(t>=o)return s(e,t,o,"day");if(t>=i)return s(e,t,i,"hour");if(t>=r)return s(e,t,r,"minute");if(t>=n)return s(e,t,n,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=o)return Math.round(e/o)+"d";if(t>=i)return Math.round(e/i)+"h";if(t>=r)return Math.round(e/r)+"m";if(t>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1877:function(e,t,n){var r,i=n(1878),o=n(901);try{r=window.localStorage}catch(e){r=null}function s(e,t){var n=arguments.length;return 0==n?u():2<=n?a(e,t):1==n?null==e?r.clear():"string"==typeof e?c(e):"object"==typeof e?o(e,a):void 0:void 0}function a(e,t){return null==t?r.removeItem(e):r.setItem(e,JSON.stringify(t))}function c(e){return i(r.getItem(e))}function u(){for(var e,t=r.length,n={};0<=--t;)n[e=r.key(t)]=c(e);return n}e.exports=s,s.supported=!!r},1878:function(e,t){e.exports=function(e){try{return JSON.parse(e)}catch(t){return e||void 0}}},1879:function(e,t,n){"use strict";var r,i=Object.defineProperty,o=window.location.protocol;function s(){return r||window.location.protocol}function a(e){try{i(window.location,"protocol",{get:function(){return e}})}catch(t){r=e}}e.exports=function(e){return 0===arguments.length?s():a(e)},e.exports.http=function(){a("http:")},e.exports.https=function(){a("https:")},e.exports.reset=function(){a(o)}},1880:function(e,t,n){"use strict";var r=n(676),i=n(680).parse,o=Object.prototype.hasOwnProperty;function s(e){var t;"?"===e.charAt(0)&&(e=e.substring(1)),e=e.replace(/\?/g,"&");var n=i(e),r={};for(var s in n)o.call(n,s)&&"utm_"===s.substr(0,4)&&("campaign"===(t=s.substr(4))&&(t="name"),r[t]=n[s]);return r}var a={name:!0,term:!0,source:!0,medium:!0,content:!0};e.exports=s,e.exports.strict=function(e){return r((function(e,t,n){return o.call(a,n)&&(e[n]=t),e}),{},s(e))}},1881:function(e,t,n){for(var r=n(1882),i=[],o={},s=0;s<256;s++)i[s]=(s+256).toString(16).substr(1),o[i[s]]=s;function a(e,t){var n=t||0,r=i;return r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]}var c=r(),u=[1|c[0],c[1],c[2],c[3],c[4],c[5]],l=16383&(c[6]<<8|c[7]),f=0,d=0;function p(e,t,n){var i=t&&n||0;"string"==typeof e&&(t="binary"==e?new Array(16):null,e=null);var o=(e=e||{}).random||(e.rng||r)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t)for(var s=0;s<16;s++)t[i+s]=o[s];return t||a(o)}var h=p;h.v1=function(e,t,n){var r=t&&n||0,i=t||[],o=void 0!==(e=e||{}).clockseq?e.clockseq:l,s=void 0!==e.msecs?e.msecs:(new Date).getTime(),c=void 0!==e.nsecs?e.nsecs:d+1,p=s-f+(c-d)/1e4;if(p<0&&void 0===e.clockseq&&(o=o+1&16383),(p<0||s>f)&&void 0===e.nsecs&&(c=0),c>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");f=s,d=c,l=o;var h=(1e4*(268435455&(s+=122192928e5))+c)%**********;i[r++]=h>>>24&255,i[r++]=h>>>16&255,i[r++]=h>>>8&255,i[r++]=255&h;var m=s/***********1e4&268435455;i[r++]=m>>>8&255,i[r++]=255&m,i[r++]=m>>>24&15|16,i[r++]=m>>>16&255,i[r++]=o>>>8|128,i[r++]=255&o;for(var g=e.node||u,v=0;v<6;v++)i[r+v]=g[v];return t||a(i)},h.v4=p,h.parse=function(e,t,n){var r=t&&n||0,i=0;for(t=t||[],e.toLowerCase().replace(/[0-9a-f]{2}/g,(function(e){i<16&&(t[r+i++]=o[e])}));i<16;)t[r+i++]=0;return t},h.unparse=a,e.exports=h},1882:function(e,t,n){(function(t){var n,r=t.crypto||t.msCrypto;if(r&&r.getRandomValues){var i=new Uint8Array(16);n=function(){return r.getRandomValues(i),i}}if(!n){var o=new Array(16);n=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=***********Math.random()),o[t]=e>>>((3&t)<<3)&255;return o}}e.exports=n}).call(this,n(97))},1883:function(e,t,n){"use strict";var r=n(918).defaultEngine,i=n(918).inMemoryEngine,o=n(209),s=n(418),a=n(353);function c(e,t,n,i){this.id=t,this.name=e,this.keys=n||{},this.engine=i||r,this.originalEngine=this.engine}c.prototype.set=function(e,t){var n=this._createValidKey(e);if(n)try{this.engine.setItem(n,a.stringify(t))}catch(n){(function(e){var t=!1;if(e.code)switch(e.code){case 22:t=!0;break;case 1014:"NS_ERROR_DOM_QUOTA_REACHED"===e.name&&(t=!0)}else-2147024882===e.number&&(t=!0);return t})(n)&&(this._swapEngine(),this.set(e,t))}},c.prototype.get=function(e){try{var t=this.engine.getItem(this._createValidKey(e));return null===t?null:a.parse(t)}catch(e){return null}},c.prototype.getOriginalEngine=function(){return this.originalEngine},c.prototype.remove=function(e){this.engine.removeItem(this._createValidKey(e))},c.prototype._createValidKey=function(e){var t,n=this.name,r=this.id;return s(this.keys).length?(o((function(i){i===e&&(t=[n,r,e].join("."))}),this.keys),t):[n,r,e].join(".")},c.prototype._swapEngine=function(){var e=this;o((function(t){var n=e.get(t);i.setItem([e.name,e.id,t].join("."),n),e.remove(t)}),this.keys),this.engine=i},e.exports=c},1884:function(e,t,n){"use strict";var r=n(209),i={setTimeout:function(e,t){return window.setTimeout(e,t)},clearTimeout:function(e){return window.clearTimeout(e)},Date:window.Date},o=i;function s(){this.tasks={},this.nextId=1}s.prototype.now=function(){return+new o.Date},s.prototype.run=function(e,t){var n=this.nextId++;return this.tasks[n]=o.setTimeout(this._handle(n,e),t),n},s.prototype.cancel=function(e){this.tasks[e]&&(o.clearTimeout(this.tasks[e]),delete this.tasks[e])},s.prototype.cancelAll=function(){r(o.clearTimeout,this.tasks),this.tasks={}},s.prototype._handle=function(e,t){var n=this;return function(){return delete n.tasks[e],t()}},s.setClock=function(e){o=e},s.resetClock=function(){o=i},e.exports=s},1885:function(e,t){function n(e){return n.enabled(e)?function(t){t=r(t);var i=new Date,o=i-(n[e]||i);n[e]=i,t=e+" "+t+" +"+n.humanize(o),window.console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}:function(){}}function r(e){return e instanceof Error?e.stack||e.message:e}e.exports=n,n.names=[],n.skips=[],n.enable=function(e){try{localStorage.debug=e}catch(e){}for(var t=(e||"").split(/[\s,]+/),r=t.length,i=0;i<r;i++)"-"===(e=t[i].replace("*",".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$"))},n.disable=function(){n.enable("")},n.humanize=function(e){return e>=36e5?(e/36e5).toFixed(1)+"h":e>=6e4?(e/6e4).toFixed(1)+"m":e>=1e3?(e/1e3|0)+"s":e+"ms"},n.enabled=function(e){for(var t=0,r=n.skips.length;t<r;t++)if(n.skips[t].test(e))return!1;for(t=0,r=n.names.length;t<r;t++)if(n.names[t].test(e))return!0;return!1};try{window.localStorage&&n.enable(localStorage.debug)}catch(e){}},1886:function(e,t,n){"use strict";n.r(t);var r=n(1),i={IN_PROGRESS:"inProgress",QUEUE:"queue",ACK:"ack",RECLAIM_START:"reclaimStart",RECLAIM_END:"reclaimEnd"},o=Object.values(i);t.default=function(e){if("string"==typeof e&&0!==e.length)try{var t=new Array(window.localStorage.length).fill("").map((function(e,t){return window.localStorage.key(t)})).filter((function(t){return t.startsWith(e+".")})).map((function(e){return e.split(".")})).filter((function(e){return 3===e.length&&o.includes(e[2])})).reduce((function(e,t){var n=Object(r.__read)(t,3),i=n[1],o=n[2];return void 0===e[i]&&(e[i]={}),e[i][o]=!0,e}),{});Object.keys(t).filter((function(e){return!0!==t[e][i.ACK]})).forEach((function(t){localStorage.removeItem(e+"."+t+"."+i.IN_PROGRESS),localStorage.removeItem(e+"."+t+"."+i.QUEUE),localStorage.removeItem(e+"."+t+"."+i.RECLAIM_START),localStorage.removeItem(e+"."+t+"."+i.RECLAIM_END)}))}catch(e){}}},208:function(e,t,n){"use strict";t.inherit=n(672),t.clone=n(351),t.type=n(1783)},209:function(e,t,n){"use strict";var r=n(418),i=Object.prototype.toString,o="function"==typeof Array.isArray?Array.isArray:function(e){return"[object Array]"===i.call(e)},s=function(e){return null!=e&&(o(e)||"function"!==e&&function(e){var t=typeof e;return"number"===t||"object"===t&&"[object Number]"===i.call(e)}(e.length))},a=function(e,t){for(var n=0;n<t.length&&!1!==e(t[n],n,t);n+=1);},c=function(e,t){for(var n=r(t),i=0;i<n.length&&!1!==e(t[n[i]],n[i],t);i+=1);};e.exports=function(e,t){return(s(t)?a:c).call(this,e,t)}},2098:function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"default",(function(){return Oe})),n.d(r,"apdexType",(function(){return S})),n.d(r,"envType",(function(){return I})),n.d(r,"eventType",(function(){return E})),n.d(r,"originType",(function(){return k})),n.d(r,"platformType",(function(){return T})),n.d(r,"tenantType",(function(){return C})),n.d(r,"userType",(function(){return x})),n.d(r,"originTracingType",(function(){return j})),n.d(r,"TypeAheadHelper",(function(){return Me})),n.d(r,"DwellTimeHelper",(function(){return Pe})),n.d(r,"DwellTimeHelperWithBrowserInteraction",(function(){return Ue})),n.d(r,"CompressionRule",(function(){return Ce}));n(106),n(306),n(255);var i=n(384),o=n.n(i),s=n(1100),a=n.n(s),c=n(75),u=n.n(c),l=n(719),f=n.n(l),d=n(1),p=n(539),h=n(1103),m=n.n(h),g=n(280),v=n(1104),y=n.n(v),_=n(721),b=n.n(_)()("BeforeSend");Object.assign(b.prototype,{initialize:function(){var e=this;this.ready(),this.analytics.on("invoke",(function(t){t&&t.obj&&t.obj.context&&t.obj.context.page&&(t.obj.context.page=void 0,e.ready())}))},loaded:function(){return!0}});var w=b,I=Object.freeze({LOCAL:"local",DEV:"dev",STAGING:"staging",PROD:"prod"}),E=Object.freeze({TRACK:"track",UI:"ui",OPERATIONAL:"operational",SCREEN:"screen",IDENTIFY:"identify"}),T=Object.freeze({MAC:"mac",LINUX:"linux",WINDOWS:"windows",DESKTOP:"desktop",WEB:"web",MOBILE_WEB:"mobileWeb"}),k=Object.freeze({DESKTOP:"desktop",WEB:"web"}),C=Object.freeze({CLOUD_ID:"cloudId",ORG_ID:"orgId",OPSGENIE_CUSTOMER_ID:"opsgenieCustomerId",NONE:"none"}),x=Object.freeze({ATLASSIAN_ACCOUNT:"atlassianAccount",HASHED_EMAIL:"hashedEmail",TRELLO:"trello",OPSGENIE:"opsgenie"}),S=Object.freeze({TRANSITION:"transition",INITIAL_LOAD:"initialLoad"}),A=Object.freeze([T.DESKTOP,T.MAC,T.LINUX,T.WINDOWS]),O=Object.freeze([T.WEB,T.MOBILE_WEB]),j=Object.freeze({ATL_ORIGIN:"atlOrigin"});function M(e){return Object.keys(e).map((function(t){return e[t]}))}function D(e,t){return M(e).indexOf(t)>-1}var R=new function(){var e=this;this._data={},this.length=0,this.setItem=function(t,n){return e._data[t]=n,e.length=Object.keys(e._data).length,n},this.getItem=function(t){return t in e._data?e._data[t]:null},this.removeItem=function(t){return t in e._data&&delete e._data[t],e.length=Object.keys(e._data).length,null},this.clear=function(){e._data={},e.length=0},this.key=function(t){return Object.keys(e._data)[t]}};function N(e){try{if(!e)return!1;var t="awc.storage.support";e.setItem(t,"test_value");var n=e.getItem(t);return e.removeItem(t),"test_value"===n}catch(e){return!1}}var P=function(e,t){var n=this;void 0===t&&(t=null),this.getStore=function(){return n._store},this.getItem=function(e){return n._store.getItem(n.createKey(e))},this.removeItem=function(e){return n._store.removeItem(n.createKey(e))},this.setItem=function(e,t){try{n._store.setItem(n.createKey(e),t)}catch(r){(function(e){var t=!1;if(e.code)switch(e.code){case 22:t=!0;break;case 1014:"NS_ERROR_DOM_QUOTA_REACHED"===e.name&&(t=!0)}else-2147024882===e.number&&(t=!0);return t})(r)&&(n.swapToInMemory(),n._store.setItem(n.createKey(e),t))}},this.swapToInMemory=function(){var e,t=[];if(n._store)for(var r=0;r<n._store.length;r++)t.push(n._store.key(r));N(window.awcInMemoryStorageFallback)?e=window.awcInMemoryStorageFallback:(e=R,window.awcInMemoryStorageFallback||(window.awcInMemoryStorageFallback=R)),t.forEach((function(t){if(0===t.indexOf(n.getPrefix())){var r=n._store.getItem(t);e.setItem(t,r)}})),n._store=e},this.clear=function(){return n._store.clear()},this.key=function(e){return n._store.key(e)},this.getPrefix=function(){return n._prefix},this.createKey=function(e){return n.getPrefix()+"."+e},this._prefix=t&&t!==I.PROD?"awc-"+t:"awc",N(e)?this._store=e:this.swapToInMemory()},L=function(e){function t(t){var n;try{n=window.localStorage||R}catch(e){n=R}return e.call(this,n,t)||this}return Object(d.__extends)(t,e),t}(P),F=function(e,t,n,r){var i=this;if(void 0===r&&(r={}),this.start=function(){i._startInterval(),i._bindEventListeners()},this.stop=function(){i._stopInterval(),i._unbindEventListeners()},this.resetTimers=function(){i._intervalId&&(i.stop(),i.start())},this._bindEventListeners=function(){window.addEventListener("focus",i._focusListener),window.addEventListener("blur",i._blurListener)},this._unbindEventListeners=function(){window.removeEventListener("focus",i._focusListener),window.removeEventListener("blur",i._blurListener)},this._startInterval=function(){clearInterval(i._intervalId),i._intervalId=setInterval(i._handleInterval,i._delay)},this._stopInterval=function(){i._intervalId&&(clearInterval(i._intervalId),i._intervalId=null)},this._getProductKey=function(e,t){var n=e;return t&&(n+="-"+t),n},this._getLastSentTimestamp=function(e,t,n){return(((i._parseLocalStorageData()||{})[n]||{})[t]||{})[e]||null},this._parseLocalStorageData=function(){var e=i._safeLocalStorage.getItem(i._storageKey);try{return JSON.parse(e)}catch(e){return i._safeLocalStorage.removeItem(i._storageKey),null}},this._setLastSentTimestamp=function(e,t,n){var r=JSON.parse(i._safeLocalStorage.getItem(i._storageKey))||{},o=r[n]||{},s=o[t]||{};s[e]=Date.now(),o[t]=s,r[n]=o,i._safeLocalStorage.setItem(i._storageKey,JSON.stringify(r))},this._shouldSendEvent=function(e,t,n,r){return!!document.hasFocus()&&!(t!==C.NONE&&!n||!r)&&Date.now()-i._getLastSentTimestamp(e,n,r)>i._throttle},this._handleInterval=function(){var e=i._getContext(),t=e.embeddedProduct,n=Object(d.__rest)(e,["embeddedProduct"]);i._sendEvent(i._product,n),t&&i._sendEvent(t,n,!0)},this._sendEvent=function(e,t,n){var r=t.subproduct,o=t.tenantIdType,s=t.tenantId,a=t.userId,c=t.lastScreenEvent,u=t.attributes,l=i._getProductKey(e,r);if(i._shouldSendEvent(l,o,s,a)){i._setLastSentTimestamp(l,s,a);var f=i._createEvent(e,r,n,c,u);i._onEvent(f)}},this._createEvent=function(e,t,n,r,o){var s={product:e,source:"ui",action:"viewed",actionSubject:"ui",attributes:o};return n&&(s.subproduct=null,s.version=null,s.attributes=Object(d.__assign)(Object(d.__assign)({},s.attributes),{embeddedInEnv:i._productInfo.env,embeddedInProduct:i._productInfo.product,embeddedInSubproduct:t,embeddedInVersion:i._productInfo.version,embeddedInOrigin:i._productInfo.origin,embeddedInPlatform:i._productInfo.platform})),r&&(s.attributes=Object(d.__assign)(Object(d.__assign)({},s.attributes),{lastScreenEvent:{name:r.name,attributes:r.attributes}})),s},!e)throw new Error("Missing productInfo");if(!e.product)throw new Error("Missing productInfo.product");if(!t)throw new Error("Missing getContext callback");if("function"!=typeof t)throw new Error("Invalid getContext, must be function");if(!n)throw new Error("Missing onEvent callback");if("function"!=typeof n)throw new Error("Invalid onEvent, must be function");this._productInfo=e,this._product=e.product,this._getContext=t,this._onEvent=n,this._delay=r.delay||2e3,this._throttle=r.throttle||36e5,this._storageKey=r.storageKey||"ui.viewed.last.sent",this._safeLocalStorage=new L(this._productInfo.env),this._focusListener=function(){i._startInterval()},this._blurListener=function(){i._stopInterval()}},U=function(){function e(){this._performance=window.performance}return e.prototype.clearMarks=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._performance&&this._performance.clearMarks&&(e=this._performance).clearMarks.apply(e,Object(d.__spread)(t))},e.prototype.mark=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._performance&&this._performance.mark&&(e=this._performance).mark.apply(e,Object(d.__spread)(t))},e.prototype.getEntriesByName=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return this._performance&&this._performance.getEntriesByName?(e=this._performance).getEntriesByName.apply(e,Object(d.__spread)(t)):[]},e.prototype.getTimeOrigin=function(){return this._performance&&(this._performance.timeOrigin||this._performance.timing)?this._performance.timeOrigin||this._performance.timing.navigationStart:0},e.prototype.now=function(){return this._performance&&this._performance.now?this._performance.now():Date.now?Date.now():+new Date},e.prototype.isAvailable=function(){return!!(this._performance&&this._performance.clearMarks&&this._performance.mark&&this._performance.getEntriesByName&&this._performance.timing)},e}(),z=function(){function e(){var e=this;this._bindEventListeners=function(){document.addEventListener(e._visibilityChange,e._handleVisibilityChange,!1)},this._handleVisibilityChange=function(){var t=document[e._hidden];e._isHidden=t,e._callbacks.forEach((function(e){return e(t)}))},this._isHidden=!1,this._callbacks=new Map,this._hidden=void 0,this._visibilityChange=void 0,void 0!==document.hidden?(this._isHidden=document.hidden,this._hidden="hidden",this._visibilityChange="visibilitychange"):void 0!==document.msHidden?(this._isHidden=document.msHidden,this._hidden="msHidden",this._visibilityChange="msvisibilitychange"):void 0!==document.webkitHidden&&(this._isHidden=document.webkitHidden,this._hidden="webkitHidden",this._visibilityChange="webkitvisibilitychange"),void 0!==document.addEventListener&&void 0!==this._hidden&&this._bindEventListeners()}return e.prototype.addCallback=function(e,t){if("string"!=typeof e)throw new Error("Invalid name, must be string");if("function"!=typeof t)throw new Error("Invalid callback, must be function");this._callbacks.set(e,t)},e.prototype.removeCallback=function(e){this._callbacks.has(e)&&this._callbacks.delete(e)},e.prototype.getIsHidden=function(){return this._isHidden},e}(),B=function(){function e(e,t){var n=this;if(this._getEventKey=function(e){var t=e.task,n=e.taskId;return n?t+"."+n:t},this._getEventTimingByName=function(e){var t=n._performance.getEntriesByName(e);return t[t.length-1]},this._getApdexFields=function(e){var t=n._getApdexTimings(e),r=n._calculateApdex({duration:t.duration,threshold:e.threshold});return Object(d.__assign)(Object(d.__assign)({},t),{apdex:r})},this._getApdexTimings=function(e){var t=n._getEventKey(e),r=t+"-start",i=e.startTime||n._getStartTime(e,r),o=e.stopTime||n._getStopTime();return n._cleanApdexState(t),{startTime:i,stopTime:o,duration:o-i}},this._getStartTime=function(e,t){var r,i=n._performance.getTimeOrigin();e.type===S.INITIAL_LOAD?r=i:r=i+n._getEventTimingByName(t).startTime;return r},this._getStopTime=function(){return n._performance.getTimeOrigin()+n._performance.now()},this._cleanApdexState=function(e){n._performance.clearMarks(e+"-start"),n._performance.clearMarks(e+"-stop"),n._startedEvents.delete(e)},this._getVisibilityFields=function(e){var t;if(e.type===S.INITIAL_LOAD)t=!n._wasPreviouslyHidden;else if(e.type===S.TRANSITION){var r=n._getEventKey(e);t=n._isActiveEvents.get(r)}return(e.startTime||e.stopTime)&&(t=!n._pageVisibility.getIsHidden()),{isActiveTab:t}},this._calculateApdex=function(e){var t=e.duration,r=e.threshold,i=void 0===r?n._threshold:r;return t<=i?1:t<=4*i?.5:0},this._validateStartEvent=function(e){if(!e)throw new Error('Missing "event" in Apdex start event');if(!e.task)throw new Error('Missing "task" in Apdex start event');if("string"!=typeof e.task)throw new Error('Invalid "task" in Apdex start event');if(e.taskId&&"string"!=typeof e.taskId)throw new Error('Invalid "taskId" in Apdex start event')},this._validateStopEvent=function(e){if(!e)throw new Error('Missing "event" in Apdex stop event');if(!e.task)throw new Error('Missing "task" in Apdex stop event');if("string"!=typeof e.task)throw new Error('Invalid "task" in Apdex stop event');if(e.taskId&&"string"!=typeof e.taskId)throw new Error('Invalid "taskId" in Apdex stop event');if(!e.type)throw new Error('Missing "type" in Apdex stop event');if(e.type&&!D(S,e.type))throw new Error('Invalid "type" in Apdex stop event');if(e.threshold&&"number"!=typeof e.threshold)throw new Error('Invalid "threshold" in Apdex stop event');if(!e.startTime&&e.type===S.TRANSITION){var t=n._getEventKey(e);if(!n._startedEvents.has(t))throw new Error('Apdex event transition "'+t+'" was not started')}if(e.startTime&&!("number"==typeof e.startTime&&e.startTime>=0))throw new Error('Invalid "startTime" in Apdex stop event');if(e.stopTime&&"number"!=typeof e.stopTime)throw new Error('Invalid "stopTime" in Apdex stop event');if(e.stopTime<=e.startTime)throw new Error('"stopTime" should be greater than "startTime" in Apdex stop event')},this._shouldSendEvent=function(e){return!!e&&"number"==typeof e.apdex},this._sendEvent=function(e,t){n._shouldSendEvent(e)&&n._onEvent({source:"ui",action:"readyForUser",actionSubject:"ui",attributes:Object(d.__assign)(Object(d.__assign)({},e.additionalAttributes),{task:e.task,taskId:e.taskId,type:e.type,threshold:e.threshold||n._threshold,apdex:e.apdex,startTime:e.startTime,stopTime:e.stopTime,duration:e.duration,isActiveTab:e.isActiveTab})},t)},!e)throw new Error("Missing onEvent callback");if("function"!=typeof e)throw new Error("Invalid onEvent, must be function");if(!(t instanceof z))throw new Error("Invalid pageVisibility, must be PageVisibility class");this._startedEvents=new Map,this._performance=new U,this._onEvent=e,this._threshold=1e3,this._wasPreviouslyHidden=t.getIsHidden(),this._isActiveEvents=new Map,this._pageVisibility=t,this._pageVisibility.addCallback("apdexEvent",(function(e){n.onVisibilityChange(!e)}))}return e.prototype.start=function(e){if(this._validateStartEvent(e),this._performance.isAvailable()){var t=this._getEventKey(e),n=t+"-start";this._startedEvents.set(t,!0),this._isActiveEvents.set(t,!this._pageVisibility.getIsHidden()),this._performance.clearMarks(n),this._performance.mark(n)}},e.prototype.getStart=function(e){if(this._validateStartEvent(e),this._performance.isAvailable()){var t=this._getEventKey(e)+"-start";return this._getEventTimingByName(t)}},e.prototype.stop=function(e,t){if(this._validateStopEvent(e),this._performance.isAvailable()){var n=this._getApdexFields(e),r=this._getVisibilityFields(e);this._sendEvent(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)({},e),n),r),t)}},e.prototype.onVisibilityChange=function(e){var t=this;e||(this._wasPreviouslyHidden=!0,this._isActiveEvents.forEach((function(e,n){return t._isActiveEvents.set(n,!1)})))},e}();function X(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}var H=function(e){function t(t){var n;try{n=window.sessionStorage||R}catch(e){n=R}return e.call(this,n,t)||this}return Object(d.__extends)(t,e),t}(P),$=function(){function e(){var e=this;this._generateNewTabId=function(){var t=X();return e._safeSessionStorage.setItem("tab.id",t),t},this._safeSessionStorage=new H}return e.prototype.getCurrentTabId=function(){var e=this._safeSessionStorage.getItem("tab.id");return e||(e=this._generateNewTabId()),e},e}(),q=/^\d+$/,W=function(){function e(e){var t=this;this._generateNewSessionId=function(){var e=Date.now().toString();return t._safeLocalStorage.setItem("session.id",e),e},this._updateSessionExpiry=function(){var e=Date.now()+t._sessionExpiryTime;return t._safeLocalStorage.setItem("session.expiry",e),e},this._sessionExpiryTime=e&&e.sessionExpiryTime||18e5,this._safeLocalStorage=new L}return e.prototype.getCurrentSessionId=function(){var e=this._safeLocalStorage.getItem("session.id"),t=parseInt(this._safeLocalStorage.getItem("session.expiry"),10);return this._updateSessionExpiry(),!e||t<=Date.now()||isNaN(t)?this._generateNewSessionId():q.test(e)?e:this._generateNewSessionId()},e}(),G=n(244),V=function(e,t){return Object.keys(e).reduce((function(n,r){return n[t(e[r],r,e)]=e[r],n}),{})},K=function(e,t){return Object.keys(e).filter((function(e){return t.indexOf(e)<0})).reduce((function(t,n){return t[n]=e[n],t}),{})},J=function(e,t){if(e===t)return!0;if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(!e||!t||"object"!=typeof e&&"object"!=typeof t)return e===t;if(e.prototype!==t.prototype)return!1;var n=Object.keys(e);return n.length===Object.keys(t).length&&n.every((function(n){return J(e[n],t[n])}))},Q=function(e,t){return e.reduce((function(e,n,r,i){return e[t(n,r,i)?0:1].push(n),e}),[[],[]])},Z=function(e){return window.history.replaceState({},"",e)},Y=function(e,t){void 0===t&&(t=Z);var n,r,i=window.location.href,o=window.location.hash,s=""!==o?i.replace(o,""):i,a=Object(G.parseUrl)(s),c=a.url,u=a.query,l=(n=u,r=e,Object.keys(n).filter((function(e){return r(n[e],e)})).reduce((function(e,t){return e[t]=n[t],e}),{}));if(Object.keys(l).length>0){var f=function(e,t){return Object.keys(e).filter((function(n){return!t(e[n],n)})).reduce((function(t,n){return t[n]=e[n],t}),{})}(u,e);if(Object.keys(f).length>0)t(c+"?"+Object(G.stringify)(f)+o);else t(""+c+o)}return l},ee="taskSessionsInit",te=function(){var e=this;this._taskSessionsNotRecentlyInitialised=function(t){return null===e._safeSessionStorage.getItem("taskSessions")||null===e._safeSessionStorage.getItem(ee)||e._safeSessionStorage.getItem(ee)<t-3e3},this._removeTaskSessionPrefix=function(e,t){return t.split("awc.")[1]},this._isTaskSessionQueryParam=function(e,t){return t.startsWith("awc.")},this.getAllTaskSessions=function(){try{return JSON.parse(e._safeSessionStorage.getItem("taskSessions"))}catch(e){return{}}},this._writeToSessionStorage=function(t){try{e._safeSessionStorage.setItem("taskSessions",JSON.stringify(t))}catch(e){}},this._appendTaskSessions=function(t){var n=e.getAllTaskSessions(),r=null===n?t:Object.assign(n,t);e._writeToSessionStorage(r)},this.createTaskSession=function(t){var n=X(),r={};return r[t]=n,e._appendTaskSessions(r),n},this.createTaskSessionWithProvidedId=function(t,n){if("string"!=typeof n)throw new TypeError("invalid taskSessionId, must be string");var r={};r[t]=n,e._appendTaskSessions(r)},this.completeTaskSession=function(t){var n=e.getAllTaskSessions();delete n[t],e._writeToSessionStorage(n)},this.formatTaskSessionQueryString=function(t){var n,r=t.uri,i=t.includedTaskSessions,o=Object(G.parseUrl)(r),s=o.url,a=o.query,c=e.getAllTaskSessions(),u=i?(n=c,i.reduce((function(e,t){return t in n&&(e[t]=n[t]),e}),{})):K(c,Object.values(j));if(0===Object.keys(u).length)return r;var l=Object(d.__assign)(Object(d.__assign)({},V(u,(function(e,t){return"awc."+t}))),a);return s+"?"+Object(G.stringify)(l)},this.stripQueryParameters=function(){var t=V(Y(e._isTaskSessionQueryParam),e._removeTaskSessionPrefix);e._appendTaskSessions(t)};var t=Date.now();this._safeSessionStorage=new H;try{this._taskSessionsNotRecentlyInitialised(t)&&(this._writeToSessionStorage({}),this._safeSessionStorage.setItem(ee,t))}catch(e){}},ne=function(){this.handleOriginParameters=function(e,t){var n=Object.keys(e);if(!n.length>0)throw new Error("Empty parameter mapping provided");var r=n.filter((function(e){return Object.values(j).indexOf(e)<0}));if(r.length>0)throw new Error("Invalid Origin Tracing Parameter(s) supplied: "+r.join()+"!");var i=Y((function(e,t){return n.indexOf(t)>=0}),t);return Object.keys(i).map((function(t){var n,r=e[t](i[t]);return(n={})[t]=r,n})).reduce((function(e,t){return Object.assign(e,t)}),{})},this._originStore={}},re=n(562);function ie(e){return{tenantIdType:e.tenantIdType,tenantId:e.tenantId}}function oe(e){return{userIdType:e.userIdType}}function se(e){return{orgId:e.orgId}}function ae(e,t){return void 0===t?e:t}function ce(e,t){var n=t||{};return{env:ae(e.env,n.env),product:ae(e.product,n.product),subproduct:ae(e.subproduct(),n.subproduct),version:ae(e.version,n.version),origin:ae(e.origin,n.origin),platform:ae(e.platform,n.platform)}}function ue(e){return Object(re.isPlainObject)(e)&&Object.keys(e).length>0}function le(e,t){var n;return ue(t)?((n={})[e]=t,n):void 0}function fe(e){if(ue(e))return le("containers",function(e){var t={};return Object.keys(e).forEach((function(n){var r=e[n];t[n]={id:r.id,type:r.type}})),t}(e))}function de(e,t){return Object(d.__assign)({containerType:e.containerType,containerId:e.containerId,source:e.source,objectType:e.objectType,objectId:e.objectId,actionSubject:e.actionSubject,action:e.action,actionSubjectId:e.actionSubjectId,attributes:e.attributes,nonPrivacySafeAttributes:e.nonPrivacySafeAttributes,tags:e.tags,highPriority:e.highPriority,eventType:t},fe(e.containers))}function pe(e,t,n,r,i,o,s,a,c,u,l){var f=ce(e),p=ie(t),h=oe(n),m=se(u),g={title:"",path:"",url:"",referrer:"",search:"",eventType:E.SCREEN};return Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)({},f),p),h),m),g),function(e){return le("attributes",e)}(r)),function(e){return le("nonPrivacySafeAttributes",e)}(i)),fe(l)),{tags:o,tabId:s,sessionId:a,taskSessions:c})}function he(e){return e.actionSubject+" "+e.action}function me(e,t,n,r,i,o,s,a,c){var u=ce(e,function(e){return{env:e.env,product:e.product,subproduct:e.subproduct,version:e.version,origin:e.origin,platform:e.platform}}(r)),l=ie(t),f=se(c),p=oe(n),h=de(r,i);return Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)(Object(d.__assign)({},u),l),f),p),h),{tabId:o,sessionId:s,taskSessions:a})}var ge=Object.freeze([E.OPERATIONAL,E.TRACK,E.UI]);function ve(e){if(e){if(e&&e.constructor!==Object)throw new Error("properties.containers must be an Object");Object.values(e).forEach((function(e){return function(e){if(!Object(re.isPlainObject)(e))throw new Error("properties.containers must be an Object");if(!("id"in e))throw new Error("properties.containers is missing field 'id'");if("string"!=typeof e.id)throw new Error("properties.containers.id must be of type String");if("type"in e&&"string"!=typeof e.type)throw new Error("properties.containers.type must be of type String")}(e)}))}}function ye(e){switch(e){case E.OPERATIONAL:case E.TRACK:case E.UI:return!0;default:return!1}}function _e(e){if(!e)throw new Error("Missing event");if(!e.source)throw new Error("Missing event.source");if(!e.actionSubject)throw new Error("Missing event.actionSubject");if(!e.action)throw new Error("Missing event.action");ve(e.containers)}function be(e){if(!e)throw new Error("Missing event");if(!e.source)throw new Error("Missing event.source");if(!e.actionSubject)throw new Error("Missing event.actionSubject")}var we=n(427),Ie=n.n(we),Ee=new function(){var e=this;this._bind=function(){window.testAnalytics||(window.testAnalytics={eventCache:[],events:function(){return window.testAnalytics.eventCache},clear:function(){window.testAnalytics.eventCache=[],localStorage.removeItem("awc.ui.viewed.last.sent"),localStorage.removeItem("awc-staging.ui.viewed.last.sent"),localStorage.removeItem("awc-dev.ui.viewed.last.sent"),localStorage.removeItem("awc-local.ui.viewed.last.sent")}})},this._cache=function(e){window.testAnalytics.eventCache.length>=100&&(window.testAnalytics.eventCache=window.testAnalytics.eventCache.slice(1,window.testAnalytics.eventCache.length)),window.testAnalytics.eventCache.push(e)},this._shouldCacheEvent=function(){return!!Ie.a.get("atlassian_analytics_debug")},this.saveEvent=function(t){t&&e._shouldCacheEvent()&&(window.testAnalytics||e._bind(),t.tags instanceof Array?t.tags.push("synthetic"):t.tags=["synthetic"],e._cache(t))},this._shouldCacheEvent()&&this._bind()};function Te(e,t){return Ee.saveEvent(t),e&&"function"==typeof e?function(){e(t)}:null}var ke=function(e,t){var n="function"==typeof e;return function(){if(n)try{return e()}catch(e){return void console.error(t+" - "+e.message)}return e}},Ce=function(e,t){var n=this;if(this.canCompress=function(e){try{return e&&ye(e.eventType)&&n._predicate(e)}catch(e){return!1}},this.compress=function(e){var t=Object(d.__read)(Q(e,(function(e){return n.canCompress(e)})),2),r=t[0],i=t[1],o=n._compressFn(r);if(!o)throw new Error("No events were returned from the compression function");return o.forEach((function(e){!function(e){if(!ye(e))throw new Error("Invalid action event type: "+e+", must be one of: ["+ge+"]")}(e.eventType),_e(e)})),o.concat(i)},"function"!=typeof e)throw new Error("Invalid predicate, must be a function that accepts an event and returns a boolean");if("function"!=typeof t)throw new Error("Invalid compressFn, must be a function that both accepts and returns an array of events");this._predicate=e,this._compressFn=t},xe=function(e){var t=this;if(void 0===e&&(e=[]),this.canCompress=function(e){return t._compressionRules.some((function(t){return t.canCompress(e)}))},this.compress=function(e){return t._createGroups(e).reduce((function(e,n){return t._compressGroup(n).forEach((function(t){return e.push(t)})),e}),[])},this._createGroups=function(e){return e.reduce((function(e,n){for(var r,i=null,o=0;o<t._compressionRules.length;o++){var s=t._compressionRules[o];if(s.canCompress(n)){i=s;break}}if(i){var a=de(n,n.eventType);r=K(n,Object.keys(a))}var c=null;for(o=0;o<e.length;o++){var u=e[o];if(i===u.compressor&&J(r,u.contextFields)){c=u;break}}return c?c.events.push(n):e.push({contextFields:r,compressor:i,events:[n]}),e}),[])},this._compressGroup=function(e){if(!e.compressor)return e.events;try{return e.compressor.compress(e.events).map((function(t){return Object(d.__assign)(Object(d.__assign)({},t),e.contextFields)}))}catch(t){return console.warn("Failed to compress some analytics events. Error: "+t.message+". Sending "+e.events.length+" uncompressed events instead"),e.events}},!Array.isArray(e))throw new Error("Event compressors must be constructed with an array of CompressionRules");if(!e.every((function(e){return e instanceof Ce})))throw new Error("Event compressors can only be constructed with instances of CompressionRule");this._compressionRules=e},Se=function(e,t){var n=this;this.push=function(e,t,r,i){n._eventArgs.push({identifier:e,builtEvent:t,context:r,userInfo:i})},this.size=function(){return n._eventArgs.length},this.startFlush=function(){try{n._eventArgs=n._compressEventArgs(n._eventArgs)}catch(e){console.warn("Failed to perform compression on the delayed analytics events. Error: "+e.message+". Sending "+n._eventArgs.length+" uncompressed events instead")}n._flushNextBatch()},this.cancelFlush=function(){n._flushBatchTimeout&&(clearTimeout(n._flushBatchTimeout),n._flushBatchTimeout=null)},this._flushNextBatch=function(){n._eventArgs.splice(0,7).forEach((function(e){return n._processFn(e.identifier,e.builtEvent,e.context,e.userInfo)})),n._eventArgs.length>0?n._flushBatchTimeout=setTimeout((function(){return n._flushNextBatch()}),100):n._flushBatchTimeout=null},this._compressEventArgs=function(e){var t=Object(d.__read)(Q(e,(function(e){return n._compressor.canCompress(e.builtEvent)})),2),r=t[0],i=t[1],o=r.reduce((function(e,t){for(var n=null,r=0;r<e.length;r++){var i=e[r];if(J(i.userInfo,t.userInfo)&&J(i.context,t.context)){n=i;break}}return n?n.eventArgs.push(t):e.push({userInfo:t.userInfo,context:t.context,eventArgs:[t]}),e}),[]).reduce((function(e,t){try{var r=t.eventArgs.map((function(e){return e.builtEvent}));return n._compressor.compress(r).map((function(e){return{identifier:he(e),builtEvent:e,userInfo:t.userInfo,context:t.context}})).forEach((function(t){return e.push(t)})),e}catch(e){return console.warn("Failed to compress some analytics events. Error: "+e.message+". Sending "+t.eventArgs.length+" uncompressed events instead"),t.eventArgs}}),[]);return i.forEach((function(e){return o.push(e)})),o},this._processFn=e,this._flushBatchTimeout=null,this._eventArgs=[],this._compressor=new xe(t)},Ae=["atlassian.net","jira.com","jira-dev.com","admin.atlassian.com","admin.stg.atlassian.com"],Oe=function(e,t){var n=this;if(void 0===e&&(e={}),void 0===t&&(t={}),this._useStargate=function(e){return null==e||e},this._selectHost=function(e){var t=e.useStargate,r=e.env;return t?n._isTenantedHost()?window.location.host+"/gateway/api/gasv3/api/v1":r===I.PROD?"api-private.atlassian.com/gasv3/api/v1":"api-private.stg.atlassian.com/gasv3/api/v1":r===I.PROD?"as.atlassian.com/api/v1":"as.staging.atl-paas.net/api/v1"},this._isTenantedHost=function(){return Ae.filter((function(e){return n._endsWith(window.location.host,e)})).length>0},this._endsWith=function(e,t){return-1!==e.indexOf(t,e.length-t.length)},this._changeInternalUserId=function(e,t){n._analytics.user().id()!==e&&n._analytics.user().id(e),t&&n._analytics.user().anonymousId(t)},this._createSubproductGetter=function(e){return ke(e,"Cannot get subproduct from the callback. Proceeding without it.")},this._createEmbeddedProductGetter=function(e){return ke(e,"Cannot get embeddedProduct from the callback. Proceeding without it.")},this._getLastScreenEvent=function(){try{return JSON.parse(n._safeSessionStorage.getItem("last.screen.event"))}catch(e){return n._safeSessionStorage.removeItem("last.screen.event"),null}},this._setLastScreenEvent=function(e){n._safeSessionStorage.setItem("last.screen.event",JSON.stringify({name:e.name,attributes:e.attributes}))},this._shouldEventBeDelayed=function(e){if(!e.tags||-1===e.tags.indexOf("measurement"))return!1;var t=!1!==e.highPriority;return n._isDelayingLowPriorityEvents&&!t},this._fireEvent=function(e,t,r,i){switch(t.eventType){case E.UI:case E.OPERATIONAL:case E.TRACK:return n._analytics.track(e,t,r,i);case E.SCREEN:return n._analytics.page(e,t,r,i);case E.IDENTIFY:return n._analytics.identify(e,t,r,i);default:throw new Error("No handler has been defined for events of type "+t.eventType)}},this._fireDelayedEvent=function(e,t,r,i){try{n._changeInternalUserId(i.userId,i.anonymousId),t.tags=Object(d.__spread)(t.tags||[],["sentWithDelay"]),n._fireEvent(e,t,r,void 0)}finally{n._changeInternalUserId(n._userInfo.userId,n._userInfo.anonymousId)}},this._delayEvent=function(e,t,r,i,o){n._delayQueue.push(e,t,r,i),o&&o()},this._processEvent=function(e,t,r,i){n._shouldEventBeDelayed(t)?n._delayEvent(e,t,r,n._userInfo,i):n._fireEvent(e,t,r,i)},this.setEmbeddedProduct=function(e){n._productInfo.embeddedProduct=n._createEmbeddedProductGetter(e),n.resetUIViewedTimers()},this.clearEmbeddedProduct=function(){n._productInfo.embeddedProduct=n._createEmbeddedProductGetter(null)},this.setSubproduct=function(e){n._productInfo.subproduct=n._createSubproductGetter(e),n.resetUIViewedTimers()},this.setOriginTracingHandlers=function(e){var t=n.originTracing.handleOriginParameters(e,n._historyReplaceFn);Object.keys(t).forEach((function(e){void 0!==t[e].taskSessionId&&n.task.createTaskSessionWithProvidedId(e,t[e].taskSessionId)}));var r={};Object.keys(t).forEach((function(e){t[e].originTracingAttributes?r[e]=t[e].originTracingAttributes:console.warn("Handling method for origin parameter "+e+" has not returned any attributes")})),Object.keys(t).length>0&&n.sendOperationalEvent({action:"landed",actionSubject:"origin",source:"webClient",attributes:{originTracesLanded:r}},(function(){}))},this.setTenantInfo=function(e,t){if(!e)throw new Error("Missing tenantIdType");if(e!==C.NONE&&!t)throw new Error("Missing tenantId");if(!D(C,e))throw new Error("Invalid tenantIdType '"+e+"', must be an tenantType: ["+M(C)+"]");n._tenantInfo={tenantIdType:e,tenantId:t}},this.clearTenantInfo=function(){n._tenantInfo={}},this.setOrgInfo=function(e){if(!e)throw new Error("Missing orgId");n._orgInfo={orgId:e}},this.clearOrgInfo=function(){n._orgInfo={}},this.setUserInfo=function(e,t){!function(e,t){if(!e)throw new Error("Missing userIdType");if(!t)throw new Error("Missing userId");if(!D(x,e))throw new Error("Invalid userIdType '"+e+"', must be an userType: ["+M(x)+"]")}(e,t),n._changeInternalUserId(t),n._userInfo={userIdType:e,userId:t,anonymousId:n._analytics.user().anonymousId()}},this.clearUserInfo=function(){n._changeInternalUserId(null),n._userInfo={anonymousId:n._analytics.user().anonymousId()}},this.setUIViewedAttributes=function(e){if(!e)throw new Error("Missing uiViewedAttributes");if("object"!=typeof e||Array.isArray(e))throw new Error("Invalid uiViewedAttributes type, should be a non array object");n._uiViewedAttributes=Object(d.__assign)({},e)},this.clearUIViewedAttributes=function(){n._uiViewedAttributes={}},this.sendIdentifyEvent=function(e,t,r){n.setUserInfo(e,t);var i={userIdType:e,eventType:E.IDENTIFY};n._processEvent(t,i,n._context,r)},this.sendPageEvent=function(e,t){n.sendScreenEvent(e,t)},this.sendScreenEvent=function(e,t,r){var i,o,s,a;"object"==typeof e?(i=e.name,o=e.attributes,s=e.containers,a=e.tags):(i=e,o=r),function(e){if(!e)throw new Error("Missing name")}(i),ve(s);var c=pe(n._productInfo,n._tenantInfo,n._userInfo,o,e.nonPrivacySafeAttributes,a,n._tabTracking.getCurrentTabId(),n._sessionTracking.getCurrentSessionId(),n.task.getAllTaskSessions(),n._orgInfo,s),u=Object(d.__assign)({name:i},c);n._setLastScreenEvent(u),n._analytics.page(i,c,n._context,Te(t,u))},this.sendTrackEvent=function(e,t){!function(e){_e(e)}(e);var r=me(n._productInfo,n._tenantInfo,n._userInfo,e,E.TRACK,n._tabTracking.getCurrentTabId(),n._sessionTracking.getCurrentSessionId(),n.task.getAllTaskSessions(),n._orgInfo);n._processEvent(he(e),r,n._context,Te(t,r))},this.sendUIEvent=function(e,t){!function(e){_e(e)}(e);var r=me(n._productInfo,n._tenantInfo,n._userInfo,e,E.UI,n._tabTracking.getCurrentTabId(),n._sessionTracking.getCurrentSessionId(),n.task.getAllTaskSessions(),n._orgInfo);n._processEvent(he(e),r,n._context,Te(t,r))},this.sendOperationalEvent=function(e,t){!function(e){_e(e)}(e);var r=me(n._productInfo,n._tenantInfo,n._userInfo,e,E.OPERATIONAL,n._tabTracking.getCurrentTabId(),n._sessionTracking.getCurrentSessionId(),n.task.getAllTaskSessions(),n._orgInfo);n._processEvent(he(e),r,n._context,Te(t,r))},this.startUIViewedEvent=function(e){n.stopUIViewedEvent(),n._uiViewedEvent=new F(n._productInfo,(function(){return{embeddedProduct:n._productInfo.embeddedProduct(),subproduct:n._productInfo.subproduct(),tenantIdType:n._tenantInfo.tenantIdType,tenantId:n._tenantInfo.tenantId,userId:n._userInfo.userId,lastScreenEvent:n._getLastScreenEvent(),attributes:n._uiViewedAttributes}}),(function(t){return n.sendUIEvent(t,e)})),n._uiViewedEvent.start()},this.stopUIViewedEvent=function(){n._uiViewedEvent&&(n._uiViewedEvent.stop(),n._uiViewedEvent=null)},this.resetUIViewedTimers=function(){n._uiViewedEvent&&n._uiViewedEvent.resetTimers()},this.startApdexEvent=function(e){n._apdexEvent.start(e)},this.getApdexStart=function(e){return n._apdexEvent.getStart(e)},this.stopApdexEvent=function(e,t){n._apdexEvent.stop(e,t)},this.startLowPriorityEventDelay=function(e){if(void 0!==e&&(isNaN(e)||e<=0))throw new Error("Invalid timeout period: "+e+", must be a number greater than 0");n._delayTimeout&&clearTimeout(n._delayTimeout);var t=e?Math.min(e,1e4):1e4;n._delayTimeout=setTimeout((function(){return n.stopLowPriorityEventDelay()}),t),n._delayQueue.cancelFlush(),n._isDelayingLowPriorityEvents=!0},this.stopLowPriorityEventDelay=function(){n._delayTimeout&&(clearTimeout(n._delayTimeout),n._delayTimeout=null),n._delayQueue.startFlush(),n._isDelayingLowPriorityEvents=!1},this.onEvent=function(e,t){if(!t)throw new Error("Missing analyticsData");if(!t.eventType)throw new Error("Missing analyticsData.eventType");if(!D(E,t.eventType))throw new Error("Invalid analyticsData.eventType '"+t.eventType+"', must be an eventType: ["+M(E)+"]");t.eventType===E.TRACK?n.sendTrackEvent(t):t.eventType===E.UI?n.sendUIEvent(t):t.eventType===E.OPERATIONAL?n.sendOperationalEvent(t):t.eventType===E.SCREEN?n.sendScreenEvent(t.name,null,t.attributes):t.eventType===E.IDENTIFY&&n.sendIdentifyEvent(t.userIdType,t.userId)},!e)throw new Error("Missing productInfo");if(!e.env)throw new Error("Missing productInfo.env");if(!e.product)throw new Error("Missing productInfo.product");if(!D(I,e.env))throw new Error("Invalid productInfo.env '"+e.env+"', must be an envType: ["+M(I)+"]");if(e.origin){if(!D(k,e.origin))throw new Error("Invalid productInfo.origin '"+e.origin+"', must be an originType: ["+M(k)+"]")}else e.origin=k.WEB;e.platform?function(e){if(!D(T,e.platform))throw new Error("Invalid productInfo.platform '"+e.platform+"', must be a platformType: ["+M(T)+"]");if(e.origin===k.DESKTOP&&!D(A,e.platform))throw new Error("Invalid productInfo.platform '"+e.platform+"', must be one of [mac, linux, windows]");if(e.origin===k.WEB&&!D(O,e.platform))throw new Error("Invalid productInfo.platform '"+e.platform+"', must be one of [web, mobileWeb]")}(e):e.platform=e.origin===k.WEB?T.WEB:T.DESKTOP,e.embeddedProduct=this._createEmbeddedProductGetter(e.embeddedProduct),e.subproduct=this._createSubproductGetter(e.subproduct);var r=new m.a;this._productInfo=Object(d.__assign)({},e),this._tenantInfo={},this._orgInfo={},this._userInfo={anonymousId:r.user().anonymousId()},this._uiViewedAttributes={},this._context=function(e){var t=window.screen||{};return{context:{locale:e.locale,screen:{width:t.width,height:t.height,density:window.devicePixelRatio},library:{name:"analytics.js",version:"1.14.2"}}}}(this._productInfo),this._safeSessionStorage=new H;var i=this._useStargate(t.useStargate),o=t.maxRetryAttempts||4,s=t.minRetryDelay||1e3;r.use(w),r.use(y.a),r.init({BeforeSend:{},"Segment.io":{apiKey:t.apiKey||"",apiHost:t.apiHost||this._selectHost({useStargate:i,env:e.env}),retryQueue:!0,retryQueuePrefix:"awc-"+e.env,addBundledMetadata:!0,unbundledIntegrations:["Amplitude"],retryQueueOptions:{maxRetryDelay:6e4,minRetryDelay:s,backoffFactor:2,maxAttempts:o,maxItems:100},xidPromiseGetter:function(){if(t.xidConsent){var n=function(){return[{type:g.XIDItemType.XC,state:"TIMEOUT"},{type:g.XIDItemType.UID,state:"TIMEOUT"}]},r=function(){return[{type:g.XIDItemType.XC,state:"UNKNOWN"},{type:g.XIDItemType.UID,state:"UNKNOWN"}]},i=Promise||p.default;return i.race([new g.XID(e.env,5e3).getXidCallbackForPromise().catch((function(){return r})),new i((function(e){setTimeout((function(){return e(n)}),5e3)}))])}}}},{cookie:{secure:"https:"===window.location.protocol},user:{persist:!1,cookie:{},localStorage:{},localStorageFallbackDisabled:!0}}),this._analytics=r,this._pageVisibility=new z,this._tabTracking=new $,this._sessionTracking=new W({sessionExpiryTime:t.sessionExpiryTime}),this.task=new te,this.originTracing=new ne,this._apdexEvent=new B(this.sendOperationalEvent,this._pageVisibility),this._historyReplaceFn="function"==typeof t.historyReplaceFn?t.historyReplaceFn:Z,this._delayQueue=new Se(this._fireDelayedEvent,t.delayQueueCompressors)};function je(e,t,n){var r=Object(d.__assign)({action:e},n);return r.attributes=Object(d.__assign)(Object(d.__assign)({},t),r.attributes),r}var Me=function(){function e(e,t){this._onEvent=e,this._baseEvent=t,this._searchStartedTime=null,this._lastQueryResultsTime=null,this._queryLength=null,this._baseEvent=Object(d.__assign)({},t)}return e.prototype.search=function(e){if(!e&&""!==e)throw new Error("Missing query param");this._searchStartedTime=Date.now(),this._queryLength=(e||"").length},e.prototype.searched=function(e){if(!e)throw new Error("Missing results param");if(null===this._queryLength)throw new Error("search() must be called before searched().");var t=Date.now()-this._searchStartedTime;this._lastQueryResultsTime=Date.now(),this._lastResults=e;var n=je("searched",{responseTimeMs:t,queryLength:this._queryLength,results:e},this._baseEvent);this._onEvent(n)},e.prototype.selected=function(e){if(!e&&0!==e)throw new Error("Missing selectionIndex param");if(!this._lastQueryResultsTime)throw new Error("searched() must be called before selected().");var t=Date.now(),n=t-this._lastQueryResultsTime,r=t-this._searchStartedTime,i=this._lastResults[e],o=je("selected",{queryLength:this._queryLength,selectionIndex:e,selectionTimeMs:n,searchTimeMs:r,selectedResultValue:i},this._baseEvent);this._onEvent(o)},e}(),De=n(252),Re=n.n(De),Ne={initialPollInterval:5,pollBackoffRate:2,idleTimeout:30},Pe=function(e,t,n,r){var i=this;if(this.start=function(){if(Re.a.active)throw new Error("DwellTimeHelper should only be initialised once per page load.");i._initDwellTimeCounterAndPolling(),window.addEventListener("beforeunload",(function(){i.stop()}))},this.stop=function(){Re.a.active&&i._sendDwellEvent(i._eventData,i._searchSessionId,!0),Re.a.stopAllTimers(),Re.a.resetAllRecordedPageTimes(),Re.a.timeElapsedCallbacks=[]},this.newPage=function(e,t){be(e),i.stop(),i._eventData=e,i._searchSessionId=t,i._initDwellTimeCounterAndPolling()},this._initDwellTimeCounterAndPolling=function(){Re.a.initialize({idleTimeoutInSeconds:i._timing.idleTimeout}),i._pollDwellTime(i._timing.initialPollInterval,i._searchSessionId,i._eventData)},this._pollDwellTime=function(e,t,n){Re.a.callAfterTimeElapsedInSeconds(e,(function(){i._sendDwellEvent(n,t,!1),i._pollDwellTime(e*i._timing.pollBackoffRate,t,n)}))},this._sendDwellEvent=function(e,t,n){var r={dwellTime:Re.a.getTimeOnCurrentPageInMilliseconds(),searchReferrer:t,finalDwellEvent:n};i._trackCallback({action:"dwelled",actionSubject:e.actionSubject,actionSubjectId:e.actionSubjectId,source:e.source,containerType:e.containerType,containerId:e.containerId,containers:e.containers,objectType:e.objectType,objectId:e.objectId,attributes:Object(d.__assign)(Object(d.__assign)({},r),e.attributes),tags:e.tags})},!e)throw new Error("trackCallback is required for DwellTimeHelper");be(t),this._trackCallback=e,this._eventData=t,this._searchSessionId=n,this._timing=Object(d.__assign)(Object(d.__assign)({},Ne),r)},Le=n(1106),Fe={initialPollInterval:5e3,idleTimeout:3e4,multiplierCallback:function(e){return e+e}},Ue=function(e,t,n,r){var i=this;if(this.start=function(){i.browserInteractionTime.startTimer(),i.browserInteractionTime.addTimeIntervalEllapsedCallback({callback:function(e){return i._sendDwellEvent(i._eventData,i._searchSessionId,e,!1)},timeInMilliseconds:i._timing.initialPollInterval,multiplier:i._timing.multiplierCallback}),window.addEventListener("beforeunload",i._onBeforeUnload)},this.stop=function(){i.browserInteractionTime.isRunning()&&i._sendDwellEvent(i._eventData,i._searchSessionId,i.browserInteractionTime.getTimeInMilliseconds(),!0),i.browserInteractionTime.stopTimer()},this.newPage=function(e,t){be(e),i.stop(),i.browserInteractionTime.reset(),i.setEventData(e),i.setSearchSessionId(t),i.start()},this.setEventData=function(e){i._eventData=e},this.getEventData=function(){return i._eventData},this.setSearchSessionId=function(e){i._searchSessionId=e},this.getSearchSessionId=function(){return i._searchSessionId},this.destroy=function(){i.browserInteractionTime.destroy(),window.removeEventListener("beforeunload",i._onBeforeUnload)},this._onBeforeUnload=function(){i.stop(),i.destroy()},this._sendDwellEvent=function(e,t,n,r){var o={dwellTime:n,searchReferrer:t,finalDwellEvent:r};i._trackCallback({action:"dwelled",actionSubject:e.actionSubject,actionSubjectId:e.actionSubjectId,source:e.source,containerType:e.containerType,containerId:e.containerId,objectType:e.objectType,objectId:e.objectId,attributes:Object(d.__assign)(Object(d.__assign)({},o),e.attributes),tags:e.tags})},!e)throw new Error("trackCallback is required for DwellTimeHelper");be(t),this._trackCallback=e,this._eventData=t,this._searchSessionId=n,this._timing=Object(d.__assign)(Object(d.__assign)({},Fe),r),this.browserInteractionTime=new Le.a({idleTimeoutMs:this._timing.idleTimeout,checkCallbacksIntervalMs:500})};void 0!==window.awc_resolve&&null!==window.awc_resolve||(window.awc=new Promise((function(e,t){window.awc_resolve=e}))),window.awc_resolve(r),Object.assign(window,{Bloodhound:o.a,Strftime:a.a,_:u.a,typeahead:f.a})},2104:function(e,t,n){"use strict";n.r(t);var r=n(1),i=n(1105),o=n.n(i),s=1e3,a=3e4,c=2,u=0,l=500,f="none",d="immediate",p="wait",h=function(e,t){var n=this;this.schedule=function(e){var t=(void 0===e?{immediate:!1}:e).immediate,r=void 0!==t&&t;n.operationInFlight?r?n.queuedSchedule=d:n.queuedSchedule!==d&&(n.queuedSchedule=p):r&&0===n.failureCount?(n._clearTimeout(),n._run()):n.failureCount>0&&null===n.scheduledTimeout?n.scheduledTimeout=window.setTimeout(n._run,n._calculateBackoff()):null===n.scheduledTimeout&&(n.scheduledTimeout=window.setTimeout(n._run,n.options.waitInterval))},this.stop=function(){n._clearTimeout(),n.queuedSchedule=f},this.getFailureCount=function(){return n.failureCount},this._clearTimeout=function(){null!==n.scheduledTimeout&&(window.clearTimeout(n.scheduledTimeout),n.scheduledTimeout=null)},this._run=function(){n.operationInFlight=!0,n._clearTimeout();try{n.callback(n._done)}catch(e){n._done(e)}},this._done=function(e){n.operationInFlight=!1,e?(n.failureCount++,n._clearTimeout()):n.failureCount=0,n._processQueuedSchedule()},this._processQueuedSchedule=function(){if(n.queuedSchedule!==f){var e=n.queuedSchedule===d;n.queuedSchedule=f,n.schedule({immediate:e})}},this._calculateBackoff=function(){var e=n.options,t=e.minRetryDelay,r=e.maxRetryDelay,i=e.backoffFactor,o=e.backoffJitter,s=t*Math.pow(i,n.failureCount);if(o){var a=Math.random(),c=Math.floor(a*o*s);Math.floor(10*a)<5?s-=c:s+=c}return Number(Math.min(s,r).toPrecision(1))},this.options={minRetryDelay:e.minRetryDelay||s,maxRetryDelay:e.maxRetryDelay||a,backoffFactor:e.backoffFactor||c,backoffJitter:e.backoffJitter||u,waitInterval:e.waitInterval||l},this.scheduledTimeout=null,this.failureCount=0,this.callback=t,this.operationInFlight=!1,this.queuedSchedule=f},m=function(){var e=this;this.addItem=function(t){return e.inProgressEventIds.add(t.msg.messageId)},this.done=function(t,n){var i,o=n.map((function(e){return e.msg.messageId}));t||((i=e.pastEventIds).push.apply(i,Object(r.__spread)(o)),e.pastEventIds.length>100&&e.pastEventIds.splice(0,e.pastEventIds.length-100)),o.forEach((function(t){return e.inProgressEventIds.delete(t)}))},this.hasEventBeenSeen=function(t){return e.pastEventIds.indexOf(t.msg.messageId)>=0||e.inProgressEventIds.has(t.msg.messageId)},this.pastEventIds=[],this.inProgressEventIds=new Set},g={minRetryDelay:1,maxRetryDelay:1,backoffFactor:1,backoffJitter:0},v=function(e,t,n){var i=this;this.start=function(){i.queue.start()},this.getDiscardCounter=function(){return i.discardCounter},this.getOverflowCounter=function(){return i.overflowCounter},this.getDuplicateCounter=function(){return i.duplicateCounter},this.getGlobalRetryCount=function(){return i.scheduler.getFailureCount()},this._flush=function(e){if(0!==i.currentBatch.length){var t=i.currentBatch.splice(0,i.flushBatchSize),n=t.map((function(e){return e.item}));n.forEach((function(e){e.msg&&e.msg._metadata&&e.msg.messageId&&(e.msg._metadata.failedAttempts=i.messageFailedAttemptCounter[e.msg.messageId]||0)}));var r=n.map((function(e){return e.msg.messageId}));i.batchProcessFunc(n,(function(o,s){i.duplicateEventDetector.done(o,n),t.forEach((function(e){return e.done(o,s)})),e(o),o?i._incrementMessageIdCounters(r):(i.discardCounter=0,i.overflowCounter=0,i.duplicateCounter=0,i._resetMessageIdCounters(r)),i.currentBatch.length>0&&i._schedule()}))}else e()},this._incrementMessageIdCounters=function(e){e.forEach((function(e){i.messageFailedAttemptCounter[e]?i.messageFailedAttemptCounter[e]+=1:i.messageFailedAttemptCounter[e]=1}))},this._resetMessageIdCounters=function(e){e.forEach((function(e){return delete i.messageFailedAttemptCounter[e]}))},this._onDiscardedEvent=function(e){i.discardCounter+=1,delete i.messageFailedAttemptCounter[e.msg.messageId]},this._onOverflowEvent=function(e){i.overflowCounter+=1,delete i.messageFailedAttemptCounter[e.msg.messageId]},this._schedule=function(){i.scheduler.schedule({immediate:i.currentBatch.length>=i.flushBatchSize})},this._processSingleElement=function(e,t){if(i.duplicateEventDetector.hasEventBeenSeen(e))return t(),void i.duplicateCounter++;i.duplicateEventDetector.addItem(e),i.currentBatch.push({item:e,done:t}),i.currentBatch.length>i.maxQueueSize&&i.currentBatch.splice(0,i.currentBatch.length-i.maxQueueSize),i._schedule()},this.addItem=function(e){return i.queue.addItem(e)},this.stop=function(){i.scheduler.stop(),i.queue.stop()},this.on=function(e,t){return i.queue.on(e,t)},this.maxQueueSize=t.maxItems||500,this.queue=new o.a(e,Object(r.__assign)(Object(r.__assign)({},t),g),this._processSingleElement),this.flushBatchSize=t.batchFlushSize||7,this.batchProcessFunc=n,this.duplicateEventDetector=new m,this.currentBatch=[],this.scheduler=new h(Object(r.__assign)(Object(r.__assign)({},t),{waitInterval:t.flushWaitMs}),this._flush),this.queue.on("discard",this._onDiscardedEvent),this.queue.on("overflow",this._onOverflowEvent),this.discardCounter=0,this.overflowCounter=0,this.duplicateCounter=0,this.messageFailedAttemptCounter={}};t.default=v},252:function(e,t,n){var r;(function(){!function(n,i){if(e.exports)return e.exports=i();void 0===(r=function(){return n.TimeMe=i()}.apply(t,[]))||(e.exports=r)}(this,(function(){var e={startStopTimes:{},idleTimeoutMs:3e4,currentIdleTimeMs:0,checkStateRateMs:250,active:!1,idle:!1,currentPageName:"default-page-name",timeElapsedCallbacks:[],userLeftCallbacks:[],userReturnCallbacks:[],trackTimeOnElement:function(t){var n=document.getElementById(t);n&&(n.addEventListener("mouseover",(function(){e.startTimer(t)})),n.addEventListener("mousemove",(function(){e.startTimer(t)})),n.addEventListener("mouseleave",(function(){e.stopTimer(t)})),n.addEventListener("keypress",(function(){e.startTimer(t)})),n.addEventListener("focus",(function(){e.startTimer(t)})))},getTimeOnElementInSeconds:function(t){var n=e.getTimeOnPageInSeconds(t);return n||0},startTimer:function(t){if(t||(t=e.currentPageName),void 0===e.startStopTimes[t])e.startStopTimes[t]=[];else{var n=e.startStopTimes[t],r=n[n.length-1];if(void 0!==r&&void 0===r.stopTime)return}e.startStopTimes[t].push({startTime:new Date,stopTime:void 0}),e.active=!0},stopAllTimers:function(){for(var t=Object.keys(e.startStopTimes),n=0;n<t.length;n++)e.stopTimer(t[n])},stopTimer:function(t){t||(t=e.currentPageName);var n=e.startStopTimes[t];void 0!==n&&0!==n.length&&(void 0===n[n.length-1].stopTime&&(n[n.length-1].stopTime=new Date),e.active=!1)},getTimeOnCurrentPageInSeconds:function(){return e.getTimeOnPageInSeconds(e.currentPageName)},getTimeOnPageInSeconds:function(t){return void 0===e.getTimeOnPageInMilliseconds(t)?void 0:e.getTimeOnPageInMilliseconds(t)/1e3},getTimeOnCurrentPageInMilliseconds:function(){return e.getTimeOnPageInMilliseconds(e.currentPageName)},getTimeOnPageInMilliseconds:function(t){var n=e.startStopTimes[t];if(void 0!==n){for(var r=0,i=0;i<n.length;i++){var o=n[i].startTime,s=n[i].stopTime;void 0===s&&(s=new Date),r+=s-o}return Number(r)}},getTimeOnAllPagesInSeconds:function(){for(var t=[],n=Object.keys(e.startStopTimes),r=0;r<n.length;r++){var i=n[r],o=e.getTimeOnPageInSeconds(i);t.push({pageName:i,timeOnPage:o})}return t},setIdleDurationInSeconds:function(t){var n=parseFloat(t);if(!1!==isNaN(n))throw{name:"InvalidDurationException",message:"An invalid duration time ("+t+") was provided."};return e.idleTimeoutMs=1e3*t,this},setCurrentPageName:function(t){return e.currentPageName=t,this},resetRecordedPageTime:function(t){delete e.startStopTimes[t]},resetAllRecordedPageTimes:function(){for(var t=Object.keys(e.startStopTimes),n=0;n<t.length;n++)e.resetRecordedPageTime(t[n])},resetIdleCountdown:function(){e.idle&&e.triggerUserHasReturned(),e.idle=!1,e.currentIdleTimeMs=0},callWhenUserLeaves:function(e,t){this.userLeftCallbacks.push({callback:e,numberOfTimesToInvoke:t})},callWhenUserReturns:function(e,t){this.userReturnCallbacks.push({callback:e,numberOfTimesToInvoke:t})},triggerUserHasReturned:function(){if(!e.active)for(var t=0;t<this.userReturnCallbacks.length;t++){var n=this.userReturnCallbacks[t],r=n.numberOfTimesToInvoke;(isNaN(r)||void 0===r||r>0)&&(n.numberOfTimesToInvoke-=1,n.callback())}e.startTimer()},triggerUserHasLeftPage:function(){if(e.active)for(var t=0;t<this.userLeftCallbacks.length;t++){var n=this.userLeftCallbacks[t],r=n.numberOfTimesToInvoke;(isNaN(r)||void 0===r||r>0)&&(n.numberOfTimesToInvoke-=1,n.callback())}e.stopAllTimers()},callAfterTimeElapsedInSeconds:function(t,n){e.timeElapsedCallbacks.push({timeInSeconds:t,callback:n,pending:!0})},checkState:function(){for(var t=0;t<e.timeElapsedCallbacks.length;t++)e.timeElapsedCallbacks[t].pending&&e.getTimeOnCurrentPageInSeconds()>e.timeElapsedCallbacks[t].timeInSeconds&&(e.timeElapsedCallbacks[t].callback(),e.timeElapsedCallbacks[t].pending=!1);!1===e.idle&&e.currentIdleTimeMs>e.idleTimeoutMs?(e.idle=!0,e.triggerUserHasLeftPage()):e.currentIdleTimeMs+=e.checkStateRateMs},visibilityChangeEventName:void 0,hiddenPropName:void 0,listenForVisibilityEvents:function(){void 0!==document.hidden?(e.hiddenPropName="hidden",e.visibilityChangeEventName="visibilitychange"):void 0!==document.mozHidden?(e.hiddenPropName="mozHidden",e.visibilityChangeEventName="mozvisibilitychange"):void 0!==document.msHidden?(e.hiddenPropName="msHidden",e.visibilityChangeEventName="msvisibilitychange"):void 0!==document.webkitHidden&&(e.hiddenPropName="webkitHidden",e.visibilityChangeEventName="webkitvisibilitychange"),document.addEventListener(e.visibilityChangeEventName,(function(){document[e.hiddenPropName]?e.triggerUserHasLeftPage():e.triggerUserHasReturned()}),!1),window.addEventListener("blur",(function(){e.triggerUserHasLeftPage()})),window.addEventListener("focus",(function(){e.triggerUserHasReturned()})),document.addEventListener("mousemove",(function(){e.resetIdleCountdown()})),document.addEventListener("keyup",(function(){e.resetIdleCountdown()})),document.addEventListener("touchstart",(function(){e.resetIdleCountdown()})),window.addEventListener("scroll",(function(){e.resetIdleCountdown()})),setInterval((function(){e.checkState()}),e.checkStateRateMs)},websocket:void 0,websocketHost:void 0,setUpWebsocket:function(t){if(window.WebSocket&&t){var n=t.websocketHost;try{e.websocket=new WebSocket(n),window.onbeforeunload=function(n){e.sendCurrentTime(t.appId)},e.websocket.onopen=function(){e.sendInitWsRequest(t.appId)},e.websocket.onerror=function(e){console&&console.log("Error occurred in websocket connection: "+e)},e.websocket.onmessage=function(e){console&&console.log(e.data)}}catch(e){console&&console.error("Failed to connect to websocket host.  Error:"+e)}}return this},websocketSend:function(t){e.websocket.send(JSON.stringify(t))},sendCurrentTime:function(t){var n={type:"INSERT_TIME",appId:t,timeOnPageMs:e.getTimeOnCurrentPageInMilliseconds(),pageName:e.currentPageName};e.websocketSend(n)},sendInitWsRequest:function(t){var n={type:"INIT",appId:t};e.websocketSend(n)},initialize:function(t){var n=e.idleTimeoutMs||30,r=e.currentPageName||"default-page-name",i=void 0;t&&(n=t.idleTimeoutInSeconds||n,r=t.currentPageName||r,i=t.websocketOptions),e.setIdleDurationInSeconds(n).setCurrentPageName(r).setUpWebsocket(i).listenForVisibilityEvents(),e.startTimer()}};return e}))}).call(this)},280:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(1).__exportStar(n(419),t);var r=n(1822);t.XID=r.XID},310:function(e,t,n){"use strict";var r=n(311);r.Alias=n(1788),r.Group=n(1789),r.Identify=n(904),r.Track=n(674),r.Page=n(905),r.Screen=n(1791),r.Delete=n(1792),e.exports=r},311:function(e,t,n){"use strict";var r=n(1782),i=n(208).clone,o=n(1784),s=n(673),a=n(520),c=n(900),u=n(208).type;function l(e,t){"clone"in(t=t||{})||(t.clone=!0),t.clone&&(e=i(e)),"traverse"in t||(t.traverse=!0),e.timestamp="timestamp"in e?s(e.timestamp):new Date,t.traverse&&c(e),this.opts=t,this.obj=e}function f(e){return i(e)}l.prototype.proxy=function(e){var t=e.split("."),n=this[e=t.shift()]||this.field(e);return n?("function"==typeof n&&(n=n.call(this)||{}),0===t.length||(n=a(n,t.join("."))),this.opts.clone?f(n):n):n},l.prototype.field=function(e){var t=this.obj[e];return this.opts.clone?f(t):t},l.proxy=function(e){return function(){return this.proxy(e)}},l.field=function(e){return function(){return this.field(e)}},l.multi=function(e){return function(){var t=this.proxy(e+"s");if("array"===u(t))return t;var n=this.proxy(e);return n&&(n=[this.opts.clone?i(n):n]),n||[]}},l.one=function(e){return function(){var t=this.proxy(e);if(t)return t;var n=this.proxy(e+"s");return"array"===u(n)?n[0]:void 0}},l.prototype.json=function(){var e=this.opts.clone?i(this.obj):this.obj;return this.type&&(e.type=this.type()),e},l.prototype.options=function(e){var t=this.obj.options||this.obj.context||{},n=this.opts.clone?i(t):t;if(!e)return n;if(this.enabled(e)){var r=this.integrations(),o=r[e]||a(r,e);return"object"!=typeof o&&(o=a(this.options(),e)),"object"==typeof o?o:{}}},l.prototype.context=l.prototype.options,l.prototype.enabled=function(e){var t=this.proxy("options.providers.all");"boolean"!=typeof t&&(t=this.proxy("options.all")),"boolean"!=typeof t&&(t=this.proxy("integrations.all")),"boolean"!=typeof t&&(t=!0);var n=t&&o(e),r=this.integrations();if(r.providers&&r.providers.hasOwnProperty(e)&&(n=r.providers[e]),r.hasOwnProperty(e)){var i=r[e];n="boolean"!=typeof i||i}return!!n},l.prototype.integrations=function(){return this.obj.integrations||this.proxy("options.providers")||this.options()},l.prototype.active=function(){var e=this.proxy("options.active");return null==e&&(e=!0),e},l.prototype.anonymousId=function(){return this.field("anonymousId")||this.field("sessionId")},l.prototype.sessionId=l.prototype.anonymousId,l.prototype.groupId=l.proxy("options.groupId"),l.prototype.traits=function(e){var t=this.proxy("options.traits")||{},n=this.userId();for(var r in e=e||{},n&&(t.id=n),e){var i=null==this[r]?this.proxy("options.traits."+r):this[r]();null!=i&&(t[e[r]]=i,delete t[r])}return t},l.prototype.library=function(){var e=this.proxy("options.library");return e?"string"==typeof e?{name:e,version:null}:e:{name:"unknown",version:null}},l.prototype.device=function(){var e=this.proxy("context.device");"object"!==u(e)&&(e={});var t=this.library().name;return e.type||(t.indexOf("ios")>-1&&(e.type="ios"),t.indexOf("android")>-1&&(e.type="android")),e},l.prototype.userAgent=l.proxy("context.userAgent"),l.prototype.timezone=l.proxy("context.timezone"),l.prototype.timestamp=l.field("timestamp"),l.prototype.channel=l.field("channel"),l.prototype.ip=l.proxy("context.ip"),l.prototype.userId=l.field("userId"),r(l.prototype),e.exports=l},312:function(e,t){var n=Object.prototype.toString;e.exports=function(e){switch(n.call(e)){case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Array]":return"array";case"[object Error]":return"error"}return null===e?"null":void 0===e?"undefined":e!=e?"nan":e&&1===e.nodeType?"element":null!=(t=e)&&(t._isBuffer||t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t))?"buffer":typeof(e=e.valueOf?e.valueOf():Object.prototype.valueOf.apply(e));var t}},313:function(e,t,n){"use strict";var r=n(907);e.exports=function(e){for(var t in e){"function"==typeof e[t]&&(e[t]=r(e,e[t]))}return e}},314:function(e,t){function n(e){return n.enabled(e)?function(t){t=r(t);var i=new Date,o=i-(n[e]||i);n[e]=i,t=e+" "+t+" +"+n.humanize(o),window.console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}:function(){}}function r(e){return e instanceof Error?e.stack||e.message:e}e.exports=n,n.names=[],n.skips=[],n.enable=function(e){try{localStorage.debug=e}catch(e){}for(var t=(e||"").split(/[\s,]+/),r=t.length,i=0;i<r;i++)"-"===(e=t[i].replace("*",".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$"))},n.disable=function(){n.enable("")},n.humanize=function(e){return e>=36e5?(e/36e5).toFixed(1)+"h":e>=6e4?(e/6e4).toFixed(1)+"m":e>=1e3?(e/1e3|0)+"s":e+"ms"},n.enabled=function(e){for(var t=0,r=n.skips.length;t<r;t++)if(n.skips[t].test(e))return!1;for(t=0,r=n.names.length;t<r;t++)if(n.names[t].test(e))return!0;return!1};try{window.localStorage&&n.enable(localStorage.debug)}catch(e){}},351:function(e,t,n){"use strict";var r=n(312);e.exports=function e(t){var n=r(t);if("object"===n){var i={};for(var o in t)t.hasOwnProperty(o)&&(i[o]=e(t[o]));return i}if("array"===n){i=new Array(t.length);for(var s=0,a=t.length;s<a;s++)i[s]=e(t[s]);return i}if("regexp"===n){var c="";return c+=t.multiline?"m":"",c+=t.global?"g":"",c+=t.ignoreCase?"i":"",new RegExp(t.source,c)}return"date"===n?new Date(t.getTime()):t}},352:function(e,t,n){"use strict";var r=n(1797),i=n(1798),o=Object.prototype.hasOwnProperty,s=Object.prototype.toString,a=function(e){return Boolean(e)&&"object"==typeof e},c=function(e){return Boolean(e)&&"[object Object]"===s.call(e)},u=function(e,t,n,r){return o.call(t,r)&&void 0===e[r]&&(e[r]=n),t},l=function(e,t,n,r){return o.call(t,r)&&(c(e[r])&&c(n)?e[r]=d(e[r],n):void 0===e[r]&&(e[r]=n)),t},f=function(e,t){if(!a(t))return t;e=e||u;for(var n=r(2,arguments),i=0;i<n.length;i+=1)for(var o in n[i])e(t,n[i],n[i][o],o);return t},d=function(e){return f.apply(null,[l,e].concat(i(arguments)))};e.exports=function(e){return f.apply(null,[null,e].concat(i(arguments)))},e.exports.deep=d},353:function(e,t,n){(function(e,r){var i;/*! JSON v3.3.2 | http://bestiejs.github.io/json3 | Copyright 2012-2014, Kit Cambridge | http://kit.mit-license.org */(function(){var o=n(1799),s={function:!0,object:!0},a=s[typeof t]&&t&&!t.nodeType&&t,c=s[typeof window]&&window||this,u=a&&s[typeof e]&&e&&!e.nodeType&&"object"==typeof r&&r;function l(e,t){e||(e=c.Object()),t||(t=c.Object());var n=e.Number||c.Number,r=e.String||c.String,i=e.Object||c.Object,o=e.Date||c.Date,a=e.SyntaxError||c.SyntaxError,u=e.TypeError||c.TypeError,f=e.Math||c.Math,d=e.JSON||c.JSON;"object"==typeof d&&d&&(t.stringify=d.stringify,t.parse=d.parse);var p,h,m=i.prototype,g=m.toString,v=new o(-0xc782b5b800cec);try{v=-109252==v.getUTCFullYear()&&0===v.getUTCMonth()&&1===v.getUTCDate()&&10==v.getUTCHours()&&37==v.getUTCMinutes()&&6==v.getUTCSeconds()&&708==v.getUTCMilliseconds()}catch(e){}function y(e){if(void 0!==y[e])return y[e];var i;if("bug-string-char-index"==e)i="a"!="a"[0];else if("json"==e)i=y("json-stringify")&&y("json-parse");else{var s,a='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==e){var c=t.stringify,u="function"==typeof c&&v;if(u){(s=function(){return 1}).toJSON=s;try{u="0"===c(0)&&"0"===c(new n)&&'""'==c(new r)&&void 0===c(g)&&void 0===c(void 0)&&void 0===c()&&"1"===c(s)&&"[1]"==c([s])&&"[null]"==c([void 0])&&"null"==c(null)&&"[null,null,null]"==c([void 0,g,null])&&c({a:[s,!0,!1,null,"\0\b\n\f\r\t"]})==a&&"1"===c(null,s)&&"[\n 1,\n 2\n]"==c([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==c(new o(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==c(new o(864e13))&&'"-000001-01-01T00:00:00.000Z"'==c(new o(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==c(new o(-1))}catch(e){u=!1}}i=u}if("json-parse"==e){var l=t.parse;if("function"==typeof l)try{if(0===l("0")&&!l(!1)){var f=5==(s=l(a)).a.length&&1===s.a[0];if(f){try{f=!l('"\t"')}catch(e){}if(f)try{f=1!==l("01")}catch(e){}if(f)try{f=1!==l("1.")}catch(e){}}}}catch(e){f=!1}i=f}}return y[e]=!!i}if(!y("json")){var _=y("bug-string-char-index");if(!v)var b=f.floor,w=[0,31,59,90,120,151,181,212,243,273,304,334],I=function(e,t){return w[t]+365*(e-1970)+b((e-1969+(t=+(t>1)))/4)-b((e-1901+t)/100)+b((e-1601+t)/400)};if((p=m.hasOwnProperty)||(p=function(e){var t,n={};return(n.__proto__=null,n.__proto__={toString:1},n).toString!=g?p=function(e){var t=this.__proto__,n=e in(this.__proto__=null,this);return this.__proto__=t,n}:(t=n.constructor,p=function(e){var n=(this.constructor||t).prototype;return e in this&&!(e in n&&this[e]===n[e])}),n=null,p.call(this,e)}),h=function(e,t){var n,r,i,o=0;for(i in(n=function(){this.valueOf=0}).prototype.valueOf=0,r=new n)p.call(r,i)&&o++;return n=r=null,o?h=2==o?function(e,t){var n,r={},i="[object Function]"==g.call(e);for(n in e)i&&"prototype"==n||p.call(r,n)||!(r[n]=1)||!p.call(e,n)||t(n)}:function(e,t){var n,r,i="[object Function]"==g.call(e);for(n in e)i&&"prototype"==n||!p.call(e,n)||(r="constructor"===n)||t(n);(r||p.call(e,n="constructor"))&&t(n)}:(r=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],h=function(e,t){var n,i,o="[object Function]"==g.call(e),a=!o&&"function"!=typeof e.constructor&&s[typeof e.hasOwnProperty]&&e.hasOwnProperty||p;for(n in e)o&&"prototype"==n||!a.call(e,n)||t(n);for(i=r.length;n=r[--i];a.call(e,n)&&t(n));}),h(e,t)},!y("json-stringify")){var E={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},T=function(e,t){return("000000"+(t||0)).slice(-e)},k=function(e){for(var t='"',n=0,r=e.length,i=!_||r>10,o=i&&(_?e.split(""):e);n<r;n++){var s=e.charCodeAt(n);switch(s){case 8:case 9:case 10:case 12:case 13:case 34:case 92:t+=E[s];break;default:if(s<32){t+="\\u00"+T(2,s.toString(16));break}t+=i?o[n]:e.charAt(n)}}return t+'"'},C=function(e,t,n,r,i,o,s){var a,c,l,f,d,m,v,y,_,w,E,x,S,A,O,j;try{a=t[e]}catch(e){}if("object"==typeof a&&a)if("[object Date]"!=(c=g.call(a))||p.call(a,"toJSON"))"function"==typeof a.toJSON&&("[object Number]"!=c&&"[object String]"!=c&&"[object Array]"!=c||p.call(a,"toJSON"))&&(a=a.toJSON(e));else if(a>-1/0&&a<1/0){if(I){for(d=b(a/864e5),l=b(d/365.2425)+1970-1;I(l+1,0)<=d;l++);for(f=b((d-I(l,0))/30.42);I(l,f+1)<=d;f++);d=1+d-I(l,f),v=b((m=(a%864e5+864e5)%864e5)/36e5)%24,y=b(m/6e4)%60,_=b(m/1e3)%60,w=m%1e3}else l=a.getUTCFullYear(),f=a.getUTCMonth(),d=a.getUTCDate(),v=a.getUTCHours(),y=a.getUTCMinutes(),_=a.getUTCSeconds(),w=a.getUTCMilliseconds();a=(l<=0||l>=1e4?(l<0?"-":"+")+T(6,l<0?-l:l):T(4,l))+"-"+T(2,f+1)+"-"+T(2,d)+"T"+T(2,v)+":"+T(2,y)+":"+T(2,_)+"."+T(3,w)+"Z"}else a=null;if(n&&(a=n.call(t,e,a)),null===a)return"null";if("[object Boolean]"==(c=g.call(a)))return""+a;if("[object Number]"==c)return a>-1/0&&a<1/0?""+a:"null";if("[object String]"==c)return k(""+a);if("object"==typeof a){for(A=s.length;A--;)if(s[A]===a)throw u();if(s.push(a),E=[],O=o,o+=i,"[object Array]"==c){for(S=0,A=a.length;S<A;S++)x=C(S,a,n,r,i,o,s),E.push(void 0===x?"null":x);j=E.length?i?"[\n"+o+E.join(",\n"+o)+"\n"+O+"]":"["+E.join(",")+"]":"[]"}else h(r||a,(function(e){var t=C(e,a,n,r,i,o,s);void 0!==t&&E.push(k(e)+":"+(i?" ":"")+t)})),j=E.length?i?"{\n"+o+E.join(",\n"+o)+"\n"+O+"}":"{"+E.join(",")+"}":"{}";return s.pop(),j}};t.stringify=function(e,t,n){var r,i,o,a;if(s[typeof t]&&t)if("[object Function]"==(a=g.call(t)))i=t;else if("[object Array]"==a){o={};for(var c,u=0,l=t.length;u<l;c=t[u++],("[object String]"==(a=g.call(c))||"[object Number]"==a)&&(o[c]=1));}if(n)if("[object Number]"==(a=g.call(n))){if((n-=n%1)>0)for(r="",n>10&&(n=10);r.length<n;r+=" ");}else"[object String]"==a&&(r=n.length<=10?n:n.slice(0,10));return C("",((c={})[""]=e,c),i,o,r,"",[])}}if(!y("json-parse")){var x,S,A=r.fromCharCode,O={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},j=function(){throw x=S=null,a()},M=function(){for(var e,t,n,r,i,o=S,s=o.length;x<s;)switch(i=o.charCodeAt(x)){case 9:case 10:case 13:case 32:x++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=_?o.charAt(x):o[x],x++,e;case 34:for(e="@",x++;x<s;)if((i=o.charCodeAt(x))<32)j();else if(92==i)switch(i=o.charCodeAt(++x)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=O[i],x++;break;case 117:for(t=++x,n=x+4;x<n;x++)(i=o.charCodeAt(x))>=48&&i<=57||i>=97&&i<=102||i>=65&&i<=70||j();e+=A("0x"+o.slice(t,x));break;default:j()}else{if(34==i)break;for(i=o.charCodeAt(x),t=x;i>=32&&92!=i&&34!=i;)i=o.charCodeAt(++x);e+=o.slice(t,x)}if(34==o.charCodeAt(x))return x++,e;j();default:if(t=x,45==i&&(r=!0,i=o.charCodeAt(++x)),i>=48&&i<=57){for(48==i&&((i=o.charCodeAt(x+1))>=48&&i<=57)&&j(),r=!1;x<s&&((i=o.charCodeAt(x))>=48&&i<=57);x++);if(46==o.charCodeAt(x)){for(n=++x;n<s&&((i=o.charCodeAt(n))>=48&&i<=57);n++);n==x&&j(),x=n}if(101==(i=o.charCodeAt(x))||69==i){for(43!=(i=o.charCodeAt(++x))&&45!=i||x++,n=x;n<s&&((i=o.charCodeAt(n))>=48&&i<=57);n++);n==x&&j(),x=n}return+o.slice(t,x)}if(r&&j(),"true"==o.slice(x,x+4))return x+=4,!0;if("false"==o.slice(x,x+5))return x+=5,!1;if("null"==o.slice(x,x+4))return x+=4,null;j()}return"$"},D=function(e){var t,n;if("$"==e&&j(),"string"==typeof e){if("@"==(_?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];"]"!=(e=M());n||(n=!0))n&&(","==e?"]"==(e=M())&&j():j()),","==e&&j(),t.push(D(e));return t}if("{"==e){for(t={};"}"!=(e=M());n||(n=!0))n&&(","==e?"}"==(e=M())&&j():j()),","!=e&&"string"==typeof e&&"@"==(_?e.charAt(0):e[0])&&":"==M()||j(),t[e.slice(1)]=D(M());return t}j()}return e},R=function(e,t,n){var r=N(e,t,n);void 0===r?delete e[t]:e[t]=r},N=function(e,t,n){var r,i=e[t];if("object"==typeof i&&i)if("[object Array]"==g.call(i))for(r=i.length;r--;)R(i,r,n);else h(i,(function(e){R(i,e,n)}));return n.call(e,t,i)};t.parse=function(e,t){var n,r;return x=0,S=""+e,n=D(M()),"$"!=M()&&j(),x=S=null,t&&"[object Function]"==g.call(t)?N(((r={})[""]=n,r),"",t):n}}}return t.runInContext=l,t}if(!u||u.global!==u&&u.window!==u&&u.self!==u||(c=u),a&&!o)l(c,a);else{var f=c.JSON,d=c.JSON3,p=!1,h=l(c,c.JSON3={noConflict:function(){return p||(p=!0,c.JSON=f,c.JSON3=d,f=d=null),h}});c.JSON={parse:h.parse,stringify:h.stringify}}o&&(void 0===(i=function(){return h}.call(t,n,t,e))||(e.exports=i))}).call(this)}).call(this,n(495)(e),n(97))},418:function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty,i=String.prototype.charAt,o=Object.prototype.toString,s=function(e,t){return i.call(e,t)},a=function(e,t){return r.call(e,t)},c=function(e,t){t=t||a;for(var n=[],r=0,i=e.length;r<i;r+=1)t(e,r)&&n.push(String(r));return n};e.exports=function(e){return null==e?[]:(t=e,"[object String]"===o.call(t)?c(e,s):function(e){return null!=e&&"function"!=typeof e&&"number"==typeof e.length}(e)?c(e,a):function(e,t){t=t||a;var n=[];for(var r in e)t(e,r)&&n.push(String(r));return n}(e));var t}},419:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.TEST="test",e.LOCAL="local",e.DEV="dev",e.STAGE="stage",e.PROD="prod"}(t.Environment||(t.Environment={})),function(e){e.XC="xc",e.UID="uid"}(t.XIDItemType||(t.XIDItemType={})),function(e){e.NEW="NEW",e.CHANGED="CHANGED",e.EXISTING="EXISTING",e.TIMEOUT="TIMEOUT",e.ERROR="ERROR",e.UNAVAILABLE="UNAVAILABLE",e.MALFORMED="MALFORMED",e.UNKNOWN="UNKNOWN"}(t.XIDState||(t.XIDState={})),function(e){e.XC_ONLY="XC_ONLY",e.UID_ONLY="UID_ONLY",e.CACHE_ONLY="CACHE_ONLY",e.XC_UID="XC_UID",e.CACHE_XC_UID="CACHE_XC_UID"}(t.XIDGenerateMode||(t.XIDGenerateMode={}))},427:function(e,t,n){"use strict";var r=n(313),i=n(351),o=n(675),s=n(314)("analytics.js:cookie"),a=n(352),c=n(353),u=n(908);function l(e){this.options(e)}l.prototype.options=function(e){if(0===arguments.length)return this._options;e=e||{};var t="."+u(window.location.href);"."===t&&(t=null),this._options=a(e,{maxage:31536e6,path:"/",domain:t}),this.set("ajs:test",!0),this.get("ajs:test")||(s("fallback to domain=null"),this._options.domain=null),this.remove("ajs:test")},l.prototype.set=function(e,t){try{return t=c.stringify(t),o(e,t,i(this._options)),!0}catch(e){return!1}},l.prototype.get=function(e){try{var t=o(e);return t=t?c.parse(t):null}catch(e){return null}},l.prototype.remove=function(e){try{return o(e,null,i(this._options)),!0}catch(e){return!1}},e.exports=r(new l),e.exports.Cookie=l},50:function(e,t){e.exports=jQuery},520:function(e,t){function n(e){return function(t,n,r,i){var s;normalize=i&&function(e){return"function"==typeof e}(i.normalizer)?i.normalizer:o,n=normalize(n);for(var a=!1;!a;)c();function c(){for(s in t){var e=normalize(s);if(0===n.indexOf(e)){var r=n.substr(e.length);if("."===r.charAt(0)||0===r.length){n=r.substr(1);var i=t[s];return null==i?void(a=!0):n.length?void(t=i):void(a=!0)}}}s=void 0,a=!0}if(s)return null==t?t:e(t,s,r)}}function r(e,t){return e.hasOwnProperty(t)&&delete e[t],e}function i(e,t,n){return e.hasOwnProperty(t)&&(e[t]=n),e}function o(e){return e.replace(/[^a-zA-Z0-9\.]+/g,"").toLowerCase()}e.exports=n((function(e,t){if(e.hasOwnProperty(t))return e[t]})),e.exports.find=e.exports,e.exports.replace=function(e,t,r,o){return n(i).call(this,e,t,r,o),e},e.exports.del=function(e,t,i){return n(r).call(this,e,t,null,i),e}},521:function(e,t,n){"use strict";
/**!
 * is
 * the definitive JavaScript type testing library
 *
 * @copyright 2013-2014 Enrico Marino / Jordan Harband
 * @license MIT
 */var r,i=Object.prototype,o=i.hasOwnProperty,s=i.toString;"function"==typeof Symbol&&(r=Symbol.prototype.valueOf);var a=function(e){return e!=e},c={boolean:1,number:1,string:1,undefined:1},u=/^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$/,l=/^[A-Fa-f0-9]+$/,f={};f.a=f.type=function(e,t){return typeof e===t},f.defined=function(e){return void 0!==e},f.empty=function(e){var t,n=s.call(e);if("[object Array]"===n||"[object Arguments]"===n||"[object String]"===n)return 0===e.length;if("[object Object]"===n){for(t in e)if(o.call(e,t))return!1;return!0}return!e},f.equal=function(e,t){if(e===t)return!0;var n,r=s.call(e);if(r!==s.call(t))return!1;if("[object Object]"===r){for(n in e)if(!f.equal(e[n],t[n])||!(n in t))return!1;for(n in t)if(!f.equal(e[n],t[n])||!(n in e))return!1;return!0}if("[object Array]"===r){if((n=e.length)!==t.length)return!1;for(;n--;)if(!f.equal(e[n],t[n]))return!1;return!0}return"[object Function]"===r?e.prototype===t.prototype:"[object Date]"===r&&e.getTime()===t.getTime()},f.hosted=function(e,t){var n=typeof t[e];return"object"===n?!!t[e]:!c[n]},f.instance=f.instanceof=function(e,t){return e instanceof t},f.nil=f.null=function(e){return null===e},f.undef=f.undefined=function(e){return void 0===e},f.args=f.arguments=function(e){var t="[object Arguments]"===s.call(e),n=!f.array(e)&&f.arraylike(e)&&f.object(e)&&f.fn(e.callee);return t||n},f.array=Array.isArray||function(e){return"[object Array]"===s.call(e)},f.args.empty=function(e){return f.args(e)&&0===e.length},f.array.empty=function(e){return f.array(e)&&0===e.length},f.arraylike=function(e){return!!e&&!f.bool(e)&&o.call(e,"length")&&isFinite(e.length)&&f.number(e.length)&&e.length>=0},f.bool=f.boolean=function(e){return"[object Boolean]"===s.call(e)},f.false=function(e){return f.bool(e)&&!1===Boolean(Number(e))},f.true=function(e){return f.bool(e)&&!0===Boolean(Number(e))},f.date=function(e){return"[object Date]"===s.call(e)},f.date.valid=function(e){return f.date(e)&&!isNaN(Number(e))},f.element=function(e){return void 0!==e&&"undefined"!=typeof HTMLElement&&e instanceof HTMLElement&&1===e.nodeType},f.error=function(e){return"[object Error]"===s.call(e)},f.fn=f.function=function(e){if("undefined"!=typeof window&&e===window.alert)return!0;var t=s.call(e);return"[object Function]"===t||"[object GeneratorFunction]"===t||"[object AsyncFunction]"===t},f.number=function(e){return"[object Number]"===s.call(e)},f.infinite=function(e){return e===1/0||e===-1/0},f.decimal=function(e){return f.number(e)&&!a(e)&&!f.infinite(e)&&e%1!=0},f.divisibleBy=function(e,t){var n=f.infinite(e),r=f.infinite(t),i=f.number(e)&&!a(e)&&f.number(t)&&!a(t)&&0!==t;return n||r||i&&e%t==0},f.integer=f.int=function(e){return f.number(e)&&!a(e)&&e%1==0},f.maximum=function(e,t){if(a(e))throw new TypeError("NaN is not a valid value");if(!f.arraylike(t))throw new TypeError("second argument must be array-like");for(var n=t.length;--n>=0;)if(e<t[n])return!1;return!0},f.minimum=function(e,t){if(a(e))throw new TypeError("NaN is not a valid value");if(!f.arraylike(t))throw new TypeError("second argument must be array-like");for(var n=t.length;--n>=0;)if(e>t[n])return!1;return!0},f.nan=function(e){return!f.number(e)||e!=e},f.even=function(e){return f.infinite(e)||f.number(e)&&e==e&&e%2==0},f.odd=function(e){return f.infinite(e)||f.number(e)&&e==e&&e%2!=0},f.ge=function(e,t){if(a(e)||a(t))throw new TypeError("NaN is not a valid value");return!f.infinite(e)&&!f.infinite(t)&&e>=t},f.gt=function(e,t){if(a(e)||a(t))throw new TypeError("NaN is not a valid value");return!f.infinite(e)&&!f.infinite(t)&&e>t},f.le=function(e,t){if(a(e)||a(t))throw new TypeError("NaN is not a valid value");return!f.infinite(e)&&!f.infinite(t)&&e<=t},f.lt=function(e,t){if(a(e)||a(t))throw new TypeError("NaN is not a valid value");return!f.infinite(e)&&!f.infinite(t)&&e<t},f.within=function(e,t,n){if(a(e)||a(t)||a(n))throw new TypeError("NaN is not a valid value");if(!f.number(e)||!f.number(t)||!f.number(n))throw new TypeError("all arguments must be numbers");return f.infinite(e)||f.infinite(t)||f.infinite(n)||e>=t&&e<=n},f.object=function(e){return"[object Object]"===s.call(e)},f.primitive=function(e){return!e||!("object"==typeof e||f.object(e)||f.fn(e)||f.array(e))},f.hash=function(e){return f.object(e)&&e.constructor===Object&&!e.nodeType&&!e.setInterval},f.regexp=function(e){return"[object RegExp]"===s.call(e)},f.string=function(e){return"[object String]"===s.call(e)},f.base64=function(e){return f.string(e)&&(!e.length||u.test(e))},f.hex=function(e){return f.string(e)&&(!e.length||l.test(e))},f.symbol=function(e){return"function"==typeof Symbol&&"[object Symbol]"===s.call(e)&&"symbol"==typeof r.call(e)},e.exports=f},522:function(e,t,n){"use strict";var r=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;e.exports=function(e){return r.test(e)}},523:function(e,t,n){function r(e){if(e)return function(e){for(var t in r.prototype)e[t]=r.prototype[t];return e}(e)}e.exports=r,r.prototype.on=r.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},r.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((n=r[i])===t||n.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},r.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,t)}return this},r.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},r.prototype.hasListeners=function(e){return!!this.listeners(e).length}},524:function(e,t,n){"use strict";(function(t,n){var r,i;r=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e},i=function(e){var t,n=document.createTextNode(""),i=0;return new e((function(){var e;t&&(e=t,t=null,"function"!=typeof e?e.forEach((function(e){e()})):e())})).observe(n,{characterData:!0}),function(e){r(e),t?"function"==typeof t?t=[t,e]:t.push(e):(t=e,n.data=i=++i%2)}},e.exports=function(){if(void 0!==t&&t&&"function"==typeof t.nextTick)return t.nextTick;if("object"==typeof document&&document){if("function"==typeof MutationObserver)return i(MutationObserver);if("function"==typeof WebKitMutationObserver)return i(WebKitMutationObserver)}return"function"==typeof n?function(e){n(r(e))}:"function"==typeof setTimeout?function(e){setTimeout(r(e),0)}:null}()}).call(this,n(182),n(649).setImmediate)},539:function(e,t,n){"use strict";n.r(t),function(e){var r=n(1101),i=n(1102),o=setTimeout;function s(e){return Boolean(e&&void 0!==e.length)}function a(){}function c(e){if(!(this instanceof c))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],h(e,this)}function u(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,c._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void f(t.promise,e)}l(t.promise,r)}else(1===e._state?l:f)(t.promise,e._value)}))):e._deferreds.push(t)}function l(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof c)return e._state=3,e._value=t,void d(e);if("function"==typeof n)return void h((r=n,i=t,function(){r.apply(i,arguments)}),e)}e._state=1,e._value=t,d(e)}catch(t){f(e,t)}var r,i}function f(e,t){e._state=2,e._value=t,d(e)}function d(e){2===e._state&&0===e._deferreds.length&&c._immediateFn((function(){e._handled||c._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)u(e,e._deferreds[t]);e._deferreds=null}function p(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function h(e,t){var n=!1;try{e((function(e){n||(n=!0,l(t,e))}),(function(e){n||(n=!0,f(t,e))}))}catch(e){if(n)return;n=!0,f(t,e)}}c.prototype.catch=function(e){return this.then(null,e)},c.prototype.then=function(e,t){var n=new this.constructor(a);return u(this,new p(e,t,n)),n},c.prototype.finally=r.a,c.all=function(e){return new c((function(t,n){if(!s(e))return n(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(e);if(0===r.length)return t([]);var i=r.length;function o(e,s){try{if(s&&("object"==typeof s||"function"==typeof s)){var a=s.then;if("function"==typeof a)return void a.call(s,(function(t){o(e,t)}),n)}r[e]=s,0==--i&&t(r)}catch(e){n(e)}}for(var a=0;a<r.length;a++)o(a,r[a])}))},c.allSettled=i.a,c.resolve=function(e){return e&&"object"==typeof e&&e.constructor===c?e:new c((function(t){t(e)}))},c.reject=function(e){return new c((function(t,n){n(e)}))},c.race=function(e){return new c((function(t,n){if(!s(e))return n(new TypeError("Promise.race accepts an array"));for(var r=0,i=e.length;r<i;r++)c.resolve(e[r]).then(t,n)}))},c._immediateFn="function"==typeof e&&function(t){e(t)}||function(e){o(e,0)},c._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},t.default=c}.call(this,n(649).setImmediate)},562:function(e,t,n){"use strict";function r(e){return!0==(null!==(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}e.exports={isPlainObject:function(e){if(!1===r(e))return!1;var t=e.constructor;if("function"!=typeof t)return!1;var n=t.prototype;return!1!==r(n)&&"isPrototypeOf"in n}}},672:function(e,t){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},673:function(e,t,n){"use strict";var r=n(521),i=n(899),o=n(1785),s=n(1786);e.exports=function(e){return r.date(e)?e:r.number(e)?new Date((t=e)<315576e5?1e3*t:t):i.is(e)?i.parse(e):o.is(e)?o.parse(e):s.is(e)?s.parse(e):new Date(e);var t}},674:function(e,t,n){"use strict";var r=n(208).inherit,i=n(208).type,o=n(311),s=n(904),a=n(522),c=n(520);function u(e,t){o.call(this,e,t)}r(u,o),u.prototype.action=function(){return"track"},u.prototype.type=u.prototype.action,u.prototype.event=o.field("event"),u.prototype.value=o.proxy("properties.value"),u.prototype.category=o.proxy("properties.category"),u.prototype.id=o.proxy("properties.id"),u.prototype.productId=function(){return this.proxy("properties.product_id")||this.proxy("properties.productId")},u.prototype.promotionId=function(){return this.proxy("properties.promotion_id")||this.proxy("properties.promotionId")},u.prototype.cartId=function(){return this.proxy("properties.cart_id")||this.proxy("properties.cartId")},u.prototype.checkoutId=function(){return this.proxy("properties.checkout_id")||this.proxy("properties.checkoutId")},u.prototype.paymentId=function(){return this.proxy("properties.payment_id")||this.proxy("properties.paymentId")},u.prototype.couponId=function(){return this.proxy("properties.coupon_id")||this.proxy("properties.couponId")},u.prototype.wishlistId=function(){return this.proxy("properties.wishlist_id")||this.proxy("properties.wishlistId")},u.prototype.reviewId=function(){return this.proxy("properties.review_id")||this.proxy("properties.reviewId")},u.prototype.orderId=function(){return this.proxy("properties.id")||this.proxy("properties.order_id")||this.proxy("properties.orderId")},u.prototype.sku=o.proxy("properties.sku"),u.prototype.tax=o.proxy("properties.tax"),u.prototype.name=o.proxy("properties.name"),u.prototype.price=o.proxy("properties.price"),u.prototype.total=o.proxy("properties.total"),u.prototype.repeat=o.proxy("properties.repeat"),u.prototype.coupon=o.proxy("properties.coupon"),u.prototype.shipping=o.proxy("properties.shipping"),u.prototype.discount=o.proxy("properties.discount"),u.prototype.shippingMethod=function(){return this.proxy("properties.shipping_method")||this.proxy("properties.shippingMethod")},u.prototype.paymentMethod=function(){return this.proxy("properties.payment_method")||this.proxy("properties.paymentMethod")},u.prototype.description=o.proxy("properties.description"),u.prototype.plan=o.proxy("properties.plan"),u.prototype.subtotal=function(){var e=c(this.properties(),"subtotal"),t=this.total()||this.revenue();if(e)return e;if(!t)return 0;if(this.total()){var n=this.tax();n&&(t-=n),(n=this.shipping())&&(t-=n),(n=this.discount())&&(t+=n)}return t},u.prototype.products=function(){var e=this.properties(),t=c(e,"products");return"array"===i(t)?t:[]},u.prototype.quantity=function(){return(this.obj.properties||{}).quantity||1},u.prototype.currency=function(){return(this.obj.properties||{}).currency||"USD"},u.prototype.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},u.prototype.query=o.proxy("options.query"),u.prototype.properties=function(e){var t=this.field("properties")||{};for(var n in e=e||{}){var r=null==this[n]?this.proxy("properties."+n):this[n]();null!=r&&(t[e[n]]=r,delete t[n])}return t},u.prototype.username=function(){return this.proxy("traits.username")||this.proxy("properties.username")||this.userId()||this.sessionId()},u.prototype.email=function(){var e=this.proxy("traits.email")||this.proxy("properties.email")||this.proxy("options.traits.email");if(e)return e;var t=this.userId();return a(t)?t:void 0},u.prototype.revenue=function(){var e=this.proxy("properties.revenue"),t=this.event();return!e&&t&&t.match(/^[ _]?completed[ _]?order[ _]?|^[ _]?order[ _]?completed[ _]?$/i)&&(e=this.proxy("properties.total")),function(e){if(!e)return;if("number"==typeof e)return e;if("string"!=typeof e)return;if(e=e.replace(/\$/g,""),e=parseFloat(e),!isNaN(e))return e}(e)},u.prototype.cents=function(){var e=this.revenue();return"number"!=typeof e?this.value()||0:100*e},u.prototype.identify=function(){var e=this.json();return e.traits=this.traits(),new s(e,this.opts)},e.exports=u},675:function(e,t,n){var r=n(1794)("cookie");function i(e,t,n){n=n||{};var r=a(e)+"="+a(t);null==t&&(n.maxage=-1),n.maxage&&(n.expires=new Date(+new Date+n.maxage)),n.path&&(r+="; path="+n.path),n.domain&&(r+="; domain="+n.domain),n.expires&&(r+="; expires="+n.expires.toUTCString()),n.secure&&(r+="; secure"),document.cookie=r}function o(){var e;try{e=document.cookie}catch(e){return"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e.stack||e),{}}return function(e){var t,n={},r=e.split(/ *; */);if(""==r[0])return n;for(var i=0;i<r.length;++i)t=r[i].split("="),n[c(t[0])]=c(t[1]);return n}(e)}function s(e){return o()[e]}function a(e){try{return encodeURIComponent(e)}catch(t){r("error `encode(%o)` - %o",e,t)}}function c(e){try{return decodeURIComponent(e)}catch(t){r("error `decode(%o)` - %o",e,t)}}e.exports=function(e,t,n){switch(arguments.length){case 3:case 2:return i(e,t,n);case 1:return s(e);default:return o()}}},676:function(e,t,n){"use strict";var r=n(209);e.exports=function(e,t,n){if("function"!=typeof e)throw new TypeError("Expected a function but received a "+typeof e);return r((function(n,r,i){t=e(t,n,r,i)}),n),t}},677:function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty;e.exports=function(e){for(var t=Array.prototype.slice.call(arguments,1),n=0;n<t.length;n+=1)for(var i in t[n])r.call(t[n],i)&&(e[i]=t[n][i]);return e}},678:function(e,t,n){"use strict";var r=n(313),i=n(352),o=n(1809);function s(e){this.options(e)}s.prototype.options=function(e){if(0===arguments.length)return this._options;i(e=e||{},{enabled:!0}),this.enabled=e.enabled&&o.enabled,this._options=e},s.prototype.set=function(e,t){return!!this.enabled&&o.set(e,t)},s.prototype.get=function(e){return this.enabled?o.get(e):null},s.prototype.remove=function(e){return!!this.enabled&&o.remove(e)},e.exports=r(new s),e.exports.Store=s},679:function(e,t,n){"use strict";var r=n(209),i=String.prototype.indexOf;e.exports=function(e,t){var n=!1;return"string"==typeof t?-1!==i.call(t,e):(r((function(t){if((r=t)===(i=e)?0!==r||1/r==1/i:r!=r&&i!=i)return n=!0,!1;var r,i}),t),n)}},680:function(e,t,n){var r=n(1819),i=n(1820),o=/(\w+)\[(\d+)\]/,s=function(e){try{return encodeURIComponent(e)}catch(t){return e}},a=function(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(t){return e}};t.parse=function(e){if("string"!=typeof e)return{};if(""==(e=r(e)))return{};"?"==e.charAt(0)&&(e=e.slice(1));for(var t={},n=e.split("&"),i=0;i<n.length;i++){var s,c=n[i].split("="),u=a(c[0]);(s=o.exec(u))?(t[s[1]]=t[s[1]]||[],t[s[1]][s[2]]=a(c[1])):t[c[0]]=null==c[1]?"":a(c[1])}return t},t.stringify=function(e){if(!e)return"";var t=[];for(var n in e){var r=e[n];if("array"!=i(r))t.push(s(n)+"="+s(e[n]));else for(var o=0;o<r.length;++o)t.push(s(n+"["+o+"]")+"="+s(r[o]))}return t.join("&")}},681:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=function(){try{return function(e){var t={},n=e.split(/ *; */);if(""===n[0])return t;return n.forEach((function(e){var n=e.split("="),r=s(n[0]),i=s(n[1]);r&&i&&(t[r]=i)})),t}(document.cookie)}catch(e){return"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e.stack||e),{}}};function o(e){try{return encodeURIComponent(e)}catch(t){return console.log("error `encode(%o)` - %o",e,t),""}}function s(e){try{return decodeURIComponent(e)}catch(t){return console.log("error `decode(%o)` - %o",e,t),""}}t.getCookie=function(e){return function(e){return i()[e]}(e)},t.getAllCookies=function(){return i()},t.setCookie=function(e,t,n){return function(e,t,n){var r,i=o(e)+"="+o((t||"").toString());return null==t&&(n.maxage=-1),n.maxage&&(r=new Date(+new Date+n.maxage)),n.path&&(i+="; path="+n.path),n.domain&&(i+="; domain="+n.domain),r&&(i+="; expires="+r.toUTCString()),n.secure&&(i+="; secure"),n.sameSite&&(i+="; samesite="+n.sameSite),document.cookie=i,i}(e,t,r.__assign({maxage:31536e6,secure:!1,path:"/",expires:null,domain:null,sameSite:"lax"},n))},t.getCrossDomainCookieOption=function(){return{sameSite:"None",domain:window.location.hostname.split(".").slice(-2).join("."),secure:!0}}},721:function(e,t,n){"use strict";var r=n(907),i=n(351),o=n(1856),s=n(352),a=n(677),c=n(1859),u=n(1860),l=n(1869);e.exports=function(e){function t(n){if(n&&n.addIntegration)return n.addIntegration(t);this.debug=o("analytics:integration:"+c(e)),this.options=s(i(n)||{},this.defaults),this._queue=[],this.once("ready",r(this,this.flush)),t.emit("construct",this),this.ready=r(this,this.ready),this._wrapInitialize(),this._wrapPage(),this._wrapTrack()}return t.prototype.defaults={},t.prototype.globals=[],t.prototype.templates={},t.prototype.name=e,a(t,l),a(t.prototype,u),t}},899:function(e,t,n){"use strict";var r=/^(\d{4})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:([ T])(\d{2}):?(\d{2})(?::?(\d{2})(?:[,\.](\d{1,}))?)?(?:(Z)|([+\-])(\d{2})(?::?(\d{2}))?)?)?$/;t.parse=function(e){var t=[1,5,6,7,11,12],n=r.exec(e),i=0;if(!n)return new Date(e);for(var o,s=0;o=t[s];s++)n[o]=parseInt(n[o],10)||0;n[2]=parseInt(n[2],10)||1,n[3]=parseInt(n[3],10)||1,n[2]--,n[8]=n[8]?(n[8]+"00").substring(0,3):0," "===n[4]?i=(new Date).getTimezoneOffset():"Z"!==n[9]&&n[10]&&(i=60*n[11]+n[12],"+"===n[10]&&(i=0-i));var a=Date.UTC(n[1],n[2],n[3],n[5],n[6]+i,n[7],n[8]);return new Date(a)},t.is=function(e,t){return(!t||!1!==/^\d{4}-\d{2}-\d{2}/.test(e))&&r.test(e)}},900:function(e,t,n){"use strict";var r=n(312),i=n(901),o=n(899);function s(e,t){return void 0===t&&(t=!0),"object"===r(e)?function(e,t){e.length&&"number"==typeof e.length&&!(e.length-1 in e)&&(e.lengthNonArray=e.length,delete e.length);i(e,(function(n,i){o.is(i,t)?e[n]=o.parse(i):"object"!==r(i)&&"array"!==r(i)||s(i,t)})),e.lengthNonArray&&(e.length=e.lengthNonArray,delete e.lengthNonArray);return e}(e,t):"array"===r(e)?function(e,t){return i(e,(function(n,i){"object"===r(n)?s(n,t):o.is(n,t)&&(e[i]=o.parse(n))})),e}(e,t):e}e.exports=s},901:function(e,t,n){try{var r=n(902)}catch(e){r=n(902)}var i=n(1787),o=Object.prototype.hasOwnProperty;function s(e,t,n){for(var r=0;r<e.length;++r)t.call(n,e[r],r)}e.exports=function(e,t,n){switch(t=i(t),n=n||this,r(e)){case"array":return s(e,t,n);case"object":return"number"==typeof e.length?s(e,t,n):function(e,t,n){for(var r in e)o.call(e,r)&&t.call(n,r,e[r])}(e,t,n);case"string":return function(e,t,n){for(var r=0;r<e.length;++r)t.call(n,e.charAt(r),r)}(e,t,n)}}},902:function(e,t){var n=Object.prototype.toString;e.exports=function(e){switch(n.call(e)){case"[object Function]":return"function";case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Array]":return"array";case"[object String]":return"string"}return null===e?"null":void 0===e?"undefined":e&&1===e.nodeType?"element":e===Object(e)?"object":typeof e}},903:function(e,t){var n=/\b(Array|Date|Object|Math|JSON)\b/g;e.exports=function(e,t){var r=function(e){for(var t=[],n=0;n<e.length;n++)~t.indexOf(e[n])||t.push(e[n]);return t}(function(e){return e.replace(/\.\w+|\w+ *\(|"[^"]*"|'[^']*'|\/([^/]+)\//g,"").replace(n,"").match(/[a-zA-Z_]\w*/g)||[]}(e));return t&&"string"==typeof t&&(t=function(e){return function(t){return e+t}}(t)),t?function(e,t,n){return e.replace(/\.\w+|\w+ *\(|"[^"]*"|'[^']*'|\/([^/]+)\/|[a-zA-Z_]\w*/g,(function(e){return"("==e[e.length-1]||~t.indexOf(e)?n(e):e}))}(e,r,t):r}},904:function(e,t,n){"use strict";var r=n(311),i=n(520),o=n(208).inherit,s=n(522),a=n(673),c=n(1790),u=n(208).type;function l(e,t){r.call(this,e,t)}o(l,r),l.prototype.action=function(){return"identify"},l.prototype.type=l.prototype.action,l.prototype.traits=function(e){var t=this.field("traits")||{},n=this.userId();for(var r in e=e||{},n&&(t.id=n),e){var i=null==this[r]?this.proxy("traits."+r):this[r]();null!=i&&(t[e[r]]=i,r!==e[r]&&delete t[r])}return t},l.prototype.email=function(){var e=this.proxy("traits.email");if(e)return e;var t=this.userId();return s(t)?t:void 0},l.prototype.created=function(){var e=this.proxy("traits.created")||this.proxy("traits.createdAt");if(e)return a(e)},l.prototype.companyCreated=function(){var e=this.proxy("traits.company.created")||this.proxy("traits.company.createdAt");if(e)return a(e)},l.prototype.companyName=function(){return this.proxy("traits.company.name")},l.prototype.name=function(){var e=this.proxy("traits.name");if("string"==typeof e)return c(e);var t=this.firstName(),n=this.lastName();return t&&n?c(t+" "+n):void 0},l.prototype.firstName=function(){var e=this.proxy("traits.firstName");if("string"==typeof e)return c(e);var t=this.proxy("traits.name");return"string"==typeof t?c(t).split(" ")[0]:void 0},l.prototype.lastName=function(){var e=this.proxy("traits.lastName");if("string"==typeof e)return c(e);var t=this.proxy("traits.name");if("string"==typeof t){var n=c(t).indexOf(" ");if(-1!==n)return c(t.substr(n+1))}},l.prototype.uid=function(){return this.userId()||this.username()||this.email()},l.prototype.description=function(){return this.proxy("traits.description")||this.proxy("traits.background")},l.prototype.age=function(){var e=this.birthday(),t=i(this.traits(),"age");return null!=t?t:"date"===u(e)?(new Date).getFullYear()-e.getFullYear():void 0},l.prototype.avatar=function(){var e=this.traits();return i(e,"avatar")||i(e,"photoUrl")||i(e,"avatarUrl")},l.prototype.position=function(){var e=this.traits();return i(e,"position")||i(e,"jobTitle")},l.prototype.username=r.proxy("traits.username"),l.prototype.website=r.one("traits.website"),l.prototype.websites=r.multi("traits.website"),l.prototype.phone=r.one("traits.phone"),l.prototype.phones=r.multi("traits.phone"),l.prototype.address=r.proxy("traits.address"),l.prototype.gender=r.proxy("traits.gender"),l.prototype.birthday=r.proxy("traits.birthday"),e.exports=l},905:function(e,t,n){"use strict";var r=n(208).inherit,i=n(311),o=n(674),s=n(522);function a(e,t){i.call(this,e,t)}r(a,i),a.prototype.action=function(){return"page"},a.prototype.type=a.prototype.action,a.prototype.category=i.field("category"),a.prototype.name=i.field("name"),a.prototype.title=i.proxy("properties.title"),a.prototype.path=i.proxy("properties.path"),a.prototype.url=i.proxy("properties.url"),a.prototype.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},a.prototype.properties=function(e){var t=this.field("properties")||{},n=this.category(),r=this.name();for(var i in e=e||{},n&&(t.category=n),r&&(t.name=r),e){var o=null==this[i]?this.proxy("properties."+i):this[i]();null!=o&&(t[e[i]]=o,i!==e[i]&&delete t[i])}return t},a.prototype.email=function(){var e=this.proxy("context.traits.email")||this.proxy("properties.email");if(e)return e;var t=this.userId();return s(t)?t:void 0},a.prototype.fullName=function(){var e=this.category(),t=this.name();return t&&e?e+" "+t:t},a.prototype.event=function(e){return e?"Viewed "+e+" Page":"Loaded a Page"},a.prototype.track=function(e){var t=this.json();return t.event=this.event(e),t.timestamp=this.timestamp(),t.properties=this.properties(),new o(t,this.opts)},e.exports=a},906:function(e,t,n){"use strict";var r=n(310);function i(e){var t=[];return e.getMiddlewares=function(){return t.slice()},e.add=function(e){if("function"!=typeof e)throw new Error("attempted to add non-function middleware");if(-1!==t.indexOf(e))throw new Error("middleware is already registered");t.push(e)},function(e,n,i){if("object"!=typeof n)throw new Error("applyMiddlewares requires a payload object");if("function"!=typeof i)throw new Error("applyMiddlewares requires a function callback");var o=t.slice();o.push(i),function e(t,n,i,o){if(null===n)return void i[i.length-1](null);n instanceof r||(n=new r(n));var s=i[o];s&&(i[o+1]?t(s,n,(function(n){e(t,n,i,++o)})):s(n))}(e,n,o,0)}}e.exports.SourceMiddlewareChain=function(){var e=i(this);this.applyMiddlewares=function(t,n,r){return e((function(e,t,r){e({integrations:n,next:r,payload:t})}),t,r)}},e.exports.IntegrationMiddlewareChain=function(){var e=i(this);this.applyMiddlewares=function(t,n,r){return e((function(e,t,r){e(t,n,r)}),t,r)}},e.exports.middlewareChain=i},907:function(e,t){var n=[].slice;e.exports=function(e,t){if("string"==typeof t&&(t=e[t]),"function"!=typeof t)throw new Error("bind() requires a function");var r=n.call(arguments,2);return function(){return t.apply(e,r.concat(n.call(arguments)))}}},908:function(e,t,n){"use strict";var r=n(909).parse,i=n(675);function o(e){for(var n=t.cookie,r=t.levels(e),i=0;i<r.length;++i){var o=r[i],s={domain:"."+o};if(n("__tld__",1,s),n("__tld__"))return n("__tld__",null,s),o}return""}o.levels=function(e){var t=r(e).hostname.split("."),n=t[t.length-1],i=[];if(4===t.length&&n===parseInt(n,10))return i;if(t.length<=1)return i;for(var o=t.length-2;o>=0;--o)i.push(t.slice(o).join("."));return i},o.cookie=i,t=e.exports=o},909:function(e,t){function n(e){switch(e){case"http:":return 80;case"https:":return 443;default:return location.port}}t.parse=function(e){var t=document.createElement("a");return t.href=e,{href:t.href,host:t.host||location.host,port:"0"===t.port||""===t.port?n(t.protocol):t.port,hash:t.hash,hostname:t.hostname||location.hostname,pathname:"/"!=t.pathname.charAt(0)?"/"+t.pathname:t.pathname,protocol:t.protocol&&":"!=t.protocol?t.protocol:location.protocol,search:t.search,query:t.search.slice(1)}},t.isAbsolute=function(e){return 0==e.indexOf("//")||!!~e.indexOf("://")},t.isRelative=function(e){return!t.isAbsolute(e)},t.isCrossDomain=function(e){e=t.parse(e);var n=t.parse(window.location.href);return e.hostname!==n.hostname||e.port!==n.port||e.protocol!==n.protocol}},910:function(e,t,n){"use strict";var r=n(353),i=n(1801),o=n(1803),s=n(1804);function a(e,t,n,i){3===arguments.length&&(i=n,n={});var o=new XMLHttpRequest;for(var s in o.onerror=i,o.onreadystatechange=a,o.open("POST",e,!0),n)o.setRequestHeader(s,n[s]);function a(){if(4===o.readyState)return i(null,o)}o.send(r.stringify(t))}function c(e,n,r,i){3===arguments.length&&(i=r);var o=t.prefix,a=u(n);s(e+="?"+o+"="+a,{param:t.callback},(function(t,n){if(t)return i(t);i(null,{url:e,body:n})}))}function u(e){var t="";return t=r.stringify(e),t=(t=i(t)).replace(/\+/g,"-").replace(/\//g,"_"),encodeURIComponent(t)}(t=e.exports=o?a:c).callback="callback",t.prefix="data",t.json=a,t.base64=c,t.type=o?"xhr":"jsonp"},911:function(e,t,n){"use strict";var r=n(351),i=n(427),o=n(314)("analytics:entity"),s=n(352),a=n(677),c=n(912),u=n(678),l=n(900);function f(e){this.options(e),this.initialize()}e.exports=f,f.prototype.initialize=function(){if(i.set("ajs:cookies",!0),i.get("ajs:cookies"))return i.remove("ajs:cookies"),void(this._storage=i);u.enabled?this._storage=u:(o("warning using memory store both cookies and localStorage are disabled"),this._storage=c)},f.prototype.storage=function(){return this._storage},f.prototype.options=function(e){if(0===arguments.length)return this._options;this._options=s(e||{},this.defaults||{})},f.prototype.id=function(e){switch(arguments.length){case 0:return this._getId();case 1:return this._setId(e)}},f.prototype._getId=function(){if(!this._options.persist)return void 0===this._id?null:this._id;var e=this._getIdFromCookie();if(e)return e;var t=this._getIdFromLocalStorage();return t?(this._setIdInCookies(t),t):null},f.prototype._getIdFromCookie=function(){return this.storage().get(this._options.cookie.key)},f.prototype._getIdFromLocalStorage=function(){return this._options.localStorageFallbackDisabled?null:u.get(this._options.cookie.key)},f.prototype._setId=function(e){this._options.persist?(this._setIdInCookies(e),this._setIdInLocalStorage(e)):this._id=e},f.prototype._setIdInCookies=function(e){this.storage().set(this._options.cookie.key,e)},f.prototype._setIdInLocalStorage=function(e){this._options.localStorageFallbackDisabled||u.set(this._options.cookie.key,e)},f.prototype.properties=f.prototype.traits=function(e){switch(arguments.length){case 0:return this._getTraits();case 1:return this._setTraits(e)}},f.prototype._getTraits=function(){var e=this._options.persist?u.get(this._options.localStorage.key):this._traits;return e?l(r(e)):{}},f.prototype._setTraits=function(e){e=e||{},this._options.persist?u.set(this._options.localStorage.key,e):this._traits=e},f.prototype.identify=function(e,t){t=t||{};var n=this.id();null!==n&&n!==e||(t=a(this.traits(),t)),e&&this.id(e),this.debug("identify %o, %o",e,t),this.traits(t),this.save()},f.prototype.save=function(){return!!this._options.persist&&(this._setId(this.id()),this._setTraits(this.traits()),!0)},f.prototype.logout=function(){this.id(null),this.traits({}),this.storage().remove(this._options.cookie.key),u.remove(this._options.cookie.key),u.remove(this._options.localStorage.key)},f.prototype.reset=function(){this.logout(),this.options({})},f.prototype.load=function(){this.id(this.id()),this.traits(this.traits())}},912:function(e,t,n){"use strict";var r=n(313),i=n(351),o=Object.prototype.hasOwnProperty;function s(){this.store={}}e.exports=r(new s),s.prototype.set=function(e,t){return this.store[e]=i(t),!0},s.prototype.get=function(e){if(o.call(this.store,e))return i(this.store[e])},s.prototype.remove=function(e){return delete this.store[e],!0}},913:function(e,t,n){for(var r=n(1813),i=[],o={},s=0;s<256;s++)i[s]=(s+256).toString(16).substr(1),o[i[s]]=s;function a(e,t){var n=t||0,r=i;return r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+"-"+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]+r[e[n++]]}var c=r(),u=[1|c[0],c[1],c[2],c[3],c[4],c[5]],l=16383&(c[6]<<8|c[7]),f=0,d=0;function p(e,t,n){var i=t&&n||0;"string"==typeof e&&(t="binary"==e?new Array(16):null,e=null);var o=(e=e||{}).random||(e.rng||r)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t)for(var s=0;s<16;s++)t[i+s]=o[s];return t||a(o)}var h=p;h.v1=function(e,t,n){var r=t&&n||0,i=t||[],o=void 0!==(e=e||{}).clockseq?e.clockseq:l,s=void 0!==e.msecs?e.msecs:(new Date).getTime(),c=void 0!==e.nsecs?e.nsecs:d+1,p=s-f+(c-d)/1e4;if(p<0&&void 0===e.clockseq&&(o=o+1&16383),(p<0||s>f)&&void 0===e.nsecs&&(c=0),c>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");f=s,d=c,l=o;var h=(1e4*(268435455&(s+=122192928e5))+c)%**********;i[r++]=h>>>24&255,i[r++]=h>>>16&255,i[r++]=h>>>8&255,i[r++]=255&h;var m=s/***********1e4&268435455;i[r++]=m>>>8&255,i[r++]=255&m,i[r++]=m>>>24&15|16,i[r++]=m>>>16&255,i[r++]=o>>>8|128,i[r++]=255&o;for(var g=e.node||u,v=0;v<6;v++)i[r+v]=g[v];return t||a(i)},h.v4=p,h.parse=function(e,t,n){var r=t&&n||0,i=0;for(t=t||[],e.toLowerCase().replace(/[0-9a-f]{2}/g,(function(e){i<16&&(t[r+i++]=o[e])}));i<16;)t[r+i++]=0;return t},h.unparse=a,e.exports=h},914:function(e,t,n){e.exports=function(e){"use strict";var t=function(e,t){return e+t&4294967295},n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function r(e,n,r,i,o,s){return n=t(t(n,e),t(i,s)),t(n<<o|n>>>32-o,r)}function i(e,t,n,i,o,s,a){return r(t&n|~t&i,e,t,o,s,a)}function o(e,t,n,i,o,s,a){return r(t&i|n&~i,e,t,o,s,a)}function s(e,t,n,i,o,s,a){return r(t^n^i,e,t,o,s,a)}function a(e,t,n,i,o,s,a){return r(n^(t|~i),e,t,o,s,a)}function c(e,n){var r=e[0],c=e[1],u=e[2],l=e[3];r=i(r,c,u,l,n[0],7,-680876936),l=i(l,r,c,u,n[1],12,-389564586),u=i(u,l,r,c,n[2],17,606105819),c=i(c,u,l,r,n[3],22,-1044525330),r=i(r,c,u,l,n[4],7,-176418897),l=i(l,r,c,u,n[5],12,1200080426),u=i(u,l,r,c,n[6],17,-1473231341),c=i(c,u,l,r,n[7],22,-45705983),r=i(r,c,u,l,n[8],7,1770035416),l=i(l,r,c,u,n[9],12,-1958414417),u=i(u,l,r,c,n[10],17,-42063),c=i(c,u,l,r,n[11],22,-1990404162),r=i(r,c,u,l,n[12],7,1804603682),l=i(l,r,c,u,n[13],12,-40341101),u=i(u,l,r,c,n[14],17,-1502002290),r=o(r,c=i(c,u,l,r,n[15],22,1236535329),u,l,n[1],5,-165796510),l=o(l,r,c,u,n[6],9,-1069501632),u=o(u,l,r,c,n[11],14,643717713),c=o(c,u,l,r,n[0],20,-373897302),r=o(r,c,u,l,n[5],5,-701558691),l=o(l,r,c,u,n[10],9,38016083),u=o(u,l,r,c,n[15],14,-660478335),c=o(c,u,l,r,n[4],20,-405537848),r=o(r,c,u,l,n[9],5,568446438),l=o(l,r,c,u,n[14],9,-1019803690),u=o(u,l,r,c,n[3],14,-187363961),c=o(c,u,l,r,n[8],20,1163531501),r=o(r,c,u,l,n[13],5,-1444681467),l=o(l,r,c,u,n[2],9,-51403784),u=o(u,l,r,c,n[7],14,1735328473),r=s(r,c=o(c,u,l,r,n[12],20,-1926607734),u,l,n[5],4,-378558),l=s(l,r,c,u,n[8],11,-2022574463),u=s(u,l,r,c,n[11],16,1839030562),c=s(c,u,l,r,n[14],23,-35309556),r=s(r,c,u,l,n[1],4,-1530992060),l=s(l,r,c,u,n[4],11,1272893353),u=s(u,l,r,c,n[7],16,-155497632),c=s(c,u,l,r,n[10],23,-1094730640),r=s(r,c,u,l,n[13],4,681279174),l=s(l,r,c,u,n[0],11,-358537222),u=s(u,l,r,c,n[3],16,-722521979),c=s(c,u,l,r,n[6],23,76029189),r=s(r,c,u,l,n[9],4,-640364487),l=s(l,r,c,u,n[12],11,-421815835),u=s(u,l,r,c,n[15],16,530742520),r=a(r,c=s(c,u,l,r,n[2],23,-995338651),u,l,n[0],6,-198630844),l=a(l,r,c,u,n[7],10,1126891415),u=a(u,l,r,c,n[14],15,-1416354905),c=a(c,u,l,r,n[5],21,-57434055),r=a(r,c,u,l,n[12],6,1700485571),l=a(l,r,c,u,n[3],10,-1894986606),u=a(u,l,r,c,n[10],15,-1051523),c=a(c,u,l,r,n[1],21,-2054922799),r=a(r,c,u,l,n[8],6,1873313359),l=a(l,r,c,u,n[15],10,-30611744),u=a(u,l,r,c,n[6],15,-1560198380),c=a(c,u,l,r,n[13],21,1309151649),r=a(r,c,u,l,n[4],6,-145523070),l=a(l,r,c,u,n[11],10,-1120210379),u=a(u,l,r,c,n[2],15,718787259),c=a(c,u,l,r,n[9],21,-343485551),e[0]=t(r,e[0]),e[1]=t(c,e[1]),e[2]=t(u,e[2]),e[3]=t(l,e[3])}function u(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}function l(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n}function f(e){var t,n,r,i,o,s,a=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=a;t+=64)c(l,u(e.substring(t-64,t)));for(n=(e=e.substring(t-64)).length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(c(l,r),t=0;t<16;t+=1)r[t]=0;return i=(i=8*a).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,r[14]=o,r[15]=s,c(l,r),l}function d(e){var t,r="";for(t=0;t<4;t+=1)r+=n[e>>8*t+4&15]+n[e>>8*t&15];return r}function p(e){var t;for(t=0;t<e.length;t+=1)e[t]=d(e[t]);return e.join("")}function h(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),e}function m(e){var t,n=[],r=e.length;for(t=0;t<r-1;t+=2)n.push(parseInt(e.substr(t,2),16));return String.fromCharCode.apply(String,n)}function g(){this.reset()}return"5d41402abc4b2a76b9719d911017c592"!==p(f("hello"))&&(t=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function t(e,t){return(e=0|e||0)<0?Math.max(e+t,0):Math.min(e,t)}ArrayBuffer.prototype.slice=function(n,r){var i,o,s,a,c=this.byteLength,u=t(n,c),l=c;return r!==e&&(l=t(r,c)),u>l?new ArrayBuffer(0):(i=l-u,o=new ArrayBuffer(i),s=new Uint8Array(o),a=new Uint8Array(this,u,i),s.set(a),o)}}(),g.prototype.append=function(e){return this.appendBinary(h(e)),this},g.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,n=this._buff.length;for(t=64;t<=n;t+=64)c(this._hash,u(this._buff.substring(t-64,t)));return this._buff=this._buff.substring(t-64),this},g.prototype.end=function(e){var t,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=r.charCodeAt(t)<<(t%4<<3);return this._finish(o,i),n=p(this._hash),e&&(n=m(n)),this.reset(),n},g.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},g.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash}},g.prototype.setState=function(e){return this._buff=e.buff,this._length=e.length,this._hash=e.hash,this},g.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},g.prototype._finish=function(e,t){var n,r,i,o=t;if(e[o>>2]|=128<<(o%4<<3),o>55)for(c(this._hash,e),o=0;o<16;o+=1)e[o]=0;n=(n=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(n[2],16),i=parseInt(n[1],16)||0,e[14]=r,e[15]=i,c(this._hash,e)},g.hash=function(e,t){return g.hashBinary(h(e),t)},g.hashBinary=function(e,t){var n=p(f(e));return t?m(n):n},g.ArrayBuffer=function(){this.reset()},g.ArrayBuffer.prototype.append=function(e){var t,n,r,i,o,s=(n=this._buff.buffer,r=e,i=!0,(o=new Uint8Array(n.byteLength+r.byteLength)).set(new Uint8Array(n)),o.set(new Uint8Array(r),n.byteLength),i?o:o.buffer),a=s.length;for(this._length+=e.byteLength,t=64;t<=a;t+=64)c(this._hash,l(s.subarray(t-64,t)));return this._buff=t-64<a?new Uint8Array(s.buffer.slice(t-64)):new Uint8Array(0),this},g.ArrayBuffer.prototype.end=function(e){var t,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<i;t+=1)o[t>>2]|=r[t]<<(t%4<<3);return this._finish(o,i),n=p(this._hash),e&&(n=m(n)),this.reset(),n},g.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},g.ArrayBuffer.prototype.getState=function(){var e,t=g.prototype.getState.call(this);return t.buff=(e=t.buff,String.fromCharCode.apply(null,new Uint8Array(e))),t},g.ArrayBuffer.prototype.setState=function(e){return e.buff=function(e,t){var n,r=e.length,i=new ArrayBuffer(r),o=new Uint8Array(i);for(n=0;n<r;n+=1)o[n]=e.charCodeAt(n);return t?o:i}(e.buff,!0),g.prototype.setState.call(this,e)},g.ArrayBuffer.prototype.destroy=g.prototype.destroy,g.ArrayBuffer.prototype._finish=g.prototype._finish,g.ArrayBuffer.hash=function(e,t){var n=p(function(e){var t,n,r,i,o,s,a=e.length,u=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=a;t+=64)c(u,l(e.subarray(t-64,t)));for(e=t-64<a?e.subarray(t-64):new Uint8Array(0),n=e.length,r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)r[t>>2]|=e[t]<<(t%4<<3);if(r[t>>2]|=128<<(t%4<<3),t>55)for(c(u,r),t=0;t<16;t+=1)r[t]=0;return i=(i=8*a).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(i[2],16),s=parseInt(i[1],16)||0,r[14]=o,r[15]=s,c(u,r),u}(new Uint8Array(e)));return t?m(n):n},g}()},915:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=n(681),o=n(419),s=n(1823),a=n(916),c=n(1826);t.XC_CURRENT="atl_xid.xc";var u=function(){function e(){}return e.getCachedXID=function(){try{var e=s.getXidItemFromCache("atl_xid.current"),t=s.getCache("atl_xid.ts"),n=!1;if(e.length>0&&e[0].type===o.XIDItemType.UID?n=parseInt(t||"",10)>=(new Date).getTime()-6048e5:e.length>0&&e[0].type===o.XIDItemType.XC&&(n=parseInt(t||"",10)>=(new Date).getTime()-2592e6),n){for(var r=0,i=e;r<i.length;r++){i[r].state="EXISTING"}return e}return[]}catch(e){return[]}},e.saveXid=function(e){try{i.setCookie("atl_xid.ts",(new Date).getTime()),s.setLocalstorage("atl_xid.ts",(new Date).getTime().toString());for(var t=[],n=0,r=e;n<r.length;n++){var o=r[n];if(o.value&&o.createdAt){t.push({type:o.type,value:o.value,createdAt:o.createdAt});break}}i.setCookie("atl_xid.current",JSON.stringify(t)),s.setLocalstorage("atl_xid.current",JSON.stringify(t))}catch(e){console.error("Error setting xid cookie.")}},e.updateXc=function(t,n){try{if("prod"===t&&a.isAtlassianComDomain())return e.processAtlassianComDomainXc(n);e.saveXcLocalstorage(n)}catch(e){console.error("Error setting xc cookie.")}return n},e.getUids=function(){for(var e=s.getXidItemFromCache("atl_xid.uid")||[],t=0,n=e;t<n.length;t++){n[t].state="EXISTING"}return e},e.saveUids=function(e){try{for(var t=[],n=0,r=e;n<r.length;n++){var i=r[n];i.value&&i.createdAt&&t.push({type:i.type,value:i.value,createdAt:i.createdAt})}t.slice(0,20),s.setLocalstorage("atl_xid.uid",JSON.stringify(t))}catch(e){console.error("Error setting uid cookie.")}},e.saveXcLocalstorage=function(e){if(e.value&&e.createdAt){var n=s.getXidFromLocalStorage(t.XC_CURRENT);if(n&&n.value===e.value)return;n&&n.value!==e.value&&(e.state="CHANGED");var r={type:e.type,value:e.value,createdAt:e.createdAt};s.setLocalstorage(t.XC_CURRENT,JSON.stringify(r))}},e.processAtlassianComDomainXc=function(n){if(n.value&&n.createdAt){var a=s.getXidFromCookie(t.XC_CURRENT);if(a){i.setCookie(t.XC_CURRENT,JSON.stringify(a),i.getCrossDomainCookieOption());var u=r.__assign({state:"EXISTING"},a);return e.saveXcLocalstorage(u),u}var l={value:c.uuidv4(),createdAt:(new Date).toISOString(),type:o.XIDItemType.XC};i.setCookie(t.XC_CURRENT,JSON.stringify(l),i.getCrossDomainCookieOption());var f=r.__assign({state:"NEW"},l);return e.saveXcLocalstorage(f),f}return n},e}();t.Storage=u},916:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(681),i=n(1825);t.domain=function(e){for(var t=0,n=function(e){var t=i.parse(e).hostname.split("."),n=t[t.length-1],r=[];if(4===t.length&&!isNaN(parseInt(n,10)))return r;if(t.length<=1)return r;for(var o=t.length-2;o>=0;--o)r.push(t.slice(o).join("."));return r}(e);t<n.length;t++){var o=n[t],s={domain:"."+o};if(r.setCookie("__tld__",1,s),r.getCookie("__tld__"))return r.setCookie("__tld__",null,s),o}return""},t.inIframe=function(){try{return window.self!==window.top}catch(e){return!0}},t.isAtlassianComDomain=function(){return/atlassian\.com$/.test(window.location.hostname)}},917:function(e,t){e.exports=function(e,t){return e.addEventListener?function(e,t){e.addEventListener("load",(function(e,n){t(null,n)}),!1),e.addEventListener("error",(function(n){var r=new Error('script error "'+e.src+'"');r.event=n,t(r)}),!1)}(e,t):function(e,t){e.attachEvent("onreadystatechange",(function(n){/complete|loaded/.test(e.readyState)&&t(null,n)})),e.attachEvent("onerror",(function(n){var r=new Error('failed to load the script "'+e.src+'"');r.event=n||window.event,t(r)}))}(e,t)}},918:function(e,t,n){"use strict";var r=n(418),i=n(164).v4,o={_data:{},length:0,setItem:function(e,t){return this._data[e]=t,this.length=r(this._data).length,t},getItem:function(e){return e in this._data?this._data[e]:null},removeItem:function(e){return e in this._data&&delete this._data[e],this.length=r(this._data).length,null},clear:function(){this._data={},this.length=0},key:function(e){return r(this._data)[e]}};e.exports.defaultEngine=function(){try{if(!window.localStorage)return!1;var e=i();window.localStorage.setItem(e,"test_value");var t=window.localStorage.getItem(e);return window.localStorage.removeItem(e),"test_value"===t}catch(e){return!1}}()?window.localStorage:o,e.exports.inMemoryEngine=o}},[[1781,1,0]]]);
//# sourceMappingURL=globals-a28066baf9516e9e964f.chunk.js.map