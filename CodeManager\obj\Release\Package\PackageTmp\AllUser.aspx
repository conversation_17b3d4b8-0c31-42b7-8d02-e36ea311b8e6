﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AllUser.aspx.cs" Inherits="Account.Web.AllUser" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head runat="server">
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            账号：<asp:TextBox ID="txtApp" runat="server"></asp:TextBox>
            类别：<asp:TextBox ID="txtType" runat="server"></asp:TextBox>
            <asp:Button ID="btnOnLineUser" runat="server" Text="查询在线" OnClick="btnbtnOnLineUser_Click" />
            <asp:Button ID="btnAllUser" runat="server" Text="查询所有" OnClick="btnAllUser_Click" />
            <asp:Button ID="btnVacuum" runat="server" Text="优化" OnClick="btnVacuum_Click" />
            <a href="NewUser.aspx?pwd=<%=Request["pwd"] %>">用户报表</a>
        </div>
        <div>
            <asp:Label ID="lblCount" runat="server" Text=""></asp:Label>
        </div>
        <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
            BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
            <FooterStyle BackColor="White" ForeColor="#000066" />
            <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
            <RowStyle ForeColor="#000066" />
            <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
        </asp:GridView>
        <asp:HiddenField ID="hidType" runat="server" Value="" />
    </form>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/jquery/3.6.0/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript">
        function jiDate(date1, date2) {
            var d1_ar = date1.split("-");
            var d2_ar = date2.split("-");
            var d1_time = Date.UTC(d1_ar[0], d1_ar[1] - 1, d1_ar[2]);
            var d2_time = Date.UTC(d2_ar[0], d2_ar[1] - 1, d2_ar[2]);
            if (d1_time > d2_time) {
                return "已过期";
            }
            return Math.ceil((d2_time - d1_time) / 86400000) + '天';
        }
        function LoadDate() {
            var rows = document.getElementById("gvDataSource").rows;
            if (rows.length > 0) {
                if (document.getElementById("hidType").value == 1) {
                    for (var i = 1; i < rows.length; i++) {
                        (function (i) {
                            var tmp = jiDate(rows[i].cells[3].innerHTML.substring(0, 10), rows[i].cells[4].innerHTML.substring(0, 10));
                            rows[i].cells[5].innerHTML = tmp;
                            tmp = jiDate('<%=DateTime.Now.ToString("yyyy-MM-dd") %>', rows[i].cells[4].innerHTML.substring(0, 10));
                            rows[i].cells[6].innerHTML = tmp.replace("已过期", "-");
                        })(i)
                    }
                }
                else {

                }
            }
        }
        function LoadIp() {
            if (document.getElementById("hidType").value == 1) {
                var dataGridView = document.getElementById("gvDataSource");
                var i = 1;
                function processNextRow() {
                    if (i < dataGridView.rows.length) {
                        try {
                            const row = dataGridView.rows[i];
                            const ipCell = row.cells[10];
                            const ip = ipCell.innerText;
                            fetchLocationInfo(ip)
                                .then(response => {
                                    const country = response.data.country;
                                    const province = response.data.prov;
                                    const newValue = `${ip}(${country},${province})`;
                                    ipCell.innerText = newValue;
                                    i++;
                                    setTimeout(processNextRow, 500);
                                })
                                .catch(error => {
                                    console.error('Failed to fetch location info:', error);
                                    i++;
                                    setTimeout(processNextRow, 1000);
                                });
                        }
                        catch { }
                    }
                }
                processNextRow();
            }
        }
        function fetchLocationInfo(ip) {
            return new Promise((resolve, reject) => {
                const url = "https://qifu-api.baidubce.com/ip/geo/v1/district?ip=" + encodeURIComponent(ip);
                const xhr = new XMLHttpRequest();
                xhr.open('GET', url, true);
                xhr.onload = function () {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } else {
                        reject(xhr.statusText);
                    }
                };
                xhr.onerror = function () {
                    reject(xhr.statusText);
                };
                xhr.send();
            });
        }

        window.onload = function () {
            LoadDate();
            LoadIp();
        };
    </script>
</body>
</html>