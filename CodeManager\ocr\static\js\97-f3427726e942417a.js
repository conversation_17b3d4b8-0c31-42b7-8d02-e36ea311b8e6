(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[97], {
    86416: function (e, t, n) {
        "use strict";
        n(30467);
        var r = n(55673)
            , i = (n(58136),
                n(5789))
            , c = n(25675)
            , o = n.n(c)
            , a = n(30576)
            , s = n.n(a)
            , l = n(85893);
        t.Z = function (e) {
            var t = e.data;
            return (0,
                l.jsxs)("div", {})
        }
    },
    45179: function (e, t, n) {
        "use strict";
        n(53294);
        var r = n(56697)
            , i = (n(54067),
                n(64713))
            , c = (n(1025),
                n(65400))
            , o = (n(92015),
                n(4863))
            , a = (n(75314),
                n(11187))
            , s = n(50029)
            , l = n(59499)
            , u = n(87794)
            , d = n.n(u)
            , f = n(67294)
            , p = n(94184)
            , v = n.n(p)
            , m = n(20640)
            , h = n.n(m)
            , j = n(5152)
            , x = n.n(j)
            , b = n(52216)
            , g = n(69519)
            , w = n(79968)
            , O = n(91938)
            , y = n.n(O)
            , Z = n(85893)
            , P = x()((function () {
                return Promise.all([n.e(171), n.e(680)]).then(n.bind(n, 96680))
            }
            ), {
                ssr: !1,
                loadableGenerated: {
                    webpack: function () {
                        return [96680]
                    }
                }
            })
            , N = [{
                tab: "text",
                text: "Result"
            }, {
                tab: "json",
                text: "JSON"
            }]
            , D = function (e) {
                var t, n = e.onChange, r = e.tabList, i = (0,
                    f.useState)(null === (t = r[0]) || void 0 === t ? void 0 : t.tab), c = i[0], o = i[1];
                return (0,
                    Z.jsx)("div", {
                        className: y()["result-tabs"],
                        children: Array.isArray(r) && r.map((function (e) {
                            return (0,
                                Z.jsxs)("div", {
                                    className: v()(y()["result-tab-item"], (0,
                                        l.Z)({}, y()["result-tab-active"], c === e.tab)),
                                    onClick: function () {
                                        return function (e) {
                                            o(e.tab),
                                                n && n(e.tab)
                                        }(e)
                                    },
                                    children: [(0,
                                        Z.jsx)("div", {
                                            className: y()["result-tab-bg"],
                                            children: (0,
                                                Z.jsx)("div", {
                                                    className: y()["result-tab-bg-color"]
                                                })
                                        }), (0,
                                            Z.jsx)("div", {
                                                className: y()["result-tab-item-content"],
                                                children: e.text
                                            })]
                                }, e.tab)
                        }
                        ))
                    })
            };
        t.Z = function (e) {
            var t = e.getContent
                , n = e.onExport
                , u = e.resultContent
                , p = e.exportList
                , m = e.textData
                , j = e.jsonData
                , x = e.showCopy
                , O = void 0 === x || x
                , C = e.textWrapStyle
                , S = (0,
                    f.useState)(N[0].tab)
                , k = S[0]
                , E = S[1]
                , R = (0,
                    f.useState)(!1)
                , _ = R[0]
                , M = R[1]
                , T = (0,
                    f.useState)(p[0])
                , A = T[0]
                , H = T[1]
                , L = (0,
                    f.useState)(!1)
                , F = L[0]
                , J = L[1]
                , W = (0,
                    f.useRef)()
                , Y = g.ZP.useContainer()
                , Q = Y.serviceStatus
                , z = Y.pageInfo
                , U = (0,
                    f.useMemo)((function () {
                        return p.map((function (e) {
                            return {
                                label: e,
                                value: e
                            }
                        }
                        ))
                    }
                    ), [])
                , X = (0,
                    f.useMemo)((function () {
                        return "".concat(w.ZL, "/market/detail/").concat(z.marketService)
                    }
                    ), [z])
                , B = function () {
                    M(!1),
                        H(p[0])
                }
                , K = function () {
                    var e = (0,
                        s.Z)(d().mark((function e() {
                            return d().wrap((function (e) {
                                for (; ;)
                                    switch (e.prev = e.next) {
                                        case 0:
                                            if (B(),
                                                J(!0),
                                                e.prev = 2,
                                                "function" !== typeof n) {
                                                e.next = 6;
                                                break
                                            }
                                            return e.next = 6,
                                                n(A);
                                        case 6:
                                            e.next = 12;
                                            break;
                                        case 8:
                                            e.prev = 8,
                                                e.t0 = e.catch(2),
                                                console.error(e.t0),
                                                a.default.error("\u670d\u52a1\u5668\u7e41\u5fd9\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5");
                                        case 12:
                                            J(!1);
                                        case 13:
                                        case "end":
                                            return e.stop()
                                    }
                            }
                            ), e, null, [[2, 8]])
                        }
                        )));
                    return function () {
                        return e.apply(this, arguments)
                    }
                }();
            return (0,
                Z.jsxs)("div", {
                    className: y()["text-result"],
                    children: [(0,
                        Z.jsx)(D, {
                            tabList: N,
                            onChange: function (e) {
                                var n;
                                "json" === e && t("json"),
                                    null !== (n = W.current) && void 0 !== n && n.scrollTop && (W.current.scrollTop = 0),
                                    E(e)
                            }
                        }), (0,
                            Z.jsxs)("div", {
                                className: y()["text-result-content"],
                                ref: W,
                                style: C,
                                children: [(0,
                                    Z.jsx)("div", {
                                        className: v()(y()["text-result-text"], (0,
                                            l.Z)({}, y()["result-active"], k === N[0].tab)),
                                        children: Q === g.lZ.success && !(null === m || void 0 === m || !m.length) && u
                                    }), (0,
                                        Z.jsx)("div", {
                                            className: v()(y()["text-result-json"], (0,
                                                l.Z)({}, y()["result-active"], k === N[1].tab)),
                                            children: j && (0,
                                                Z.jsxs)(Z.Fragment, {
                                                    children: [(0,
                                                            Z.jsx)(P, {
                                                                jsonData: j
                                                            })]
                                                })
                                        })]
                            }), (0,
                                Z.jsxs)("div", {
                                    className: y()["text-result-btn"],
                                    children: [O && (0,
                                        Z.jsx)(c.default, {
                                            onClick: function () {
                                                var e;
                                                e = k === N[0].tab ? t() : (0,
                                                    w.N2)(t("json")),
                                                    h()(e),
                                                    a.default.success("\u590d\u5236\u6210\u529f")
                                            },
                                            children: "\u590d\u5236"
                                        }), (0,
                                            Z.jsx)(c.default, {
                                                type: "primary",
                                                onClick: function () {
                                                    return M(!0)
                                                },
                                                loading: F,
                                                children: "\u5bfc\u51fa"
                                            })]
                                }), (0,
                                    Z.jsx)(r.Z, {
                                        title: "\u5bfc\u51fa\u683c\u5f0f",
                                        visible: _,
                                        onCancel: B,
                                        onOk: K,
                                        okText: "\u5bfc\u51fa",
                                        width: 420,
                                        centered: !0,
                                        children: (0,
                                            Z.jsx)(i.ZP.Group, {
                                                options: U,
                                                value: A,
                                                onChange: function (e) {
                                                    return H(e.target.value)
                                                },
                                                className: y()["export-list"]
                                            })
                                    })]
                })
        }
    },
    34408: function (e, t, n) {
        "use strict";
        var r = n(59499)
            , i = n(27812)
            , c = n(67294)
            , o = n(79968)
            , a = n(85893);
        function s(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function l(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? s(Object(n), !0).forEach((function (t) {
                    (0,
                        r.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : s(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        var u = (0,
            c.forwardRef)((function (e, t) {
                var n = e.textData
                    , s = e.jsonData
                    , u = e.setText
                    , d = e.setJson
                    , f = e.active
                    , p = (0,
                        c.useState)((function () {
                            return (0,
                                o.DO)()
                        }
                        ))
                    , v = p[0]
                    , m = p[1]
                    , h = (0,
                        c.useState)(-1)
                    , j = h[0]
                    , x = h[1]
                    , b = (0,
                        c.useState)([])
                    , g = b[0]
                    , w = b[1]
                    , O = (0,
                        c.useRef)()
                    , y = (0,
                        c.useMemo)((function () {
                            if (null !== g && void 0 !== g && g.length && f) {
                                var e = "";
                                return j > -1 && (e += ".edit-area-".concat(v, " > .row-").concat(j, " { color: #1A66FF; caret-color: #1A66FF }")),
                                    g.forEach((function (t, n) {
                                        return "both" === t ? e += ".edit-area-".concat(v, " > div:nth-child(").concat(n + 1, ") { padding: 6px 0 }") : "top" === t ? e += ".edit-area-".concat(v, " > div:nth-child(").concat(n + 1, ") { padding-top: 6px }") : "bottom" === t && (e += ".edit-area-".concat(v, " > div:nth-child(").concat(n + 1, ") { padding-bottom: 6px }")),
                                            e
                                    }
                                    )),
                                    e += "\n        .edit-area-".concat(v, " > div:first-child { padding-top: 0 }\n        .edit-area-").concat(v, " > div:last-child { padding-bottom: 0 }\n      ")
                            }
                        }
                        ), [g, j, f]);
                function Z() {
                    var e, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "text";
                    if (!O.current)
                        return "text" === t ? n : s;
                    var c = O.current.innerText
                        , o = O.current.children
                        , a = (0,
                            i.Z)(o).reduce((function (e, t) {
                                var n = t.dataset.index
                                    , i = (e[n] ? "".concat(e[n], "\n") : "") + (t.innerText || "");
                                return l(l({}, e), {}, (0,
                                    r.Z)({}, n, i))
                            }
                            ), {})
                        , f = (0,
                            i.Z)(n).map((function (e, t) {
                                return a[t] || ""
                            }
                            ))
                        , p = l(l({}, "object" === typeof s ? s : {
                            lines: s
                        }), null !== s && void 0 !== s && s.whole_text ? {
                            whole_text: c
                        } : {});
                    return null === (e = p.lines) || void 0 === e || e.forEach((function (e, t) {
                        e.text = a[t] || ""
                    }
                    )),
                        u && u(f),
                        d && d(p),
                        "text" === t ? c : p
                }
                (0,
                    c.useImperativeHandle)(t, (function () {
                        return {
                            getContent: Z
                        }
                    }
                    )),
                    (0,
                        c.useEffect)((function () {
                            w(n.map((function () {
                                return "both"
                            }
                            ))),
                                m((0,
                                    o.DO)())
                        }
                        ), [n]);
                var P = function (e) {
                    var t = e && [86].includes(e.keyCode);
                    setTimeout((function () {
                        N(t);
                        var e = (window.getSelection() || {}).focusNode
                            , n = null === e || void 0 === e ? void 0 : e.parentNode;
                        if (3 === n.nodeType && (n = n.parentNode),
                            n) {
                            var r = n.dataset.index;
                            r > -1 && x(Number(r))
                        }
                    }
                    ), 20)
                }
                    , N = function (e) {
                        var t = O.current.children;
                        if (e || (null === t || void 0 === t ? void 0 : t.length) !== (null === g || void 0 === g ? void 0 : g.length)) {
                            for (var n = [], r = 0; r < (null === t || void 0 === t ? void 0 : t.length); r++) {
                                var i, c, o, a = String(r), s = null === (i = t[r - 1]) || void 0 === i ? void 0 : i.className, l = null === (c = t[r + 1]) || void 0 === c ? void 0 : c.className, u = null === (o = t[a]) || void 0 === o ? void 0 : o.className;
                                u !== s && u !== l ? n[a] = "both" : u !== s ? n[a] = "top" : u !== l && (n[a] = "bottom")
                            }
                            w(n)
                        }
                    };
                return (0,
                    a.jsxs)(a.Fragment, {
                        children: [(0,
                            a.jsx)("div", {
                                className: "edit-area-".concat(v, " ant-input"),
                                ref: O,
                                onPaste: function (e) {
                                    var t = e || window.event;
                                    t.preventDefault();
                                    var n = "";
                                    n = o.sv ? (t.originalEvent || t).clipboardData.getData("Text") || "" : (t.originalEvent || t).clipboardData.getData("text/plain") || "",
                                        document.execCommand("insertText", !1, n.trim())
                                },
                                onDrop: function (e) {
                                    e.preventDefault()
                                },
                                contentEditable: !0,
                                suppressContentEditableWarning: !0,
                                onMouseDown: function (e) {
                                    var t = e.target;
                                    if (t) {
                                        var n = t.dataset.index;
                                        x(Number(n))
                                    }
                                },
                                onKeyDown: P,
                                onBlur: function () {
                                    x(-1)
                                },
                                onFocus: function () {
                                    P()
                                },
                                children: n.map((function (e, t) {
                                    return (0,
                                        a.jsx)("div", {
                                            "data-index": t,
                                            className: "row-" + t,
                                            children: e
                                        }, t)
                                }
                                ))
                            }, v), y && (0,
                                a.jsx)("style", {
                                    children: y
                                })]
                    })
            }
            ));
        t.Z = u
    },
    59764: function (e, t, n) {
        "use strict";
        n.d(t, {
            Z: function () {
                return ee
            }
        });
        n(30467);
        var r = n(55673)
            , i = (n(58136),
                n(5789))
            , c = n(59499)
            , o = (n(1131),
                n(28465))
            , a = n(4730)
            , s = (n(62642),
                n(94704))
            , l = n(67294)
            , u = n(37617)
            , d = n(76263)
            , f = n(10130)
            , p = n(45771)
            , v = n(69519)
            , m = n(25675)
            , h = n.n(m)
            , j = n(79968)
            , x = n(88875)
            , b = n(30443)
            , g = n.n(b)
            , w = n(85893)
            , O = function () {
                return (0,
                    w.jsxs)("div", {
                        className: g()["upload-desc"],
                        children: [(0,
                            w.jsx)("div", {
                                className: g()["upload-icon"],
                                children: (0,
                                    w.jsx)(h(), {
                                        src: "static/image/nodata.svg",
                                        width: 72,
                                        height: 72,
                                        alt: "upload"
                                    })
                            }), (0,
                                w.jsx)("div", {
                                    className: g()["desc-title"],
                                    children: "\u70b9\u51fb\u4e0a\u4f20\u6587\u4ef6 / \u62d6\u62fd\u6587\u4ef6\u5230\u6b64\u5904 / \u622a\u56fe\u540ectrl+v"
                                }), (0,
                                    w.jsxs)("div", {
                                        className: g()["desc-text"],
                                        children: ["\u53ef\u652f\u6301", (0,
                                            j.MP)(x.RL), "\u683c\u5f0f\u6587\u4ef6"]
                                    }), (0,
                                        w.jsxs)("div", {
                                            className: g()["desc-text"],
                                            children: ["\u4e0a\u4f20\u5355\u4e2a\u6587\u4ef6\u5927\u5c0f\u4e0d\u8d85\u8fc7", x.$j, "M"]
                                        })]
                    })
            }
            , y = n(94184)
            , Z = n.n(y)
            , P = n(5681)
            , N = n(16835)
            , D = n(58833)
            , C = n(97596)
            , S = n(64543);
        function k(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function E(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? k(Object(n), !0).forEach((function (t) {
                    (0,
                        c.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : k(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        function R(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function _(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? R(Object(n), !0).forEach((function (t) {
                    (0,
                        c.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : R(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        function M(e, t, n, r) {
            var i = t + n
                , o = (n - r) / 2;
            if (n > r) {
                if (t > 0)
                    return (0,
                        c.Z)({}, e, o);
                if (t < 0 && i < r)
                    return (0,
                        c.Z)({}, e, -o)
            } else if (t < 0 || i > r)
                return (0,
                    c.Z)({}, e, t < 0 ? o : -o);
            return {}
        }
        function T(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function A(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? T(Object(n), !0).forEach((function (t) {
                    (0,
                        c.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : T(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        var H = function (e) {
            return (0,
                w.jsx)("svg", A(A({}, e), {}, {
                    children: (0,
                        w.jsx)("path", {
                            d: "M8.333 1.875a6.458 6.458 0 0 1 4.987 10.563l4.622 4.62a.625.625 0 0 1-.797.956l-.087-.072-4.62-4.622A6.458 6.458 0 1 1 8.334 1.875Zm0 1.25a5.208 5.208 0 1 0 0 10.417 5.208 5.208 0 0 0 0-10.417Zm2.5 4.583a.625.625 0 0 1 .113 1.24l-.113.01h-5a.625.625 0 0 1-.112-1.24l.112-.01h5Z",
                            fill: "currentColor",
                            fillRule: "evenodd"
                        })
                }))
        };
        H.defaultProps = {
            width: "20",
            height: "20",
            xmlns: "http://www.w3.org/2000/svg"
        };
        var L = function (e) {
            return (0,
                w.jsx)("svg", A(A({}, e), {}, {
                    children: (0,
                        w.jsx)("path", {
                            d: "M8.333 1.875a6.458 6.458 0 0 1 4.987 10.563l4.622 4.62a.625.625 0 0 1-.797.956l-.087-.072-4.62-4.622A6.458 6.458 0 1 1 8.334 1.875Zm0 1.25a5.208 5.208 0 1 0 0 10.417 5.208 5.208 0 0 0 0-10.417Zm0 2.083c.307 0 .562.221.615.513l.01.112v1.875h1.875a.625.625 0 0 1 .113 1.24l-.113.01H8.958v1.875a.625.625 0 0 1-1.24.113l-.01-.113V8.958H5.833a.625.625 0 0 1-.112-1.24l.112-.01h1.875V5.833c0-.345.28-.625.625-.625Z",
                            fill: "currentColor",
                            fillRule: "evenodd"
                        })
                }))
        };
        L.defaultProps = {
            width: "20",
            height: "20",
            xmlns: "http://www.w3.org/2000/svg"
        };
        var F = function (e) {
            return (0,
                w.jsx)("svg", A(A({}, e), {}, {
                    children: (0,
                        w.jsx)("path", {
                            d: "M15.833 3.542a2.292 2.292 0 0 1 2.292 2.291v8.334a2.292 2.292 0 0 1-2.292 2.291H4.167a2.292 2.292 0 0 1-2.292-2.291V5.833a2.292 2.292 0 0 1 2.292-2.291Zm0 1.25H4.167c-.576 0-1.042.466-1.042 1.041v8.334c0 .575.466 1.041 1.042 1.041h11.666c.576 0 1.042-.466 1.042-1.041V5.833c0-.575-.466-1.041-1.042-1.041ZM7.5 7.708c.307 0 .562.221.615.513l.01.112v3.334a.625.625 0 0 1-1.24.112l-.01-.112v-2.71l-.208.001a.625.625 0 0 1-.615-.512l-.01-.113c0-.306.22-.562.512-.615l.113-.01H7.5Zm5.833 0c.307 0 .562.221.615.513l.01.112v3.334a.625.625 0 0 1-1.24.112l-.01-.112v-2.71l-.208.001a.625.625 0 0 1-.615-.512l-.01-.113c0-.306.221-.562.513-.615l.112-.01h.833ZM10 10.625c.307 0 .562.221.615.513l.01.112a.625.625 0 0 1-1.24.12l-.01-.112c0-.353.28-.633.625-.633Zm0-2.5c.307 0 .562.221.615.513l.01.112a.625.625 0 0 1-1.24.12l-.01-.112c0-.353.28-.633.625-.633Z",
                            fill: "currentColor",
                            fillRule: "evenodd"
                        })
                }))
        };
        F.defaultProps = {
            width: "20",
            height: "20",
            xmlns: "http://www.w3.org/2000/svg"
        };
        var J = function (e) {
            return (0,
                w.jsx)("svg", A(A({}, e), {}, {
                    children: (0,
                        w.jsx)("path", {
                            d: "M16.788 7.267a7.32 7.32 0 0 1-2.656 8.775h2.535c.306 0 .562.22.615.512l.01.113a.625.625 0 0 1-.513.615l-.112.01H12.5a.625.625 0 0 1-.615-.513l-.01-.112V12.5a.625.625 0 0 1 1.24-.112l.01.112v2.702a6.07 6.07 0 0 0-5.392-10.83.625.625 0 0 1-.466-1.16 7.318 7.318 0 0 1 9.521 4.055Zm-7.621 8.725c.306 0 .562.22.615.512l.01.113a.625.625 0 0 1-1.24.12l-.01-.112c0-.354.28-.633.625-.633Zm-3.2-1.309c.306 0 .562.221.615.513l.01.112a.625.625 0 0 1-1.24.12l-.01-.111c0-.354.28-.634.625-.634Zm-2.109-2.725c.307 0 .562.221.615.513l.01.112a.625.625 0 0 1-1.24.12l-.01-.111c0-.354.28-.634.625-.634Zm-.475-3.416c.307 0 .562.22.615.512l.01.113a.625.625 0 0 1-1.24.12l-.01-.112c0-.354.28-.633.625-.633Zm1.309-3.2c.306 0 .562.22.615.512l.01.113a.625.625 0 0 1-1.24.12l-.01-.112c0-.354.28-.633.625-.633Z",
                            fill: "currentColor",
                            fillRule: "evenodd"
                        })
                }))
        };
        J.defaultProps = {
            width: "20",
            height: "20",
            xmlns: "http://www.w3.org/2000/svg"
        };
        var W = {
            x: 0,
            y: 0
        };
        function Y() {
            var e = (0,
                l.useRef)(null)
                , t = (0,
                    l.useRef)(null)
                , n = (0,
                    l.useState)(1)
                , r = n[0]
                , i = n[1]
                , c = (0,
                    l.useState)(0)
                , o = c[0]
                , a = c[1]
                , s = function (e) {
                    var t = (0,
                        l.useRef)(null)
                        , n = (0,
                            l.useState)(e)
                        , r = n[0]
                        , i = n[1]
                        , c = (0,
                            l.useRef)([]);
                    return (0,
                        l.useEffect)((function () {
                            return function () {
                                return t.current && S.default.cancel(t.current)
                            }
                        }
                        ), []),
                        [r, function (e) {
                            null === t.current && (c.current = [],
                                t.current = (0,
                                    S.default)((function () {
                                        i((function (e) {
                                            var n = e;
                                            return c.current.forEach((function (e) {
                                                n = E(E({}, n), e)
                                            }
                                            )),
                                                t.current = null,
                                                n
                                        }
                                        ))
                                    }
                                    ))),
                                c.current.push(e)
                        }
                        ]
                }(W)
                , u = (0,
                    N.Z)(s, 2)
                , d = u[0]
                , f = u[1]
                , p = l.useRef({
                    originX: 0,
                    originY: 0,
                    deltaX: 0,
                    deltaY: 0
                })
                , v = l.useState(!1)
                , m = (0,
                    N.Z)(v, 2)
                , h = m[0]
                , j = m[1]
                , x = function () {
                    r < 4 && i((function (e) {
                        return e + .25
                    }
                    )),
                        f(W)
                }
                , b = function () {
                    r > 1 && i((function (e) {
                        return e - .25
                    }
                    )),
                        f(W)
                }
                , g = function () {
                    f(W),
                        w(),
                        j(!1),
                        a(0)
                }
                , w = function () {
                    return i(1)
                }
                , O = [{
                    icon: H,
                    onClick: b,
                    type: "zoomOut",
                    disabled: 1 === r
                }, {
                    icon: L,
                    onClick: x,
                    type: "zoomIn",
                    disabled: 4 === r
                }, {
                    icon: F,
                    onClick: function () {
                        g()
                    },
                    type: "normal",
                    disabled: 1 === r
                }, {
                    icon: J,
                    onClick: function () {
                        a((function (e) {
                            return e + 90
                        }
                        ))
                    },
                    type: "rotateRight"
                }];
            function y() {
                var n;
                if (e.current) {
                    var i = e.current.offsetWidth * r
                        , c = e.current.offsetHeight * r
                        , a = e.current.getBoundingClientRect()
                        , s = a.left
                        , l = a.top
                        , u = (null === (n = t.current) || void 0 === n ? void 0 : n.getBoundingClientRect()) || {
                            left: 0,
                            top: 0
                        }
                        , d = u.left
                        , p = u.top
                        , v = o % 180 !== 0
                        , m = function (e, t, n, r, i) {
                            var c = null;
                            if (!e)
                                return c;
                            var o = e
                                , a = o.clientWidth
                                , s = o.clientHeight;
                            return t <= a && n <= s ? c = {
                                x: 0,
                                y: 0
                            } : (t > a || n > s) && (c = _(_({}, M("x", r, t, a)), M("y", i, n, s))),
                                c
                        }(t.current, v ? c : i, v ? i : c, s - d, l - p);
                    m && f(A({}, m))
                }
            }
            (0,
                l.useEffect)((function () {
                    o && y()
                }
                ), [o]);
            var Z = function () {
                h && (j(!1),
                    y())
            }
                , P = function (e) {
                    h && f({
                        x: e.pageX - p.current.deltaX,
                        y: e.pageY - p.current.deltaY
                    })
                }
                , k = (0,
                    l.useState)(0)
                , R = k[0]
                , T = k[1];
            (0,
                D.Z)((function () {
                    if (R)
                        return R > 1 ? (x(),
                            void T(0)) : void (R < -1 && (b(),
                                T(0)))
                }
                ), [R], {
                    wait: 40
                });
            return (0,
                l.useEffect)((function () {
                    var e = (0,
                        C.Z)(window, "mouseup", Z, !1)
                        , t = (0,
                            C.Z)(window, "mousemove", P, !1);
                    return function () {
                        e.remove(),
                            t.remove()
                    }
                }
                ), [h]),
            {
                imgRef: e,
                imgContainerRef: t,
                tools: O,
                scale: r,
                rotate: o,
                position: d,
                onMouseDown: function (e) {
                    e.preventDefault(),
                        e.stopPropagation(),
                        p.current.deltaX = e.pageX - d.x,
                        p.current.deltaY = e.pageY - d.y,
                        p.current.originX = d.x,
                        p.current.originY = d.y,
                        j(!0)
                },
                onWheel: function (e) {
                    if (e.stopPropagation(),
                        e.preventDefault(),
                        !e.ctrlKey) {
                        var t = e.deltaY > 0 ? "down" : "up";
                        T((function (e) {
                            var n = e;
                            return "up" === t ? n += 1 : n -= 1,
                                n
                        }
                        ))
                    }
                },
                resizeScale: w
            }
        }
        var Q = ["icon", "text", "className", "textClassName", "disabled"]
            , z = ["type"];
        function U(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function X(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? U(Object(n), !0).forEach((function (t) {
                    (0,
                        c.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : U(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        var B = function (e) {
            return (0,
                w.jsx)("svg", X(X({}, e), {}, {
                    children: (0,
                        w.jsx)("path", {
                            d: "M16.667 13.567a.6.6 0 0 1 .592.502l.008.098v1.666c0 1.2-.932 2.182-2.112 2.262L15 18.1H5c-1.2 0-2.182-.932-2.261-2.111l-.006-.156v-1.666a.6.6 0 0 1 1.192-.098l.008.098v1.666c0 .55.416 1.003.95 1.06L5 16.9h10c.55 0 1.002-.416 1.06-.95l.007-.117v-1.666a.6.6 0 0 1 .6-.6ZM10.355 2.819l.087.072 4.167 4.167a.625.625 0 0 1-.798.956l-.086-.072-3.126-3.126.001 8.517a.6.6 0 0 1-1.192.098l-.008-.098V4.816L6.274 7.942a.625.625 0 0 1-.797.072l-.087-.072a.625.625 0 0 1-.072-.797l.072-.087 4.167-4.167a.625.625 0 0 1 .797-.072Z",
                            fill: "currentColor",
                            fillRule: "evenodd"
                        })
                }))
        };
        B.defaultProps = {
            width: "20",
            height: "20",
            xmlns: "http://www.w3.org/2000/svg"
        };
        var K = function (e) {
            var t = e.icon
                , n = e.text
                , r = e.className
                , i = e.textClassName
                , o = e.disabled
                , s = (0,
                    a.Z)(e, Q)
                , l = t;
            return (0,
                w.jsxs)("span", X(X({
                    className: Z()(g()["operate-btn"], (0,
                        c.Z)({}, g()["disabled-btn"], o), r)
                }, s), {}, {
                    children: [(0,
                        w.jsx)(l, {}), n && (0,
                            w.jsx)("span", {
                                style: {
                                    paddingLeft: 4
                                },
                                className: i,
                                children: n
                            })]
                }))
        }
            , V = function () {
                var e = v.ZP.useContainer()
                    , t = e.base64
                    , n = e.serviceStatus
                    , c = e.resetHandle
                    , o = Y()
                    , s = o.scale
                    , l = o.imgRef
                    , u = o.imgContainerRef
                    , d = o.position
                    , f = o.tools
                    , p = o.rotate
                    , m = o.onMouseDown
                    , h = o.onWheel;
                return (0,
                    P.Z)("wheel", h, {
                        target: u,
                        passive: !1
                    }),
                    (0,
                        w.jsxs)("div", {
                            className: g()["origin-image-wrap"],
                            children: [(0,
                                w.jsxs)("div", {
                                    className: g()["origin-image-content"],
                                    ref: u,
                                    children: [(0,
                                        w.jsx)("div", {
                                            style: {
                                                transform: "translate3d(".concat(d.x, "px, ").concat(d.y, "px, 0)"),
                                                transition: j.rv ? "none" : "transform .3s cubic-bezier(0,0,.25,1) 0s",
                                                width: "100%",
                                                textAlign: "center"
                                            },
                                            children: (0,
                                                w.jsx)("img", {
                                                    src: t,
                                                    alt: "image",
                                                    ref: l,
                                                    onMouseDown: m,
                                                    style: {
                                                        transform: "scale3d(".concat(s, ", ").concat(s, ", 1) rotate(").concat(p, "deg)"),
                                                        transition: j.rv ? "none" : "transform .3s cubic-bezier(0,0,.25,1) 0s"
                                                    }
                                                })
                                        }), n === v.lZ.default && (0,
                                            w.jsx)("div", {
                                                className: g()["origin-image-progress"]
                                            })]
                                }), (0,
                                    w.jsx)("div", {
                                        className: g()["origin-image-operate"],
                                        children: (0,
                                            w.jsxs)(r.Z, {
                                                justify: "space-between",
                                                children: [(0,
                                                    w.jsx)(i.Z, {
                                                        children: f.map((function (e) {
                                                            var t = e.type
                                                                , n = (0,
                                                                    a.Z)(e, z);
                                                            return (0,
                                                                w.jsx)(K, X({}, n), t)
                                                        }
                                                        ))
                                                    }), (0,
                                                        w.jsx)(i.Z, {
                                                            children: (0,
                                                                w.jsx)(K, {
                                                                    icon: B,
                                                                    text: "\u91cd\u65b0\u4e0a\u4f20",
                                                                    onClick: c
                                                                })
                                                        })]
                                            })
                                    })]
                        })
            }
            , I = ["url", "base64", "showTips", "filename"];
        function q(e, t) {
            var n = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var r = Object.getOwnPropertySymbols(e);
                t && (r = r.filter((function (t) {
                    return Object.getOwnPropertyDescriptor(e, t).enumerable
                }
                ))),
                    n.push.apply(n, r)
            }
            return n
        }
        function G(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? q(Object(n), !0).forEach((function (t) {
                    (0,
                        c.Z)(e, t, n[t])
                }
                )) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : q(Object(n)).forEach((function (t) {
                    Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                }
                ))
            }
            return e
        }
        var $ = s.Z.Step
            , ee = function (e) {
                var t = e.data
                    , n = e.resultContent
                    , c = (0,
                        l.useState)(0)
                    , m = c[0]
                    , h = c[1]
                    , j = (0,
                        l.useState)(!1)
                    , b = j[0]
                    , y = j[1]
                    , Z = (0,
                        l.useState)()
                    , P = Z[0]
                    , N = Z[1]
                    , D = (0,
                        l.useRef)()
                    , C = (0,
                        u.Z)({
                            sizeLimit: x.$j,
                            base64: !0,
                            paste: 0 === m,
                            accept: x.id
                        }, P)
                    , S = C.url
                    , k = C.base64
                    , E = C.showTips
                    , R = C.filename
                    , _ = (0,
                        a.Z)(C, I)
                    , M = v.ZP.useContainer()
                    , T = M.setType
                    , A = M.setUrl
                    , H = M.setFilename
                    , L = M.setStatus
                    , F = M.setBase64
                    , J = M.resetDeps
                    , W = M.transformRes;
                (0,
                    l.useEffect)((function () {
                        S && (E ? y(!0) : Y())
                    }
                    ), [S]),
                    (0,
                        l.useEffect)((function () {
                            W && (L(d.Dm.finish),
                                h(2))
                        }
                        ), [W]),
                    (0,
                        l.useEffect)((function () {
                            h(0),
                                setTimeout((function () {
                                    N((new Date).getTime())
                                }
                                ), 200)
                        }
                        ), [J]);
                var Y = function () {
                    T(v.UW.text),
                        A(S),
                        F(k),
                        H(R),
                        h(1),
                        L(d.Dm.uploading)
                };
                return (0,
                    w.jsxs)("div", {
                        className: g()["text-recognition-wrap"],
                        children: [0 === m && (0,
                            w.jsxs)("div", {
                                className: g()["upload-steps-wrap"],
                                children: [(0,
                                    w.jsx)(s.Z, {
                                        current: m,
                                        responsive: !1,
                                        children: t.map((function (e) {
                                            return (0,
                                                w.jsx)($, {
                                                    title: e
                                                }, e)
                                        }
                                        ))
                                    }), (0,
                                        w.jsx)("div", {
                                            className: g()["upload-wrap"],
                                            ref: D,
                                            children: (0,
                                                w.jsx)(o.Z.Dragger, G(G({}, _), {}, {
                                                    accept: x.id.join(),
                                                    disabled: !!m,
                                                    children: (0,
                                                        w.jsx)("div", {
                                                            className: g()["upload-content"],
                                                            children: (0,
                                                                w.jsx)(O, {})
                                                        })
                                                }))
                                        }), (0,
                                            w.jsx)(f.C, {})]
                            }), m > 0 && (0,
                                w.jsxs)("div", {
                                    className: g()["image-recognition-wrap"],
                                    children: [(0,
                                        w.jsxs)(r.Z, {
                                            className: g()["image-recognition-content"],
                                            gutter: 40,
                                            children: [(0,
                                                w.jsx)(i.Z, {
                                                    span: 12,
                                                    children: (0,
                                                        w.jsx)(V, {})
                                                }), (0,
                                                    w.jsx)(i.Z, {
                                                        span: 12,
                                                        children: n
                                                    })]
                                        }), (0,
                                            w.jsx)(f.Z, {})]
                                }), (0,
                                    w.jsx)(p.Z, {
                                        visible: b,
                                        onCancel: function () {
                                            return y(!1)
                                        },
                                        onOk: Y
                                    })]
                    })
            }
    },
    30576: function (e) {
        e.exports = {
            "product-list": "TKxZEz5h",
            "product-title": "ZLSV4B9_",
            "product-subtitle": "__97nuwEiF",
            "product-list-col": "RPDtdwqH",
            "product-item-title": "vjAADAWl",
            "product-item-content": "ZmK6h01P",
            "product-desc": "jo19UkN5",
            "product-item-number": "__9yjSUq",
            "product-item-symbol": "h_uhW1nt",
            "product-item-keywords": "mKo57QFE",
            "product-item-text": "O15tirue"
        }
    },
    91938: function (e) {
        e.exports = {
            "text-result": "r3Ro6SSD",
            "result-tabs": "BCkqoT_g",
            "result-tab-item": "Q6wzUOHB",
            "result-tab-bg": "OilE85AZ",
            "result-tab-bg-color": "fs677_tB",
            "result-tab-item-content": "X76ECPuG",
            "result-tab-active": "zAa75T9J",
            "text-result-content": "JocXWZp2",
            "text-result-text": "NU80OkiA",
            "result-active": "GPd9Jx7w",
            "text-result-json": "xJjFk5vu",
            "text-json-link": "IkYWsud4",
            "text-result-btn": "JFQ7HtMm",
            "export-list": "__37q2y4kJ"
        }
    },
    30443: function (e) {
        e.exports = {
            "upload-steps-wrap": "ub_V7Qrd",
            "upload-wrap": "e_PClGCQ",
            "upload-desc": "J75OY4C_",
            "upload-icon": "nMnmvFKj",
            "desc-title": "x4UZHQKD",
            "desc-text": "aaeTkeUt",
            "text-recognition-wrap": "PPCKtJf0",
            "image-recognition-wrap": "PzldSQpN",
            "image-recognition-content": "__2C_LEUIr",
            "origin-image-wrap": "WJLYyvQ7",
            "origin-image-content": "aveA_LDj",
            "origin-image-progress": "ry0e5plX",
            move: "y1ugZ3xg",
            "origin-image-operate": "B9Qkrqv1",
            "operate-btn": "qPf36Daj",
            "disabled-btn": "__9QGEUVuk"
        }
    },
    37511: function (e) {
        e.exports = {
            "page-top": "J08R4Y7Q"
        }
    }
}]);
