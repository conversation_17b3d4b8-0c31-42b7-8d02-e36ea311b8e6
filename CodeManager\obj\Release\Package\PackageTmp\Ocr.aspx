﻿<%@ Page Title="OCR文字识别助手功能体验中心 | 免费在线识别" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手功能体验中心，提供免费在线文字识别、表格识别、公式识别、图片转文字、PDF解析等功能。支持高精度多语言识别，无需安装即用即走。" />
    <meta name="keywords" content="免费OCR,在线文字识别,表格识别,图片转文字,公式识别,PDF解析,多语言识别,高精度识别" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <iframe id="frm" style="padding-top: 50px; width: 100%" frameborder="0" border="0" scrolling="no" allowtransparency="yes" allow="clipboard-read;clipboard-write;fullscreen"></iframe>
    <script type="text/javascript">
        function getCurrentLanguage() {
            var pathParts = window.location.pathname.split('/');
            if (pathParts.length > 1 && pathParts[1] && pathParts[1].length > 0) {
                var possibleLang = pathParts[1];
                if (possibleLang.length >= 2 && possibleLang.indexOf('.') === -1) {
                    return possibleLang;
                }
            }
            return "zh-Hans";
        }

        const toolType = new URLSearchParams(window.location.search).get('type') || "index";

        document.getElementById('frm').src = "/" + getCurrentLanguage() + "/ocr/" + toolType + ".html";
        var iFrames = document.getElementsByTagName('iframe');
        function iResize() {
            for (var i = 0, j = iFrames.length; i < j; i++) {
                var bHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.body ? iFrames[i].contentWindow.document.body.scrollHeight : 0;
                var dHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.documentElement ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;
                var cHeight = iFrames[i].document && iFrames[i].document.documentElement ? iFrames[i].document.documentElement.scrollHeight : 0;
                var dHeight = window.innerHeight - 100;
                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';
            }
        }
        window.setInterval("iResize()", 200);
    </script>
</asp:Content>
