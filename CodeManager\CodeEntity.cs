﻿using Account.Web.Common;
using CommonLib;
using System;

namespace Account.Web
{
    public class CodeEntity
    {
        public static CodeEntity GetNewEntity(string account, string nickName, string password = null)
        {
            var isSendRegMsg = string.IsNullOrEmpty(password);
            if (isSendRegMsg)
            {
                password = account.Length >= 6 ?
                (BoxUtil.IsEmail(account) ? account.Substring(0, 6) : account.Substring(account.Length - 6))
                : "888888";
            }
            var code = new CodeEntity()
            {
                StrAppCode = account,
                StrNickName = nickName,
                DtReg = ServerTime.LocalTime,
                StrPwd = CommonValidateCode.GetMD5String(password + "OCRREG").ToUpper()
            };
            if (isSendRegMsg)
            {
                var isEmail = BoxUtil.IsEmail(account);
                var notice = new NoticeQueueEntity()
                {
                    MacCode = account,
                    To = isEmail ? account : "",
                    MobileNo = isEmail ? "" : account,
                    Subject = "账号注册成功",
                    NoticeType = isEmail ? NoticeType.邮件 : NoticeType.短信,
                    Body = UserConst.StrSMSSign + string.Format(UserConst.StrRegSuccessSMS, password)
                };
                MsgProcessHelper.SendMsg(notice);
                var noticeToMe = new NoticeQueueEntity()
                {
                    MacCode = account + "-Reg",
                    To = "<EMAIL>",
                    MobileNo = "",
                    Subject = "账号注册成功",
                    NoticeType = NoticeType.邮件,
                    Body = "账号:" + account + "注册成功,初始密码为：" + password + "，请及时修改！"
                };
                MsgProcessHelper.SendMsg(noticeToMe);
            }
            return code;
        }

        public string StrRemark { get; set; } = "";

        public string StrAppCode { get; set; } = "";

        public string StrPwd { get; set; } = "";

        public string StrNickName { get; set; }

        public string StrType { get; set; } = "";

        public DateTime DtReg { get; set; } = DateTime.MinValue;

        public DateTime DtExpire { get; set; } = DateTime.MinValue;

        public bool IsExpired
        {
            get
            {
                return DtExpire < CommonLib.ServerTime.DateTime;
            }
        }

        public bool IsForbid { get; set; } = false;

        public int NMaxWindow { get; set; } = 1;

        public int NMaxLogin { get; set; } = 1;
    }
}