﻿using Microsoft.Azure.Cosmos;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Security.Cryptography;
using System.Web;
using System.Web.UI.WebControls;

namespace Account.Web.BLL
{
    public static class AzureDB
    {
        // The Azure Cosmos DB endpoint for running this sample.
        private static readonly string EndpointUri = ConfigurationManager.AppSettings["EndPointUri"];

        // The primary key for the Azure Cosmos account.
        private static readonly string PrimaryKey = ConfigurationManager.AppSettings["PrimaryKey"];

        // The Cosmos client instance
        private static CosmosClient cosmosClient;

        // The database we will create
        private static Database database;

        // The container we will create.
        private static Dictionary<string, Container> tables;

        // The name of the database and container we will create
        private static string containerId = "Users";

        static AzureDB()
        {
            if (!string.IsNullOrEmpty(EndpointUri) && !string.IsNullOrEmpty(PrimaryKey))
            {
                cosmosClient = new CosmosClient(EndpointUri, PrimaryKey, new CosmosClientOptions() { ApplicationName = "Account.Web" });
                database = cosmosClient.CreateDatabaseIfNotExistsAsync("AccountDB").Result;
                InitContainer("reg", "appCode");
                InitContainer("userdata", "UID");
            }
        }

        private static void InitContainer(string table, string partitionKey)
        {
            if (!tables.ContainsKey(table))
            {
                var container = database.CreateContainerIfNotExistsAsync(containerId, "/" + partitionKey).Result;
                tables.Add(table, container);
            }
        }

        public static T InsertOrUpdate<T>(T obj, string table)
        {
            ItemResponse<T> andersenFamilyResponse = tables[table].UpsertItemAsync(obj, new PartitionKey(table)).Result;
            return andersenFamilyResponse.Resource;
        }

        public static T GetById<T>(string id, string table)
        {
            return tables[table].ReadItemAsync<ItemResponse<T>>(id, new PartitionKey(table)).Result.Resource.Resource;
        }

        public static List<T> GetBySql<T>(string sql, string table)
        {
            List<T> lstResult = new List<T>();
            QueryDefinition queryDefinition = new QueryDefinition(sql);
            FeedIterator<T> queryResultSetIterator = tables[table].GetItemQueryIterator<T>(queryDefinition);
            while (queryResultSetIterator.HasMoreResults)
            {
                FeedResponse<T> currentResultSet = queryResultSetIterator.ReadNextAsync().Result;
                lstResult.AddRange(currentResultSet.Resource);
            }
            return lstResult;
        }
    }
}