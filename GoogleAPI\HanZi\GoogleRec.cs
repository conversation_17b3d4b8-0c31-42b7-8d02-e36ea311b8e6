﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;
using System.Text;
using System.Net;
using System.Security.Cryptography;
using System.Diagnostics;

namespace GoogleAPI
{
    public class GoogleRec
    {
        public static string GetRecImg()
        {
            string result = "";
            string base64 = "";
            for (int i = 11; i < 15; i++)
            {
                var byt = File.ReadAllBytes(@"D:\助手\Image\0108\Old\" + i + ".jpg");//10584306645.jpg");
                if (byt != null && byt.Length > 0)
                    base64 = Convert.ToBase64String(byt);

                result += GetContext(base64);

            }
            return result;
        }

        static string strSpilt = "\"description\": \"";

        public static string GetContext(string strBase64)
        {
            string result = "";

            string strPost = "{\"requests\":[{\"image\":{\"content\":\"" + strBase64.Replace("%2B", "+")
                            + "\"},\"features\":[{\"type\": \"TEXT_DETECTION\",\"maxResults\": 1}]}]}";
            string strTmp = WebClientExt.GetHtml("https://content-vision.googleapis.com/v1/images:annotate?key=AIzaSyBt_4fpmnF4uZNuvl0_SOshFu2nw1LwIbc&alt=json"
                , "", "", strPost, 1, CommonHelper.NMaxTimeOut);

            if (!string.IsNullOrEmpty(strTmp))
            {
                if (strTmp.Contains(strSpilt))
                {
                    result = strTmp.Substring(strTmp.IndexOf(strSpilt) + strSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                    if (result.IndexOf("\\n") > 0)
                    {
                        if (result.IndexOf("\\n") != result.LastIndexOf("\\n"))
                        {
                            var newStr = result.Substring(0, result.IndexOf("\\n"));
                            //BaiDuCode._Log.Info("Old:" + result + Environment.NewLine + "New:" + newStr);
                            if (!string.IsNullOrEmpty(newStr))
                                result = newStr;
                        }
                    }
                }
            }
            return result.Replace("\\n", "").Trim();
        }
    }
}