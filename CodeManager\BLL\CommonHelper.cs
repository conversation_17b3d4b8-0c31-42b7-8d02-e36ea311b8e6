﻿using CommonLib;
using log4net;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Web;

namespace Account.Web
{
    public class CommonHelper
    {
        public static T DeepCopy<T>(T obj)
        {
            //如果是字符串或值类型则直接返回
            if (obj == null || obj is string || obj.GetType().IsValueType) return obj;


            object retval = Activator.CreateInstance(obj.GetType());
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
            foreach (FieldInfo field in fields)
            {
                try { field.SetValue(retval, DeepCopy(field.GetValue(obj))); }
                catch { }
            }
            return (T)retval;
        }

        public static readonly ILog _Log;

        static CommonHelper()
        {
            _Log = LogManager.GetLogger("WebLog");
        }

        public static bool IsValidateVersion(HttpRequest Request)
        {
            var result = true;
            if (string.IsNullOrEmpty(Request.GetValue("uid")))
            {
                result = false;
            }
            else
            {
                if (ConfigHelper.MinVersionDate.Year > 1900)
                {
                    var strVersion = BoxUtil.GetStringFromObject(Request.GetValue("ver"));
                    DateTime dtNowVersion = DateTime.MinValue;
                    if (!string.IsNullOrEmpty(strVersion))
                        DateTime.TryParse(strVersion, out dtNowVersion);
                    if (dtNowVersion < ConfigHelper.MinVersionDate)
                    {
                        result = false;
                    }
                }
            }
            //if (!result)
            //{
            //    Request.Log("版本过低！", false);
            //}
            return result;
        }

        private static string strDBFile = "Code.txt";
        private static string strPWD = "";

        public static string StrPWD
        {
            get
            {
                if (string.IsNullOrEmpty(strPWD))
                {
                    try
                    {
                        strPWD = ConfigurationManager.AppSettings.Get("pwd");
                    }
                    catch (Exception oe)
                    {
                        strPWD = "123";
                    }
                }
                return CommonHelper.strPWD;
            }
            set { CommonHelper.strPWD = value; }
        }
        private static string strEncrypt = "";

        public static string StrEncrypt
        {
            get
            {
                if (string.IsNullOrEmpty(strEncrypt))
                {
                    try
                    {
                        strEncrypt = ConfigurationManager.AppSettings.Get("Encrypt");
                    }
                    catch (Exception oe)
                    {
                        strEncrypt = "!(*&^%$#";
                    }
                }
                return CommonHelper.strEncrypt;
            }
            set { CommonHelper.strEncrypt = value; }
        }

        public static bool IsEncrypt
        {
            get
            {
                try
                {
                    return ConfigurationManager.AppSettings.Get("IsEncrypt").Equals("true");
                }
                catch (Exception oe)
                {
                }
                return false;
            }
        }

        public static string SubString(string strSource, string strSpilt, string strEnd = "")
        {
            return SubStringHorspool(strSource, strSpilt, strEnd).Trim();
        }

        //Horspool匹配算法
        public static string SubStringHorspool(string str, string strStart, string strEnd = "")
        {
            int index;
            if (!string.IsNullOrEmpty(strStart))
            {
                index = str.IndexOf(strStart);
                if (index >= 0)
                {
                    str = str.Substring(index + strStart.Length);
                }
                else
                    str = "";
            }
            if (!string.IsNullOrEmpty(str) && !string.IsNullOrEmpty(strEnd))
            {
                index = str.IndexOf(strEnd);
                if (index >= 0)
                {
                    str = str.Substring(0, index);
                }
                //else
                //    str = "";
            }
            return str;
        }


        private static string strConnectString = string.Empty;

        private static IFreeSql _DBHelper = null;

        private static object objLock = new object();

        public static IFreeSql DBHelper
        {
            get
            {
                lock (objLock)
                {
                    if (string.IsNullOrEmpty(strConnectString))
                    {
                        var dbFilePath = ConfigurationManager.AppSettings.Get("DBFilePath");
                        var isMySql = false;
                        if (!string.IsNullOrEmpty(dbFilePath) && dbFilePath.StartsWith("Data Source"))
                        {
                            isMySql = true;
                            strConnectString = dbFilePath;
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(dbFilePath))
                            {
                                dbFilePath = AppDomain.CurrentDomain.BaseDirectory;
                            }
                            if (File.Exists(dbFilePath + "\\DB\\" + strDBFile))
                                strConnectString = "Data Source=" + dbFilePath + "\\DB\\" + strDBFile;
                            else
                                strConnectString = "Data Source=" + Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\DB\\" + strDBFile;
                        }

                        _DBHelper = new FreeSql.FreeSqlBuilder()
                        .UseConnectionString(isMySql ? FreeSql.DataType.MySql : FreeSql.DataType.Sqlite, strConnectString).Build();
                        _DBHelper.Aop.CommandBefore += Aop_CommandBefore;
                        _DBHelper.Aop.CurdAfter += Aop_CurdAfter;
                    }
                }
                return CommonHelper._DBHelper;
            }
            set { CommonHelper._DBHelper = value; }
        }

        private static void Aop_CommandBefore(object sender, FreeSql.Aop.CommandBeforeEventArgs e)
        {
            if (e.Command.CommandText.Contains("truncate") || e.Command.CommandText.Contains("drop table"))
            {
                LogHelper.Log.Info(string.Format("BadSQL:{0}", e.Command.CommandText));
                throw new Exception("Bad Request");
            }
            if (e.Command.CommandText.Contains("insert ") || e.Command.CommandText.Contains("update ") || e.Command.CommandText.Contains("delete "))
            {
                if (e.Command.CommandText.Contains("this_is_a_test_string") || e.Command.CommandText.Contains("md5(") || e.Command.CommandText.Contains("die(")
                    || e.Command.CommandText.Contains("print(") || e.Command.CommandText.Contains("sleep(") || e.Command.CommandText.Contains("{") || e.Command.CommandText.Contains("}")
                    || e.Command.CommandText.Contains("$") || e.Command.CommandText.Contains("#") || e.Command.CommandText.Contains("/*") || e.Command.CommandText.Contains("()"))
                {
                    LogHelper.Log.Info(string.Format("BadSQL:{0}", e.Command.CommandText));
                    throw new Exception("Bad Request");
                }
            }
        }

        private static void Aop_CurdAfter(object sender, FreeSql.Aop.CurdAfterEventArgs e)
        {
            if (e.ElapsedMilliseconds > 100)
            {
                LogHelper.Log.Info(string.Format("执行超时：{0}ms,SQL:{1}", e.ElapsedMilliseconds.ToString("F0"), e.Sql));
            }
            if (e.Exception != null)
            {
                LogHelper.Log.Error($"SQL:{e.Sql}", e.Exception);
            }
        }
    }

    public class RelpaceEntity
    {
        public string StrFirst { get; set; }

        public string StrNext { get; set; }
    }
    public class FileComparer : IComparer
    {
        int IComparer.Compare(Object o1, Object o2)
        {
            FileInfo fi1 = o1 as FileInfo;
            FileInfo fi2 = o2 as FileInfo;
            return fi2.CreationTime.CompareTo(fi1.CreationTime);
        }
    }
    public class DirectoryComparer : IComparer
    {
        int IComparer.Compare(Object o1, Object o2)
        {
            DirectoryInfo fi1 = o1 as DirectoryInfo;
            DirectoryInfo fi2 = o2 as DirectoryInfo;
            return fi2.CreationTime.CompareTo(fi1.CreationTime);
        }
    }
}
