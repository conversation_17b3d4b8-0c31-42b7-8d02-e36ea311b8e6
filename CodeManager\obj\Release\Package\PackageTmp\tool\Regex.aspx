﻿<%@ Page Title="在线正则表达式测试工具 | 实时正则表达式验证" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="static/css/regex.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="keywords" content="正则表达式测试,在线正则验证,正则表达式生成器,正则表达式语法,实时正则匹配,正则模式测试,正则帮助文档,常用正则表达式,正则表达式模板,模式匹配工具"/>
    <meta name="description" content="免费在线正则表达式测试工具，支持语法高亮与实时正则验证。内置丰富常用正则表达式模板，支持邮箱、电话、身份证、时间格式等模式匹配测试，帮助开发者高效调试正则表达式。"/>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="panel panel-default" style="margin-bottom: 0px;">
        <div class="panel-heading">
            <h3 class="panel-title">在线正则表达式匹配工具</h3>
        </div>
    </div>
    <div class="panel-body mod-regexp">
        <div class="ui-po-r ui-mb-20">
            <h3 class="panel-subtitle">正则表达式</h3>
            <span class="pannel-select reg-list">
                <select class="form-control reglist_select" id="regList">
                    <option value="">-- 常用正则表达式 --</option>
                    <optgroup label="常用字符">
                        <option value="/[\u4e00-\u9fa5]/gm">匹配中文字符</option>
                        <option value="/[^\x00-\xff]/igm">匹配双字节字符</option>
                        <option value="/(^\s*)|(\s*$)/">匹配行尾行首空白</option>
                        <option value="/^\d+$/">只能输入数字</option>
                        <option value="/^\d{n}$/">只能输入n个数字</option>
                        <option value="/^\d{n,}$/">至少输入n个以上的数字</option>
                        <option value="/^\d{m,n}$/">只能输入m到n个数字</option>
                        <option value="/^[a-z]+$/i">只能由英文字母组成</option>
                        <option value="/^[A-Z]+$/">只能由大写英文字母组成</option>
                        <option value="/^[a-z0-9]+$/i">只能由英文和数字组成</option>
                        <option value="/^\w+$/">只能由英文、数字、下划线组成</option>
                    </optgroup>
                    <optgroup label="常用表单">
                        <option value="/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/">匹配Email地址</option>
                        <option value="/^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i">匹配URL地址</option>
                        <option value="/^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/">匹配手机号码</option>
                        <option value="/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/">匹配身份证号</option>
                        <option value="/^[1-9]\d{5}(?!\d)$/">匹配邮编号</option>
                        <option value="/^[1-2][0-9][0-9][0-9]-[0-1]{0,1}[0-9]-[0-3]{0,1}[0-9]$/">匹配日期(yyyy-MM-dd)</option>
                    </optgroup>
                    <optgroup label="浏览器navigator.userAgent">
                        <option value="/msie (\d+\.\d+)/i">从UA判断是否为IE浏览器</option>
                        <option value="/webkit/i">从UA判断是否为webkit内核</option>
                        <option value="/chrome\/(\d+\.\d+)/i">从UA判断是否为chrome浏览器</option>
                        <option value="/firefox\/(\d+\.\d+)/i">从UA判断是否为firefox浏览器</option>
                        <option value="/opera(\/| )(\d+(\.\d+)?)(.+?(version\/(\d+(\.\d+)?)))?/i">从UA判断是否为opera浏览器</option>
                        <option value="/(\d+\.\d)?(?:\.\d)?\s+safari\/?(\d+\.\d+)?/i">从UA判断是否为Safari浏览器</option>
                        <option value="/android/i">从UA中判断是否为Android系统</option>
                        <option value="/ipad/i">从UA中判断是否为iPad</option>
                        <option value="/iphone/i">从UA中判断是否为iPhone</option>
                        <option value="/macintosh/i">从UA判断是否为Mac OS平台</option>
                        <option value="/windows/i">从UA中判断是否为Windows平台</option>
                        <option value="/(nokia|iphone|android|ipad|motorola|^mot\-|softbank|foma|docomo|kddi|up\.browser|up\.link|htc|dopod|blazer|netfront|helio|hosin|huawei|novarra|CoolPad|webos|techfaith|palmsource|blackberry|alcatel|amoi|ktouch|nexian|samsung|^sam\-|s[cg]h|^lge|ericsson|philips|sagem|wellcom|bunjalloo|maui|symbian|smartphone|midp|wap|phone|windows ce|iemobile|^spice|^bird|^zte\-|longcos|pantech|gionee|^sie\-|portalmmm|jig\s browser|hiptop|^ucweb|^benq|haier|^lct|opera\s*mobi|opera\*mini|320x320|240x320|176x220)/i">从UA中判断是否为移动终端</option>
                    </optgroup>
                    <optgroup label="HTML相关">
                        <option value="/\<link\s(.*?)\s*(([^&]>)|(\/\>)|(\<\/link\>))/gi">匹配link标签</option>
                        <option value="/<(\S*?) [^>]*>.*?</\1>|<.*?/>/gm">匹配HTML标签</option>
                        <option value="/^[^<>`~!/@\#}$%:;)(_^{&*=|'+]+$/">匹配非HTML标签</option>
                        <option value="/<script[^>]*>[\s\S]*?<\/[^>]*script>/gi">匹配script标签</option>
                        <option value="/<!--[\s\S]*?--\>/g">匹配HTML注释</option>
                        <option value="/\[\s*if\s+[^\]][\s\w]*\]/i">匹配HTML条件注释</option>
                        <option value="/^\[if\s+(!IE|false)\]>.*<!\[endif\]$/i">匹配非IE的条件注释</option>
                        <option value="/expression[\s\r\n ]?\(/gi">匹配CSS expression</option>
                        <option value="/<\W+>/gi">匹配不合法的HTML标签</option>
                        <option value="/<textarea[^>]*>[\s\S]*?<\/[^>]*textarea>/gi">匹配textarea标签</option>
                    </optgroup>
                </select></span>
        </div>
        <textarea class="reg_reg_input form-control" id="regText" placeholder="输入正则表达式，如：/[0-9A-Z].*?/igm" rows="1" tabindex="10"></textarea>
        <div id="src" class="ui-mt-20">
            <h3 class="panel-subtitle">文本内容</h3>
            <div id="srcWrapper">
                <pre class="reg_pre" id="srcBackground"></pre>
                <textarea class="reg_textarea" id="srcCode" placeholder="输入待匹配的文本" contenteditable="true"></textarea>
            </div>
        </div>
        <div id="rst" class="">
            <h3 class="panel-subtitle">匹配结果  <span id="rstCount"></span></h3>
            <div id="rstCode"></div>
        </div>
    </div>
    <script src="static/js/jquery-3.3.1.min.js"></script>
    <script src="static/js/regex.js"></script>
</asp:Content>
