fml.define("blog:common/core",[],function(require,exports){(function(){var a="abbr,article,aside,audio,canvas,datalist,details,dialog,eventsource,figure,footer,header,hgroup,mark,menu,meter,nav,output,progress,section,time,video".split(","),b=a.length;while(b--)document.createElement(a[b])})(),function(){window.importScriptList||(window.importScriptList={}),window.importScript=function(a){if(!a)return;var b=document.getElementsByTagName("script"),c=b.length,d=b[c-1].src,e=d.indexOf("/static/"),f=d.substr(0,e)+"/static/";a.indexOf("http://")==-1&&a.indexOf("https://")==-1&&(a.substr(0,1)=="/"&&(a=a.substr(1)),a=f+a);if(a in importScriptList)return;importScriptList[a]=!0,document.write('<script src="'+a+'" type="text/javascript"></'+"script>")}}(),window.registNS=function(fullNS,isIgnorSelf){var reg=/^[_$a-z]+[_$a-z0-9]*/i,nsArray=fullNS.split("."),sEval="",sNS="",n=isIgnorSelf?nsArray.length-1:nsArray.length;for(var i=0;i<n;i++){if(!reg.test(nsArray[i]))throw new Error("Invalid namespace:"+nsArray[i]+"");i!=0&&(sNS+="."),sNS+=nsArray[i],sEval+="if(typeof("+sNS+")=='undefined') "+sNS+"=new Object();else "+sNS+";"}return sEval!=""?eval(sEval):{}},window.addEventMap=function(a,b,c,d){$.each(b,function(b,e){$(a).bind(e,function(a){var b=a.target||a.srcElement;if(!b)return!1;for(var f in c[e]){var g=c[e][f];if(b.className&&$(b).hasClass(f)){g.call(b,a);break}if(ancestor=$(b).parents("."+f)[0]){g.call(ancestor,a);break}}typeof d=="function"&&d.call(a)})})};var fixedTheElementOnScroll=function(a,b){if(!a)return!1;b=parseInt(b,10)||0;var c=function(c){var d=$(a).offset();$(a).attr("data-fixed")!="1"&&$(a).attr({"data-original-top":d.top});var e=$(window).scrollTop()+b;$(a).attr("data-original-top")<=e?$.browser.ie==6?($(a).attr({"data-fixed":1}),$(a).css({position:"absolute",top:e-$(a).attr("data-original-top")+a.offsetHeight,left:0})):$(a).attr("data-fixed")!="1"&&($(a).attr({"data-fixed":1}),$(a).css({position:"fixed",top:b,left:d.left})):($(a).attr("data-fixed",0),$(a).css({position:"static"}))};c(),$(window).scroll(c)};String.prototype.trim=function(){return this.replace(/^\s*|\s*$/g,"")},String.format=function(a,b){a=String(a);var c=Array.prototype.slice.call(arguments,1),d=Object.prototype.toString;return c.length?(c=c.length==1?b!==null&&/\[object Array\]|\[object Object\]/.test(d.call(b))?b:c:c,a.replace(/#\{(.+?)\}/g,function(a,b){var e=c[b];return"[object Function]"==d.call(e)&&(e=e(b)),"undefined"==typeof e?"":e})):a}});