!function(){window.html_beautify=function(t,i){return function(t,i,n,e){var s,h,r,a,_,o;for(h=(i=i||{}).indent_size||4,r=i.indent_char||" ",_=i.brace_style||"collapse",a=0===i.max_char?1/0:i.max_char||250,o=i.unformatted||["a","span","bdo","em","strong","dfn","code","samp","kbd","var","cite","abbr","acronym","q","sub","sup","tt","i","b","big","small","u","s","strike","font","ins","del","pre","address","dt","h1","h2","h3","h4","h5","h6"],(s=new function(){return this.pos=0,this.token="",this.current_mode="CONTENT",this.tags={parent:"parent1",parentcount:1,parent1:""},this.tag_type="",this.token_text=this.last_token=this.last_text=this.token_type="",this.Utils={whitespace:"\n\r\t ".split(""),single_token:"br,input,link,meta,!doctype,basefont,base,area,hr,wbr,param,img,isindex,?xml,embed,?php,?,?=".split(","),extra_liners:"head,body,/html".split(","),in_array:function(t,i){for(var n=0;n<i.length;n++)if(t===i[n])return!0;return!1}},this.get_content=function(){for(var t="",i=[],n=!1;"<"!==this.input.charAt(this.pos);){if(this.pos>=this.input.length)return i.length?i.join(""):["","TK_EOF"];if(t=this.input.charAt(this.pos),this.pos++,this.line_char_count++,this.Utils.in_array(t,this.Utils.whitespace))i.length&&(n=!0),this.line_char_count--;else{if(n){if(this.line_char_count>=this.max_char){i.push("\n");for(var e=0;e<this.indent_level;e++)i.push(this.indent_string);this.line_char_count=0}else i.push(" "),this.line_char_count++;n=!1}i.push(t)}}return i.length?i.join(""):""},this.get_contents_to=function(t){if(this.pos===this.input.length)return["","TK_EOF"];var i="",n=new RegExp("</"+t+"\\s*>","igm");n.lastIndex=this.pos;var e=n.exec(this.input),s=e?e.index:this.input.length;return this.pos<s&&(i=this.input.substring(this.pos,s),this.pos=s),i},this.record_tag=function(t){this.tags[t+"count"]?(this.tags[t+"count"]++,this.tags[t+this.tags[t+"count"]]=this.indent_level):(this.tags[t+"count"]=1,this.tags[t+this.tags[t+"count"]]=this.indent_level),this.tags[t+this.tags[t+"count"]+"parent"]=this.tags.parent,this.tags.parent=t+this.tags[t+"count"]},this.retrieve_tag=function(t){if(this.tags[t+"count"]){for(var i=this.tags.parent;i&&t+this.tags[t+"count"]!==i;)i=this.tags[i+"parent"];i&&(this.indent_level=this.tags[t+this.tags[t+"count"]],this.tags.parent=this.tags[i+"parent"]),delete this.tags[t+this.tags[t+"count"]+"parent"],delete this.tags[t+this.tags[t+"count"]],1===this.tags[t+"count"]?delete this.tags[t+"count"]:this.tags[t+"count"]--}},this.get_tag=function(t){var i,n,e="",s=[],h="",r=!1,a=this.pos,_=this.line_char_count;t=void 0!==t&&t;do{if(this.pos>=this.input.length)return t&&(this.pos=a,this.line_char_count=_),s.length?s.join(""):["","TK_EOF"];e=this.input.charAt(this.pos),this.pos++,this.line_char_count++,this.Utils.in_array(e,this.Utils.whitespace)?(r=!0,this.line_char_count--):("'"!==e&&'"'!==e||s[1]&&"!"===s[1]||(e+=this.get_unformatted(e),r=!0),"="===e&&(r=!1),s.length&&"="!==s[s.length-1]&&">"!==e&&r&&(this.line_char_count>=this.max_char?(this.print_newline(!1,s),this.line_char_count=0):(s.push(" "),this.line_char_count++),r=!1),"<"===e&&(i=this.pos-1),s.push(e))}while(">"!==e);var u,c=s.join("");u=-1!==c.indexOf(" ")?c.indexOf(" "):c.indexOf(">");var p=c.substring(1,u).toLowerCase();return"/"===c.charAt(c.length-2)||this.Utils.in_array(p,this.Utils.single_token)?t||(this.tag_type="SINGLE"):"script"===p?t||(this.record_tag(p),this.tag_type="SCRIPT"):"style"===p?t||(this.record_tag(p),this.tag_type="STYLE"):this.is_unformatted(p,o)?(h=this.get_unformatted("</"+p+">",c),s.push(h),i>0&&this.Utils.in_array(this.input.charAt(i-1),this.Utils.whitespace)&&s.splice(0,0,this.input.charAt(i-1)),n=this.pos-1,this.Utils.in_array(this.input.charAt(n+1),this.Utils.whitespace)&&s.push(this.input.charAt(n+1)),this.tag_type="SINGLE"):"!"===p.charAt(0)?-1!==p.indexOf("[if")?(-1!==c.indexOf("!IE")&&(h=this.get_unformatted("--\x3e",c),s.push(h)),t||(this.tag_type="START")):-1!==p.indexOf("[endif")?(this.tag_type="END",this.unindent()):-1!==p.indexOf("[cdata[")?(h=this.get_unformatted("]]>",c),s.push(h),t||(this.tag_type="SINGLE")):(h=this.get_unformatted("--\x3e",c),s.push(h),this.tag_type="SINGLE"):t||("/"===p.charAt(0)?(this.retrieve_tag(p.substring(1)),this.tag_type="END"):(this.record_tag(p),this.tag_type="START"),this.Utils.in_array(p,this.Utils.extra_liners)&&this.print_newline(!0,this.output)),t&&(this.pos=a,this.line_char_count=_),s.join("")},this.get_unformatted=function(t,i){if(i&&-1!==i.toLowerCase().indexOf(t))return"";var n="",e="",s=!0;do{if(this.pos>=this.input.length)return e;if(n=this.input.charAt(this.pos),this.pos++,this.Utils.in_array(n,this.Utils.whitespace)){if(!s){this.line_char_count--;continue}if("\n"===n||"\r"===n){e+="\n",this.line_char_count=0;continue}}e+=n,this.line_char_count++,s=!0}while(-1===e.toLowerCase().indexOf(t));return e},this.get_token=function(){var t;if("TK_TAG_SCRIPT"===this.last_token||"TK_TAG_STYLE"===this.last_token){var i=this.last_token.substr(7);return"string"!=typeof(t=this.get_contents_to(i))?t:[t,"TK_"+i]}return"CONTENT"===this.current_mode?"string"!=typeof(t=this.get_content())?t:[t,"TK_CONTENT"]:"TAG"===this.current_mode?"string"!=typeof(t=this.get_tag())?t:[t,"TK_TAG_"+this.tag_type]:void 0},this.get_full_indent=function(t){return(t=this.indent_level+t||0)<1?"":Array(t+1).join(this.indent_string)},this.is_unformatted=function(t,i){if(!this.Utils.in_array(t,i))return!1;if("a"!==t.toLowerCase()||!this.Utils.in_array("a",i))return!0;var n=(this.get_tag(!0)||"").match(/^\s*<\s*\/?([a-z]*)\s*[^>]*>\s*$/);return!(n&&!this.Utils.in_array(n,i))},this.printer=function(t,i,n,e,s){this.input=t||"",this.output=[],this.indent_character=i,this.indent_string="",this.indent_size=n,this.brace_style=s,this.indent_level=0,this.max_char=e,this.line_char_count=0;for(var h=0;h<this.indent_size;h++)this.indent_string+=this.indent_character;this.print_newline=function(t,i){if(this.line_char_count=0,i&&i.length){if(!t)for(;this.Utils.in_array(i[i.length-1],this.Utils.whitespace);)i.pop();i.push("\n");for(var n=0;n<this.indent_level;n++)i.push(this.indent_string)}},this.print_token=function(t){this.output.push(t)},this.indent=function(){this.indent_level++},this.unindent=function(){this.indent_level>0&&this.indent_level--}},this}).printer(t,r,h,a,_);;){var u=s.get_token();if(s.token_text=u[0],s.token_type=u[1],"TK_EOF"===s.token_type)break;switch(s.token_type){case"TK_TAG_START":s.print_newline(!1,s.output),s.print_token(s.token_text),s.indent(),s.current_mode="CONTENT";break;case"TK_TAG_STYLE":case"TK_TAG_SCRIPT":s.print_newline(!1,s.output),s.print_token(s.token_text),s.current_mode="CONTENT";break;case"TK_TAG_END":if("TK_CONTENT"===s.last_token&&""===s.last_text){var c=s.token_text.match(/\w+/)[0],p=s.output[s.output.length-1].match(/<\s*(\w+)/);null!==p&&p[1]===c||s.print_newline(!0,s.output)}s.print_token(s.token_text),s.current_mode="CONTENT";break;case"TK_TAG_SINGLE":var l=s.token_text.match(/^\s*<([a-z]+)/i);l&&s.Utils.in_array(l[1],o)||s.print_newline(!1,s.output),s.print_token(s.token_text),s.current_mode="CONTENT";break;case"TK_CONTENT":""!==s.token_text&&s.print_token(s.token_text),s.current_mode="TAG";break;case"TK_STYLE":case"TK_SCRIPT":if(""!==s.token_text){s.output.push("\n");var g,d=s.token_text,f=1;"TK_SCRIPT"===s.token_type?g="function"==typeof n&&n:"TK_STYLE"===s.token_type&&(g="function"==typeof e&&e),"keep"===i.indent_scripts?f=0:"separate"===i.indent_scripts&&(f=-s.indent_level);var T=s.get_full_indent(f);if(g)d=g(d.replace(/^\s*/,T),i);else{var k=d.match(/^\s*/)[0].match(/[^\n\r]*$/)[0].split(s.indent_string).length-1,y=s.get_full_indent(f-k);d=d.replace(/^\s*/,T).replace(/\r\n|\r|\n/g,"\n"+y).replace(/\s*$/,"")}d&&(s.print_token(d),s.print_newline(!0,s.output))}s.current_mode="TAG"}s.last_token=s.token_type,s.last_text=s.token_text}return s.output.join("")}(t,i,window.js_beautify,window.css_beautify)}}();