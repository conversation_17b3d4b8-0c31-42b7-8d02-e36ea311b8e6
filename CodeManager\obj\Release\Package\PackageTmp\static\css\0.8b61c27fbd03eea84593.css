._3c5eahGieigDTmRHO-d7l3,
._2NFgh0Z6tkGorUToiId47k {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 28px;
  line-height: 1.75rem;
  margin-top: 4px;
  margin-top: 0.25rem;
}

._3c5eahGieigDTmRHO-d7l3 [class^="Content__ChildWrapper"] > [class^="Content"],
._2NFgh0Z6tkGorUToiId47k [class^="Content__ChildWrapper"] > [class^="Content"] {
  border-color: #bbb;
  border-width: 1px;
  background-color: white;
}

@media screen and (max-width: 768px) {
  ._3c5eahGieigDTmRHO-d7l3,
  ._2NFgh0Z6tkGorUToiId47k {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

._3c5eahGieigDTmRHO-d7l3 .status-dropdown__option > div,
._2NFgh0Z6tkGorUToiId47k .status-dropdown__option > div,
._3c5eahGieigDTmRHO-d7l3 .status-dropdown__single-value > div,
._2NFgh0Z6tkGorUToiId47k .status-dropdown__single-value > div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

._3c5eahGieigDTmRHO-d7l3 .status-dropdown__option,
._2NFgh0Z6tkGorUToiId47k .status-dropdown__option {
  position: relative;
}

._3c5eahGieigDTmRHO-d7l3 .status-dropdown__option::before,
._2NFgh0Z6tkGorUToiId47k .status-dropdown__option::before {
  position: absolute;
  content: "";
  width: 9px;
  height: 10px;
  top: 13px;
  left: 17px;
  z-index: 1;
  background-color: white;
}

._38pbMcqJ77EGfPqseO7Gxg {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

._38pbMcqJ77EGfPqseO7Gxg ._3cRRSE6UNDPUEXZCBwF56D {
  width: 100%;
}

._38pbMcqJ77EGfPqseO7Gxg > label {
  width: 100%;
}

._3sZIxoRKMF5wZfiPS6UBcZ {
  -ms-flex-preferred-size: content;
      flex-basis: content;
  white-space: nowrap;
  margin-right: 16px;
  margin-right: 1rem;
  margin-bottom: 0 !important;
  line-height: 40px;
  line-height: 2.5rem;
}

._3sZIxoRKMF5wZfiPS6UBcZ input[type='checkbox'] {
  margin-right: 12px;
  margin-right: 0.75rem;
  margin-top: 0;
}

._3QmuBtWt51qcyPXby622wM {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: 40px;
  min-width: 97px;
  margin-left: -5px;
  padding-right: 10px;
}

._3QmuBtWt51qcyPXby622wM input[type='checkbox'] {
  visibility: hidden;
  position: absolute;
}

._3cRRSE6UNDPUEXZCBwF56D {
  width: 240px;
  height: 40px;
  padding: 0;
}

@media screen and (max-width: 768px) {
  ._3cRRSE6UNDPUEXZCBwF56D {
    width: 100%;
  }
}

._3dZEMCFENcC-7J99E-_ryB {
  display: none;
}

._2NFgh0Z6tkGorUToiId47k {
  margin-top: 0;
}

._2NFgh0Z6tkGorUToiId47k label > label {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

._2cgnn8jOgeTLsVfOQFh5Rs,
.EXjpOz67RQ324-IAAubN5 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.EXjpOz67RQ324-IAAubN5 i {
  color: #A5ADBA !important;
}


._3DswDMo3rPhwmGGm-RnGoI {
  position: relative;
}

._3DswDMo3rPhwmGGm-RnGoI input {
  font-size: 14px;
  height: 40px;
  padding-left: 48px;
  border: 1px solid #b0b0b0;
  border-radius: 0.25rem;
  width: 100% !important;
}

._3DswDMo3rPhwmGGm-RnGoI td[aria-selected="true"] div {
  background-color: #2ecc71;
  color: white;
}

._3DswDMo3rPhwmGGm-RnGoI span {
  vertical-align: middle;
}

._1S2oVfy_sqWLNSz8I7kVKD {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100%;
  position: absolute;
  left: 0;
  width: 40px;
  line-height: 40px;
  text-align: center;
  border-right: 1px solid #b0b0b0;
}

._2-I6T1F9kpUp2j9G31LMYN {
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, .075);
          box-shadow: 0 2px 5px rgba(0, 0, 0, .075);
  border: 1px solid #d0d0d0;
  margin-top: 4px;
  margin-top: 0.25rem;
  margin-left: -2px;
  margin-left: -0.125rem;
  border-radius: 0.25rem;
  /* NASTY HACK TO DIG INTO ATLASKIT COMPONENT */
  /* NASTY HACK TO DIG INTO ATLASKIT COMPONENT */
}

._2-I6T1F9kpUp2j9G31LMYN > div {
  border-radius: inherit;
}

._2-I6T1F9kpUp2j9G31LMYN > div > div {
  border-radius: inherit;
}


._2ZUfc3exqVAVq82-RXmmRy {
  min-height: 25%;
}

._2wOD5aJH3F-IjQcHIh8Udc {
  background-color: white;
  line-height: 40px;
  padding: 1px 10px 0;
}

._3qy5Vy5niTA5YAR6-x9PZ0 {
  background-color: #42526e;
  color: #fff;
}

._34-pLs8rXfQ3pFVMFFpFqG {
  font-size: 14px;
  border: 1px solid #bbb;
  border-radius: 3px;
  padding: 1px 10px 0;
  height: 40px;
  width: 100% !important;
  font-size: 14px;
}


._3cxeOzQec8zqStfZCXZWfG {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

._3l7TrIa5AKEks_HCGGD6q5 {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

._3l7TrIa5AKEks_HCGGD6q5:not(:first-of-type) {
  padding-left: 16px;
  padding-left: 1rem;
}

._1JN3aEKlPD7X5jqB1dXYzx {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

._1JN3aEKlPD7X5jqB1dXYzx > div {
  position: relative;
  width: 100%;
}

._1JN3aEKlPD7X5jqB1dXYzx input {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: 100% !important;
  border: 1px solid #bbb;
  border-radius: 3px;
  padding: 1px 10px 0;
  height: 40px;
}

._1JN3aEKlPD7X5jqB1dXYzx input:focus {
  outline: auto 5px -webkit-focus-ring-color;
}

._1JN3aEKlPD7X5jqB1dXYzx label {
  font-weight: normal;
  line-height: 40px;
  padding-right: 8px;
  position: absolute;
  right: 24px;
  right: 1.5rem;
  text-align: right;
  text-transform: lowercase;
}

._2dv0LNfUzjAAPh-wPACjXb {
  background-color: white;
  line-height: 40px;
  padding: 1px 10px 0;
}

.wPHh9nW_2p42Vk16HDf2n {
  background-color: #42526e;
  color: #fff;
}


._2xTplyAjARswn7VUflwp5X {
  color: #6b778c;
}

.UapkQHfU3FpdbfOgah92H {
  margin-bottom: 32px;
  margin-bottom: 2rem;
}

.UapkQHfU3FpdbfOgah92H .WQm8eph_y5NJ-SQ9HDwMn {
  font-size: 12px;
  margin-bottom: 4px;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.UapkQHfU3FpdbfOgah92H .WQm8eph_y5NJ-SQ9HDwMn small {
  font-weight: normal;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > .p6g8uSKtJvzyCmNQbKYQ1 {
  width: 10%;
  line-height: 40px;
  vertical-align: middle;
  text-align: center;
  margin: 0 10px;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > ._3hns9AjCqRrrlsgglJxVFi {
  width: 45%;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > ._3hns9AjCqRrrlsgglJxVFi > ._160eXeDkZpdkRiWGifZUg0 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > ._3hns9AjCqRrrlsgglJxVFi > ._160eXeDkZpdkRiWGifZUg0 > ._3PCd34aFIQLsAjdWWy3tMO {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > ._3hns9AjCqRrrlsgglJxVFi > ._160eXeDkZpdkRiWGifZUg0 > .HTwgtOWNAQe4c8ZxoQEk3 {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  padding-left: 14px;
}

@media screen and (max-width: 768px) {
  .UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }

  .UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > .p6g8uSKtJvzyCmNQbKYQ1 {
    width: 100%;
    text-align: left;
    margin: 0.5rem 0;
  }

  .UapkQHfU3FpdbfOgah92H .lsbkhNPVsrc4AOE815dW5 > ._3hns9AjCqRrrlsgglJxVFi {
    width: 100%;
  }
}


/* Common styles for Markdown rendering */

.markdown-display h1,
.markdown-display h2,
.markdown-display h3,
.markdown-display h4,
.markdown-display h5,
.markdown-display h6,
.markdown-display p,
.markdown-display pre,
.ak-editor-wrapper h1,
.ak-editor-wrapper h2,
.ak-editor-wrapper h3,
.ak-editor-wrapper h4,
.ak-editor-wrapper h5,
.ak-editor-wrapper h6,
.ak-editor-wrapper p,
.ak-editor-wrapper pre {
  margin-bottom: 16px;
  margin-bottom: 1rem;
}

.markdown-display h1:last-child,
.markdown-display h2:last-child,
.markdown-display h3:last-child,
.markdown-display h4:last-child,
.markdown-display h5:last-child,
.markdown-display h6:last-child,
.markdown-display p:last-child,
.markdown-display pre:last-child,
.ak-editor-wrapper h1:last-child,
.ak-editor-wrapper h2:last-child,
.ak-editor-wrapper h3:last-child,
.ak-editor-wrapper h4:last-child,
.ak-editor-wrapper h5:last-child,
.ak-editor-wrapper h6:last-child,
.ak-editor-wrapper p:last-child,
.ak-editor-wrapper pre:last-child {
  margin-bottom: 0;
}

.markdown-display h1,
.ak-editor-wrapper h1 {
  font-size: 29px;
  font-weight: 600;
}

.markdown-display h2,
.ak-editor-wrapper h2 {
  font-size: 24px;
  font-weight: 500;
}

.markdown-display h3,
.ak-editor-wrapper h3 {
  font-size: 20px;
  font-weight: 500;
}

.markdown-display h4,
.ak-editor-wrapper h4 {
  font-size: 16px;
  font-weight: 600;
}

.markdown-display h5,
.ak-editor-wrapper h5 {
  font-size: 14px;
  font-weight: 600;
}

.markdown-display h6,
.ak-editor-wrapper h6 {
  color: #5e6c84;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.markdown-display h1,
.markdown-display h2,
.markdown-display h3,
.markdown-display h4,
.markdown-display h5,
.markdown-display h6,
.ak-editor-wrapper h1,
.ak-editor-wrapper h2,
.ak-editor-wrapper h3,
.ak-editor-wrapper h4,
.ak-editor-wrapper h5,
.ak-editor-wrapper h6 {
  line-height: 1.5;
}

.markdown-display h1:first-child,
.markdown-display h2:first-child,
.markdown-display h3:first-child,
.markdown-display h4:first-child,
.markdown-display h5:first-child,
.markdown-display h6:first-child,
.ak-editor-wrapper h1:first-child,
.ak-editor-wrapper h2:first-child,
.ak-editor-wrapper h3:first-child,
.ak-editor-wrapper h4:first-child,
.ak-editor-wrapper h5:first-child,
.ak-editor-wrapper h6:first-child {
  margin-top: 0;
}

.markdown-display ul,
.markdown-display ol,
.ak-editor-wrapper ul,
.ak-editor-wrapper ol {
  margin-bottom: 16px;
  margin-bottom: 1rem;
  margin-left: 20px;
  padding-left: 0 !important;
}

.markdown-display ul:last-child,
.markdown-display ol:last-child,
.ak-editor-wrapper ul:last-child,
.ak-editor-wrapper ol:last-child {
  margin-bottom: 0;
}

.markdown-display ul,
.ak-editor-wrapper ul {
  list-style: disc;
}

.markdown-display ol,
.ak-editor-wrapper ol {
  list-style: decimal;
}

.markdown-display blockquote:before,
.markdown-display q:before,
.ak-editor-wrapper blockquote:before,
.ak-editor-wrapper q:before {
  content: '\201C';
}

.markdown-display blockquote:after,
.markdown-display q:after,
.ak-editor-wrapper blockquote:after,
.ak-editor-wrapper q:after {
  content: '\201D';
}

.markdown-display blockquote,
.ak-editor-wrapper blockquote {
  padding-left: 40px;
  border: none;
  margin-bottom: 16px;
  margin-bottom: 1rem;
}

.markdown-display blockquote:before,
.ak-editor-wrapper blockquote:before {
  float: left;
  margin-left: -1em;
  text-align: right;
  width: 1em;
}

.markdown-display blockquote > :last-child,
.ak-editor-wrapper blockquote > :last-child {
  display: inline-block;
}

.markdown-display .code-content pre,
.ak-editor-wrapper .code-content pre {
  border: none;
  padding: 0;
}

.markdown-display pre,
.ak-editor-wrapper pre {
  border-radius: 4px;
  font-family: Menlo, Monaco, 'Courier New', monospace;
  background-color: #f5f5f5;
  border: 1px solid #e1e1e1;
  padding: 8px;
  padding: 0.5rem;
  white-space: pre-wrap;
}

.markdown-display pre code,
.ak-editor-wrapper pre code {
  padding: 0px;
  color: inherit;
  background-color: transparent;
  border: none;
}

.markdown-display table,
.ak-editor-wrapper table {
  border-radius: 4px;
  border: 1px solid #ddd;
  width: 100%;
  border-collapse: separate;
  max-width: 100%;
  border-spacing: 0;
  table-layout: fixed;
}

.markdown-display table th,
.markdown-display table td,
.ak-editor-wrapper table th,
.ak-editor-wrapper table td {
  padding: 16px;
  padding: 1rem;
  line-height: 16px;
  line-height: 1rem;
  text-align: left;
  word-wrap: break-word;
}

.markdown-display table th,
.ak-editor-wrapper table th {
  font-weight: 700;
  vertical-align: bottom;
  font-weight: 500;
}

.markdown-display table th:first-of-type,
.ak-editor-wrapper table th:first-of-type {
  border-radius: 4px 0 0 0;
}

.markdown-display table th:last-of-type,
.ak-editor-wrapper table th:last-of-type {
  border-radius: 0 4px 0 0;
}

.markdown-display table td,
.ak-editor-wrapper table td {
  vertical-align: top;
  border-top: 1px solid #ddd;
}

.markdown-display table th + th,
.markdown-display table td + td,
.ak-editor-wrapper table th + th,
.ak-editor-wrapper table td + td {
  border-left: 1px solid #ddd;
}

.markdown-display table tr:last-of-type td:first-of-type,
.ak-editor-wrapper table tr:last-of-type td:first-of-type {
  border-radius: 0 0 0 4px;
}

.markdown-display table tr:last-of-type td:last-of-type,
.ak-editor-wrapper table tr:last-of-type td:last-of-type {
  border-radius: 0 0 4px 0;
}



/*# sourceMappingURL=0.8b61c27fbd03eea84593.css.map*/