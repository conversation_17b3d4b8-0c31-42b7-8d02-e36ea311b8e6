!function(t){"function"==typeof define&&define.amd?define("index",t):t()}((function(){"use strict";function t(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function e(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function n(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&function(t,e){(Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}(t,e)}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function l(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function u(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?l(t):e}function d(t){return function(){var e,n=s(t);if(c()){var i=s(this).constructor;e=Reflect.construct(n,arguments,i)}else e=n.apply(this,arguments);return u(this,e)}}function p(t,e,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var i=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(i){var r=Object.getOwnPropertyDescriptor(i,e);return r.get?r.get.call(n):r.value}})(t,e,n||t)}function h(t){return function(t){if(Array.isArray(t))return f(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return f(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}var _={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};const m=new RegExp("[^#a-f\\d]","gi"),v=new RegExp("^#?[a-f\\d]{3}[a-f\\d]?$|^#?[a-f\\d]{6}([a-f\\d]{2})?$","i");var g=new RegExp(/^#([a-f0-9]{3,4}|[a-f0-9]{4}(?:[a-f0-9]{2}){1,2})\b$/,"i");const y="-?\\d*(?:\\.\\d+)",b=`(${y}?)`,E=`(${y}?%)`,C=`(${y}?%?)`,A=`^\n  hsla?\\(\n    \\s*(-?\\d*(?:\\.\\d+)?(?:deg|rad|turn)?)\\s*,\n    \\s*${E}\\s*,\n    \\s*${E}\\s*\n    (?:,\\s*${C}\\s*)?\n  \\)\n  $\n`.replace(/\n|\s/g,"");var O=new RegExp(A);const T=`^\n  hsla?\\(\n    \\s*(-?\\d*(?:\\.\\d+)?(?:deg|rad|turn)?)\\s*\n    \\s+${E}\n    \\s+${E}\n    \\s*(?:\\s*\\/\\s*${C}\\s*)?\n  \\)\n  $\n`.replace(/\n|\s/g,"");var I=new RegExp(T);const S=`^\n  rgba?\\(\n    \\s*${b}\\s*,\n    \\s*${b}\\s*,\n    \\s*${b}\\s*\n    (?:,\\s*${C}\\s*)?\n  \\)\n  $\n`.replace(/\n|\s/g,"");var L=new RegExp(S);const w=`^\n  rgba?\\(\n    \\s*${E}\\s*,\n    \\s*${E}\\s*,\n    \\s*${E}\\s*\n    (?:,\\s*${C}\\s*)?\n  \\)\n  $\n`.replace(/\n|\s/g,"");var k=new RegExp(w);const N=`^\n  rgba?\\(\n    \\s*${b}\n    \\s+${b}\n    \\s+${b}\n    \\s*(?:\\s*\\/\\s*${C}\\s*)?\n  \\)\n$\n`.replace(/\n|\s/g,"");var R=new RegExp(N);const x=`^\n  rgba?\\(\n    \\s*${E}\n    \\s+${E}\n    \\s+${E}\n    \\s*(?:\\s*\\/\\s*${C}\\s*)?\n  \\)\n$\n`.replace(/\n|\s/g,"");var D=new RegExp(x),P=new RegExp(/^transparent$/,"i");const F=(t,e,n)=>Math.min(Math.max(e,t),n),H=t=>{let e=t;return"number"!=typeof e&&(e=e.endsWith("%")?255*parseFloat(e)/100:parseFloat(e)),F(Math.round(e),0,255)},M=t=>F(parseFloat(t),0,100);function B(t){let e=t;return"number"!=typeof e&&(e=e.endsWith("%")?parseFloat(e)/100:parseFloat(e)),F(e,0,1)}function V([,t,e,n,i=1]){return{type:"rgb",values:[t,e,n].map(H),alpha:B(null===i?1:i)}}
/**
   * parse-css-color
   * @version v0.1.2
   * @link http://github.com/noeldelgado/parse-css-color/
   * @license MIT
   */const j=t=>{if("string"!=typeof t)return null;const e=g.exec(t);if(e)return function(t){const[e,n,i,r]=((t,e={})=>{if("string"!=typeof t||m.test(t)||!v.test(t))throw new TypeError("Expected a valid hex string");let n=1;8===(t=t.replace(/^#/,"")).length&&(n=parseInt(t.slice(6,8),16)/255,t=t.slice(0,6)),4===t.length&&(n=parseInt(t.slice(3,4).repeat(2),16)/255,t=t.slice(0,3)),3===t.length&&(t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]);const i=parseInt(t,16),r=i>>16,o=i>>8&255,a=255&i;return"array"===e.format?[r,o,a,n]:{red:r,green:o,blue:a,alpha:n}})(t,{format:"array"});return V([null,e,n,i,r])}(e[0]);const n=I.exec(t)||O.exec(t);if(n)return function([,t,e,n,i=1]){let r=t;return r=r.endsWith("turn")?360*parseFloat(r)/1:r.endsWith("rad")?Math.round(180*parseFloat(r)/Math.PI):parseFloat(r),{type:"hsl",values:[r,M(e),M(n)],alpha:B(null===i?1:i)}}(n);const i=R.exec(t)||D.exec(t)||L.exec(t)||k.exec(t);if(i)return V(i);if(P.exec(t))return V([null,0,0,0,0]);const r=_[t.toLowerCase()];return r?V([null,r[0],r[1],r[2],1]):null};var U=function(t){var e,n,i,r,o,a=t[0]/360,s=t[1]/100,c=t[2]/100;if(0==s)return[o=255*c,o,o];e=2*c-(n=c<.5?c*(1+s):c+s-c*s),r=[0,0,0];for(var l=0;l<3;l++)(i=a+1/3*-(l-1))<0&&i++,i>1&&i--,o=6*i<1?e+6*(n-e)*i:2*i<1?n:3*i<2?e+(n-e)*(2/3-i)*6:e,r[l]=255*o;return r};function G(t){var e=Math.round(function(t,e,n){return Math.min(Math.max(t,e),n)}(t,0,255)).toString(16);return 1==e.length?"0"+e:e}var q=function(t){var e=4===t.length?G(255*t[3]):"";return"#"+G(t[0])+G(t[1])+G(t[2])+e},K=function(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),c=s-a;return s==a?e=0:i==s?e=(r-o)/c:r==s?e=2+(o-i)/c:o==s&&(e=4+(i-r)/c),(e=Math.min(60*e,360))<0&&(e+=360),n=(a+s)/2,[e,100*(s==a?0:n<=.5?c/(s+a):c/(2-s-a)),100*n]
/**
   * mix-css-color
   * @version v0.1.1
   * @link http://github.com/noeldelgado/mix-css-color/
   * @license MIT
   */};function $(t){const e=j(t);return null===e?null:("hsl"===e.type&&(e.values=U(e.values)),e)}function z(t,e,n=50){const i=$(t),r=$(e);if(!i||!r)return null;const o=Math.min(Math.max(0,n),100)/100,a=2*o-1,s=i.alpha-r.alpha,c=((a*s==-1?a:(a+s)/(1+a*s))+1)/2,l=1-c,[u,d,p]=i.values.map((t,e)=>Math.round(i.values[e]*c+r.values[e]*l)),h=parseFloat((i.alpha*o+r.alpha*(1-o)).toFixed(8));return{hex:q([u,d,p]),hexa:q([u,d,p,h]),rgba:[u,d,p,h],hsla:[...K([u,d,p]).map(Math.round),h]}}
/**
   * values.js - Get the tints and shades of a color
   * @version v2.0.0
   * @link http://noeldelgado.github.io/values.js/
   * @license MIT
   */const W=(t,e)=>null===t||isNaN(t)||"string"==typeof t?e:t;class X{constructor(t="#000",e="base",n=0){[this.rgb,this.alpha,this.type,this.weight]=[[0,0,0],1,e,n];const i=null===t?"#000":t;if("string"!=typeof i)throw new TypeError(`Input should be a string: ${i}`);const r=j(i);if(!r)throw new Error(`Unable to parse color from string: ${i}`);return this[`_setFrom${r.type.toUpperCase()}`]([...r.values,r.alpha])}get hex(){return this.hexString().replace(/^#/,"")}setColor(t){const e=j(t);return e?this[`_setFrom${e.type.toUpperCase()}`]([...e.values,e.alpha]):null}tint(t,e=W(t,50)){return new X(`rgb(${z("#fff",this.rgbString(),e).rgba})`,"tint",e)}shade(t,e=W(t,50)){return new X(`rgb(${z("#000",this.rgbString(),e).rgba})`,"shade",e)}tints(t,e=W(t,10)){return Array.from({length:100/e},(t,n)=>this.tint((n+1)*e))}shades(t,e=W(t,10)){return Array.from({length:100/e},(t,n)=>this.shade((n+1)*e))}all(t=10){return[...this.tints(t).reverse(),Object.assign(this),...this.shades(t)]}hexString(){return q(this.alpha>=1?this.rgb:[...this.rgb,this.alpha])}rgbString(){const t=(this.alpha>=1?this.rgb:[...this.rgb,this.alpha]).join(", ");return`${this.alpha>=1?"rgb":"rgba"}(${t})`}getBrightness(){return Math.round(this.rgb.reduce((t,e)=>t+e)/765*100)}_setFromRGB([t,e,n,i]){return[this.rgb,this.alpha]=[[t,e,n],i],this}_setFromHSL([t,e,n,i]){return[this.rgb,this.alpha]=[U([t,e,n]).map(Math.round),i],this}}X.VERSION="v2.0.0";var Y,Z=function(){function e(){t(this,e),i(this,"listeners",{})}return n(e,[{key:"bind",value:function(t,e){this.listeners[t]||(this.listeners[t]=[]),-1===this.listeners[t].indexOf(e)&&this.listeners[t].push(e)}},{key:"unbind",value:function(t,e){var n=this.listeners[t].indexOf(e);-1!==n&&this.listeners[t].splice(n,1)}},{key:"dispatch",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.listeners[t];if(void 0!==n)for(var i=0,r=n.length;i<r;i++)n[i].call(this,o({type:t},e))}}]),e}(),J=function(e){a(o,e);var r=d(o);function o(){var e;t(this,o);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return i(l(e=r.call.apply(r,[this].concat(a))),"parent",null),i(l(e),"children",[]),e}return n(o,[{key:"appendChild",value:function(t){var e;return null===(e=t.parent)||void 0===e||e.removeChild(t),this.children.push(t),t.name&&(this[t.name]=t),t.parent=this,t}},{key:"removeChild",value:function(t){var e=this.children.indexOf(t);if(-1!==e)return this.children.splice(e,1),delete this[t.name],t.parent=null,t}}]),o}(Z),Q=function(t,e){return function(t,e){
/*! Pickr 1.5.1 MIT | https://github.com/Simonwep/pickr */
window,t.exports=function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=1)}([function(t){t.exports=JSON.parse('{"a":"1.5.1"}')},function(t,e,n){n.r(e);var i={};function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){a(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function a(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function s(t,e,n,i,r={}){e instanceof HTMLCollection||e instanceof NodeList?e=Array.from(e):Array.isArray(e)||(e=[e]),Array.isArray(n)||(n=[n]);for(const a of e)for(const e of n)a[t](e,i,o({capture:!1},r));return Array.prototype.slice.call(arguments,1)}n.r(i),n.d(i,"on",(function(){return c})),n.d(i,"off",(function(){return l})),n.d(i,"createElementFromString",(function(){return u})),n.d(i,"removeAttribute",(function(){return d})),n.d(i,"createFromTemplate",(function(){return p})),n.d(i,"eventPath",(function(){return h})),n.d(i,"resolveElement",(function(){return f})),n.d(i,"adjustableInputNumbers",(function(){return _}));const c=s.bind(null,"addEventListener"),l=s.bind(null,"removeEventListener");function u(t){const e=document.createElement("div");return e.innerHTML=t.trim(),e.firstElementChild}function d(t,e){const n=t.getAttribute(e);return t.removeAttribute(e),n}function p(t){return function t(e,n={}){const i=d(e,":obj"),r=d(e,":ref"),o=i?n[i]={}:n;r&&(n[r]=e);for(const n of Array.from(e.children)){const e=d(n,":arr"),i=t(n,e?{}:o);e&&(o[e]||(o[e]=[])).push(Object.keys(i).length?i:n)}return n}(u(t))}function h(t){let e=t.path||t.composedPath&&t.composedPath();if(e)return e;let n=t.target.parentElement;for(e=[t.target,n];n=n.parentElement;)e.push(n);return e.push(document,window),e}function f(t){return t instanceof Element?t:"string"==typeof t?t.split(/>>/g).reduce((t,e,n,i)=>(t=t.querySelector(e),n<i.length-1?t.shadowRoot:t),document):null}function _(t,e=(t=>t)){function n(n){const i=[.001,.01,.1][Number(n.shiftKey||2*n.ctrlKey)]*(n.deltaY<0?1:-1);let r=0,o=t.selectionStart;t.value=t.value.replace(/[\d.]+/g,(t,n)=>n<=o&&n+t.length>=o?(o=n,e(Number(t),i,r)):(r++,t)),t.focus(),t.setSelectionRange(o,o),n.preventDefault(),t.dispatchEvent(new Event("input"))}c(t,"focus",()=>c(window,"wheel",n,{passive:!1})),c(t,"blur",()=>l(window,"wheel",n))}var m=n(0);const{min:v,max:g,floor:y,round:b}=Math;function E(t,e,n){e/=100,n/=100;const i=y(t=t/360*6),r=t-i,o=n*(1-e),a=n*(1-r*e),s=n*(1-(1-r)*e),c=i%6;return[255*[n,a,o,o,s,n][c],255*[s,n,n,a,o,o][c],255*[o,o,s,n,n,a][c]]}function C(t,e,n){const i=(2-(e/=100))*(n/=100)/2;return 0!==i&&(e=1===i?0:i<.5?e*n/(2*i):e*n/(2-2*i)),[t,100*e,100*i]}function A(t,e,n){const i=v(t/=255,e/=255,n/=255),r=g(t,e,n),o=r-i;let a,s;if(0===o)a=s=0;else{s=o/r;const i=((r-t)/6+o/2)/o,c=((r-e)/6+o/2)/o,l=((r-n)/6+o/2)/o;t===r?a=l-c:e===r?a=1/3+i-l:n===r&&(a=2/3+c-i),a<0?a+=1:a>1&&(a-=1)}return[360*a,100*s,100*r]}function O(t,e,n,i){return e/=100,n/=100,[...A(255*(1-v(1,(t/=100)*(1-(i/=100))+i)),255*(1-v(1,e*(1-i)+i)),255*(1-v(1,n*(1-i)+i)))]}function T(t,e,n){return e/=100,[t,2*(e*=(n/=100)<.5?n:1-n)/(n+e)*100,100*(n+e)]}function I(t){return A(...t.match(/.{2}/g).map(t=>parseInt(t,16)))}function S(t=0,e=0,n=0,i=1){const r=(t,e)=>(n=-1)=>e(~n?t.map(t=>Number(t.toFixed(n))):t),o={h:t,s:e,v:n,a:i,toHSVA(){const t=[o.h,o.s,o.v,o.a];return t.toString=r(t,t=>"hsva(".concat(t[0],", ").concat(t[1],"%, ").concat(t[2],"%, ").concat(o.a,")")),t},toHSLA(){const t=[...C(o.h,o.s,o.v),o.a];return t.toString=r(t,t=>"hsla(".concat(t[0],", ").concat(t[1],"%, ").concat(t[2],"%, ").concat(o.a,")")),t},toRGBA(){const t=[...E(o.h,o.s,o.v),o.a];return t.toString=r(t,t=>"rgba(".concat(t[0],", ").concat(t[1],", ").concat(t[2],", ").concat(o.a,")")),t},toCMYK(){const t=function(t,e,n){const i=E(t,e,n),r=i[0]/255,o=i[1]/255,a=i[2]/255,s=v(1-r,1-o,1-a);return[100*(1===s?0:(1-r-s)/(1-s)),100*(1===s?0:(1-o-s)/(1-s)),100*(1===s?0:(1-a-s)/(1-s)),100*s]}(o.h,o.s,o.v);return t.toString=r(t,t=>"cmyk(".concat(t[0],"%, ").concat(t[1],"%, ").concat(t[2],"%, ").concat(t[3],"%)")),t},toHEXA(){const t=function(t,e,n){return E(t,e,n).map(t=>b(t).toString(16).padStart(2,"0"))}(o.h,o.s,o.v),e=o.a>=1?"":Number((255*o.a).toFixed(0)).toString(16).toUpperCase().padStart(2,"0");return e&&t.push(e),t.toString=()=>"#".concat(t.join("").toUpperCase()),t},clone:()=>S(o.h,o.s,o.v,o.a)};return o}const L=t=>Math.max(Math.min(t,1),0);function w(t){const e={options:Object.assign({lock:null,onchange:()=>0,onstop:()=>0},t),_keyboard(t){const{options:n}=e,{type:i,key:r}=t;if(document.activeElement===n.wrapper){const{lock:n}=e.options,o="ArrowUp"===r,a="ArrowRight"===r,s="ArrowDown"===r,c="ArrowLeft"===r;if("keydown"===i&&(o||a||s||c)){let i=0,r=0;"v"===n?i=o||a?1:-1:"h"===n?i=o||a?-1:1:(r=o?-1:s?1:0,i=c?-1:a?1:0),e.update(L(e.cache.x+.01*i),L(e.cache.y+.01*r)),t.preventDefault()}else r.startsWith("Arrow")&&(e.options.onstop(),t.preventDefault())}},_tapstart(t){c(document,["mouseup","touchend","touchcancel"],e._tapstop),c(document,["mousemove","touchmove"],e._tapmove),t.preventDefault(),e._tapmove(t)},_tapmove(t){const{options:n,cache:i}=e,{lock:r,element:o,wrapper:a}=n,s=a.getBoundingClientRect();let c=0,l=0;if(t){const e=t&&t.touches&&t.touches[0];c=t?(e||t).clientX:0,l=t?(e||t).clientY:0,c<s.left?c=s.left:c>s.left+s.width&&(c=s.left+s.width),l<s.top?l=s.top:l>s.top+s.height&&(l=s.top+s.height),c-=s.left,l-=s.top}else i&&(c=i.x*s.width,l=i.y*s.height);"h"!==r&&(o.style.left="calc(".concat(c/s.width*100,"% - ").concat(o.offsetWidth/2,"px)")),"v"!==r&&(o.style.top="calc(".concat(l/s.height*100,"% - ").concat(o.offsetHeight/2,"px)")),e.cache={x:c/s.width,y:l/s.height};const u=L(c/s.width),d=L(l/s.height);switch(r){case"v":return n.onchange(u);case"h":return n.onchange(d);default:return n.onchange(u,d)}},_tapstop(){e.options.onstop(),l(document,["mouseup","touchend","touchcancel"],e._tapstop),l(document,["mousemove","touchmove"],e._tapmove)},trigger(){e._tapmove()},update(t=0,n=0){const{left:i,top:r,width:o,height:a}=e.options.wrapper.getBoundingClientRect();"h"===e.options.lock&&(n=t),e._tapmove({clientX:i+o*t,clientY:r+a*n})},destroy(){const{options:t,_tapstart:n,_keyboard:i}=e;l(document,["keydown","keyup"],i),l([t.wrapper,t.element],"mousedown",n),l([t.wrapper,t.element],"touchstart",n,{passive:!1})}},{options:n,_tapstart:i,_keyboard:r}=e;return c([n.wrapper,n.element],"mousedown",i),c([n.wrapper,n.element],"touchstart",i,{passive:!1}),c(document,["keydown","keyup"],r),e}function k(t={}){t=Object.assign({onchange:()=>0,className:"",elements:[]},t);const e=c(t.elements,"click",e=>{t.elements.forEach(n=>n.classList[e.target===n?"add":"remove"](t.className)),t.onchange(e)});return{destroy:()=>l(...e)}}function N({el:t,reference:e,padding:n=8}){const i={start:"sme",middle:"mse",end:"ems"},r={top:"tbrl",right:"rltb",bottom:"btrl",left:"lrbt"},o=((t={})=>(e,n=t[e])=>{if(n)return n;const[i,r="middle"]=e.split("-"),o="top"===i||"bottom"===i;return t[e]={position:i,variant:r,isVertical:o}})();return{update(a,s=!1){const{position:c,variant:l,isVertical:u}=o(a),d=e.getBoundingClientRect(),p=t.getBoundingClientRect(),h=t=>t?{t:d.top-p.height-n,b:d.bottom+n}:{r:d.right+n,l:d.left-p.width-n},f=t=>t?{s:d.left+d.width-p.width,m:-p.width/2+(d.left+d.width/2),e:d.left}:{s:d.bottom-p.height,m:d.bottom-d.height/2-p.height/2,e:d.bottom-d.height},_={},m=(t,e,n)=>{const i="top"===n,r=i?p.height:p.width,o=window[i?"innerHeight":"innerWidth"];for(const i of t){const t=e[i],a=_[n]="".concat(t,"px");if(t>0&&t+r<o)return a}return null};for(const e of[u,!u]){const n=e?"top":"left",o=e?"left":"top",a=m(r[c],h(e),n),s=m(i[l],f(e),o);if(a&&s)return t.style[o]=s,void(t.style[n]=a)}s?(t.style.top="".concat((window.innerHeight-p.height)/2,"px"),t.style.left="".concat((window.innerWidth-p.width)/2,"px")):(t.style.left=_.left,t.style.top=_.top)}}}function R(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}class x{constructor(t){R(this,"_initializingActive",!0),R(this,"_recalc",!0),R(this,"_nanopop",null),R(this,"_root",null),R(this,"_color",S()),R(this,"_lastColor",S()),R(this,"_swatchColors",[]),R(this,"_eventListener",{init:[],save:[],hide:[],show:[],clear:[],change:[],changestop:[],cancel:[],swatchselect:[]}),this.options=t=Object.assign({appClass:null,theme:"classic",useAsButton:!1,padding:8,disabled:!1,comparison:!0,closeOnScroll:!1,outputPrecision:0,lockOpacity:!1,autoReposition:!0,container:"body",components:{interaction:{}},strings:{},swatches:null,inline:!1,sliders:null,default:"#42445a",defaultRepresentation:null,position:"bottom-middle",adjustableNumbers:!0,showAlways:!1,closeWithKey:"Escape"},t);const{swatches:e,components:n,theme:i,sliders:r,lockOpacity:o,padding:a}=t;["nano","monolith"].includes(i)&&!r&&(t.sliders="h"),n.interaction||(n.interaction={});const{preview:s,opacity:c,hue:l,palette:u}=n;n.opacity=!o&&c,n.palette=u||s||c||l,this._preBuild(),this._buildComponents(),this._bindEvents(),this._finalBuild(),e&&e.length&&e.forEach(t=>this.addSwatch(t));const{button:d,app:p}=this._root;this._nanopop=N({reference:d,padding:a,el:p}),d.setAttribute("role","button"),d.setAttribute("aria-label","toggle color picker dialog");const h=this;requestAnimationFrame((function e(){if(!p.offsetWidth&&p.parentElement!==t.container)return requestAnimationFrame(e);h.setColor(t.default),h._rePositioningPicker(),t.defaultRepresentation&&(h._representation=t.defaultRepresentation,h.setColorRepresentation(h._representation)),t.showAlways&&h.show(),h._initializingActive=!1,h._emit("init")}))}_preBuild(){const t=this.options;for(const e of["el","container"])t[e]=f(t[e]);this._root=(({components:t,strings:e,useAsButton:n,inline:i,appClass:r,theme:o,lockOpacity:a})=>{const s=t=>t?"":'style="display:none" hidden',c=p('\n      <div :ref="root" class="pickr">\n\n        '.concat(n?"":'<button type="button" :ref="button" class="pcr-button"></button>','\n\n        <div :ref="app" class="pcr-app ').concat(r||"",'" data-theme="').concat(o,'" ').concat(i?'style="position: unset"':"",' aria-label="color picker dialog" role="form">\n          <div class="pcr-selection" ').concat(s(t.palette),'>\n            <div :obj="preview" class="pcr-color-preview" ').concat(s(t.preview),'>\n              <button type="button" :ref="lastColor" class="pcr-last-color" aria-label="use previous color"></button>\n              <div :ref="currentColor" class="pcr-current-color"></div>\n            </div>\n\n            <div :obj="palette" class="pcr-color-palette">\n              <div :ref="picker" class="pcr-picker"></div>\n              <div :ref="palette" class="pcr-palette" tabindex="0" aria-label="color selection area" role="listbox"></div>\n            </div>\n\n            <div :obj="hue" class="pcr-color-chooser" ').concat(s(t.hue),'>\n              <div :ref="picker" class="pcr-picker"></div>\n              <div :ref="slider" class="pcr-hue pcr-slider" tabindex="0" aria-label="hue selection slider" role="slider"></div>\n            </div>\n\n            <div :obj="opacity" class="pcr-color-opacity" ').concat(s(t.opacity),'>\n              <div :ref="picker" class="pcr-picker"></div>\n              <div :ref="slider" class="pcr-opacity pcr-slider" tabindex="0" aria-label="opacity selection slider" role="slider"></div>\n            </div>\n          </div>\n\n          <div class="pcr-swatches ').concat(t.palette?"":"pcr-last",'" :ref="swatches"></div> \n\n          <div :obj="interaction" class="pcr-interaction" ').concat(s(Object.keys(t.interaction).length),'>\n            <input :ref="result" class="pcr-result" type="text" spellcheck="false" ').concat(s(t.interaction.input),'>\n\n            <input :arr="options" class="pcr-type" data-type="HEXA" value="').concat(a?"HEX":"HEXA",'" type="button" ').concat(s(t.interaction.hex),'>\n            <input :arr="options" class="pcr-type" data-type="RGBA" value="').concat(a?"RGB":"RGBA",'" type="button" ').concat(s(t.interaction.rgba),'>\n            <input :arr="options" class="pcr-type" data-type="HSLA" value="').concat(a?"HSL":"HSLA",'" type="button" ').concat(s(t.interaction.hsla),'>\n            <input :arr="options" class="pcr-type" data-type="HSVA" value="').concat(a?"HSV":"HSVA",'" type="button" ').concat(s(t.interaction.hsva),'>\n            <input :arr="options" class="pcr-type" data-type="CMYK" value="CMYK" type="button" ').concat(s(t.interaction.cmyk),'>\n\n            <input :ref="save" class="pcr-save" value="').concat(e.save||"Save",'" type="button" ').concat(s(t.interaction.save),' aria-label="save and exit">\n            <input :ref="cancel" class="pcr-cancel" value="').concat(e.cancel||"Cancel",'" type="button" ').concat(s(t.interaction.cancel),' aria-label="cancel and exit">\n            <input :ref="clear" class="pcr-clear" value="').concat(e.clear||"Clear",'" type="button" ').concat(s(t.interaction.clear),' aria-label="clear and exit">\n          </div>\n        </div>\n      </div>\n    ')),l=c.interaction;return l.options.find(t=>!t.hidden&&!t.classList.add("active")),l.type=()=>l.options.find(t=>t.classList.contains("active")),c})(t),t.useAsButton&&(this._root.button=t.el),t.container.appendChild(this._root.root)}_finalBuild(){const t=this.options,e=this._root;if(t.container.removeChild(e.root),t.inline){const n=t.el.parentElement;t.el.nextSibling?n.insertBefore(e.app,t.el.nextSibling):n.appendChild(e.app)}else t.container.appendChild(e.app);t.useAsButton?t.inline&&t.el.remove():t.el.parentNode.replaceChild(e.root,t.el),t.disabled&&this.disable(),t.comparison||(e.button.style.transition="none",t.useAsButton||(e.preview.lastColor.style.transition="none")),this.hide()}_buildComponents(){const t=this,e=this.options.components,n=(t.options.sliders||"v").repeat(2),[i,r]=n.match(/^[vh]+$/g)?n:[],o=()=>this._color||(this._color=this._lastColor.clone()),a={palette:w({element:t._root.palette.picker,wrapper:t._root.palette.palette,onstop:()=>t._emit("changestop",t),onchange(n,i){if(!e.palette)return;const r=o(),{_root:a,options:s}=t,{lastColor:c,currentColor:l}=a.preview;t._recalc&&(r.s=100*n,r.v=100-100*i,r.v<0&&(r.v=0),t._updateOutput());const u=r.toRGBA().toString(0);this.element.style.background=u,this.wrapper.style.background="\n                        linear-gradient(to top, rgba(0, 0, 0, ".concat(r.a,"), transparent),\n                        linear-gradient(to left, hsla(").concat(r.h,", 100%, 50%, ").concat(r.a,"), rgba(255, 255, 255, ").concat(r.a,"))\n                    "),s.comparison?s.useAsButton||t._lastColor||(c.style.color=u):(a.button.style.color=u,a.button.classList.remove("clear"));const d=r.toHEXA().toString();for(const{el:e,color:n}of t._swatchColors)e.classList[d===n.toHEXA().toString()?"add":"remove"]("pcr-active");l.style.color=u}}),hue:w({lock:"v"===r?"h":"v",element:t._root.hue.picker,wrapper:t._root.hue.slider,onstop:()=>t._emit("changestop",t),onchange(n){if(!e.hue||!e.palette)return;const i=o();t._recalc&&(i.h=360*n),this.element.style.backgroundColor="hsl(".concat(i.h,", 100%, 50%)"),a.palette.trigger()}}),opacity:w({lock:"v"===i?"h":"v",element:t._root.opacity.picker,wrapper:t._root.opacity.slider,onstop:()=>t._emit("changestop",t),onchange(n){if(!e.opacity||!e.palette)return;const i=o();t._recalc&&(i.a=Math.round(100*n)/100),this.element.style.background="rgba(0, 0, 0, ".concat(i.a,")"),a.palette.trigger()}}),selectable:k({elements:t._root.interaction.options,className:"active",onchange(e){t._representation=e.target.getAttribute("data-type").toUpperCase(),t._recalc&&t._updateOutput()}})};this._components=a}_bindEvents(){const{_root:t,options:e}=this,n=[c(t.interaction.clear,"click",()=>this._clearColor()),c([t.interaction.cancel,t.preview.lastColor],"click",()=>{this._emit("cancel",this),this.setHSVA(...(this._lastColor||this._color).toHSVA(),!0)}),c(t.interaction.save,"click",()=>{!this.applyColor()&&!e.showAlways&&this.hide()}),c(t.interaction.result,["keyup","input"],t=>{this.setColor(t.target.value,!0)&&!this._initializingActive&&this._emit("change",this._color),t.stopImmediatePropagation()}),c(t.interaction.result,["focus","blur"],t=>{this._recalc="blur"===t.type,this._recalc&&this._updateOutput()}),c([t.palette.palette,t.palette.picker,t.hue.slider,t.hue.picker,t.opacity.slider,t.opacity.picker],["mousedown","touchstart"],()=>this._recalc=!0)];if(!e.showAlways){const i=e.closeWithKey;n.push(c(t.button,"click",()=>this.isOpen()?this.hide():this.show()),c(document,"keyup",t=>this.isOpen()&&(t.key===i||t.code===i)&&this.hide()),c(document,["touchstart","mousedown"],e=>{this.isOpen()&&!h(e).some(e=>e===t.app||e===t.button)&&this.hide()},{capture:!0}))}if(e.adjustableNumbers){const e={rgba:[255,255,255,1],hsva:[360,100,100,1],hsla:[360,100,100,1],cmyk:[100,100,100,100]};_(t.interaction.result,(t,n,i)=>{const r=e[this.getColorRepresentation().toLowerCase()];if(r){const e=r[i],o=t+(e>=100?1e3*n:n);return o<=0?0:Number((o<e?o:e).toPrecision(3))}return t})}if(e.autoReposition&&!e.inline){let t=null;const i=this;n.push(c(window,["scroll","resize"],()=>{i.isOpen()&&(e.closeOnScroll&&i.hide(),null===t?(t=setTimeout(()=>t=null,100),requestAnimationFrame((function e(){i._rePositioningPicker(),null!==t&&requestAnimationFrame(e)}))):(clearTimeout(t),t=setTimeout(()=>t=null,100)))},{capture:!0}))}this._eventBindings=n}_rePositioningPicker(){const{options:t}=this;t.inline||this._nanopop.update(t.position,!this._recalc)}_updateOutput(){const{_root:t,_color:e,options:n}=this;if(t.interaction.type()){const i="to".concat(t.interaction.type().getAttribute("data-type"));t.interaction.result.value="function"==typeof e[i]?e[i]().toString(n.outputPrecision):""}!this._initializingActive&&this._recalc&&this._emit("change",e)}_clearColor(t=!1){const{_root:e,options:n}=this;n.useAsButton||(e.button.style.color="rgba(0, 0, 0, 0.15)"),e.button.classList.add("clear"),n.showAlways||this.hide(),this._lastColor=null,this._initializingActive||t||(this._emit("save",null),this._emit("clear",this))}_parseLocalColor(t){const{values:e,type:n,a:i}=function(t){t=t.match(/^[a-zA-Z]+$/)?function(t){if("black"===t.toLowerCase())return"#000";const e=document.createElement("canvas").getContext("2d");return e.fillStyle=t,"#000"===e.fillStyle?null:e.fillStyle}(t):t;const e={cmyk:/^cmyk[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)/i,rgba:/^((rgba)|rgb)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]*?([\d.]+|$)/i,hsla:/^((hsla)|hsl)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]*?([\d.]+|$)/i,hsva:/^((hsva)|hsv)[\D]+([\d.]+)[\D]+([\d.]+)[\D]+([\d.]+)[\D]*?([\d.]+|$)/i,hexa:/^#?(([\dA-Fa-f]{3,4})|([\dA-Fa-f]{6})|([\dA-Fa-f]{8}))$/i},n=t=>t.map(t=>/^(|\d+)\.\d+|\d+$/.test(t)?Number(t):void 0);let i;t:for(const r in e){if(!(i=e[r].exec(t)))continue;const o=t=>!!i[2]==("number"==typeof t);switch(r){case"cmyk":{const[,t,e,o,a]=n(i);if(t>100||e>100||o>100||a>100)break t;return{values:O(t,e,o,a),type:r}}case"rgba":{const[,,,t,e,a,s]=n(i);if(t>255||e>255||a>255||s<0||s>1||!o(s))break t;return{values:[...A(t,e,a),s],a:s,type:r}}case"hexa":{let[,t]=i;4!==t.length&&3!==t.length||(t=t.split("").map(t=>t+t).join(""));const e=t.substring(0,6);let n=t.substring(6);return n=n?parseInt(n,16)/255:void 0,{values:[...I(e),n],a:n,type:r}}case"hsla":{const[,,,t,e,a,s]=n(i);if(t>360||e>100||a>100||s<0||s>1||!o(s))break t;return{values:[...T(t,e,a),s],a:s,type:r}}case"hsva":{const[,,,t,e,a,s]=n(i);if(t>360||e>100||a>100||s<0||s>1||!o(s))break t;return{values:[t,e,a,s],a:s,type:r}}}}return{values:null,type:null}}(t),{lockOpacity:r}=this.options,o=void 0!==i&&1!==i;return e&&3===e.length&&(e[3]=void 0),{values:!e||r&&o?null:e,type:n}}_emit(t,...e){this._eventListener[t].forEach(t=>t(...e,this))}on(t,e){return"function"==typeof e&&"string"==typeof t&&t in this._eventListener&&this._eventListener[t].push(e),this}off(t,e){const n=this._eventListener[t];if(n){const t=n.indexOf(e);~t&&n.splice(t,1)}return this}addSwatch(t){const{values:e}=this._parseLocalColor(t);if(e){const{_swatchColors:t,_root:n}=this,i=S(...e),r=u('<button type="button" style="color: '.concat(i.toRGBA().toString(0),'" aria-label="color swatch"/>'));return n.swatches.appendChild(r),t.push({el:r,color:i}),this._eventBindings.push(c(r,"click",()=>{this.setHSVA(...i.toHSVA(),!0),this._emit("swatchselect",i),this._emit("change",i)})),!0}return!1}removeSwatch(t){const e=this._swatchColors[t];if(e){const{el:n}=e;return this._root.swatches.removeChild(n),this._swatchColors.splice(t,1),!0}return!1}applyColor(t=!1){const{preview:e,button:n}=this._root,i=this._color.toRGBA().toString(0);return e.lastColor.style.color=i,this.options.useAsButton||(n.style.color=i),n.classList.remove("clear"),this._lastColor=this._color.clone(),this._initializingActive||t||this._emit("save",this._color),this}destroy(){this._eventBindings.forEach(t=>l(...t)),Object.keys(this._components).forEach(t=>this._components[t].destroy())}destroyAndRemove(){this.destroy();const{root:t,app:e}=this._root;t.parentElement&&t.parentElement.removeChild(t),e.parentElement.removeChild(e),Object.keys(this).forEach(t=>this[t]=null)}hide(){return this._root.app.classList.remove("visible"),this._emit("hide",this),this}show(){return this.options.disabled||(this._root.app.classList.add("visible"),this._rePositioningPicker(),this._emit("show",this)),this}isOpen(){return this._root.app.classList.contains("visible")}setHSVA(t=360,e=0,n=0,i=1,r=!1){const o=this._recalc;if(this._recalc=!1,t<0||t>360||e<0||e>100||n<0||n>100||i<0||i>1)return!1;this._color=S(t,e,n,i);const{hue:a,opacity:s,palette:c}=this._components;return a.update(t/360),s.update(i),c.update(e/100,1-n/100),r||this.applyColor(),o&&this._updateOutput(),this._recalc=o,!0}setColor(t,e=!1){if(null===t)return this._clearColor(e),!0;const{values:n,type:i}=this._parseLocalColor(t);if(n){const t=i.toUpperCase(),{options:r}=this._root.interaction,o=r.find(e=>e.getAttribute("data-type")===t);if(o&&!o.hidden)for(const t of r)t.classList[t===o?"add":"remove"]("active");return!!this.setHSVA(...n,e)&&this.setColorRepresentation(t)}return!1}setColorRepresentation(t){return t=t.toUpperCase(),!!this._root.interaction.options.find(e=>e.getAttribute("data-type").startsWith(t)&&!e.click())}getColorRepresentation(){return this._representation}getColor(){return this._color}getSelectedColor(){return this._lastColor}getRoot(){return this._root}disable(){return this.hide(),this.options.disabled=!0,this._root.button.classList.add("disabled"),this}enable(){return this.options.disabled=!1,this._root.button.classList.remove("disabled"),this}}x.utils=i,x.libs={HSVaColor:S,Moveable:w,Nanopop:N,Selectable:k},x.create=t=>new x(t),x.version=m.a,e.default=x}]).default}(e={exports:{}}),e.exports}(),tt=(Y=Q)&&Y.__esModule&&Object.prototype.hasOwnProperty.call(Y,"default")?Y.default:Y,et=(Q.Pickr,function(t){return"__".concat(t,"__")});function nt(t){var e=document.createElement("div");return e.insertAdjacentHTML("beforeend",t),e.firstElementChild}function it(t){var e,n;this.el.id!==et(t.uuid)?null===(e=this.element)||void 0===e||null===(n=e.querySelector("#".concat(et(t.uuid))))||void 0===n||n.replaceWith(t.element):this.el=t.el}var rt,ot=function(e){a(s,e);var r=d(s);function s(){var e,n,o,a,c,u,d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var p in t(this,s),i(l(a=r.call(this)),"active",!1),i(l(a),"__destroyed",!1),Object.assign(l(a),a.constructor.defaults,d),a.el||(a.el=nt(a.template()),a.children.forEach((c=l(a),it).bind(c)),(u=a.element.classList).add.apply(u,h(a.constructor.ELEMENT_CLASS.split(" ")))),a.attr)a.el.setAttribute(p,a.attr[p]);return(e=a.element.classList).add.apply(e,h(null!==(n=null===(o=a.className)||void 0===o?void 0:o.split(" "))&&void 0!==n?n:"")),a}return n(s,[{key:"template",value:function(){return"<div></div>"}},{key:"activate",value:function(){return this.active=!0,this.element.classList.add("active"),this}},{key:"deactivate",value:function(){return this.active=!1,this.element.classList.remove("active"),this}},{key:"destroy",value:function(){for(var t,e,n=this.children.length;n>0;)this.children[0].destroy(),this.children.length===n&&this.children.shift(),n--;return null===(t=this.parent)||void 0===t||t.removeChild(this),null===(e=this.element)||void 0===e||e.remove(),this.el=this.children=this.listeners=null,this.__destroyed=!0,null}},{key:"render",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"beforeend";return t.insertAdjacentElement(e,this.el),this}},{key:"h",value:function(t,e){var n=Date.now();return this.appendChild(new t(o({uuid:n},e))),"<span id='".concat(et(n),"'></span>")}},{key:"element",get:function(){return this.el}}]),s}(function(e){a(o,e);var r=d(o);function o(){var e;t(this,o);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return i(l(e=r.call.apply(r,[this].concat(a))),"parent",null),i(l(e),"children",[]),e}return n(o,[{key:"appendChild",value:function(t){var e;return null===(e=t.parent)||void 0===e||e.removeChild(t),this.children.push(t),t.name&&(this[t.name]=t),t.parent=this,t}},{key:"removeChild",value:function(t){var e=this.children.indexOf(t);if(-1!==e)return this.children.splice(e,1),delete this[t.name],t.parent=null,t}}]),o}(Z));i(ot,"ELEMENT_CLASS","widget"),i(ot,"defaults",{el:null,className:null,attr:{}});
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0

  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.

  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** */
var at=function(t,e){return(at=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function st(t,e){function n(){this.constructor=t}at(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var ct=function(){return(ct=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function lt(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var i,r,o=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a}function ut(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(lt(arguments[e]));return t}
/**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */var dt=function(){function t(t){void 0===t&&(t={}),this.adapter_=t}return Object.defineProperty(t,"cssClasses",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(t,"strings",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(t,"numbers",{get:function(){return{}},enumerable:!0,configurable:!0}),Object.defineProperty(t,"defaultAdapter",{get:function(){return{}},enumerable:!0,configurable:!0}),t.prototype.init=function(){},t.prototype.destroy=function(){},t}(),pt=function(){function t(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];this.root_=t,this.initialize.apply(this,ut(n)),this.foundation_=void 0===e?this.getDefaultFoundation():e,this.foundation_.init(),this.initialSyncWithDOM()}return t.attachTo=function(e){return new t(e,new dt({}))},t.prototype.initialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},t.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},t.prototype.initialSyncWithDOM=function(){},t.prototype.destroy=function(){this.foundation_.destroy()},t.prototype.listen=function(t,e,n){this.root_.addEventListener(t,e,n)},t.prototype.unlisten=function(t,e,n){this.root_.removeEventListener(t,e,n)},t.prototype.emit=function(t,e,n){var i;void 0===n&&(n=!1),"function"==typeof CustomEvent?i=new CustomEvent(t,{bubbles:n,detail:e}):(i=document.createEvent("CustomEvent")).initCustomEvent(t,n,!1,e),this.root_.dispatchEvent(i)},t}();
/**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */
/**
   * @license
   * Copyright 2019 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */function ht(t){return void 0===t&&(t=window),!!function(t){void 0===t&&(t=window);var e=!1;try{var n={get passive(){return e=!0,!1}},i=function(){};t.document.addEventListener("test",i,n),t.document.removeEventListener("test",i,n)}catch(t){e=!1}return e}
/**
   * @license
   * Copyright 2018 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */(t)&&{passive:!0}}function ft(t,e){if(t.closest)return t.closest(e);for(var n=t;n;){if(_t(n,e))return n;n=n.parentElement}return null}function _t(t,e){return(t.matches||t.webkitMatchesSelector||t.msMatchesSelector).call(t,e)}
/**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */var mt={BG_FOCUSED:"mdc-ripple-upgraded--background-focused",FG_ACTIVATION:"mdc-ripple-upgraded--foreground-activation",FG_DEACTIVATION:"mdc-ripple-upgraded--foreground-deactivation",ROOT:"mdc-ripple-upgraded",UNBOUNDED:"mdc-ripple-upgraded--unbounded"},vt={VAR_FG_SCALE:"--mdc-ripple-fg-scale",VAR_FG_SIZE:"--mdc-ripple-fg-size",VAR_FG_TRANSLATE_END:"--mdc-ripple-fg-translate-end",VAR_FG_TRANSLATE_START:"--mdc-ripple-fg-translate-start",VAR_LEFT:"--mdc-ripple-left",VAR_TOP:"--mdc-ripple-top"},gt={DEACTIVATION_TIMEOUT_MS:225,FG_DEACTIVATION_MS:150,INITIAL_ORIGIN_SCALE:.6,PADDING:10,TAP_DELAY_MS:300},yt=["touchstart","pointerdown","mousedown","keydown"],bt=["touchend","pointerup","mouseup","contextmenu"],Et=[],Ct=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.activationAnimationHasEnded_=!1,i.activationTimer_=0,i.fgDeactivationRemovalTimer_=0,i.fgScale_="0",i.frame_={width:0,height:0},i.initialSize_=0,i.layoutFrame_=0,i.maxRadius_=0,i.unboundedCoords_={left:0,top:0},i.activationState_=i.defaultActivationState_(),i.activationTimerCallback_=function(){i.activationAnimationHasEnded_=!0,i.runDeactivationUXLogicIfReady_()},i.activateHandler_=function(t){return i.activate_(t)},i.deactivateHandler_=function(){return i.deactivate_()},i.focusHandler_=function(){return i.handleFocus()},i.blurHandler_=function(){return i.handleBlur()},i.resizeHandler_=function(){return i.layout()},i}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return mt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return vt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return gt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){var t=this,n=this.supportsPressRipple_();if(this.registerRootHandlers_(n),n){var i=e.cssClasses,r=i.ROOT,o=i.UNBOUNDED;requestAnimationFrame((function(){t.adapter_.addClass(r),t.adapter_.isUnbounded()&&(t.adapter_.addClass(o),t.layoutInternal_())}))}},e.prototype.destroy=function(){var t=this;if(this.supportsPressRipple_()){this.activationTimer_&&(clearTimeout(this.activationTimer_),this.activationTimer_=0,this.adapter_.removeClass(e.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer_&&(clearTimeout(this.fgDeactivationRemovalTimer_),this.fgDeactivationRemovalTimer_=0,this.adapter_.removeClass(e.cssClasses.FG_DEACTIVATION));var n=e.cssClasses,i=n.ROOT,r=n.UNBOUNDED;requestAnimationFrame((function(){t.adapter_.removeClass(i),t.adapter_.removeClass(r),t.removeCssVars_()}))}this.deregisterRootHandlers_(),this.deregisterDeactivationHandlers_()},e.prototype.activate=function(t){this.activate_(t)},e.prototype.deactivate=function(){this.deactivate_()},e.prototype.layout=function(){var t=this;this.layoutFrame_&&cancelAnimationFrame(this.layoutFrame_),this.layoutFrame_=requestAnimationFrame((function(){t.layoutInternal_(),t.layoutFrame_=0}))},e.prototype.setUnbounded=function(t){var n=e.cssClasses.UNBOUNDED;t?this.adapter_.addClass(n):this.adapter_.removeClass(n)},e.prototype.handleFocus=function(){var t=this;requestAnimationFrame((function(){return t.adapter_.addClass(e.cssClasses.BG_FOCUSED)}))},e.prototype.handleBlur=function(){var t=this;requestAnimationFrame((function(){return t.adapter_.removeClass(e.cssClasses.BG_FOCUSED)}))},e.prototype.supportsPressRipple_=function(){return this.adapter_.browserSupportsCssVars()},e.prototype.defaultActivationState_=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},e.prototype.registerRootHandlers_=function(t){var e=this;t&&(yt.forEach((function(t){e.adapter_.registerInteractionHandler(t,e.activateHandler_)})),this.adapter_.isUnbounded()&&this.adapter_.registerResizeHandler(this.resizeHandler_)),this.adapter_.registerInteractionHandler("focus",this.focusHandler_),this.adapter_.registerInteractionHandler("blur",this.blurHandler_)},e.prototype.registerDeactivationHandlers_=function(t){var e=this;"keydown"===t.type?this.adapter_.registerInteractionHandler("keyup",this.deactivateHandler_):bt.forEach((function(t){e.adapter_.registerDocumentInteractionHandler(t,e.deactivateHandler_)}))},e.prototype.deregisterRootHandlers_=function(){var t=this;yt.forEach((function(e){t.adapter_.deregisterInteractionHandler(e,t.activateHandler_)})),this.adapter_.deregisterInteractionHandler("focus",this.focusHandler_),this.adapter_.deregisterInteractionHandler("blur",this.blurHandler_),this.adapter_.isUnbounded()&&this.adapter_.deregisterResizeHandler(this.resizeHandler_)},e.prototype.deregisterDeactivationHandlers_=function(){var t=this;this.adapter_.deregisterInteractionHandler("keyup",this.deactivateHandler_),bt.forEach((function(e){t.adapter_.deregisterDocumentInteractionHandler(e,t.deactivateHandler_)}))},e.prototype.removeCssVars_=function(){var t=this,n=e.strings;Object.keys(n).forEach((function(e){0===e.indexOf("VAR_")&&t.adapter_.updateCssVariable(n[e],null)}))},e.prototype.activate_=function(t){var e=this;if(!this.adapter_.isSurfaceDisabled()){var n=this.activationState_;if(!n.isActivated){var i=this.previousActivationEvent_;i&&void 0!==t&&i.type!==t.type||(n.isActivated=!0,n.isProgrammatic=void 0===t,n.activationEvent=t,n.wasActivatedByPointer=!n.isProgrammatic&&void 0!==t&&("mousedown"===t.type||"touchstart"===t.type||"pointerdown"===t.type),void 0!==t&&Et.length>0&&Et.some((function(t){return e.adapter_.containsEventTarget(t)}))?this.resetActivationState_():(void 0!==t&&(Et.push(t.target),this.registerDeactivationHandlers_(t)),n.wasElementMadeActive=this.checkElementMadeActive_(t),n.wasElementMadeActive&&this.animateActivation_(),requestAnimationFrame((function(){Et=[],n.wasElementMadeActive||void 0===t||" "!==t.key&&32!==t.keyCode||(n.wasElementMadeActive=e.checkElementMadeActive_(t),n.wasElementMadeActive&&e.animateActivation_()),n.wasElementMadeActive||(e.activationState_=e.defaultActivationState_())}))))}}},e.prototype.checkElementMadeActive_=function(t){return void 0===t||"keydown"!==t.type||this.adapter_.isSurfaceActive()},e.prototype.animateActivation_=function(){var t=this,n=e.strings,i=n.VAR_FG_TRANSLATE_START,r=n.VAR_FG_TRANSLATE_END,o=e.cssClasses,a=o.FG_DEACTIVATION,s=o.FG_ACTIVATION,c=e.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal_();var l="",u="";if(!this.adapter_.isUnbounded()){var d=this.getFgTranslationCoordinates_(),p=d.startPoint,h=d.endPoint;l=p.x+"px, "+p.y+"px",u=h.x+"px, "+h.y+"px"}this.adapter_.updateCssVariable(i,l),this.adapter_.updateCssVariable(r,u),clearTimeout(this.activationTimer_),clearTimeout(this.fgDeactivationRemovalTimer_),this.rmBoundedActivationClasses_(),this.adapter_.removeClass(a),this.adapter_.computeBoundingRect(),this.adapter_.addClass(s),this.activationTimer_=setTimeout((function(){return t.activationTimerCallback_()}),c)},e.prototype.getFgTranslationCoordinates_=function(){var t,e=this.activationState_,n=e.activationEvent;return{startPoint:t={x:(t=e.wasActivatedByPointer?function(t,e,n){if(!t)return{x:0,y:0};var i,r,o=e.x,a=e.y,s=o+n.left,c=a+n.top;if("touchstart"===t.type){var l=t;i=l.changedTouches[0].pageX-s,r=l.changedTouches[0].pageY-c}else{var u=t;i=u.pageX-s,r=u.pageY-c}return{x:i,y:r}}(n,this.adapter_.getWindowPageOffset(),this.adapter_.computeBoundingRect()):{x:this.frame_.width/2,y:this.frame_.height/2}).x-this.initialSize_/2,y:t.y-this.initialSize_/2},endPoint:{x:this.frame_.width/2-this.initialSize_/2,y:this.frame_.height/2-this.initialSize_/2}}},e.prototype.runDeactivationUXLogicIfReady_=function(){var t=this,n=e.cssClasses.FG_DEACTIVATION,i=this.activationState_,r=i.hasDeactivationUXRun,o=i.isActivated;(r||!o)&&this.activationAnimationHasEnded_&&(this.rmBoundedActivationClasses_(),this.adapter_.addClass(n),this.fgDeactivationRemovalTimer_=setTimeout((function(){t.adapter_.removeClass(n)}),gt.FG_DEACTIVATION_MS))},e.prototype.rmBoundedActivationClasses_=function(){var t=e.cssClasses.FG_ACTIVATION;this.adapter_.removeClass(t),this.activationAnimationHasEnded_=!1,this.adapter_.computeBoundingRect()},e.prototype.resetActivationState_=function(){var t=this;this.previousActivationEvent_=this.activationState_.activationEvent,this.activationState_=this.defaultActivationState_(),setTimeout((function(){return t.previousActivationEvent_=void 0}),e.numbers.TAP_DELAY_MS)},e.prototype.deactivate_=function(){var t=this,e=this.activationState_;if(e.isActivated){var n=ct({},e);e.isProgrammatic?(requestAnimationFrame((function(){return t.animateDeactivation_(n)})),this.resetActivationState_()):(this.deregisterDeactivationHandlers_(),requestAnimationFrame((function(){t.activationState_.hasDeactivationUXRun=!0,t.animateDeactivation_(n),t.resetActivationState_()})))}},e.prototype.animateDeactivation_=function(t){var e=t.wasActivatedByPointer,n=t.wasElementMadeActive;(e||n)&&this.runDeactivationUXLogicIfReady_()},e.prototype.layoutInternal_=function(){this.frame_=this.adapter_.computeBoundingRect();var t=Math.max(this.frame_.height,this.frame_.width);this.maxRadius_=this.adapter_.isUnbounded()?t:Math.sqrt(Math.pow(this.frame_.width,2)+Math.pow(this.frame_.height,2))+e.numbers.PADDING;var n=Math.floor(t*e.numbers.INITIAL_ORIGIN_SCALE);this.adapter_.isUnbounded()&&n%2!=0?this.initialSize_=n-1:this.initialSize_=n,this.fgScale_=""+this.maxRadius_/this.initialSize_,this.updateLayoutCssVars_()},e.prototype.updateLayoutCssVars_=function(){var t=e.strings,n=t.VAR_FG_SIZE,i=t.VAR_LEFT,r=t.VAR_TOP,o=t.VAR_FG_SCALE;this.adapter_.updateCssVariable(n,this.initialSize_+"px"),this.adapter_.updateCssVariable(o,this.fgScale_),this.adapter_.isUnbounded()&&(this.unboundedCoords_={left:Math.round(this.frame_.width/2-this.initialSize_/2),top:Math.round(this.frame_.height/2-this.initialSize_/2)},this.adapter_.updateCssVariable(i,this.unboundedCoords_.left+"px"),this.adapter_.updateCssVariable(r,this.unboundedCoords_.top+"px"))},e}(dt),At=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.disabled=!1,e}return st(e,t),e.attachTo=function(t,n){void 0===n&&(n={isUnbounded:void 0});var i=new e(t);return void 0!==n.isUnbounded&&(i.unbounded=n.isUnbounded),i},e.createAdapter=function(t){return{addClass:function(e){return t.root_.classList.add(e)},browserSupportsCssVars:function(){return function(t,e){void 0===e&&(e=!1);var n,i=window.CSS;if("boolean"==typeof rt&&!e)return rt;if(!i||"function"!=typeof i.supports)return!1;var r=i.supports("--css-vars","yes"),o=i.supports("(--css-vars: yes)")&&i.supports("color","#00000000");return n=r||o,e||(rt=n),n}()},computeBoundingRect:function(){return t.root_.getBoundingClientRect()},containsEventTarget:function(e){return t.root_.contains(e)},deregisterDocumentInteractionHandler:function(t,e){return document.documentElement.removeEventListener(t,e,ht())},deregisterInteractionHandler:function(e,n){return t.root_.removeEventListener(e,n,ht())},deregisterResizeHandler:function(t){return window.removeEventListener("resize",t)},getWindowPageOffset:function(){return{x:window.pageXOffset,y:window.pageYOffset}},isSurfaceActive:function(){return _t(t.root_,":active")},isSurfaceDisabled:function(){return Boolean(t.disabled)},isUnbounded:function(){return Boolean(t.unbounded)},registerDocumentInteractionHandler:function(t,e){return document.documentElement.addEventListener(t,e,ht())},registerInteractionHandler:function(e,n){return t.root_.addEventListener(e,n,ht())},registerResizeHandler:function(t){return window.addEventListener("resize",t)},removeClass:function(e){return t.root_.classList.remove(e)},updateCssVariable:function(e,n){return t.root_.style.setProperty(e,n)}}},Object.defineProperty(e.prototype,"unbounded",{get:function(){return Boolean(this.unbounded_)},set:function(t){this.unbounded_=Boolean(t),this.setUnbounded_()},enumerable:!0,configurable:!0}),e.prototype.activate=function(){this.foundation_.activate()},e.prototype.deactivate=function(){this.foundation_.deactivate()},e.prototype.layout=function(){this.foundation_.layout()},e.prototype.getDefaultFoundation=function(){return new Ct(e.createAdapter(this))},e.prototype.initialSyncWithDOM=function(){var t=this.root_;this.unbounded="mdcRippleIsUnbounded"in t.dataset},e.prototype.setUnbounded_=function(){this.foundation_.setUnbounded(Boolean(this.unbounded_))},e}(pt),Ot=function(e){a(r,e);var n=d(r);function r(e){var o;return t(this,r),i(l(o=n.call(this,e)),"mdcripple",null),o.mdcripple=new At(o.el),o}return r}(ot),Tt=function(){function t(t,e){void 0===e&&(e={}),this.root=t,this.options=e,this.elFocusedBeforeTrapFocus=null}return t.prototype.trapFocus=function(){var t=this.getFocusableElements(this.root);if(0===t.length)throw new Error("FocusTrap: Element must have at least one focusable child.");this.elFocusedBeforeTrapFocus=document.activeElement instanceof HTMLElement?document.activeElement:null,this.wrapTabFocus(this.root,t),this.options.skipInitialFocus||this.focusInitialElement(t,this.options.initialFocusEl)},t.prototype.releaseFocus=function(){[].slice.call(this.root.querySelectorAll(".mdc-dom-focus-sentinel")).forEach((function(t){t.parentElement.removeChild(t)})),this.elFocusedBeforeTrapFocus&&this.elFocusedBeforeTrapFocus.focus()},t.prototype.wrapTabFocus=function(t,e){var n=this.createSentinel(),i=this.createSentinel();n.addEventListener("focus",(function(){e.length>0&&e[e.length-1].focus()})),i.addEventListener("focus",(function(){e.length>0&&e[0].focus()})),t.insertBefore(n,t.children[0]),t.appendChild(i)},t.prototype.focusInitialElement=function(t,e){var n=0;e&&(n=Math.max(t.indexOf(e),0)),t[n].focus()},t.prototype.getFocusableElements=function(t){return[].slice.call(t.querySelectorAll("[autofocus], [tabindex], a, input, textarea, select, button")).filter((function(t){var e="true"===t.getAttribute("aria-disabled")||null!=t.getAttribute("disabled")||null!=t.getAttribute("hidden")||"true"===t.getAttribute("aria-hidden"),n=t.tabIndex>=0&&t.getBoundingClientRect().width>0&&!t.classList.contains("mdc-dom-focus-sentinel")&&!e,i=!1;if(n){var r=getComputedStyle(t);i="none"===r.display||"hidden"===r.visibility}return n&&!i}))},t.prototype.createSentinel=function(){var t=document.createElement("div");return t.setAttribute("tabindex","0"),t.setAttribute("aria-hidden","true"),t.classList.add("mdc-dom-focus-sentinel"),t},t}(),It={CLOSING:"mdc-dialog--closing",OPEN:"mdc-dialog--open",OPENING:"mdc-dialog--opening",SCROLLABLE:"mdc-dialog--scrollable",SCROLL_LOCK:"mdc-dialog-scroll-lock",STACKED:"mdc-dialog--stacked"},St={ACTION_ATTRIBUTE:"data-mdc-dialog-action",BUTTON_DEFAULT_ATTRIBUTE:"data-mdc-dialog-button-default",BUTTON_SELECTOR:".mdc-dialog__button",CLOSED_EVENT:"MDCDialog:closed",CLOSE_ACTION:"close",CLOSING_EVENT:"MDCDialog:closing",CONTAINER_SELECTOR:".mdc-dialog__container",CONTENT_SELECTOR:".mdc-dialog__content",DESTROY_ACTION:"destroy",INITIAL_FOCUS_ATTRIBUTE:"data-mdc-dialog-initial-focus",OPENED_EVENT:"MDCDialog:opened",OPENING_EVENT:"MDCDialog:opening",SCRIM_SELECTOR:".mdc-dialog__scrim",SUPPRESS_DEFAULT_PRESS_SELECTOR:["textarea",".mdc-menu .mdc-list-item"].join(", "),SURFACE_SELECTOR:".mdc-dialog__surface"},Lt={DIALOG_ANIMATION_CLOSE_TIME_MS:75,DIALOG_ANIMATION_OPEN_TIME_MS:150},wt=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.isOpen_=!1,i.animationFrame_=0,i.animationTimer_=0,i.layoutFrame_=0,i.escapeKeyAction_=St.CLOSE_ACTION,i.scrimClickAction_=St.CLOSE_ACTION,i.autoStackButtons_=!0,i.areButtonsStacked_=!1,i}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return It},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return St},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return Lt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addBodyClass:function(){},addClass:function(){},areButtonsStacked:function(){return!1},clickDefaultButton:function(){},eventTargetMatches:function(){return!1},getActionFromEvent:function(){return""},getInitialFocusEl:function(){return null},hasClass:function(){return!1},isContentScrollable:function(){return!1},notifyClosed:function(){},notifyClosing:function(){},notifyOpened:function(){},notifyOpening:function(){},releaseFocus:function(){},removeBodyClass:function(){},removeClass:function(){},reverseButtons:function(){},trapFocus:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){this.adapter_.hasClass(It.STACKED)&&this.setAutoStackButtons(!1)},e.prototype.destroy=function(){this.isOpen_&&this.close(St.DESTROY_ACTION),this.animationTimer_&&(clearTimeout(this.animationTimer_),this.handleAnimationTimerEnd_()),this.layoutFrame_&&(cancelAnimationFrame(this.layoutFrame_),this.layoutFrame_=0)},e.prototype.open=function(){var t=this;this.isOpen_=!0,this.adapter_.notifyOpening(),this.adapter_.addClass(It.OPENING),this.runNextAnimationFrame_((function(){t.adapter_.addClass(It.OPEN),t.adapter_.addBodyClass(It.SCROLL_LOCK),t.layout(),t.animationTimer_=setTimeout((function(){t.handleAnimationTimerEnd_(),t.adapter_.trapFocus(t.adapter_.getInitialFocusEl()),t.adapter_.notifyOpened()}),Lt.DIALOG_ANIMATION_OPEN_TIME_MS)}))},e.prototype.close=function(t){var e=this;void 0===t&&(t=""),this.isOpen_&&(this.isOpen_=!1,this.adapter_.notifyClosing(t),this.adapter_.addClass(It.CLOSING),this.adapter_.removeClass(It.OPEN),this.adapter_.removeBodyClass(It.SCROLL_LOCK),cancelAnimationFrame(this.animationFrame_),this.animationFrame_=0,clearTimeout(this.animationTimer_),this.animationTimer_=setTimeout((function(){e.adapter_.releaseFocus(),e.handleAnimationTimerEnd_(),e.adapter_.notifyClosed(t)}),Lt.DIALOG_ANIMATION_CLOSE_TIME_MS))},e.prototype.isOpen=function(){return this.isOpen_},e.prototype.getEscapeKeyAction=function(){return this.escapeKeyAction_},e.prototype.setEscapeKeyAction=function(t){this.escapeKeyAction_=t},e.prototype.getScrimClickAction=function(){return this.scrimClickAction_},e.prototype.setScrimClickAction=function(t){this.scrimClickAction_=t},e.prototype.getAutoStackButtons=function(){return this.autoStackButtons_},e.prototype.setAutoStackButtons=function(t){this.autoStackButtons_=t},e.prototype.layout=function(){var t=this;this.layoutFrame_&&cancelAnimationFrame(this.layoutFrame_),this.layoutFrame_=requestAnimationFrame((function(){t.layoutInternal_(),t.layoutFrame_=0}))},e.prototype.handleClick=function(t){if(this.adapter_.eventTargetMatches(t.target,St.SCRIM_SELECTOR)&&""!==this.scrimClickAction_)this.close(this.scrimClickAction_);else{var e=this.adapter_.getActionFromEvent(t);e&&this.close(e)}},e.prototype.handleKeydown=function(t){var e="Enter"===t.key||13===t.keyCode;if(e&&!this.adapter_.getActionFromEvent(t)){var n=!this.adapter_.eventTargetMatches(t.target,St.SUPPRESS_DEFAULT_PRESS_SELECTOR);e&&n&&this.adapter_.clickDefaultButton()}},e.prototype.handleDocumentKeydown=function(t){("Escape"===t.key||27===t.keyCode)&&""!==this.escapeKeyAction_&&this.close(this.escapeKeyAction_)},e.prototype.layoutInternal_=function(){this.autoStackButtons_&&this.detectStackedButtons_(),this.detectScrollableContent_()},e.prototype.handleAnimationTimerEnd_=function(){this.animationTimer_=0,this.adapter_.removeClass(It.OPENING),this.adapter_.removeClass(It.CLOSING)},e.prototype.runNextAnimationFrame_=function(t){var e=this;cancelAnimationFrame(this.animationFrame_),this.animationFrame_=requestAnimationFrame((function(){e.animationFrame_=0,clearTimeout(e.animationTimer_),e.animationTimer_=setTimeout(t,0)}))},e.prototype.detectStackedButtons_=function(){this.adapter_.removeClass(It.STACKED);var t=this.adapter_.areButtonsStacked();t&&this.adapter_.addClass(It.STACKED),t!==this.areButtonsStacked_&&(this.adapter_.reverseButtons(),this.areButtonsStacked_=t)},e.prototype.detectScrollableContent_=function(){this.adapter_.removeClass(It.SCROLLABLE),this.adapter_.isContentScrollable()&&this.adapter_.addClass(It.SCROLLABLE)},e}(dt),kt=wt.strings,Nt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),Object.defineProperty(e.prototype,"isOpen",{get:function(){return this.foundation_.isOpen()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"escapeKeyAction",{get:function(){return this.foundation_.getEscapeKeyAction()},set:function(t){this.foundation_.setEscapeKeyAction(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrimClickAction",{get:function(){return this.foundation_.getScrimClickAction()},set:function(t){this.foundation_.setScrimClickAction(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"autoStackButtons",{get:function(){return this.foundation_.getAutoStackButtons()},set:function(t){this.foundation_.setAutoStackButtons(t)},enumerable:!0,configurable:!0}),e.attachTo=function(t){return new e(t)},e.prototype.initialize=function(t){var e,n;void 0===t&&(t=function(t,e){return new Tt(t,e)});var i=this.root_.querySelector(kt.CONTAINER_SELECTOR);if(!i)throw new Error("Dialog component requires a "+kt.CONTAINER_SELECTOR+" container element");this.container_=i,this.content_=this.root_.querySelector(kt.CONTENT_SELECTOR),this.buttons_=[].slice.call(this.root_.querySelectorAll(kt.BUTTON_SELECTOR)),this.defaultButton_=this.root_.querySelector("["+kt.BUTTON_DEFAULT_ATTRIBUTE+"]"),this.focusTrapFactory_=t,this.buttonRipples_=[];try{for(var r=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],i=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(this.buttons_),o=r.next();!o.done;o=r.next()){var a=o.value;this.buttonRipples_.push(new At(a))}}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}},e.prototype.initialSyncWithDOM=function(){var t,e=this;this.focusTrap_=(t=this.container_,(0,this.focusTrapFactory_)(t,{initialFocusEl:this.getInitialFocusEl_()||void 0})),this.handleClick_=this.foundation_.handleClick.bind(this.foundation_),this.handleKeydown_=this.foundation_.handleKeydown.bind(this.foundation_),this.handleDocumentKeydown_=this.foundation_.handleDocumentKeydown.bind(this.foundation_),this.handleLayout_=this.layout.bind(this);var n=["resize","orientationchange"];this.handleOpening_=function(){n.forEach((function(t){return window.addEventListener(t,e.handleLayout_)})),document.addEventListener("keydown",e.handleDocumentKeydown_)},this.handleClosing_=function(){n.forEach((function(t){return window.removeEventListener(t,e.handleLayout_)})),document.removeEventListener("keydown",e.handleDocumentKeydown_)},this.listen("click",this.handleClick_),this.listen("keydown",this.handleKeydown_),this.listen(kt.OPENING_EVENT,this.handleOpening_),this.listen(kt.CLOSING_EVENT,this.handleClosing_)},e.prototype.destroy=function(){this.unlisten("click",this.handleClick_),this.unlisten("keydown",this.handleKeydown_),this.unlisten(kt.OPENING_EVENT,this.handleOpening_),this.unlisten(kt.CLOSING_EVENT,this.handleClosing_),this.handleClosing_(),this.buttonRipples_.forEach((function(t){return t.destroy()})),t.prototype.destroy.call(this)},e.prototype.layout=function(){this.foundation_.layout()},e.prototype.open=function(){this.foundation_.open()},e.prototype.close=function(t){void 0===t&&(t=""),this.foundation_.close(t)},e.prototype.getDefaultFoundation=function(){var t=this;return new wt({addBodyClass:function(t){return document.body.classList.add(t)},addClass:function(e){return t.root_.classList.add(e)},areButtonsStacked:function(){return e=t.buttons_,n=new Set,[].forEach.call(e,(function(t){return n.add(t.offsetTop)})),n.size>1;var e,n},clickDefaultButton:function(){return t.defaultButton_&&t.defaultButton_.click()},eventTargetMatches:function(t,e){return!!t&&_t(t,e)},getActionFromEvent:function(t){if(!t.target)return"";var e=ft(t.target,"["+kt.ACTION_ATTRIBUTE+"]");return e&&e.getAttribute(kt.ACTION_ATTRIBUTE)},getInitialFocusEl:function(){return t.getInitialFocusEl_()},hasClass:function(e){return t.root_.classList.contains(e)},isContentScrollable:function(){return!!(e=t.content_)&&e.scrollHeight>e.offsetHeight;var e},notifyClosed:function(e){return t.emit(kt.CLOSED_EVENT,e?{action:e}:{})},notifyClosing:function(e){return t.emit(kt.CLOSING_EVENT,e?{action:e}:{})},notifyOpened:function(){return t.emit(kt.OPENED_EVENT,{})},notifyOpening:function(){return t.emit(kt.OPENING_EVENT,{})},releaseFocus:function(){return t.focusTrap_.releaseFocus()},removeBodyClass:function(t){return document.body.classList.remove(t)},removeClass:function(e){return t.root_.classList.remove(e)},reverseButtons:function(){t.buttons_.reverse(),t.buttons_.forEach((function(t){t.parentElement.appendChild(t)}))},trapFocus:function(){return t.focusTrap_.trapFocus()}})},e.prototype.getInitialFocusEl_=function(){return document.querySelector("["+kt.INITIAL_FOCUS_ATTRIBUTE+"]")},e}(pt),Rt=function(e){a(o,e);var r=d(o);function o(e){var n;return t(this,o),i(l(n=r.call(this,e)),"mdcdialog",null),n.mdcdialog=new Nt(n.el),n}return n(o,[{key:"template",value:function(){return'\n    <div class="mdc-dialog">\n      <div class="mdc-dialog__container">\n        <div class="mdc-dialog__surface" role="alertdialog" aria-modal="true" aria-labelledby="my-dialog-title" aria-describedby="my-dialog-content">\n          <h2 class="mdc-dialog__title" id="my-dialog-title">'.concat(this.title,'</h2>\n          <div class="mdc-dialog__content" id="my-dialog-content">').concat(this.content(),'</div>\n        </div>\n      </div>\n      <div class="mdc-dialog__scrim"></div>\n    </div>')}},{key:"content",value:function(){return""}}]),n(o,[{key:"open",value:function(){return this.mdcdialog.open(),this}},{key:"close",value:function(){return this.mdcdialog.close(),this}}]),o}(ot);
/**
   * @license
   * Copyright 2020 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */i(Rt,"defaults",{title:""});var xt=function(e){a(o,e);var r=d(o);function o(e){var n;return t(this,o),i(l(n=r.call(this,e)),"mdcripple",null),n.ripple&&(n.mdcripple=new At(n.el).unbounded=!0),n}return n(o,[{key:"template",value:function(){return"\n    <button class='mdc-icon-button' type=\"button\" aria-label='".concat(this.label,"'>\n      ").concat(this.h(this.icon,{className:"mdc-icon-button__icon"}),"\n    </button>\n    ")}}]),o}(ot);i(xt,"defaults",{icon:null,label:"",ripple:!0});
/**
   * @license
   * Copyright 2018 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */
var Dt={CLOSING:"mdc-snackbar--closing",OPEN:"mdc-snackbar--open",OPENING:"mdc-snackbar--opening"},Pt={ACTION_SELECTOR:".mdc-snackbar__action",ARIA_LIVE_LABEL_TEXT_ATTR:"data-mdc-snackbar-label-text",CLOSED_EVENT:"MDCSnackbar:closed",CLOSING_EVENT:"MDCSnackbar:closing",DISMISS_SELECTOR:".mdc-snackbar__dismiss",LABEL_SELECTOR:".mdc-snackbar__label",OPENED_EVENT:"MDCSnackbar:opened",OPENING_EVENT:"MDCSnackbar:opening",REASON_ACTION:"action",REASON_DISMISS:"dismiss",SURFACE_SELECTOR:".mdc-snackbar__surface"},Ft={DEFAULT_AUTO_DISMISS_TIMEOUT_MS:5e3,INDETERMINATE:-1,MAX_AUTO_DISMISS_TIMEOUT_MS:1e4,MIN_AUTO_DISMISS_TIMEOUT_MS:4e3,SNACKBAR_ANIMATION_CLOSE_TIME_MS:75,SNACKBAR_ANIMATION_OPEN_TIME_MS:150,ARIA_LIVE_DELAY_MS:1e3},Ht=Ft.ARIA_LIVE_DELAY_MS,Mt=Pt.ARIA_LIVE_LABEL_TEXT_ATTR;function Bt(t,e){void 0===e&&(e=t);var n=t.getAttribute("aria-live"),i=e.textContent.trim();i&&n&&(t.setAttribute("aria-live","off"),e.textContent="",e.innerHTML='<span style="display: inline-block; width: 0; height: 1px;">&nbsp;</span>',e.setAttribute(Mt,i),setTimeout((function(){t.setAttribute("aria-live",n),e.removeAttribute(Mt),e.textContent=i}),Ht))}
/**
   * @license
   * Copyright 2018 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */var Vt=Dt.OPENING,jt=Dt.OPEN,Ut=Dt.CLOSING,Gt=Pt.REASON_ACTION,qt=Pt.REASON_DISMISS,Kt=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.isOpen_=!1,i.animationFrame_=0,i.animationTimer_=0,i.autoDismissTimer_=0,i.autoDismissTimeoutMs_=Ft.DEFAULT_AUTO_DISMISS_TIMEOUT_MS,i.closeOnEscape_=!0,i}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return Dt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return Pt},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return Ft},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},announce:function(){},notifyClosed:function(){},notifyClosing:function(){},notifyOpened:function(){},notifyOpening:function(){},removeClass:function(){}}},enumerable:!0,configurable:!0}),e.prototype.destroy=function(){this.clearAutoDismissTimer_(),cancelAnimationFrame(this.animationFrame_),this.animationFrame_=0,clearTimeout(this.animationTimer_),this.animationTimer_=0,this.adapter_.removeClass(Vt),this.adapter_.removeClass(jt),this.adapter_.removeClass(Ut)},e.prototype.open=function(){var t=this;this.clearAutoDismissTimer_(),this.isOpen_=!0,this.adapter_.notifyOpening(),this.adapter_.removeClass(Ut),this.adapter_.addClass(Vt),this.adapter_.announce(),this.runNextAnimationFrame_((function(){t.adapter_.addClass(jt),t.animationTimer_=setTimeout((function(){var e=t.getTimeoutMs();t.handleAnimationTimerEnd_(),t.adapter_.notifyOpened(),e!==Ft.INDETERMINATE&&(t.autoDismissTimer_=setTimeout((function(){t.close(qt)}),e))}),Ft.SNACKBAR_ANIMATION_OPEN_TIME_MS)}))},e.prototype.close=function(t){var e=this;void 0===t&&(t=""),this.isOpen_&&(cancelAnimationFrame(this.animationFrame_),this.animationFrame_=0,this.clearAutoDismissTimer_(),this.isOpen_=!1,this.adapter_.notifyClosing(t),this.adapter_.addClass(Dt.CLOSING),this.adapter_.removeClass(Dt.OPEN),this.adapter_.removeClass(Dt.OPENING),clearTimeout(this.animationTimer_),this.animationTimer_=setTimeout((function(){e.handleAnimationTimerEnd_(),e.adapter_.notifyClosed(t)}),Ft.SNACKBAR_ANIMATION_CLOSE_TIME_MS))},e.prototype.isOpen=function(){return this.isOpen_},e.prototype.getTimeoutMs=function(){return this.autoDismissTimeoutMs_},e.prototype.setTimeoutMs=function(t){var e=Ft.MIN_AUTO_DISMISS_TIMEOUT_MS,n=Ft.MAX_AUTO_DISMISS_TIMEOUT_MS;if(!(t===Ft.INDETERMINATE||t<=n&&t>=e))throw new Error("\n        timeoutMs must be an integer in the range "+e+"–"+n+"\n        (or "+Ft.INDETERMINATE+" to disable), but got '"+t+"'");this.autoDismissTimeoutMs_=t},e.prototype.getCloseOnEscape=function(){return this.closeOnEscape_},e.prototype.setCloseOnEscape=function(t){this.closeOnEscape_=t},e.prototype.handleKeyDown=function(t){("Escape"===t.key||27===t.keyCode)&&this.getCloseOnEscape()&&this.close(qt)},e.prototype.handleActionButtonClick=function(t){this.close(Gt)},e.prototype.handleActionIconClick=function(t){this.close(qt)},e.prototype.clearAutoDismissTimer_=function(){clearTimeout(this.autoDismissTimer_),this.autoDismissTimer_=0},e.prototype.handleAnimationTimerEnd_=function(){this.animationTimer_=0,this.adapter_.removeClass(Dt.OPENING),this.adapter_.removeClass(Dt.CLOSING)},e.prototype.runNextAnimationFrame_=function(t){var e=this;cancelAnimationFrame(this.animationFrame_),this.animationFrame_=requestAnimationFrame((function(){e.animationFrame_=0,clearTimeout(e.animationTimer_),e.animationTimer_=setTimeout(t,0)}))},e}(dt),$t=Pt.SURFACE_SELECTOR,zt=Pt.LABEL_SELECTOR,Wt=Pt.ACTION_SELECTOR,Xt=Pt.DISMISS_SELECTOR,Yt=Pt.OPENING_EVENT,Zt=Pt.OPENED_EVENT,Jt=Pt.CLOSING_EVENT,Qt=Pt.CLOSED_EVENT,te=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},e.prototype.initialize=function(t){void 0===t&&(t=function(){return Bt}),this.announce_=t()},e.prototype.initialSyncWithDOM=function(){var t=this;this.surfaceEl_=this.root_.querySelector($t),this.labelEl_=this.root_.querySelector(zt),this.actionEl_=this.root_.querySelector(Wt),this.handleKeyDown_=function(e){return t.foundation_.handleKeyDown(e)},this.handleSurfaceClick_=function(e){var n=e.target;t.isActionButton_(n)?t.foundation_.handleActionButtonClick(e):t.isActionIcon_(n)&&t.foundation_.handleActionIconClick(e)},this.registerKeyDownHandler_(this.handleKeyDown_),this.registerSurfaceClickHandler_(this.handleSurfaceClick_)},e.prototype.destroy=function(){t.prototype.destroy.call(this),this.deregisterKeyDownHandler_(this.handleKeyDown_),this.deregisterSurfaceClickHandler_(this.handleSurfaceClick_)},e.prototype.open=function(){this.foundation_.open()},e.prototype.close=function(t){void 0===t&&(t=""),this.foundation_.close(t)},e.prototype.getDefaultFoundation=function(){var t=this;return new Kt({addClass:function(e){return t.root_.classList.add(e)},announce:function(){return t.announce_(t.labelEl_)},notifyClosed:function(e){return t.emit(Qt,e?{reason:e}:{})},notifyClosing:function(e){return t.emit(Jt,e?{reason:e}:{})},notifyOpened:function(){return t.emit(Zt,{})},notifyOpening:function(){return t.emit(Yt,{})},removeClass:function(e){return t.root_.classList.remove(e)}})},Object.defineProperty(e.prototype,"timeoutMs",{get:function(){return this.foundation_.getTimeoutMs()},set:function(t){this.foundation_.setTimeoutMs(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"closeOnEscape",{get:function(){return this.foundation_.getCloseOnEscape()},set:function(t){this.foundation_.setCloseOnEscape(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return this.foundation_.isOpen()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"labelText",{get:function(){return this.labelEl_.textContent},set:function(t){this.labelEl_.textContent=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"actionButtonText",{get:function(){return this.actionEl_.textContent},set:function(t){this.actionEl_.textContent=t},enumerable:!0,configurable:!0}),e.prototype.registerKeyDownHandler_=function(t){this.listen("keydown",t)},e.prototype.deregisterKeyDownHandler_=function(t){this.unlisten("keydown",t)},e.prototype.registerSurfaceClickHandler_=function(t){this.surfaceEl_.addEventListener("click",t)},e.prototype.deregisterSurfaceClickHandler_=function(t){this.surfaceEl_.removeEventListener("click",t)},e.prototype.isActionButton_=function(t){return Boolean(ft(t,Wt))},e.prototype.isActionIcon_=function(t){return Boolean(ft(t,Xt))},e}(pt),ee=function(e){a(r,e);var i=d(r);function r(){return t(this,r),i.apply(this,arguments)}return n(r,[{key:"template",value:function(){return"\n    <svg viewBox='".concat(this.viewBox,"' width=").concat(this.width," height=").concat(this.height," aria-hidden='true'>\n      ").concat(this.content,"\n    </svg>\n    ")}}]),r}(ot);i(ee,"defaults",{viewBox:"0 0 24 24",content:"",width:24,height:24});var ne=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return i}(ee);i(ne,"defaults",o({},ee.defaults,{content:'<path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />'}));var ie=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return i}(ee);i(ie,"defaults",o({},ee.defaults,{content:"<path d='M12 2A10 10 0 1 1 2 12A10 10 0 0 1 12 2Z' />"}));var re=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return i}(ee);i(re,"defaults",o({},ee.defaults,{content:"<path d='M0 0h24v24H0V0z' fill='none'/><path d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm-1 4H8c-1.1 0-1.99.9-1.99 2L6 21c0 1.1.89 2 1.99 2H19c1.1 0 2-.9 2-2V11l-6-6zM8 21V7h6v5h5v9H8z'/>"}));var oe=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return i}(ee);i(oe,"defaults",o({},ee.defaults,{content:'\n      <path d="M0 0h24v24H0V0z" fill="none"/>\n      <path d="M18 19H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h5c.55 0 1-.45 1-1s-.45-1-1-1H5c-1.11 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-6c0-.55-.45-1-1-1s-1 .45-1 1v5c0 .55-.45 1-1 1zM14 4c0 .55.45 1 1 1h2.59l-9.13 9.13c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L19 6.41V9c0 .55.45 1 1 1s1-.45 1-1V3h-6c-.55 0-1 .45-1 1z"/>\n    '}));var ae=function(e){a(i,e);var n=d(i);function i(){return t(this,i),n.apply(this,arguments)}return i}(ee);i(ae,"defaults",o({},ee.defaults,{content:"<path d='M12 20A8 8 0 1 1 20 12A8 8 0 0 1 12 20M12 2A10 10 0 1 0 22 12A10 10 0 0 0 12 2Z' />"}));var se=function(e){a(o,e);var r=d(o);function o(e){var n,a;return t(this,o),i(l(a=r.call(this,e)),"mdcsnackbar",null),a.mdcsnackbar=new te(a.el),a.mdcsnackbar.timeoutMs=a.timeoutMs,a.leading&&a.el.classList.add("mdc-snackbar--leading"),a.mdcsnackbar.listen("MDCSnackbar:closed",(n=a).destroy.bind(n)),a}return n(o,[{key:"template",value:function(){return'\n    <div class="mdc-snackbar">\n      <div class="mdc-snackbar__surface">\n        <div class="mdc-snackbar__label" role="status" aria-live="polite">'.concat(this.labelText,'</div>\n        <div class="mdc-snackbar__actions">\n          ').concat(this.h(xt,{icon:ne,className:"mdc-snackbar__dismiss",attr:{title:"Dismiss"},ripple:!1}),"\n        </div>\n      </div>\n    </div>")}}]),n(o,[{key:"open",value:function(){return this.mdcsnackbar.open(),this}},{key:"close",value:function(){return this.mdcsnackbar.close(),this}},{key:"destroy",value:function(){p(s(o.prototype),"destroy",this).call(this),this.mdcsnackbar.unlisten("MDCSnackbar:closed",this.destroy.bind(this)),this.mdcsnackbar=null}}]),o}(ot);i(se,"defaults",{timeoutMs:5e3,labelText:"",leading:!1});
/**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */
var ce={LABEL_FLOAT_ABOVE:"mdc-floating-label--float-above",LABEL_SHAKE:"mdc-floating-label--shake",ROOT:"mdc-floating-label"},le=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.shakeAnimationEndHandler_=function(){return i.handleShakeAnimationEnd_()},i}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return ce},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},getWidth:function(){return 0},registerInteractionHandler:function(){},deregisterInteractionHandler:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){this.adapter_.registerInteractionHandler("animationend",this.shakeAnimationEndHandler_)},e.prototype.destroy=function(){this.adapter_.deregisterInteractionHandler("animationend",this.shakeAnimationEndHandler_)},e.prototype.getWidth=function(){return this.adapter_.getWidth()},e.prototype.shake=function(t){var n=e.cssClasses.LABEL_SHAKE;t?this.adapter_.addClass(n):this.adapter_.removeClass(n)},e.prototype.float=function(t){var n=e.cssClasses,i=n.LABEL_FLOAT_ABOVE,r=n.LABEL_SHAKE;t?this.adapter_.addClass(i):(this.adapter_.removeClass(i),this.adapter_.removeClass(r))},e.prototype.handleShakeAnimationEnd_=function(){var t=e.cssClasses.LABEL_SHAKE;this.adapter_.removeClass(t)},e}(dt),ue=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},e.prototype.shake=function(t){this.foundation_.shake(t)},e.prototype.float=function(t){this.foundation_.float(t)},e.prototype.getWidth=function(){return this.foundation_.getWidth()},e.prototype.getDefaultFoundation=function(){var t=this;return new le({addClass:function(e){return t.root_.classList.add(e)},removeClass:function(e){return t.root_.classList.remove(e)},getWidth:function(){return function(t){var e=t;if(null!==e.offsetParent)return e.scrollWidth;var n=e.cloneNode(!0);n.style.setProperty("position","absolute"),n.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(n);var i=n.scrollWidth;return document.documentElement.removeChild(n),i}(t.root_)},registerInteractionHandler:function(e,n){return t.listen(e,n)},deregisterInteractionHandler:function(e,n){return t.unlisten(e,n)}})},e}(pt),de={LINE_RIPPLE_ACTIVE:"mdc-line-ripple--active",LINE_RIPPLE_DEACTIVATING:"mdc-line-ripple--deactivating"},pe=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.transitionEndHandler_=function(t){return i.handleTransitionEnd(t)},i}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return de},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},setStyle:function(){},registerEventHandler:function(){},deregisterEventHandler:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){this.adapter_.registerEventHandler("transitionend",this.transitionEndHandler_)},e.prototype.destroy=function(){this.adapter_.deregisterEventHandler("transitionend",this.transitionEndHandler_)},e.prototype.activate=function(){this.adapter_.removeClass(de.LINE_RIPPLE_DEACTIVATING),this.adapter_.addClass(de.LINE_RIPPLE_ACTIVE)},e.prototype.setRippleCenter=function(t){this.adapter_.setStyle("transform-origin",t+"px center")},e.prototype.deactivate=function(){this.adapter_.addClass(de.LINE_RIPPLE_DEACTIVATING)},e.prototype.handleTransitionEnd=function(t){var e=this.adapter_.hasClass(de.LINE_RIPPLE_DEACTIVATING);"opacity"===t.propertyName&&e&&(this.adapter_.removeClass(de.LINE_RIPPLE_ACTIVE),this.adapter_.removeClass(de.LINE_RIPPLE_DEACTIVATING))},e}(dt),he=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},e.prototype.activate=function(){this.foundation_.activate()},e.prototype.deactivate=function(){this.foundation_.deactivate()},e.prototype.setRippleCenter=function(t){this.foundation_.setRippleCenter(t)},e.prototype.getDefaultFoundation=function(){var t=this;return new pe({addClass:function(e){return t.root_.classList.add(e)},removeClass:function(e){return t.root_.classList.remove(e)},hasClass:function(e){return t.root_.classList.contains(e)},setStyle:function(e,n){return t.root_.style.setProperty(e,n)},registerEventHandler:function(e,n){return t.listen(e,n)},deregisterEventHandler:function(e,n){return t.unlisten(e,n)}})},e}(pt),fe={NOTCH_ELEMENT_SELECTOR:".mdc-notched-outline__notch"},_e={NOTCH_ELEMENT_PADDING:8},me={NO_LABEL:"mdc-notched-outline--no-label",OUTLINE_NOTCHED:"mdc-notched-outline--notched",OUTLINE_UPGRADED:"mdc-notched-outline--upgraded"},ve=function(t){function e(n){return t.call(this,ct(ct({},e.defaultAdapter),n))||this}return st(e,t),Object.defineProperty(e,"strings",{get:function(){return fe},enumerable:!0,configurable:!0}),Object.defineProperty(e,"cssClasses",{get:function(){return me},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return _e},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},setNotchWidthProperty:function(){},removeNotchWidthProperty:function(){}}},enumerable:!0,configurable:!0}),e.prototype.notch=function(t){var n=e.cssClasses.OUTLINE_NOTCHED;t>0&&(t+=_e.NOTCH_ELEMENT_PADDING),this.adapter_.setNotchWidthProperty(t),this.adapter_.addClass(n)},e.prototype.closeNotch=function(){var t=e.cssClasses.OUTLINE_NOTCHED;this.adapter_.removeClass(t),this.adapter_.removeNotchWidthProperty()},e}(dt),ge=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},e.prototype.initialSyncWithDOM=function(){this.notchElement_=this.root_.querySelector(fe.NOTCH_ELEMENT_SELECTOR);var t=this.root_.querySelector("."+le.cssClasses.ROOT);t?(t.style.transitionDuration="0s",this.root_.classList.add(me.OUTLINE_UPGRADED),requestAnimationFrame((function(){t.style.transitionDuration=""}))):this.root_.classList.add(me.NO_LABEL)},e.prototype.notch=function(t){this.foundation_.notch(t)},e.prototype.closeNotch=function(){this.foundation_.closeNotch()},e.prototype.getDefaultFoundation=function(){var t=this;return new ve({addClass:function(e){return t.root_.classList.add(e)},removeClass:function(e){return t.root_.classList.remove(e)},setNotchWidthProperty:function(e){return t.notchElement_.style.setProperty("width",e+"px")},removeNotchWidthProperty:function(){return t.notchElement_.style.removeProperty("width")}})},e}(pt),ye={ROOT:"mdc-text-field-character-counter"},be={ROOT_SELECTOR:"."+ye.ROOT},Ee=function(t){function e(n){return t.call(this,ct(ct({},e.defaultAdapter),n))||this}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return ye},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return be},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{setContent:function(){}}},enumerable:!0,configurable:!0}),e.prototype.setCounterValue=function(t,e){t=Math.min(t,e),this.adapter_.setContent(t+" / "+e)},e}(dt),Ce=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},Object.defineProperty(e.prototype,"foundation",{get:function(){return this.foundation_},enumerable:!0,configurable:!0}),e.prototype.getDefaultFoundation=function(){var t=this;return new Ee({setContent:function(e){t.root_.textContent=e}})},e}(pt),Ae={ARIA_CONTROLS:"aria-controls",INPUT_SELECTOR:".mdc-text-field__input",LABEL_SELECTOR:".mdc-floating-label",LEADING_ICON_SELECTOR:".mdc-text-field__icon--leading",LINE_RIPPLE_SELECTOR:".mdc-line-ripple",OUTLINE_SELECTOR:".mdc-notched-outline",PREFIX_SELECTOR:".mdc-text-field__affix--prefix",SUFFIX_SELECTOR:".mdc-text-field__affix--suffix",TRAILING_ICON_SELECTOR:".mdc-text-field__icon--trailing"},Oe={DISABLED:"mdc-text-field--disabled",FOCUSED:"mdc-text-field--focused",FULLWIDTH:"mdc-text-field--fullwidth",HELPER_LINE:"mdc-text-field-helper-line",INVALID:"mdc-text-field--invalid",LABEL_FLOATING:"mdc-text-field--label-floating",NO_LABEL:"mdc-text-field--no-label",OUTLINED:"mdc-text-field--outlined",ROOT:"mdc-text-field",TEXTAREA:"mdc-text-field--textarea",WITH_LEADING_ICON:"mdc-text-field--with-leading-icon",WITH_TRAILING_ICON:"mdc-text-field--with-trailing-icon"},Te={LABEL_SCALE:.75},Ie=["pattern","min","max","required","step","minlength","maxlength"],Se=["color","date","datetime-local","month","range","time","week"],Le=["mousedown","touchstart"],we=["click","keydown"],ke=function(t){function e(n,i){void 0===i&&(i={});var r=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return r.isFocused_=!1,r.receivedUserInput_=!1,r.isValid_=!0,r.useNativeValidation_=!0,r.helperText_=i.helperText,r.characterCounter_=i.characterCounter,r.leadingIcon_=i.leadingIcon,r.trailingIcon_=i.trailingIcon,r.inputFocusHandler_=function(){return r.activateFocus()},r.inputBlurHandler_=function(){return r.deactivateFocus()},r.inputInputHandler_=function(){return r.handleInput()},r.setPointerXOffset_=function(t){return r.setTransformOrigin(t)},r.textFieldInteractionHandler_=function(){return r.handleTextFieldInteraction()},r.validationAttributeChangeHandler_=function(t){return r.handleValidationAttributeChange(t)},r}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return Oe},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return Ae},enumerable:!0,configurable:!0}),Object.defineProperty(e,"numbers",{get:function(){return Te},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"shouldAlwaysFloat_",{get:function(){var t=this.getNativeInput_().type;return Se.indexOf(t)>=0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"shouldFloat",{get:function(){return this.shouldAlwaysFloat_||this.isFocused_||!!this.getValue()||this.isBadInput_()},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"shouldShake",{get:function(){return!this.isFocused_&&!this.isValid()&&!!this.getValue()},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!0},registerTextFieldInteractionHandler:function(){},deregisterTextFieldInteractionHandler:function(){},registerInputInteractionHandler:function(){},deregisterInputInteractionHandler:function(){},registerValidationAttributeChangeHandler:function(){return new MutationObserver((function(){}))},deregisterValidationAttributeChangeHandler:function(){},getNativeInput:function(){return null},isFocused:function(){return!1},activateLineRipple:function(){},deactivateLineRipple:function(){},setLineRippleTransformOrigin:function(){},shakeLabel:function(){},floatLabel:function(){},hasLabel:function(){return!1},getLabelWidth:function(){return 0},hasOutline:function(){return!1},notchOutline:function(){},closeOutline:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){var t=this;this.adapter_.isFocused()?this.inputFocusHandler_():this.adapter_.hasLabel()&&this.shouldFloat&&(this.notchOutline(!0),this.adapter_.floatLabel(!0),this.styleFloating_(!0)),this.adapter_.registerInputInteractionHandler("focus",this.inputFocusHandler_),this.adapter_.registerInputInteractionHandler("blur",this.inputBlurHandler_),this.adapter_.registerInputInteractionHandler("input",this.inputInputHandler_),Le.forEach((function(e){t.adapter_.registerInputInteractionHandler(e,t.setPointerXOffset_)})),we.forEach((function(e){t.adapter_.registerTextFieldInteractionHandler(e,t.textFieldInteractionHandler_)})),this.validationObserver_=this.adapter_.registerValidationAttributeChangeHandler(this.validationAttributeChangeHandler_),this.setCharacterCounter_(this.getValue().length)},e.prototype.destroy=function(){var t=this;this.adapter_.deregisterInputInteractionHandler("focus",this.inputFocusHandler_),this.adapter_.deregisterInputInteractionHandler("blur",this.inputBlurHandler_),this.adapter_.deregisterInputInteractionHandler("input",this.inputInputHandler_),Le.forEach((function(e){t.adapter_.deregisterInputInteractionHandler(e,t.setPointerXOffset_)})),we.forEach((function(e){t.adapter_.deregisterTextFieldInteractionHandler(e,t.textFieldInteractionHandler_)})),this.adapter_.deregisterValidationAttributeChangeHandler(this.validationObserver_)},e.prototype.handleTextFieldInteraction=function(){var t=this.adapter_.getNativeInput();t&&t.disabled||(this.receivedUserInput_=!0)},e.prototype.handleValidationAttributeChange=function(t){var e=this;t.some((function(t){return Ie.indexOf(t)>-1&&(e.styleValidity_(!0),!0)})),t.indexOf("maxlength")>-1&&this.setCharacterCounter_(this.getValue().length)},e.prototype.notchOutline=function(t){if(this.adapter_.hasOutline())if(t){var e=this.adapter_.getLabelWidth()*Te.LABEL_SCALE;this.adapter_.notchOutline(e)}else this.adapter_.closeOutline()},e.prototype.activateFocus=function(){this.isFocused_=!0,this.styleFocused_(this.isFocused_),this.adapter_.activateLineRipple(),this.adapter_.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter_.floatLabel(this.shouldFloat),this.styleFloating_(this.shouldFloat),this.adapter_.shakeLabel(this.shouldShake)),this.helperText_&&this.helperText_.showToScreenReader()},e.prototype.setTransformOrigin=function(t){var e=t.touches,n=e?e[0]:t,i=n.target.getBoundingClientRect(),r=n.clientX-i.left;this.adapter_.setLineRippleTransformOrigin(r)},e.prototype.handleInput=function(){this.autoCompleteFocus(),this.setCharacterCounter_(this.getValue().length)},e.prototype.autoCompleteFocus=function(){this.receivedUserInput_||this.activateFocus()},e.prototype.deactivateFocus=function(){this.isFocused_=!1,this.adapter_.deactivateLineRipple();var t=this.isValid();this.styleValidity_(t),this.styleFocused_(this.isFocused_),this.adapter_.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter_.floatLabel(this.shouldFloat),this.styleFloating_(this.shouldFloat),this.adapter_.shakeLabel(this.shouldShake)),this.shouldFloat||(this.receivedUserInput_=!1)},e.prototype.getValue=function(){return this.getNativeInput_().value},e.prototype.setValue=function(t){this.getValue()!==t&&(this.getNativeInput_().value=t),this.setCharacterCounter_(t.length);var e=this.isValid();this.styleValidity_(e),this.adapter_.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter_.floatLabel(this.shouldFloat),this.styleFloating_(this.shouldFloat),this.adapter_.shakeLabel(this.shouldShake))},e.prototype.isValid=function(){return this.useNativeValidation_?this.isNativeInputValid_():this.isValid_},e.prototype.setValid=function(t){this.isValid_=t,this.styleValidity_(t);var e=!t&&!this.isFocused_&&!!this.getValue();this.adapter_.hasLabel()&&this.adapter_.shakeLabel(e)},e.prototype.setUseNativeValidation=function(t){this.useNativeValidation_=t},e.prototype.isDisabled=function(){return this.getNativeInput_().disabled},e.prototype.setDisabled=function(t){this.getNativeInput_().disabled=t,this.styleDisabled_(t)},e.prototype.setHelperTextContent=function(t){this.helperText_&&this.helperText_.setContent(t)},e.prototype.setLeadingIconAriaLabel=function(t){this.leadingIcon_&&this.leadingIcon_.setAriaLabel(t)},e.prototype.setLeadingIconContent=function(t){this.leadingIcon_&&this.leadingIcon_.setContent(t)},e.prototype.setTrailingIconAriaLabel=function(t){this.trailingIcon_&&this.trailingIcon_.setAriaLabel(t)},e.prototype.setTrailingIconContent=function(t){this.trailingIcon_&&this.trailingIcon_.setContent(t)},e.prototype.setCharacterCounter_=function(t){if(this.characterCounter_){var e=this.getNativeInput_().maxLength;if(-1===e)throw new Error("MDCTextFieldFoundation: Expected maxlength html property on text input or textarea.");this.characterCounter_.setCounterValue(t,e)}},e.prototype.isBadInput_=function(){return this.getNativeInput_().validity.badInput||!1},e.prototype.isNativeInputValid_=function(){return this.getNativeInput_().validity.valid},e.prototype.styleValidity_=function(t){var n=e.cssClasses.INVALID;t?this.adapter_.removeClass(n):this.adapter_.addClass(n),this.helperText_&&this.helperText_.setValidity(t)},e.prototype.styleFocused_=function(t){var n=e.cssClasses.FOCUSED;t?this.adapter_.addClass(n):this.adapter_.removeClass(n)},e.prototype.styleDisabled_=function(t){var n=e.cssClasses,i=n.DISABLED,r=n.INVALID;t?(this.adapter_.addClass(i),this.adapter_.removeClass(r)):this.adapter_.removeClass(i),this.leadingIcon_&&this.leadingIcon_.setDisabled(t),this.trailingIcon_&&this.trailingIcon_.setDisabled(t)},e.prototype.styleFloating_=function(t){var n=e.cssClasses.LABEL_FLOATING;t?this.adapter_.addClass(n):this.adapter_.removeClass(n)},e.prototype.getNativeInput_=function(){return(this.adapter_?this.adapter_.getNativeInput():null)||{disabled:!1,maxLength:-1,type:"input",validity:{badInput:!1,valid:!0},value:""}},e}(dt),Ne={HELPER_TEXT_PERSISTENT:"mdc-text-field-helper-text--persistent",HELPER_TEXT_VALIDATION_MSG:"mdc-text-field-helper-text--validation-msg",ROOT:"mdc-text-field-helper-text"},Re={ARIA_HIDDEN:"aria-hidden",ROLE:"role",ROOT_SELECTOR:"."+Ne.ROOT},xe=function(t){function e(n){return t.call(this,ct(ct({},e.defaultAdapter),n))||this}return st(e,t),Object.defineProperty(e,"cssClasses",{get:function(){return Ne},enumerable:!0,configurable:!0}),Object.defineProperty(e,"strings",{get:function(){return Re},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},setAttr:function(){},removeAttr:function(){},setContent:function(){}}},enumerable:!0,configurable:!0}),e.prototype.setContent=function(t){this.adapter_.setContent(t)},e.prototype.setPersistent=function(t){t?this.adapter_.addClass(Ne.HELPER_TEXT_PERSISTENT):this.adapter_.removeClass(Ne.HELPER_TEXT_PERSISTENT)},e.prototype.setValidation=function(t){t?this.adapter_.addClass(Ne.HELPER_TEXT_VALIDATION_MSG):this.adapter_.removeClass(Ne.HELPER_TEXT_VALIDATION_MSG)},e.prototype.showToScreenReader=function(){this.adapter_.removeAttr(Re.ARIA_HIDDEN)},e.prototype.setValidity=function(t){var e=this.adapter_.hasClass(Ne.HELPER_TEXT_PERSISTENT),n=this.adapter_.hasClass(Ne.HELPER_TEXT_VALIDATION_MSG)&&!t;n?this.adapter_.setAttr(Re.ROLE,"alert"):this.adapter_.removeAttr(Re.ROLE),e||n||this.hide_()},e.prototype.hide_=function(){this.adapter_.setAttr(Re.ARIA_HIDDEN,"true")},e}(dt),De=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},Object.defineProperty(e.prototype,"foundation",{get:function(){return this.foundation_},enumerable:!0,configurable:!0}),e.prototype.getDefaultFoundation=function(){var t=this;return new xe({addClass:function(e){return t.root_.classList.add(e)},removeClass:function(e){return t.root_.classList.remove(e)},hasClass:function(e){return t.root_.classList.contains(e)},setAttr:function(e,n){return t.root_.setAttribute(e,n)},removeAttr:function(e){return t.root_.removeAttribute(e)},setContent:function(e){t.root_.textContent=e}})},e}(pt),Pe={ICON_EVENT:"MDCTextField:icon",ICON_ROLE:"button"},Fe={ROOT:"mdc-text-field__icon"},He=["click","keydown"],Me=function(t){function e(n){var i=t.call(this,ct(ct({},e.defaultAdapter),n))||this;return i.savedTabIndex_=null,i.interactionHandler_=function(t){return i.handleInteraction(t)},i}return st(e,t),Object.defineProperty(e,"strings",{get:function(){return Pe},enumerable:!0,configurable:!0}),Object.defineProperty(e,"cssClasses",{get:function(){return Fe},enumerable:!0,configurable:!0}),Object.defineProperty(e,"defaultAdapter",{get:function(){return{getAttr:function(){return null},setAttr:function(){},removeAttr:function(){},setContent:function(){},registerInteractionHandler:function(){},deregisterInteractionHandler:function(){},notifyIconAction:function(){}}},enumerable:!0,configurable:!0}),e.prototype.init=function(){var t=this;this.savedTabIndex_=this.adapter_.getAttr("tabindex"),He.forEach((function(e){t.adapter_.registerInteractionHandler(e,t.interactionHandler_)}))},e.prototype.destroy=function(){var t=this;He.forEach((function(e){t.adapter_.deregisterInteractionHandler(e,t.interactionHandler_)}))},e.prototype.setDisabled=function(t){this.savedTabIndex_&&(t?(this.adapter_.setAttr("tabindex","-1"),this.adapter_.removeAttr("role")):(this.adapter_.setAttr("tabindex",this.savedTabIndex_),this.adapter_.setAttr("role",Pe.ICON_ROLE)))},e.prototype.setAriaLabel=function(t){this.adapter_.setAttr("aria-label",t)},e.prototype.setContent=function(t){this.adapter_.setContent(t)},e.prototype.handleInteraction=function(t){var e="Enter"===t.key||13===t.keyCode;("click"===t.type||e)&&(t.preventDefault(),this.adapter_.notifyIconAction())},e}(dt),Be=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},Object.defineProperty(e.prototype,"foundation",{get:function(){return this.foundation_},enumerable:!0,configurable:!0}),e.prototype.getDefaultFoundation=function(){var t=this;return new Me({getAttr:function(e){return t.root_.getAttribute(e)},setAttr:function(e,n){return t.root_.setAttribute(e,n)},removeAttr:function(e){return t.root_.removeAttribute(e)},setContent:function(e){t.root_.textContent=e},registerInteractionHandler:function(e,n){return t.listen(e,n)},deregisterInteractionHandler:function(e,n){return t.unlisten(e,n)},notifyIconAction:function(){return t.emit(Me.strings.ICON_EVENT,{},!0)}})},e}(pt),Ve=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return st(e,t),e.attachTo=function(t){return new e(t)},e.prototype.initialize=function(t,e,n,i,r,o,a){void 0===t&&(t=function(t,e){return new At(t,e)}),void 0===e&&(e=function(t){return new he(t)}),void 0===n&&(n=function(t){return new De(t)}),void 0===i&&(i=function(t){return new Ce(t)}),void 0===r&&(r=function(t){return new Be(t)}),void 0===o&&(o=function(t){return new ue(t)}),void 0===a&&(a=function(t){return new ge(t)}),this.input_=this.root_.querySelector(Ae.INPUT_SELECTOR);var s=this.root_.querySelector(Ae.LABEL_SELECTOR);this.label_=s?o(s):null;var c=this.root_.querySelector(Ae.LINE_RIPPLE_SELECTOR);this.lineRipple_=c?e(c):null;var l=this.root_.querySelector(Ae.OUTLINE_SELECTOR);this.outline_=l?a(l):null;var u=xe.strings,d=this.root_.nextElementSibling,p=d&&d.classList.contains(Oe.HELPER_LINE),h=p&&d&&d.querySelector(u.ROOT_SELECTOR);this.helperText_=h?n(h):null;var f=Ee.strings,_=this.root_.querySelector(f.ROOT_SELECTOR);!_&&p&&d&&(_=d.querySelector(f.ROOT_SELECTOR)),this.characterCounter_=_?i(_):null;var m=this.root_.querySelector(Ae.LEADING_ICON_SELECTOR);this.leadingIcon_=m?r(m):null;var v=this.root_.querySelector(Ae.TRAILING_ICON_SELECTOR);this.trailingIcon_=v?r(v):null,this.prefix_=this.root_.querySelector(Ae.PREFIX_SELECTOR),this.suffix_=this.root_.querySelector(Ae.SUFFIX_SELECTOR),this.ripple=this.createRipple_(t)},e.prototype.destroy=function(){this.ripple&&this.ripple.destroy(),this.lineRipple_&&this.lineRipple_.destroy(),this.helperText_&&this.helperText_.destroy(),this.characterCounter_&&this.characterCounter_.destroy(),this.leadingIcon_&&this.leadingIcon_.destroy(),this.trailingIcon_&&this.trailingIcon_.destroy(),this.label_&&this.label_.destroy(),this.outline_&&this.outline_.destroy(),t.prototype.destroy.call(this)},e.prototype.initialSyncWithDOM=function(){this.disabled=this.input_.disabled},Object.defineProperty(e.prototype,"value",{get:function(){return this.foundation_.getValue()},set:function(t){this.foundation_.setValue(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"disabled",{get:function(){return this.foundation_.isDisabled()},set:function(t){this.foundation_.setDisabled(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"valid",{get:function(){return this.foundation_.isValid()},set:function(t){this.foundation_.setValid(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"required",{get:function(){return this.input_.required},set:function(t){this.input_.required=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pattern",{get:function(){return this.input_.pattern},set:function(t){this.input_.pattern=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"minLength",{get:function(){return this.input_.minLength},set:function(t){this.input_.minLength=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"maxLength",{get:function(){return this.input_.maxLength},set:function(t){t<0?this.input_.removeAttribute("maxLength"):this.input_.maxLength=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"min",{get:function(){return this.input_.min},set:function(t){this.input_.min=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"max",{get:function(){return this.input_.max},set:function(t){this.input_.max=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"step",{get:function(){return this.input_.step},set:function(t){this.input_.step=t},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"helperTextContent",{set:function(t){this.foundation_.setHelperTextContent(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"leadingIconAriaLabel",{set:function(t){this.foundation_.setLeadingIconAriaLabel(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"leadingIconContent",{set:function(t){this.foundation_.setLeadingIconContent(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"trailingIconAriaLabel",{set:function(t){this.foundation_.setTrailingIconAriaLabel(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"trailingIconContent",{set:function(t){this.foundation_.setTrailingIconContent(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"useNativeValidation",{set:function(t){this.foundation_.setUseNativeValidation(t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"prefixText",{get:function(){return this.prefix_?this.prefix_.textContent:null},set:function(t){this.prefix_&&(this.prefix_.textContent=t)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"suffixText",{get:function(){return this.suffix_?this.suffix_.textContent:null},set:function(t){this.suffix_&&(this.suffix_.textContent=t)},enumerable:!0,configurable:!0}),e.prototype.focus=function(){this.input_.focus()},e.prototype.layout=function(){var t=this.foundation_.shouldFloat;this.foundation_.notchOutline(t)},e.prototype.getDefaultFoundation=function(){var t=ct(ct(ct(ct(ct({},this.getRootAdapterMethods_()),this.getInputAdapterMethods_()),this.getLabelAdapterMethods_()),this.getLineRippleAdapterMethods_()),this.getOutlineAdapterMethods_());return new ke(t,this.getFoundationMap_())},e.prototype.getRootAdapterMethods_=function(){var t=this;return{addClass:function(e){return t.root_.classList.add(e)},removeClass:function(e){return t.root_.classList.remove(e)},hasClass:function(e){return t.root_.classList.contains(e)},registerTextFieldInteractionHandler:function(e,n){return t.listen(e,n)},deregisterTextFieldInteractionHandler:function(e,n){return t.unlisten(e,n)},registerValidationAttributeChangeHandler:function(e){var n=new MutationObserver((function(t){return e(function(t){return t.map((function(t){return t.attributeName})).filter((function(t){return t}))}(t))}));return n.observe(t.input_,{attributes:!0}),n},deregisterValidationAttributeChangeHandler:function(t){return t.disconnect()}}},e.prototype.getInputAdapterMethods_=function(){var t=this;return{getNativeInput:function(){return t.input_},isFocused:function(){return document.activeElement===t.input_},registerInputInteractionHandler:function(e,n){return t.input_.addEventListener(e,n,ht())},deregisterInputInteractionHandler:function(e,n){return t.input_.removeEventListener(e,n,ht())}}},e.prototype.getLabelAdapterMethods_=function(){var t=this;return{floatLabel:function(e){return t.label_&&t.label_.float(e)},getLabelWidth:function(){return t.label_?t.label_.getWidth():0},hasLabel:function(){return Boolean(t.label_)},shakeLabel:function(e){return t.label_&&t.label_.shake(e)}}},e.prototype.getLineRippleAdapterMethods_=function(){var t=this;return{activateLineRipple:function(){t.lineRipple_&&t.lineRipple_.activate()},deactivateLineRipple:function(){t.lineRipple_&&t.lineRipple_.deactivate()},setLineRippleTransformOrigin:function(e){t.lineRipple_&&t.lineRipple_.setRippleCenter(e)}}},e.prototype.getOutlineAdapterMethods_=function(){var t=this;return{closeOutline:function(){return t.outline_&&t.outline_.closeNotch()},hasOutline:function(){return Boolean(t.outline_)},notchOutline:function(e){return t.outline_&&t.outline_.notch(e)}}},e.prototype.getFoundationMap_=function(){return{characterCounter:this.characterCounter_?this.characterCounter_.foundation:void 0,helperText:this.helperText_?this.helperText_.foundation:void 0,leadingIcon:this.leadingIcon_?this.leadingIcon_.foundation:void 0,trailingIcon:this.trailingIcon_?this.trailingIcon_.foundation:void 0}},e.prototype.createRipple_=function(t){var e=this,n=this.root_.classList.contains(Oe.TEXTAREA),i=this.root_.classList.contains(Oe.OUTLINED);if(n||i)return null;var r=ct(ct({},At.createAdapter(this)),{isSurfaceActive:function(){return _t(e.input_,":active")},registerInteractionHandler:function(t,n){return e.input_.addEventListener(t,n,ht())},deregisterInteractionHandler:function(t,n){return e.input_.removeEventListener(t,n,ht())}});return t(this.root_,new Ct(r))},e}(pt),je=function(e){a(o,e);var r=d(o);function o(e){var n;return t(this,o),i(l(n=r.call(this,e)),"input",null),i(l(n),"mdctextfield",null),n.input=n.el.querySelector("input"),n.mdctextfield=new Ve(n.el),n.mdctextfield.useNativeValidation=n.useNativeValidation,n}return n(o,[{key:"setError",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.mdctextfield.valid=!t,this}},{key:"value",get:function(){return this.mdctextfield.value},set:function(t){this.mdctextfield.value=t}}]),o}(ot);
/**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   */i(je,"defaults",{useNativeValidation:!0});var Ue=function(e){a(r,e);var i=d(r);function r(){return t(this,r),i.apply(this,arguments)}return n(r,[{key:"content",value:function(){return"\n    <p>Color tints and shades generator tool.</p>\n    <ul>\n      <li>Accepted Color input:\n        <ul>\n          <li>#RGB, #RRGGBB, #RGBA #RRGGBBAA</li>\n          <li>RGB/A, HSL/A</li>\n          <li>Pre-defined ".concat(this._link("https://www.w3.org/wiki/CSS/Properties/color/keywords","color keywords"),"</p>\n        </ul>\n      </li>\n      <li>The accepted percent balance point goes from 1 to 100. The math is <code>round(100 / &lt;percent&gt;)</code>, so:\n        <ul>\n          <li>1 will produce 100 tints and 100 shades</li>\n          <li>2 will produce 50 tints and 50 shades</li>\n          <li>and so on...</li>\n        </ul>\n      </li>\n    </ul>\n    <p>").concat(this._link("https://github.com/noeldelgado/shadowlord","<span>View source on GitHub</span>"),"</p>\n    <h3>Credits</h3>\n    <ul>\n      <li>").concat(this._link("https://github.com/noeldelgado/values.js","values.js")," — JS library to get the tints and shades.</li>\n      <li>Material Design Components & Icons by ").concat(this._link("https://twitter.com/Google","@Google"),".</li>\n      <li>“Percent” icon by Austin Andrews ").concat(this._link("https://twitter.com/templarian","@templarian"),"</li>\n    </ul>\n    <p>MIT © ").concat(this._link("https://pixelia.me","Noel Delgado"),"</p>\n    ")}},{key:"_link",value:function(t,e){return"\n    <a href=".concat(t,' target="_blank" rel="noopener noreferrer" aria-label=\'(will open in a new window)\'>\x3c!--\n      --\x3e').concat(e,"\x3c!--\n      --\x3e").concat(this.h(oe,{width:16,height:16}),"\x3c!--\n   --\x3e</a>")}}]),r}(Rt);i(Ue,"ELEMENT_CLASS","dialog_info");var Ge=Math.random,qe=function(t){try{return new X(t),!0}catch(t){return!1}},Ke=function(){return"#".concat(Ge().toString(16).slice(2,8))},$e=function(t,e){return(e||document).querySelector(t)},ze=function(e){a(o,e);var r=d(o);function o(e){var n;return t(this,o),i(l(n=r.call(this,e)),"pickr",null),i(l(n),"colorInput",null),i(l(n),"infoBtn",null),i(l(n),"rangeInput",null),i(l(n),"randomColorBtn",null),i(l(n),"dialog",null),n.pickr=tt.create({el:$e("[data-btn-color-picker]",n.el),default:n.color,theme:"nano",useAsButton:!0,comparison:!1,position:"bottom-end",components:{hue:!0,opacity:!0}}),n.appendChild(new je({name:"colorInput",el:$e("[data-color-input]",n.el),useNativeValidation:!1})),n.appendChild(new xt({name:"infoBtn",el:$e("button[data-btn-info]",n.el)})),n.appendChild(new Ot({name:"colorPickerBtn",el:$e("button[data-btn-color-picker]",n.el)})),n.appendChild(new je({name:"rangeInput",el:$e("[data-percent-input]",n.el)})),n.appendChild(new xt({name:"randomColorBtn",el:$e("button[data-btn-random]",n.el)})),n.appendChild(new Ue({name:"dialog",title:"About"})).render(document.body),n.colorInput.value=n.color,n.rangeInput.value=n.percentage,n._bindEvents(),n}return n(o,[{key:"update",value:function(t){return this.color=t,this.pickr.setColor(t,!0),this.colorInput.value=t,this}},{key:"_bindEvents",value:function(){var t;return this.infoBtn.el.addEventListener("click",(t=this.dialog).open.bind(t)),this.colorInput.input.addEventListener("change",this._handleColorInputChange.bind(this)),this.rangeInput.input.addEventListener("change",this._handleRangeInputChange.bind(this)),this.randomColorBtn.el.addEventListener("click",this._handleRandomColorButtonClick.bind(this)),this.pickr.on("show",(function(t){return t.getRoot().palette.palette.focus()})),this.pickr.on("hide",(function(t){return t.getRoot().button.focus()})),this.pickr.on("changestop",this._handlePickrChangeStop.bind(this)),this}},{key:"_handleColorInputChange",value:function(t){var e=t.target.value;if(!1===qe(e))return this.colorInput.setError();this.pickr.setColor(e,!0),this.colorInput.setError(!1),this.dispatch("colorchange",{color:e})}},{key:"_handleRangeInputChange",value:function(t){var e,n=t.target;if(!n.validity.valid)return this.rangeInput.setError();this.rangeInput.setError(!1),this.dispatch("percentagechange",{percentage:(e=n.value,Number(e))})}},{key:"_handleRandomColorButtonClick",value:function(){var t=Ke();this.pickr.setColor(t,!0),this.colorInput.value=t,this.dispatch("colorchange",{color:t})}},{key:"_handlePickrChangeStop",value:function(t){var e=t.getColor().toHEXA().toString();this.colorInput.setError(!1).value=e,this.dispatch("colorchange",{color:e})}}]),o}(ot);i(ze,"defaults",{color:"#000",percentage:20});const We=(t,{target:e=document.body}={})=>{const n=document.createElement("textarea"),i=document.activeElement;n.value=t,n.setAttribute("readonly",""),n.style.contain="strict",n.style.position="absolute",n.style.left="-9999px",n.style.fontSize="12pt";const r=document.getSelection();let o=!1;r.rangeCount>0&&(o=r.getRangeAt(0)),e.append(n),n.select(),n.selectionStart=0,n.selectionEnd=t.length;let a=!1;try{a=document.execCommand("copy")}catch(t){}return n.remove(),o&&(r.removeAllRanges(),r.addRange(o)),i&&i.focus(),a};var Xe=We,Ye=We;Xe.default=Ye;var Ze=function(e){a(r,e);var i=d(r);function r(){return t(this,r),i.apply(this,arguments)}return n(r,[{key:"template",value:function(){return"<p></p>"}},{key:"setText",value:function(t){return this.el.textContent=t,this}}]),r}(ot),Je=function(e){a(o,e);var r=d(o);function o(e){var n,a;return t(this,o),i(l(a=r.call(this,e)),"color",null),a.copyButton.el.addEventListener("click",(n=a)._copyClickHandler.bind(n)),a}return n(o,[{key:"template",value:function(){return"\n    <div class='color-item'>\n      <div>\n        <div class='item_percent flex items-center'>\n          ".concat(this.h(ie,{className:"icon-shade",width:12,height:12}),"\n          ").concat(this.h(ae,{className:"icon-tint",width:12,height:12}),"\n          ").concat(this.h(Ze,{name:"ariaLabelPercentage",className:"sr-only",attr:{style:"margin: 0"}}),"\n          ").concat(this.h(Ze,{name:"percentageLabel",className:"percentage--label"}),"\n        </div>\n        ").concat(this.h(Ze,{name:"hexLabel",className:"hex--label"}),"\n      </div>\n      <div class='color-item__copy-btn'>\n        ").concat(this.h(xt,{name:"copyButton",icon:re,label:"copy"}),"\n      </div>\n    </div>\n  ")}}]),n(o,[{key:"_copyClickHandler",value:function(){!1!==Xe(this.color)&&this.appendChild(new se({labelText:"“".concat(this.color,"” copied to clipboard")})).render(document.body).open()}},{key:"update",value:function(t){var e,n,i,r,o,a=this.constructor.CLASSNAMES,s=a.LIGHT,c=a.DARK;return this.color=t.hexString(),this.ariaLabelPercentage.setText(t.type),this.percentageLabel.setText("".concat((e=null!==(n=null===(i=t.weight)||void 0===i?void 0:i.toFixed(2))&&void 0!==n?n:0,Number(e)),"%")),this.hexLabel.setText(this.color),this.element.style.backgroundColor=this.color,(r=this.element.classList).remove.apply(r,h(Object.values(this.constructor.CLASSNAMES))),(o=this.element.classList).add.apply(o,h(["-is-".concat(t.type),t.getBrightness()>55?s:c].filter((function(t){return t})))),this}},{key:"activate",value:function(){return this.el.parentElement||this.render(this.parent.el),this}},{key:"deactivate",value:function(){return this.el.parentElement?(this.el.remove(),this):this}}]),o}(ot);i(Je,"CLASSNAMES",{BASE:"-is-base",TINT:"-is-tint",SHADE:"-is-shade",LIGHT:"-is-light",DARK:"-is-dark"});var Qe=function(e){a(r,e);var i=d(r);function r(){return t(this,r),i.apply(this,arguments)}return n(r,[{key:"template",value:function(){var t=this;return"\n    <div class='colors-collection'>\n      ".concat(Array(this.constructor.totalElements).fill("").map((function(){return t.h(Je)})).join(""),"\n    </div>\n    ")}},{key:"update",value:function(t){for(var e,n,i=0;i<this.constructor.totalElements;i++)e=t[i],n=this.children[i],e?n.activate().update(e):n.deactivate();return this}}]),r}(ot);i(Qe,"totalElements",201);var tn=function(e){a(o,e);var r=d(o);function o(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t(this,o),i(l(e=r.call(this)),"header",null),i(l(e),"colorsCollection",null),i(l(e),"_hash",location.hash),i(l(e),"_values",new X),Object.assign(l(e),e.constructor.defaults,n),e}return n(o,[{key:"run",value:function(){return this.appendChild(new ze({name:"header",el:$e("header"),color:this.color,percentage:this.percentage})),this.appendChild(new Qe({name:"colorsCollection"})).render($e("main")),this._bindEvents()._updateUI(this.color),this}},{key:"_bindEvents",value:function(){var t=this;return this.header.bind("colorchange",(function(e){var n=e.color;return t._updateUI(n)})),this.header.bind("percentagechange",this._percentchangeHandler.bind(this)),self.addEventListener("hashchange",this._hashChangeHandler.bind(this)),this}},{key:"_hashChangeHandler",value:function(){var t=location.hash;this._hash!==t&&qe(t)&&(this.header.update(t),this._updateUI(t))}},{key:"_percentchangeHandler",value:function(t){var e=t.percentage;this.percentage=e,this._updateUI(this._values.hexString())}},{key:"_updateUI",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color,e=this._values.setColor(t).all(this.percentage);this._hash=location.hash="#".concat(this._values.hex),this.colorsCollection.update(e)}}]),o}(J);i(tn,"defaults",{color:"#6200ee",percentage:3});var en=location.hash,nn=qe(en)?en:Ke();self.Shadowlord=new tn({color:nn,percentage:10}).run()}));