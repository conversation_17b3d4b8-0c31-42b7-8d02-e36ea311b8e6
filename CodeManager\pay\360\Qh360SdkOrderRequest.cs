﻿using System.Runtime.InteropServices;

namespace Account.Web.pay._360
{
    /// <summary>
    /// 创建订单结果通知函数
    /// </summary>
    /// <param name="dwTicket">SDK360_Pay或SDK360_AsyncPay的返回值</param>
    /// <param name="fpOderResponse">创建订单的详细信息</param>
    public delegate void SDK360_ORDERRESULT_CALLBACK(uint dwTicket, ref OderResponse fpOderResponse);

    /// <summary>
    /// 订单支付状态通知回调函数
    /// </summary>
    /// <param name="dwTicket">SDK360_Pay或SDK360_AsyncPay的返回值</param>
    /// <param name="iOderStatus">订单状态 10 -待付款(初始状态) 20-付款完成（待通知厂商） 30-待厂商发权益（已通知厂商） 40-售后中（厂商发起退款） 50-交易完成（正常完成，厂商完成物品发放） 60-已取消（支付超时，过期等原因） 70-交易关闭（退款完成）</param>
    /// <param name="iPayChanel">支付渠道，1-微信，2-支付宝</param>
    public delegate void SDK360_PAYSTATUS_CALLBACK(uint dwTicket, int iOderStatus, int iPayChanel);

    /// <summary>
    /// 订单请求参数
    /// </summary>
    public class Qh360SdkOrderRequest
    {
        /// <summary>
        /// * 最大长度40
        /// 厂商订单 id，厂商需要保证在同一应用下订单 id 的唯一性
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// * 订单金额，单位“分”
        /// </summary>
        public uint Amount { get; set; }

        /// <summary>
        /// * 厂商创建该订单的时间戳（秒），后期对账使用
        /// </summary>
        public long OrderCreateTime { get; set; }

        /// <summary>
        /// 最大长度40
        /// 用户 ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// * 最大长度40
        /// 商品 id，商品在厂商方的标识，后期对账使用，由厂商提供
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// * 最大长度400
        /// 商品描述，后期对账使用
        /// </summary>
        public string ProductDescription { get; set; }

        /// <summary>
        /// 最大长度128
        /// 保留字段
        /// </summary>
        public string Attach { get; set; }

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint DeductionType { get; set; }

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint DeductionAmount { get; set; }

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint DeductionPeriod { get; set; }

        /// <summary>
        /// 保留字段
        /// </summary>
        public long FirstDeductionTime { get; set; }

        /// <summary>
        /// 最大长度1024
        /// 保留字段
        /// </summary>
        public string ContractNotify { get; set; }

        internal void CopyFromOderResponse(OderRequest oderRequest)
        {
            Amount = oderRequest.dwAmount;
            DeductionAmount = oderRequest.dwDeductionAmount;
            Attach = oderRequest.wszAttach;
            DeductionPeriod = oderRequest.dwDeductionPeriod;
            DeductionType = oderRequest.dwDeductionType;
            FirstDeductionTime = oderRequest.tFirstDeductionTime;
            OrderCreateTime = oderRequest.tOrderCreateTime;
            ContractNotify = oderRequest.wszContractNotify;
            OrderId = oderRequest.wszOrderId;
            ProductDescription = oderRequest.wszProductDescription;
            ProductId = oderRequest.wszProductId;
            UserId = oderRequest.wszUserId;
        }

        internal OderRequest ToOderRequest()
        {
            OderRequest result = default;
            result.dwSize = (uint)Marshal.SizeOf(typeof(OderRequest));
            result.dwAmount = Amount;
            result.dwDeductionAmount = DeductionAmount;
            result.wszAttach = Attach;
            result.dwDeductionPeriod = DeductionPeriod;
            result.dwDeductionType = DeductionType;
            result.tFirstDeductionTime = FirstDeductionTime;
            result.tOrderCreateTime = OrderCreateTime;
            result.wszContractNotify = ContractNotify;
            result.wszOrderId = OrderId;
            result.wszProductDescription = ProductDescription;
            result.wszProductId = ProductId;
            result.wszUserId = UserId;
            return result;
        }
    }

    /// <summary>
    /// 订单返回的内容
    /// </summary>
    public class Qh360SdkOrderResponse
    {
        /// <summary>
        /// * SDK360_Pay或SDK360_AsyncPay的返回值
        /// </summary>
        public uint Ticket { get; set; }

        /// <summary>
        /// * 订单的生成状态，0:生成订单正确: 非 0，生成订单错误
        /// </summary>
        public uint Errno { get; set; }

        /// <summary>
        /// * 订单生成状态的详细描述
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        /// 若订单生成成功，该变量保存订单支付链接，用于生成二维码
        /// </summary>
        public string Qrcode { get; set; }

        /// <summary>
        /// 若订单生成成功，该变量保存订单金额，与请求金额一致
        /// </summary>
        public uint Amount { get; set; }

        /// <summary>
        /// 若订单生成成功，该变量保存订单失效的时间戳
        /// </summary>
        public long ExpireTime { get; set; }

        /// <summary>
        /// 若订单生成成功，该变量保存订单有效时长，单位“秒”。若担心客户端本地时间不准确，可使用该字段记录二维码有效期
        /// </summary>
        public uint Live { get; set; }

        internal void CopyFromOderResponse(OderResponse oderResponse)
        {
            Amount = oderResponse.dwAmount;
            Live = oderResponse.dwLive;
            Ticket = oderResponse.dwTicket;
            Errno = oderResponse.nErrno;
            ExpireTime = oderResponse.tExpireTime;
            Msg = oderResponse.wszMsg;
            Qrcode = oderResponse.wszQrCode;
        }

        internal OderResponse ToOderResponse()
        {
            OderResponse result = default(OderResponse);
            result.dwSize = (uint)Marshal.SizeOf(typeof(OderResponse));
            result.dwAmount = Amount;
            result.dwLive = Live;
            result.dwTicket = Ticket;
            result.nErrno = Errno;
            result.tExpireTime = ExpireTime;
            result.wszMsg = Msg;
            result.wszQrCode = Qrcode;
            return result;
        }
    }

    /// <summary>
    /// 创建支付订单需要厂商提供的相关信息
    /// </summary>
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode, Pack = 1)]
    public struct OderRequest
    {
        /// <summary>
        /// *
        /// </summary>
        public uint dwSize;

        /// <summary>
        /// * 厂商订单 id，厂商需要保证在同一应用下订单 id 的唯一性
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 40)]
        public string wszOrderId;

        /// <summary>
        /// * 订单金额，单位“分”
        /// </summary>
        public uint dwAmount;

        /// <summary>
        /// * 厂商创建该订单的时间戳，后期对账使用
        /// </summary>
        public long tOrderCreateTime;

        /// <summary>
        /// 用户 ID
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 40)]
        public string wszUserId;

        /// <summary>
        /// * 商品 id，商品在厂商方的标识，后期对账使用，由厂商提供
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 40)]
        public string wszProductId;

        /// <summary>
        /// * 商品描述，后期对账使用
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 400)]
        public string wszProductDescription;

        /// <summary>
        /// 保留字段
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
        public string wszAttach;

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint dwDeductionType;

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint dwDeductionAmount;

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint dwDeductionPeriod;

        /// <summary>
        /// 保留字段
        /// </summary>
        public long tFirstDeductionTime;

        /// <summary>
        /// 保留字段
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1024)]
        public string wszContractNotify;
    }

    /// <summary>
    /// 创建订单返回的结果
    /// </summary>
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    public struct OderResponse
    {
        /// <summary>
        /// * 
        /// </summary>
        public uint dwSize;

        /// <summary>
        /// * SDK360_Pay或SDK360_AsyncPay的返回值
        /// </summary>
        public uint dwTicket;

        /// <summary>
        /// * 订单的生成状态，0:生成订单正确: 非 0，生成订单错误
        /// </summary>
        public uint nErrno;

        /// <summary>
        /// * 订单生成状态的详细描述
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
        public string wszMsg;

        /// <summary>
        /// 若订单生成成功，该变量保存订单支付链接，用于生成二维码
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 2048)]
        public string wszQrCode;

        /// <summary>
        /// 若订单生成成功，该变量保存订单金额，与请求金额一致
        /// </summary>
        public uint dwAmount;

        /// <summary>
        /// 若订单生成成功，该变量保存订单失效的时间戳
        /// </summary>
        public long tExpireTime;

        /// <summary>
        /// 若订单生成成功，该变量保存订单有效时长，单位“秒”。若担心客户端本地时间不准确，可使用该字段记录二维码有效期
        /// </summary>
        public uint dwLive;
    }
}
