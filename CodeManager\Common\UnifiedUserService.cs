using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Account.Web.Common
{
    /// <summary>
    /// 统一的用户操作服务 - 供客户端和网页端共同使用
    /// </summary>
    public static class UnifiedUserService
    {
        #region 用户注册

        /// <summary>
        /// 统一的用户注册逻辑
        /// </summary>
        public static UserOperationResult RegisterUser(string account, string password, string nickname, string verifyCode, string lang = "")
        {
            try
            {
                // 1. 使用统一的参数验证
                var validation = ValidationService.ValidateRegisterParams(account, password, verifyCode, nickname, lang);
                if (!validation.IsValid)
                    return UserOperationResult.Error(validation.ErrorMessage);

                // 2. 检查用户是否已存在
                if (CodeHelper.IsExitsCode(account))
                    return UserOperationResult.Error(UserConst.StrHasReged.GetTrans(lang));

                // 3. 验证注册验证码
                var verifyValidation = ValidationService.ValidateRegisterVerifyCode(account, verifyCode, lang);
                if (!verifyValidation.IsValid)
                    return UserOperationResult.Error(verifyValidation.ErrorMessage);

                // 4. 处理默认昵称
                if (string.IsNullOrEmpty(nickname))
                    nickname = account;

                // 5. 创建用户实体（使用统一的密码加密）
                var code = new CodeEntity
                {
                    StrAppCode = account,
                    StrNickName = nickname,
                    StrPwd = PasswordService.EncryptPassword(password),
                    DtReg = ServerTime.LocalTime
                };

                // 6. 分配用户类型和到期时间
                var isEmail = BoxUtil.IsEmail(account);
                AssignUserTypeAndExpiration(code, isEmail);

                // 7. 保存用户
                if (CodeHelper.AddOrUpdateCode(code))
                {
                    // 清理缓存
                    RdsCacheHelper.LstAccountCache.Remove(code.StrAppCode);
                    return UserOperationResult.Success("注册成功");
                }
                else
                {
                    return UserOperationResult.Error(UserConst.StrServerError.GetTrans(lang));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("统一用户注册异常", ex);
                return UserOperationResult.Error("注册失败，请重试");
            }
        }

        /// <summary>
        /// 分配用户类型和到期时间（与现有逻辑完全一致）
        /// </summary>
        private static void AssignUserTypeAndExpiration(CodeEntity code, bool isEmail)
        {
            if (isEmail && DisposeEmailHelper.IsDisposeEmail(code.StrAppCode))
            {
                LogHelper.Log.Info($"临时邮箱:{JsonConvert.SerializeObject(code)}");
                code.StrType = UserTypeEnum.体验版.ToString();
                code.DtExpire = code.DtReg;
            }
            else
            {
                if (ConfigHelper.IsRegToGeRen)
                {
                    code.StrType = UserTypeEnum.个人版.ToString();
                    code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                }
                else if (ConfigHelper.IsRegToProfessional)
                {
                    code.StrType = UserTypeEnum.专业版.ToString();
                    code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                }
                else
                {
                    code.StrType = UserTypeEnum.体验版.ToString();
                    code.DtExpire = code.DtReg;
                }
            }
        }

        #endregion

        #region 用户登录

        /// <summary>
        /// 统一的用户登录逻辑
        /// </summary>
        public static UserLoginResult LoginUser(string account, string password, string uid, bool isWeb = false, string lang = "")
        {
            try
            {
                // 1. 基础参数验证（使用统一的ValidationService）
                var validation = ValidationService.ValidateLoginParams(account, password, lang);
                if (!validation.IsValid)
                    return UserLoginResult.Error(validation.ErrorMessage);

                // 2. 获取用户信息
                var user = CodeHelper.GetCodeByAccountId(account);
                if (user == null || string.IsNullOrEmpty(user.StrAppCode))
                    return UserLoginResult.Error(UserConst.StrAccountNotExsits.GetTrans(lang));

                // 3. 密码验证（使用统一的PasswordService）
                if (!PasswordService.VerifyPassword(password, user.StrPwd))
                    return UserLoginResult.Error("用户名或密码错误！");

                // 4. 设备相关检查（客户端和网页端都需要，但网页端uid是随机生成的）
                if (!isWeb)
                {
                    // 客户端：检查设备是否被禁用
                    if (CodeHelper.CheckIsForbid(account, uid))
                        return UserLoginResult.Error("当前设备已经被禁用！");

                    // 客户端：设备限制检查（非体验版用户）
                    if (!Equals(user.StrType, UserTypeEnum.体验版.ToString())
                         && string.IsNullOrEmpty(user.StrRemark)
                         && !CodeHelper.IsCanReg(uid, ""))
                    {
                        LogHelper.Log.Error("封号：" + user.StrAppCode);
                        user.DtExpire = user.DtReg;
                    }
                }

                // 5. 创建登录信息
                var loginInfo = CreateLoginInfo(user, uid, isWeb);

                // 6. 处理到期用户的自动降级
                HandleExpiredUser(user, loginInfo);

                // 7. 管理Token（客户端和网页端逻辑不同）
                ManageUserTokens(loginInfo, uid, isWeb);

                // 8. 保存登录信息
                RdsCacheHelper.LstAccountCache.InsertOrUpdateUser(loginInfo);
                CodeHelper.UpdateLastLogin(new List<string> { loginInfo.Account });

                return UserLoginResult.Success(loginInfo);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("统一用户登录异常", ex);
                return UserLoginResult.Error("登录失败，请重试");
            }
        }

        /// <summary>
        /// 创建登录信息（客户端包含完整功能权限，网页端包含基础信息）
        /// </summary>
        private static UserLoginInfo CreateLoginInfo(CodeEntity user, string uid, bool isWeb)
        {
            var loginInfo = new UserLoginInfo
            {
                Account = user.StrAppCode,
                NickName = user.StrNickName,
                UserType = (UserTypeEnum)Enum.Parse(typeof(UserTypeEnum), user.StrType),
                Remark = user.StrRemark,
                DtExpired = user.DtExpire,
                DtReg = user.DtReg,
                DtLogin = ServerTime.LocalTime,
                LstToken = RdsCacheHelper.LstAccountCache.GetUserInfo(user.StrAppCode)?.LstToken
            };

            if (loginInfo.LstToken == null)
                loginInfo.LstToken = new List<TokenEntity>();

            // 客户端需要完整的功能权限信息，网页端暂时不需要但也设置（为将来扩展做准备）
            var userTypeInfo = UserTypeHelper.GetUserInfo(loginInfo.UserType);
            loginInfo.UserTypeName = loginInfo.UserType.ToString();
            loginInfo.IsSetOtherResult = userTypeInfo.IsSetOtherResult;
            loginInfo.IsSupportBatch = userTypeInfo.IsSupportBatch;
            loginInfo.IsSupportMath = userTypeInfo.IsSupportMath;
            loginInfo.IsSupportImageFile = userTypeInfo.IsSupportImageFile;
            loginInfo.IsSupportDocFile = userTypeInfo.IsSupportDocFile;
            loginInfo.IsSupportTable = userTypeInfo.IsSupportTable;
            loginInfo.IsSupportTxt = userTypeInfo.IsSupportTxt;
            loginInfo.IsSupportVertical = userTypeInfo.IsSupportVertical;
            loginInfo.IsSupportTranslate = userTypeInfo.IsSupportTranslate;
            loginInfo.MaxUploadSize = userTypeInfo.MaxUploadSize;
            loginInfo.PerTimeSpanExecCount = userTypeInfo.PerTimeSpanExecCount;
            loginInfo.PerTimeSpan = userTypeInfo.PerTimeSpan;
            loginInfo.LimitPerDayCount = userTypeInfo.LimitPerDayCount;
            loginInfo.IsSupportPassage = userTypeInfo.IsSupportPassage;
            loginInfo.MaxPassageCount = userTypeInfo.MaxPassageCount;
            loginInfo.IsSupportLocalOcr = userTypeInfo.IsSupportLocalOcr;

            return loginInfo;
        }

        /// <summary>
        /// 处理到期用户的自动降级
        /// </summary>
        private static void HandleExpiredUser(CodeEntity user, UserLoginInfo loginInfo)
        {
            if (user.IsExpired)
            {
                // 其他版本(非个人版)到期，自动降级为个人版
                if (!string.IsNullOrEmpty(user.StrRemark) && user.StrRemark.Contains("版") &&
                    !user.StrRemark.Contains(UserTypeEnum.个人版.ToString()))
                {
                    var code = new CodeEntity
                    {
                        StrAppCode = user.StrAppCode,
                        DtExpire = ServerTime.DateTime.AddYears(100),
                        StrType = UserTypeEnum.个人版.ToString()
                    };
                    CodeHelper.AddOrUpdateCode(code);

                    loginInfo.DtExpired = code.DtExpire;
                    loginInfo.UserType = UserTypeEnum.个人版;
                }
                else
                {
                    // 否则，降级为体验版
                    loginInfo.UserType = UserTypeEnum.体验版;
                    if (!Equals(user.StrType, loginInfo.UserType.ToString()))
                    {
                        var code = new CodeEntity
                        {
                            StrAppCode = user.StrAppCode,
                            StrType = loginInfo.UserType.ToString()
                        };
                        CodeHelper.AddOrUpdateCode(code);
                    }
                }
            }
        }

        /// <summary>
        /// 管理用户Token（客户端和网页端逻辑不同）
        /// </summary>
        private static void ManageUserTokens(UserLoginInfo loginInfo, string uid, bool isWeb)
        {
            if (isWeb)
            {
                // 网页端：简单的Token管理，不限制数量（因为uid是随机生成的）
                var token = new TokenEntity
                {
                    Token = uid,
                    DtExpired = ServerTime.LocalTime.AddHours(1)
                };
                loginInfo.Token = token.Token;
                loginInfo.LstToken.Add(token);
            }
            else
            {
                // 客户端：完整的设备Token管理
                // 移除相同设备的旧Token
                loginInfo.LstToken.RemoveAll(p => Equals(p.Token, uid));

                // 限制Token数量（根据用户类型）
                var userTypeInfo = UserTypeHelper.GetUserInfo(loginInfo.UserType);
                while (loginInfo.LstToken.Count >= userTypeInfo.MaxLoginCount)
                {
                    loginInfo.LstToken = loginInfo.LstToken.OrderBy(p => p.DtExpired).ToList();
                    loginInfo.LstToken.RemoveAt(0);
                }

                // 添加新Token
                var token = new TokenEntity
                {
                    Token = uid,
                    DtExpired = ServerTime.LocalTime.AddHours(1)
                };
                loginInfo.Token = token.Token;
                loginInfo.LstToken.Add(token);
            }
        }

        #endregion

        #region 密码重置

        /// <summary>
        /// 统一的密码重置逻辑
        /// </summary>
        public static UserOperationResult ResetPassword(string account, string password, string verifyCode, string lang = "")
        {
            try
            {
                // 1. 使用统一的参数验证
                var validation = ValidationService.ValidatePasswordResetParams(account, password, verifyCode, lang);
                if (!validation.IsValid)
                    return UserOperationResult.Error(validation.ErrorMessage);

                // 2. 检查用户是否存在
                if (!CodeHelper.IsExitsCode(account))
                    return UserOperationResult.Error(UserConst.StrAccountNotExsits.GetTrans(lang));

                // 3. 验证重置验证码
                var verifyValidation = ValidationService.ValidatePasswordResetVerifyCode(account, verifyCode, lang);
                if (!verifyValidation.IsValid)
                    return UserOperationResult.Error(verifyValidation.ErrorMessage);

                // 4. 使用统一的密码更新服务
                if (PasswordService.UpdateUserPassword(account, password))
                {
                    return UserOperationResult.Success("密码重置成功");
                }
                else
                {
                    return UserOperationResult.Error(UserConst.StrServerError.GetTrans(lang));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("统一密码重置异常", ex);
                return UserOperationResult.Error("密码重置失败，请重试");
            }
        }

        #endregion

        #region 昵称修改

        /// <summary>
        /// 统一的昵称修改逻辑
        /// </summary>
        public static UserOperationResult UpdateNickname(string account, string nickname, string lang = "")
        {
            try
            {
                // 1. 基础参数验证
                if (string.IsNullOrEmpty(account))
                    return UserOperationResult.Error("账号不能为空");

                // 2. 检查用户是否存在
                if (!CodeHelper.IsExitsCode(account))
                    return UserOperationResult.Error(UserConst.StrAccountNotExsits.GetTrans(lang));

                // 3. 昵称格式验证（使用统一的AuthHelper验证）
                if (!AuthHelper.IsValidNickName(nickname))
                    return UserOperationResult.Error(UserConst.StrNickNameFormatError.GetTrans(lang));

                // 4. 更新昵称
                if (CodeHelper.UpdateCodeNickName(account, nickname))
                {
                    // 5. 同步更新缓存中的用户信息
                    var userInfo = RdsCacheHelper.LstAccountCache.GetUserInfo(account);
                    if (userInfo != null)
                    {
                        userInfo.NickName = nickname;
                        RdsCacheHelper.LstAccountCache.InsertOrUpdateUser(userInfo);
                    }

                    return UserOperationResult.Success("昵称修改成功");
                }
                else
                {
                    return UserOperationResult.Error(UserConst.StrServerError.GetTrans(lang));
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("统一昵称修改异常", ex);
                return UserOperationResult.Error("昵称修改失败，请重试");
            }
        }

        #endregion
    }

    /// <summary>
    /// 用户操作结果
    /// </summary>
    public class UserOperationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }

        public static UserOperationResult Success(string message = "")
        {
            return new UserOperationResult { IsSuccess = true, Message = message };
        }

        public static UserOperationResult Error(string message)
        {
            return new UserOperationResult { IsSuccess = false, Message = message };
        }
    }

    /// <summary>
    /// 用户登录结果
    /// </summary>
    public class UserLoginResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public UserLoginInfo Data { get; set; }

        public static UserLoginResult Success(UserLoginInfo data, string message = "")
        {
            return new UserLoginResult { IsSuccess = true, Data = data, Message = message };
        }

        public static UserLoginResult Error(string message)
        {
            return new UserLoginResult { IsSuccess = false, Message = message };
        }
    }
}
