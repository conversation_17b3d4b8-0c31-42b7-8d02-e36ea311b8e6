(function(a,b){function q(){var a=r(),b=a.src;return alert(b),j[b]}function r(){if(l)return l;if(m&&m.readyState==="interactive")return m;var a=n.getElementsByTagName("script");for(var b=0;b<a.length;b++){var c=a[b];if(c.readyState==="interactive")return m=c,c}}function s(a){return Object.prototype.toString.call(a)=="[object Array]"}function t(a){for(var b in a)"defer"==b?g=a[b]:c[b]=a[b]}function u(a,b){var d,f=v(a),l=B(f);if(l==p.FILE_LOADED){b&&(e[f]||(e[f]=[]),e[f].push(b));return}if(l==p.MODULE_LOADED){var m=A(f);b&&b.call(m,m);return}var n=c.modulebase,o="",q=a.indexOf(":");q>0&&(o=a.substr(0,q),a=a.substr(q+1),c.nsmap&&c.nsmap.hasOwnProperty(o)?n="http://"+c.nsmap[o].host:a=o+"/static/js/"+a),g?i.hasOwnProperty(a)||(i[a]=!0,h.push(a)):(d=n+a+".js?"+c.sversion,j[d]=f,D(d,function(){if(!k)return;k.id=f,k=null})),b&&(e[f]||(e[f]=[]),e[f].push(b))}function v(a){return a?a.replace("/static/js/",":"):""}function w(a){return A(a)}function x(a,b,d){function h(){a||f?g():window.setTimeout(g,0)}switch(arguments.length){case 3:s(b)||(b=b?[b]:[]);break;case 2:d=b,"string"==typeof a?b=y(d):(b=a,a="");break;case 1:"string"==typeof a?(b=[],d=new Function):(d=a,a="",b=y(d))}a=v(a),z(a,{tag:p.FILE_LOADED});if(!a&&document.attachEvent&&!~o.indexOf("Opera"))var f=q();var g=function(){a||(a=f||g.id);if(B(a)==p.MODULE_LOADED)return;var b={},c=d(w,b);c&&(b=c),C(a,b)};!a&&!f&&(k=g);if(b.length){var i=0,j=b.length;for(var l=0;l<j;l++){var m=b[l];if(B(m)==p.MODULE_LOADED){i++;continue}if("~"==m.substr(0,1)){var n=m.substr(1);"/"!=n.substr(0,1)&&"http://"!=n.substr(0,7)&&(n=c.modulebase+n),D(n,function(){C(m,!0)})}else u(m,null);e[m]||(e[m]=[]),e[m].push(function(a){return function(){for(var b=0,c=a.length;b<c;b++)if(B(a[b])!=p.MODULE_LOADED)return;g()}}(b))}i>=j&&h()}else h()}function y(a){depencies=[];var b=/require\((['"])([\w\:\.\/\_\-]+)\1\)/g,c=a.toString().replace(/\/\*.*\*\//m,"").replace(/\/\/.*\n/g,""),d;while(d=b.exec(c))d=d[2],depencies.push(d);return depencies}function z(a,b){d[a]=b}function A(a){return d[a]?d[a].mod:null}function B(a){return d[a]?d[a].tag:p.FILE_UNLOAD}function C(a,b){z(a,{tag:p.MODULE_LOADED,mod:b});var c=e[a];if(c){e[a]=[];for(var d=0,f=c.length;d<f;d++)c[d].call(b,b)}}function D(a,b){var c,d=function(b){f[a].state="loaded";var c;while(c=f[a].onload.shift())c()};if(!f.hasOwnProperty(a))f[a]={state:"loading",onload:[]},b&&f[a].onload.push(b),E(a,d);else if(b){c=f[a];switch(c.state){case"loading":f[a].onload.push(b);break;case"loaded":b()}}}function E(a,b,c){var d=document.createElement("script");d.defer="true",d.async="async",d.type="text/javascript",b&&(d.onerror=d.onload=d.onreadystatechange=function(){var a=this.readyState;if(!a||"loaded"==a||"complete"==a)b(),n.removeChild(d)}),d.src=a,l=d,n.appendChild(d),l=null}function F(a,b){var c=document.createElement("link");c.setAttribute("rel","stylesheet"),c.setAttribute("rev","stylesheet"),c.setAttribute("href",a),n.appendChild(c);var d=document.createElement("img");d.onerror=function(){b&&b()},d.src=a}function G(a,b){var c=a.length,d=0,e={};for(var f=0;f<c;f++){var g=a[f];u(g,function(a){return function(f){d++,e[a]=f,d>=c&&b.call(e,e)}}(g))}}function I(a,b){a in H||(H[a]=[]),b&&typeof b=="function"&&H[a].push(b)}function J(a,b,c){if(a in H){var d=H[a];if(!d||!d.length)return;for(var e=0,f=d.length;e<f;e++)d[e](b);return c&&delete H[a],!0}return}window.console||(window.console={log:function(){}});var c={sversion:1,deferHost:"/",localNS:"",modulebase:"/"},d={},e={},f={},g=!1,h=[],i={},j={},k,l,m,n=document.head||document.getElementsByTagName("head")[0]||document.documentElement,o=navigator.userAgent,p={FILE_UNLOAD:0,FILE_LOADED:1,MODULE_LOADED:2},H={};a.fml={version:1,vars:{},eventProxy:I,fireProxy:J,on:I,emit:J,loadCss:F,debug:function(){window.console&&window.console.log.apply(console,arguments)},getOption:function(a){return c[a]},setOptions:t,use:function(a,b){s(a)?G(a,b):u(a,b)},define:x,iLoad:function(){if(!g)return;g=!1;var a=h.length;for(var b=0;b<a;b++)u(h[b]);h=[],i={}},loadScript:D,alias:function(){return this}}})(this);