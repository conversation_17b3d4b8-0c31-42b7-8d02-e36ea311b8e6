﻿using CommonLib;
using RegLib;
using System;
using System.Security.Principal;
using System.Text;

namespace Account.Web
{
    public partial class RCode : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(BoxUtil.GetStringFromObject(Request.QueryString["pwd"]).Trim()))
            {
                Response.End();
                return;
            }
            if (!IsPostBack && (cmbType.DataSource == null || cmbType.SelectedIndex < 0))
            {
                cmbType.DataSource = Enum.GetValues(typeof(CommonLib.UserTypeEnum));
                cmbType.DataBind();
                //cmbType.SelectedIndex = 0;
            }
        }

        protected void btnOK_Click(object sender, EventArgs e)
        {
            if (txtPwd.Text.Trim() != "aa89128")
                return;

            if (string.IsNullOrEmpty(txtApp.Text.Trim()))
            {
                lblMsg.Text = ("账号不能为空！");
                return;
            }
            if (string.IsNullOrEmpty(cmbType.Text))
            {
                lblMsg.Text = ("类型不能为空！");
                return;
            }
            if (string.IsNullOrEmpty(txtDays.Text.Trim()))
            {
                lblMsg.Text = ("天数不能为空！");
                return;
            }
            try
            {
                var user = CodeHelper.GetCodeByAccountId(txtApp.Text.Trim());
                if (user == null || string.IsNullOrEmpty(user.StrAppCode))
                {
                    user = CodeEntity.GetNewEntity(txtApp.Text.Trim(), txtApp.Text.Trim());
                }
                user.StrType = cmbType.Text;
                user.DtExpire = ServerTime.LocalTime.AddDays(BoxUtil.GetDoubleFromObject(txtDays.Text.Trim()));
                user.StrRemark = txtMoney.Text;
                var result = CodeHelper.AddOrUpdateCode(user);
                if (result)
                {
                    CommonLib.RdsCacheHelper.LstAccountCache.Remove(user.StrAppCode);
                    lblMsg.Text = "注册成功！";
                    //DownLoadFile(entity);
                }
                else
                {
                    lblMsg.Text = ("注册失败！");
                }
            }
            catch (Exception oe)
            {
                lblMsg.Text = ("注册失败！" + oe.Message);
            }
        }

        protected void btnQuery_Click(object sender, EventArgs e)
        {
            if (txtPwd.Text.Trim() != "aa89128")
                return;
            var strNewKey = txtApp.Text.Trim();

            if (string.IsNullOrEmpty(strNewKey))
            {
                lblMsg.Text = ("账号不能为空！");
                return;
            }

            try
            {
                StringBuilder sb = new StringBuilder();

                var lstEntity = CodeHelper.GetCodeByApp(strNewKey);
                if (lstEntity != null && lstEntity.Count > 0)
                {
                    txtApp.Text = lstEntity[0].StrAppCode;
                    txtDays.Text = lstEntity[0].NLeftDays.ToString("F2");
                    txtMoney.Text = lstEntity[0].NMoney.ToString("F2");
                    cmbType.Text = lstEntity[0].StrType;

                    sb.Append("===================<br />");
                    foreach (var item in lstEntity)
                    {
                        sb.Append(item + "<br />===================");
                    }
                }

                if (sb.Length > 0)
                {
                    lblMsg.Text = ("注册信息：<br />" + sb.ToString() + "<br />");
                }
                else
                {
                    lblMsg.Text = ("查询失败！");
                }
            }
            catch (Exception oe)
            {
                lblMsg.Text = ("查询失败！" + oe.Message);
            }
        }

        protected void btnForbid_Click(object sender, EventArgs e)
        {
            if (txtPwd.Text.Trim() != "aa89128")
                return;

            if (string.IsNullOrEmpty(txtApp.Text.Trim()))
            {
                lblMsg.Text = ("账号不能为空！");
                return;
            }
            var strMacCode = txtApp.Text.Trim();

            try
            {
                if (CommonReg.FobidUser(strMacCode))
                {
                    lblMsg.Text = ("【Success】");
                }
                else
                {
                    lblMsg.Text = ("封账号失败！");
                }
            }
            catch (Exception oe)
            {
                lblMsg.Text = ("封账号失败！" + oe.Message);
            }
        }
    }
}