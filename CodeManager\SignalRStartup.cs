﻿using CommonLib;
using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Messaging;
using Microsoft.Owin;
using Owin;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

[assembly: OwinStartup(typeof(Account.Web.SignalRStartup))]
namespace Account.Web
{
    public class SignalRStartup
    {
        public void Configuration(IAppBuilder app)
        {
            var hubConfiguration = new HubConfiguration
            {
                EnableDetailedErrors = true,
                EnableJavaScriptProxies = false
            };

            //// 优化高延迟网络配置
            //GlobalHost.Configuration.ConnectionTimeout = TimeSpan.FromMinutes(5);
            //GlobalHost.Configuration.DisconnectTimeout = TimeSpan.FromMinutes(10);
            //GlobalHost.Configuration.KeepAlive = TimeSpan.FromSeconds(30);

            // 配置传输设置
            GlobalHost.Configuration.DefaultMessageBufferSize = 2000;
            GlobalHost.Configuration.MaxIncomingWebSocketMessageSize = 64 * 1024;

            //GlobalHost.DependencyResolver.UseRedis(
            //    server: "127.0.0.1",
            //    port: 3600,
            //    password: "",
            //    eventKey: "SignalR_Key"
            //);

            // 将SignalR映射到port 80
            app.MapSignalR("/signalr", hubConfiguration);

            PrewarmSignalR();

            Task.Factory.StartNew(() =>
            {
                // 5分钟后再允许SignalR连接
                Task.Delay(300 * 1000);
                Global.IsSignalRReady = true;
            });
        }

        private void PrewarmSignalR()
        {
            try
            {
                // 1. 预热Hub类
                System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(OcrHub).TypeHandle);

                // 2. 预热SignalR核心组件
                var hubContext = GlobalHost.ConnectionManager.GetHubContext<OcrHub>();
                var hub = GlobalHost.DependencyResolver.Resolve<OcrHub>();

                //// 3. 预热Redis backplane连接
                //var connectionManager = GlobalHost.DependencyResolver.Resolve<IConnectionManager>();
                //var redisConnection = GlobalHost.DependencyResolver.Resolve<IRedisConnection>();

                // 4. 预热消息总线
                var messageBus = GlobalHost.DependencyResolver.Resolve<IMessageBus>();
                messageBus.Publish("prewarm", "prewarm", "prewarm");

                var html = WebClientSyncExt.GetHtml("https://ocr.oldfish.cn/signalr/negotiate", "", 5);

                // 记录预热完成
                Trace.WriteLine("SignalR预热完成: " + DateTime.Now);
            }
            catch (Exception ex)
            {
                Trace.WriteLine("SignalR预热失败: " + ex.Message);
            }
        }
    }
}