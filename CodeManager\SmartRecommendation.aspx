﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能版本推荐</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
body{margin:0;padding:0;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:white}
#smartRecommendationContainer{width:100%;height:100vh;display:flex;align-items:center;justify-content:center}
@keyframes shimmer{0%{left:-100%}100%{left:100%}}
@keyframes magicPulse{0%,100%{transform:scale(1) rotate(0deg);color:#007cfa}50%{transform:scale(1.1) rotate(5deg);color:#0056b3}}
    </style>
</head>
<body>
    <div id="smartRecommendationContainer">
    </div>
    <script>
        var recommendationQuiz = [
            {
                id: 'usage_scenario',
                question: "您主要在什么场景下使用OCR功能？",
                type: 'single',
                options: [
                    {
                        text: "个人学习、偶尔使用",
                        desc: "扫描笔记、提取图片文字等轻度使用",
                        weight: { personal: 6, professional: 1, flagship: 0 },
                        tags: ['个人用户', '低频使用']
                    },
                    {
                        text: "日常办公、经常使用",
                        desc: "处理工作文档、合同等中等频率使用",
                        weight: { personal: 2, professional: 6, flagship: 3 },
                        tags: ['办公用户', '中频使用']
                    },
                    {
                        text: "专业工作、高频使用",
                        desc: "大量文档处理、数据提取等专业场景",
                        weight: { personal: 0, professional: 4, flagship: 6 },
                        tags: ['专业用户', '高频使用']
                    },
                    {
                        text: "团队协作、批量处理",
                        desc: "多人使用、批量文档处理等企业级应用",
                        weight: { personal: 0, professional: 3, flagship: 7 },
                        tags: ['企业用户', '批量处理']
                    }
                ]
            },
            {
                id: 'daily_usage',
                question: "您预计每天需要识别多少次？",
                type: 'single',
                options: [
                    {
                        text: "20次以内",
                        desc: "轻度使用，偶尔需要",
                        weight: { personal: 4, professional: 1, flagship: 0 },
                        tags: ['轻度使用']
                    },
                    {
                        text: "20-100次",
                        desc: "中等使用频率",
                        weight: { personal: 3, professional: 4, flagship: 1 },
                        tags: ['中度使用']
                    },
                    {
                        text: "100-500次",
                        desc: "较高使用频率",
                        weight: { personal: 1, professional: 4, flagship: 3 },
                        tags: ['高频使用']
                    },
                    {
                        text: "500次以上",
                        desc: "超高频使用或批量处理",
                        weight: { personal: 0, professional: 2, flagship: 5 },
                        tags: ['超高频使用']
                    }
                ]
            },
            {
                id: 'feature_needs',
                question: "您最需要哪些功能？（可多选）",
                type: 'multiple',
                options: [
                    {
                        text: "基础文字识别",
                        desc: "图片转文字的基本功能",
                        weight: { personal: 2, professional: 2, flagship: 1 },
                        tags: ['基础功能']
                    },
                    {
                        text: "表格识别",
                        desc: "识别表格结构和数据",
                        weight: { personal: 0, professional: 3, flagship: 3 },
                        tags: ['高级功能']
                    },
                    {
                        text: "公式识别",
                        desc: "数学公式、化学式等专业内容",
                        weight: { personal: 1, professional: 3, flagship: 3 },
                        tags: ['专业功能']
                    },
                    {
                        text: "批量处理",
                        desc: "一次处理多个文件",
                        weight: { personal: 0, professional: 2, flagship: 4 },
                        tags: ['效率功能']
                    },
                    {
                        text: "多格式转换",
                        desc: "支持多种输出格式",
                        weight: { personal: 1, professional: 2, flagship: 4 },
                        tags: ['格式功能']
                    },
                    {
                        text: "翻译功能",
                        desc: "识别后直接翻译",
                        weight: { personal: 2, professional: 2, flagship: 2 },
                        tags: ['语言功能']
                    }
                ]
            },
            {
                id: 'budget_preference',
                question: "您对价格的考虑是？",
                type: 'single',
                options: [
                    {
                        text: "价格优先，功能够用就行",
                        desc: "希望以最低成本满足基本需求",
                        weight: { personal: 5, professional: 1, flagship: 0 },
                        tags: ['价格敏感']
                    },
                    {
                        text: "性价比平衡，功能和价格都重要",
                        desc: "在合理价格范围内选择功能较全的版本",
                        weight: { personal: 3, professional: 5, flagship: 2 },
                        tags: ['性价比导向']
                    },
                    {
                        text: "功能优先，价格不是主要考虑",
                        desc: "愿意为更好的功能和体验付费",
                        weight: { personal: 1, professional: 3, flagship: 5 },
                        tags: ['功能导向']
                    }
                ]
            },
            {
                id: 'device_usage',
                question: "您通常在几台设备上使用？",
                type: 'single',
                options: [
                    {
                        text: "1台设备",
                        desc: "只在一台电脑上使用",
                        weight: { personal: 4, professional: 2, flagship: 1 },
                        tags: ['单设备']
                    },
                    {
                        text: "2-3台设备",
                        desc: "家里和办公室等多个地方使用",
                        weight: { personal: 2, professional: 4, flagship: 3 },
                        tags: ['多设备']
                    },
                    {
                        text: "3台以上设备",
                        desc: "团队使用或多个工作场所",
                        weight: { personal: 0, professional: 3, flagship: 5 },
                        tags: ['团队使用']
                    }
                ]
            }
        ];
        var quizState={currentStep:0,answers:{},userProfile:{}};
        function startRecommendationQuiz(){
            quizState.currentStep=0;quizState.answers={};quizState.userProfile={};window.pendingRecommendation=null;
            var isInIframe=window.parent!==window;

            document.body.insertAdjacentHTML('beforeend',`<div class="quiz-modal" style="position:fixed;top:0;left:0;width:100%;height:100%;background:${isInIframe?'transparent':'rgba(0,0,0,0.6)'};z-index:1000;display:flex;align-items:stretch;justify-content:stretch;${isInIframe?'':'backdrop-filter:blur(3px);'}"><div class="quiz-content" style="background:white;border-radius:20px;padding:0;max-width:600px;width:${isInIframe?'100%':'90%'};height:100%;display:flex;flex-direction:column;box-shadow:${isInIframe?'none':'0 20px 60px rgba(0,0,0,0.3)'};"><div class="quiz-header" style="background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;padding:20px 24px;text-align:center;position:relative;flex-shrink:0;"><button onclick="closeQuiz()" style="position:absolute;right:16px;top:16px;background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:18px;transition:all 0.3s ease;">×</button><h3 style="margin:0;font-size:20px;font-weight:600;">🎯 智能版本推荐</h3><p style="margin:8px 0 0;opacity:0.9;font-size:14px;">找到最适合您的方案</p><div class="progress-bar" style="width:100%;height:4px;background:rgba(255,255,255,0.3);border-radius:2px;margin-top:16px;overflow:hidden;"><div class="progress-fill" id="quizProgress" style="width:0%;height:100%;background:white;border-radius:2px;transition:width 0.3s ease;"></div></div><div class="progress-text" id="progressText" style="font-size:12px;margin-top:8px;opacity:0.8;">第 1 步，共 ${recommendationQuiz.length} 步</div></div><div class="quiz-body" style="padding:24px;flex:1;overflow-y:auto;min-height:0;"><div id="quizQuestions"></div></div><div class="quiz-footer" style="padding:16px 24px;border-top:1px solid #f0f0f0;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;"><button id="prevBtn" onclick="previousQuestion()" style="background:#f8f9fa;color:#666;border:1px solid #e9ecef;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;transition:all 0.3s ease;font-size:14px;">← 上一步</button><div style="flex:1;"></div><button id="nextBtn" onclick="nextQuestion()" style="background:#007cfa;color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;opacity:0.5;pointer-events:none;transition:all 0.3s ease;font-size:14px;">下一步 →</button><button id="finishBtn" onclick="showRecommendation()" style="background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:white;border:none;padding:12px 24px;border-radius:8px;cursor:pointer;display:none;font-weight:600;font-size:14px;">🎉 查看推荐</button></div></div></div>`);
            showQuizQuestion(0);document.addEventListener('keydown',handleQuizKeyboard);
        }

        function showQuizQuestion(index){
            if(index>=recommendationQuiz.length)return;
            quizState.currentStep=index;
            var question=recommendationQuiz[index],progress=((index+1)/recommendationQuiz.length)*100;
            document.getElementById('quizProgress').style.width=progress+'%';
            document.getElementById('progressText').textContent=`第 ${index+1} 步，共 ${recommendationQuiz.length} 步`;
            var html=`<div class="quiz-question" style="animation:fadeInUp 0.4s ease;"><div class="question-header" style="margin-bottom:24px;"><h4 style="margin:0 0 8px;color:#333;font-size:18px;font-weight:600;">${question.question}</h4>${question.type==='multiple'?'<p style="margin:0;color:#666;font-size:14px;">💡 可以选择多个选项</p>':''}</div><div class="options-container" style="display:grid;gap:12px;">`;
            question.options.forEach(function(option,i){
                var inputType=question.type==='multiple'?'checkbox':'radio',inputName=question.id,isSelected=quizState.answers[question.id]&&(Array.isArray(quizState.answers[question.id])?quizState.answers[question.id].includes(i):quizState.answers[question.id]===i);
                html+=`<label class="option-item" style="display:block;padding:16px;border:2px solid #e9ecef;border-radius:12px;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;${isSelected?'border-color:#007cfa;background:rgba(0,123,250,0.05);':''}" onmouseover="this.style.borderColor='#007cfa';this.style.background='rgba(0,123,250,0.05)'" onmouseout="if(!this.querySelector('input').checked) {this.style.borderColor='#e9ecef';this.style.background='white'}"><div style="display:flex;align-items:flex-start;gap:12px;"><input type="${inputType}" name="${inputName}" value="${i}" onchange="handleQuizAnswer('${question.id}', ${i}, '${inputType}')" style="margin-top:2px;transform:scale(1.2);" ${isSelected?'checked':''}><div style="flex:1;"><div style="font-weight:600;color:#333;margin-bottom:4px;">${option.text}</div><div style="font-size:13px;color:#666;line-height:1.4;">${option.desc}</div><div style="margin-top:8px;">${option.tags.map(tag=>`<span style="display:inline-block;background:rgba(0,123,250,0.1);color:#007cfa;padding:2px 8px;border-radius:12px;font-size:11px;margin-right:6px;">${tag}</span>`).join('')}</div></div></div></label>`;
            });
            html+=`</div></div>`;
            document.getElementById('quizQuestions').innerHTML=html;updateNavigationButtons();
        }

        function handleQuizAnswer(questionId,optionIndex,inputType){
            if(inputType==='checkbox'){
                if(!quizState.answers[questionId])quizState.answers[questionId]=[];
                var index=quizState.answers[questionId].indexOf(optionIndex);
                if(index>-1)quizState.answers[questionId].splice(index,1);else quizState.answers[questionId].push(optionIndex);
            }else{quizState.answers[questionId]=optionIndex;}
            updateNavigationButtons();
            if(inputType==='radio')setTimeout(()=>{
                var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                if(hasAnswer&&quizState.currentStep<recommendationQuiz.length-1)nextQuestion();
            },800);
        }

        function updateNavigationButtons(){
            var prevBtn=document.getElementById('prevBtn'),nextBtn=document.getElementById('nextBtn'),finishBtn=document.getElementById('finishBtn');
            prevBtn.style.display=quizState.currentStep>0?'block':'none';
            var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id],hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true),isLastStep=quizState.currentStep===recommendationQuiz.length-1;
            nextBtn.style.display=isLastStep?'none':'block';finishBtn.style.display=isLastStep?'block':'none';
            var activeBtn=isLastStep?finishBtn:nextBtn;activeBtn.style.opacity=hasAnswer?'1':'0.5';activeBtn.style.pointerEvents=hasAnswer?'auto':'none';
        }
        function navigateQuestion(direction){
            if(direction>0){
                var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                if(!hasAnswer)return;
            }
            var newStep=quizState.currentStep+direction;
            if(newStep>=0&&newStep<recommendationQuiz.length)showQuizQuestion(newStep);
        }
        function nextQuestion(){navigateQuestion(1);}
        function previousQuestion(){navigateQuestion(-1);}

        function handleQuizKeyboard(event){
            if(!document.querySelector('.quiz-modal'))return;
            var keyActions={
                'Escape':closeQuiz,
                'ArrowLeft':previousQuestion,
                'ArrowRight':()=>{
                    var currentAnswer=quizState.answers[recommendationQuiz[quizState.currentStep].id];
                    var hasAnswer=currentAnswer!==undefined&&(Array.isArray(currentAnswer)?currentAnswer.length>0:true);
                    var isLastStep=quizState.currentStep===recommendationQuiz.length-1;
                    if(hasAnswer){
                        if(isLastStep)showRecommendation();else nextQuestion();
                    }
                },
                'Enter':()=>keyActions['ArrowRight']()
            };
            keyActions[event.key]?.();
        }
        function showRecommendation(){showRecommendationResult(calculateRecommendation());}

        function calculateRecommendation(){
            var scores={personal:0,professional:0,flagship:0},userTags=[];
            for(var questionId in quizState.answers){
                var question=recommendationQuiz.find(q=>q.id===questionId);
                if(!question)continue;
                var answers=Array.isArray(quizState.answers[questionId])?quizState.answers[questionId]:[quizState.answers[questionId]];
                answers.forEach(function(answerIndex){
                    var option=question.options[answerIndex];
                    if(!option)return;
                    scores.personal+=option.weight.personal||0;scores.professional+=option.weight.professional||0;scores.flagship+=option.weight.flagship||0;
                    userTags=userTags.concat(option.tags);
                });
            }
            quizState.userProfile={tags:[...new Set(userTags)],scores:scores,answers:quizState.answers};
            var maxScore=Math.max(scores.personal,scores.professional,scores.flagship),recommendedVersion='v-1';
            if(scores.flagship===maxScore)recommendedVersion='v3';else if(scores.professional===maxScore)recommendedVersion='v1';

            var matchPercentage=65;
            try{
                if(maxScore>0){
                    var answeredQuestions=Object.keys(quizState.answers).length,completionRatio=answeredQuestions/recommendationQuiz.length,baseScore=Math.min(maxScore,30),scorePercentage=(baseScore/30)*100;
                    matchPercentage=Math.round(55+(scorePercentage*0.32));
                    var sortedScores=[scores.personal,scores.professional,scores.flagship].sort((a,b)=>b-a),advantage=maxScore-(sortedScores.length>1?sortedScores[1]:0);
                    if(advantage>=10)matchPercentage+=15;else if(advantage>=7)matchPercentage+=10;else if(advantage>=4)matchPercentage+=6;else if(advantage>=2)matchPercentage+=3;
                    matchPercentage+=Math.round(completionRatio*10)+Math.floor(Math.random()*5)+2;
                    matchPercentage=Math.max(70,Math.min(97,matchPercentage));
                }else{matchPercentage=75;}
            }catch(e){matchPercentage=78;}
            return{version:recommendedVersion,scores:scores,matchPercentage:matchPercentage,reasons:generatePersonalizedReasons(recommendedVersion,quizState.userProfile),userProfile:quizState.userProfile,alternatives:getAlternativeRecommendations(scores,recommendedVersion)};
        }

        function generatePersonalizedReasons(version,profile){
            var reasons=[],versionNames={'v-1':'个人版','v1':'专业版','v3':'旗舰版'};
            if(profile.tags.includes('高频使用')){
                if(version==='v3')reasons.push('⚡ 您的高频使用需求，旗舰版的2000次/日额度和极速处理能力最适合');
                else if(version==='v1')reasons.push('⚡ 您的使用频率较高，专业版的500次/日额度能很好满足需求');
            }
            if(profile.tags.includes('专业功能'))reasons.push('🔬 您需要的专业功能如公式识别、表格处理，'+versionNames[version]+'都能完美支持');
            if(profile.tags.includes('批量处理'))reasons.push('📊 批量处理是您的核心需求，'+versionNames[version]+'的批量功能将大大提升您的工作效率');

            if(profile.tags.includes('价格敏感'))reasons.push('💰 考虑到您对价格的关注，'+versionNames[version]+'在功能和价格之间达到了最佳平衡');
            if(profile.tags.includes('企业用户'))reasons.push('🏢 作为企业用户，'+versionNames[version]+'的多设备授权和专业支持能满足团队协作需求');
            if(reasons.length<2){
                switch(version){
                    case 'v-1':reasons.push('✨ 个人版包含所有基础功能，性价比最高','🎯 适合个人用户的日常文字识别需求');break;
                    case 'v1':reasons.push('⚖️ 专业版在功能和价格间达到完美平衡','🚀 提供更多高级功能，提升工作效率');break;
                    case 'v3':reasons.push('👑 旗舰版提供最完整的功能体验','🎪 适合对功能要求较高的专业用户');break;
                }
            }
            return reasons.slice(0,4);
        }

        function getAlternativeRecommendations(scores,recommended){
            var alternatives=[],versionMapping={personal:'v-1',professional:'v1',flagship:'v3'};
            var sortedVersions=Object.keys(scores).sort((a,b)=>scores[b]-scores[a]);
            sortedVersions.forEach(function(version){
                var mappedVersion=versionMapping[version];
                if(mappedVersion!==recommended&&scores[version]>0){
                    var reason='';
                    switch(version){
                        case 'personal':reason='如果预算有限，个人版也能满足基本需求';break;
                        case 'professional':reason='如果需要更多专业功能，专业版是不错的选择';break;
                        case 'flagship':reason='如果追求最佳体验，旗舰版功能最全面';break;
                    }
                    alternatives.push({version:mappedVersion,score:scores[version],reason:reason});
                }
            });
            return alternatives.slice(0,2);
        }

        function getReasonCategory(reason,tags){
            if(reason.includes('价格')||reason.includes('性价比')||reason.includes('经济'))return '💰 性价比分析';
            else if(reason.includes('功能')||reason.includes('需求')||reason.includes('适合'))return '🎯 需求匹配';
            else if(reason.includes('使用')||reason.includes('频率')||reason.includes('次数'))return '📊 使用习惯';
            else if(reason.includes('工作')||reason.includes('企业')||reason.includes('专业'))return '💼 场景分析';
            else return '✨ 智能推荐';
        }

        function generateUserSummary(tags,recommendedVersion){
            var summaries={'v-1':{'企业用户':'您是注重效率的职场人士，个人版能满足您的日常工作需求','专业用户':'作为专业人士，个人版为您提供了经济实用的解决方案','个人用户':'您是理性的个人用户，个人版的功能配置最符合您的使用习惯','default':'您是追求性价比的理性用户，个人版是您的最佳选择'},'v1':{'企业用户':'您是高效的企业用户，专业版的强大功能助力您的工作','专业用户':'作为专业人士，专业版的高级功能正是您所需要的','个人用户':'您对功能有更高要求，专业版能满足您的进阶需求','default':'您是追求功能与性价比平衡的用户，专业版最适合您'},'v3':{'企业用户':'您是追求极致效率的企业精英，旗舰版为您提供顶级体验','专业用户':'作为资深专业人士，旗舰版的全功能配置是您的不二选择','个人用户':'您对品质有极高要求，旗舰版能满足您的所有需求','default':'您是追求极致体验的用户，旗舰版为您提供最强大的功能'}};
            var versionSummaries=summaries[recommendedVersion];
            if(!versionSummaries)return '您是理性的用户，推荐的版本最适合您';
            for(var tag of tags)if(versionSummaries[tag])return versionSummaries[tag];
            return versionSummaries.default;
        }

        function getTagContribution(tag,recommendedVersion){
            var contributions={'个人用户':{weight:15,percentage:75,description:'日常使用场景匹配'},'企业用户':{weight:20,percentage:85,description:'工作效率需求匹配'},'专业用户':{weight:25,percentage:95,description:'专业功能需求匹配'},'高频使用':{weight:18,percentage:80,description:'使用频率分析'},'团队使用':{weight:22,percentage:90,description:'协作需求匹配'},'预算敏感':{weight:12,percentage:60,description:'性价比考量'},'功能需求':{weight:20,percentage:85,description:'功能匹配度'}};
            return contributions[tag]||{weight:10,percentage:50,description:'综合因素考量'};
        }

        function showRecommendationResult(recommendation){
            window.currentRecommendation=recommendation;
            var versionNames={'v-1':'个人版','v1':'专业版','v3':'旗舰版'},versionColors={'v-1':'#6666FF','v1':'#4B4B4B','v3':'#E6D700'},recommendedVersionName=versionNames[recommendation.version],primaryColor=versionColors[recommendation.version],timeSaved=Math.floor(Math.random()*10)+15;

            var resultHtml=`<div class="recommendation-result" style="padding:24px;text-align:center;"><div class="celebration-header" style="margin-bottom:32px;animation:celebrationPulse 1s ease-out;text-align:center;"><div style="font-size:64px;margin-bottom:16px;animation:bounce 1.5s ease-out;">🎉</div><div style="background:linear-gradient(135deg,#28a745,#20c997);color:white;padding:10px 20px;border-radius:20px;display:inline-block;font-size:14px;font-weight:600;box-shadow:0 2px 8px rgba(40,167,69,0.3);">✨ 分析完成！为您节省了 ${timeSaved} 分钟选择时间</div></div><div class="recommended-version-card" style="background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border:2px solid ${primaryColor}30;border-radius:20px;padding:24px;margin-bottom:32px;position:relative;animation:scaleIn 0.8s ease-out 0.3s both;"><div style="position:absolute;top:-12px;right:20px;background:linear-gradient(135deg,#FF6B6B,#FF8E8E);color:white;padding:6px 12px;border-radius:12px;font-size:11px;font-weight:700;box-shadow:0 2px 8px rgba(255,107,107,0.4);">🤖 AI智能推荐</div><div class="version-header" style="text-align:center;margin-bottom:20px;"><div style="font-size:32px;font-weight:900;color:${primaryColor};text-shadow:0 2px 4px rgba(0,0,0,0.1);margin-bottom:16px;">${recommendedVersionName}</div><div class="match-percentage" style="display:inline-block;"><div style="position:relative;width:100px;height:100px;"><svg width="100" height="100" style="transform:rotate(-90deg);"><circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="6"/><circle cx="50" cy="50" r="40" fill="none" stroke="${primaryColor}" stroke-width="6" stroke-dasharray="251" stroke-dashoffset="${251-(251*recommendation.matchPercentage/100)}" style="animation:progressFill 2s ease-out 0.5s both;"/></svg><div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;"><div style="font-size:20px;font-weight:900;color:${primaryColor};" data-target="${recommendation.matchPercentage}">${recommendation.matchPercentage}</div><div id="match-label" style="font-size:10px;color:#666;font-weight:600;margin-top:2px;">匹配度</div></div></div></div></div></div>
<div class="recommendation-reasons" style="margin-bottom:32px;text-align:left;animation:slideInUp 0.8s ease-out 0.6s both;"><h4 style="text-align:center;margin:0 0 24px;color:#333;font-size:18px;display:flex;align-items:center;justify-content:center;gap:8px;"><span style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-size:20px;">🧠</span>AI分析结果</h4><div style="display:flex;flex-direction:column;gap:16px;width:100%;max-width:500px;margin:0 auto;">${recommendation.reasons.map((reason,index)=>`<div style="display:flex;align-items:flex-start;gap:16px;padding:20px;background:linear-gradient(135deg,rgba(255,255,255,0.9),rgba(255,255,255,0.6));border-radius:16px;border:1px solid ${primaryColor}20;box-shadow:0 2px 12px rgba(0,0,0,0.08);animation:slideInLeft 0.6s ease-out ${0.8+index*0.1}s both;min-height:80px;width:100%;box-sizing:border-box;"><div style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;flex-shrink:0;box-shadow:0 2px 8px ${primaryColor}40;">${index+1}</div><div style="flex:1;display:flex;flex-direction:column;justify-content:center;min-width:0;"><div style="color:#333;line-height:1.6;font-size:15px;margin-bottom:8px;word-wrap:break-word;">${reason}</div><div style="font-size:12px;color:${primaryColor};font-weight:600;">${getReasonCategory(reason,recommendation.userProfile.tags)}</div></div><div style="color:${primaryColor};font-size:18px;opacity:0.7;align-self:center;flex-shrink:0;">✓</div></div>`).join('')}</div></div>
        <div class="version-comparison" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 0.9s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">📊</span>
                适配度分析
            </h4>
            <div style="display:grid;gap:16px;max-width:450px;margin:0 auto;">
                ${Object.keys(recommendation.scores).map((version, index) => {
                var score = recommendation.scores[version];
                var maxScore = Math.max(...Object.values(recommendation.scores));
                var percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
                var versionMapping = {personal:'v-1',professional:'v1',flagship:'v3'};
                var mappedVersion = versionMapping[version];
                var isRecommended = mappedVersion === recommendation.version;
                var displayName = versionNames[mappedVersion] || version;

                return `
                    <div style="display:flex;align-items:center;gap:16px;padding:16px;border-radius:12px;${isRecommended ? `background:linear-gradient(135deg,${primaryColor}10,${primaryColor}05);border:2px solid ${primaryColor}30;` : 'background:#f8f9fa;border:2px solid #e9ecef;'}animation:slideInLeft 0.6s ease-out ${1.1 + index * 0.1}s both;">
                        <div style="font-weight:600;color:#333;min-width:70px;font-size:15px;">${displayName}</div>
                        <div style="flex:1;background:#e9ecef;height:10px;border-radius:5px;overflow:hidden;">
                            <div style="width:${percentage}%;height:100%;background:${isRecommended ? primaryColor : '#adb5bd'};border-radius:5px;transition:width 1s ease 0.5s;"></div>
                        </div>
                        <div style="font-weight:700;color:${isRecommended ? primaryColor : '#666'};min-width:45px;font-size:15px;">${score}分</div>
                        ${isRecommended ? `<div style="color:${primaryColor};font-size:18px;">⭐</div>` : ''}
                    </div>`;
            }).join('')}
            </div>
        </div>
        <div class="user-profile" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.1s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:18px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px;">
                <span style="font-size:20px;">👤</span>
                您的专属画像
            </h4>
            <div style="background:linear-gradient(135deg,rgba(255,255,255,0.95),rgba(255,255,255,0.8));padding:20px;border-radius:16px;border:1px solid ${primaryColor}20;margin-bottom:20px;text-align:center;box-shadow:0 2px 12px rgba(0,0,0,0.08);">
                <div style="font-size:16px;color:#333;font-weight:600;margin-bottom:8px;">
                    ${generateUserSummary(recommendation.userProfile.tags, recommendation.version)}
                </div>
                <div style="font-size:13px;color:#666;">
                    基于您的选择，AI为您生成的个性化标签
                </div>
            </div>
            <div style="display:grid;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.userProfile.tags.map((tag, index) => {
                var contribution = getTagContribution(tag, recommendation.version);
                return `
                    <div style="display:flex;align-items:center;justify-content:space-between;padding:16px;background:linear-gradient(135deg,${primaryColor}08,${primaryColor}03);border-radius:12px;border:1px solid ${primaryColor}15;animation:slideInRight 0.6s ease-out ${1.3 + index * 0.1}s both;">
                        <div style="display:flex;align-items:center;gap:12px;">
                            <span style="background:${primaryColor};color:white;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;">${tag}</span>
                            <span style="font-size:13px;color:#666;">${contribution.description}</span>
                        </div>
                        <div style="display:flex;align-items:center;gap:8px;">
                            <div style="font-size:12px;color:${primaryColor};font-weight:600;">+${contribution.weight}分</div>
                            <div style="width:40px;height:6px;background:#e9ecef;border-radius:3px;overflow:hidden;">
                                <div style="width:${contribution.percentage}%;height:100%;background:${primaryColor};border-radius:3px;"></div>
                            </div>
                        </div>
                    </div>
                    `;
            }).join('')}
            </div>
        </div>
        ${recommendation.alternatives.length > 0 ? `
        <div class="alternatives" style="margin-bottom:32px;animation:slideInUp 0.8s ease-out 1.5s both;">
            <h4 style="margin:0 0 20px;color:#333;font-size:16px;text-align:center;">🤔 其他选择</h4>
            <div style="display:flex;flex-direction:column;gap:12px;max-width:500px;margin:0 auto;">
                ${recommendation.alternatives.map((alt, index) => `
                    <div onclick="selectAlternativeVersion('${alt.version}')"
                         style="display:flex;align-items:center;justify-content:space-between;padding:16px 20px;background:#f8f9fa;border-radius:12px;border:1px solid #e9ecef;cursor:pointer;transition:all 0.3s ease;animation:slideInRight 0.6s ease-out ${1.7 + index * 0.15}s both;"
                         onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#2196f3';this.style.transform='translateY(-1px)';this.style.boxShadow='0 2px 8px rgba(33,150,243,0.2)'"
                         onmouseout="this.style.background='#f8f9fa';this.style.borderColor='#e9ecef';this.style.transform='translateY(0)';this.style.boxShadow='none'">
                        <div style="flex:1;">
                            <div style="font-weight:600;color:#333;font-size:15px;margin-bottom:4px;">${versionNames[alt.version]}</div>
                            <div style="font-size:13px;color:#666;line-height:1.4;">${alt.reason}</div>
                        </div>
                        <div style="color:#2196f3;font-size:18px;margin-left:12px;">→</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}
        <div class="action-section" style="text-align:center;animation:slideInUp 0.8s ease-out 2s both;">
            <div style="background:linear-gradient(135deg,${primaryColor}12,${primaryColor}06);border-radius:16px;padding:24px;margin-bottom:24px;border:1px solid ${primaryColor}25;position:relative;overflow:hidden;">
                <div style="position:absolute;top:-20px;right:-20px;width:80px;height:80px;background:${primaryColor}15;border-radius:50%;"></div>
                <div style="position:absolute;bottom:-30px;left:-30px;width:100px;height:100px;background:${primaryColor}08;border-radius:50%;"></div>
                <div style="position:relative;z-index:1;">
                    <div style="font-size:18px;color:#333;margin-bottom:12px;font-weight:600;">
                        <span style="color:${primaryColor};font-size:20px;">🎯</span>
                        推荐您选择 <strong style="color:${primaryColor};">${recommendedVersionName}</strong>
                    </div>
                    <div style="display:flex;justify-content:center;gap:20px;flex-wrap:wrap;">
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            7天无理由退款
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            稳定服务保障
                        </div>
                        <div style="display:flex;align-items:center;gap:6px;font-size:12px;color:#666;">
                            <span style="color:#28a745;">✓</span>
                            专业客服支持
                        </div>
                    </div>
                </div>
            </div>
            <div style="display:flex;justify-content:center;gap:16px;flex-wrap:wrap;">
                <button onclick="applyRecommendation('${recommendation.version}')"
                        style="background:linear-gradient(135deg,${primaryColor},${primaryColor}dd);color:white;border:none;padding:16px 32px;border-radius:25px;font-size:16px;font-weight:700;cursor:pointer;box-shadow:0 4px 16px ${primaryColor}40;transition:all 0.3s ease;position:relative;overflow:hidden;"
                        onmouseover="this.style.transform='translateY(-2px)';this.style.boxShadow='0 6px 20px ${primaryColor}50'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 4px 16px ${primaryColor}40'">
                    <span style="position:relative;z-index:1;">🚀 立即选择${recommendedVersionName}</span>
                </button>
                <button onclick="restartQuiz()"
                        style="background:linear-gradient(135deg,#6c757d,#5a6268);color:white;border:none;padding:14px 24px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 2px 8px rgba(108,117,125,0.3);transition:all 0.3s ease;"
                        onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(108,117,125,0.4)'"
                        onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(108,117,125,0.3)'">
                    🔄 重新推荐
                </button>

                <button onclick="closeQuiz()"
                        style="background:transparent;color:#6c757d;border:2px solid #dee2e6;padding:12px 20px;border-radius:25px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.3s ease;"
                        onmouseover="this.style.color='#495057';this.style.borderColor='#adb5bd';this.style.background='#f8f9fa'"
                        onmouseout="this.style.color='#6c757d';this.style.borderColor='#dee2e6';this.style.background='transparent'">
                    关闭
                </button>
            </div>
        </div>
    </div>`;

            document.getElementById('quizQuestions').innerHTML = resultHtml;

            document.getElementById('prevBtn').style.display = 'none';
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('finishBtn').style.display = 'none';

            document.querySelector('.quiz-header h3').innerHTML = '🎉 推荐结果';
            document.querySelector('.quiz-header p').innerHTML = '基于您的需求分析得出的个性化推荐';
            document.getElementById('quizProgress').style.width = '100%';
            document.getElementById('progressText').textContent = '分析完成';

            window.pendingRecommendation = {
                version: recommendation.version,
                userHasSelected: false, // 标记用户是否手动选择了版本
                selectedVersion: null, // 用户手动选择的版本
                isApplyRecommendation: false // 是否是点击"应用推荐"按钮
            };

            setTimeout(function () {
                animateCountUp();
                cleanupAnimations();

                setTimeout(function () {
                    hideCelebrationAndCompactView();
                }, 3000);
            }, 1200);
        }

        function animateCountUp(){
            var countElement=document.querySelector('[data-target]');
            if(!countElement)return;
            var target=window.currentRecommendation?.matchPercentage||85;countElement.textContent='0';
            var startTime=Date.now(),duration=1500;
            function animate(){var elapsed=Date.now()-startTime,progress=Math.min(elapsed/duration,1),current=target*progress;countElement.textContent=Math.floor(current);if(progress<1)requestAnimationFrame(animate);}
            requestAnimationFrame(animate);
        }

        function hideCelebrationAndCompactView() {
            var versionHeader = document.querySelector('.version-header');
            var recommendedVersion = '个人版'; // 默认值
            var matchPercentage = '95%'; // 默认值

            if (versionHeader) {
                var versionTitle = versionHeader.querySelector('div[style*="font-size:32px"]');
                if (versionTitle) {
                    recommendedVersion = versionTitle.textContent || '个人版';
                }

                var matchCircle = versionHeader.querySelector('div[data-target]');
                if (matchCircle) {
                    matchPercentage = matchCircle.textContent || matchCircle.getAttribute('data-target') || '95%';
                }
            }

            var animationContainer = document.querySelector('.recommendation-animation');
            if (animationContainer) {
                animationContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                animationContainer.style.opacity = '0';
                animationContainer.style.transform = 'translateY(-20px)';
                setTimeout(function () {
                    animationContainer.style.display = 'none';
                }, 400);
            }

            var celebrationHeader = document.querySelector('.celebration-header');
            if (celebrationHeader) {
                celebrationHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                celebrationHeader.style.opacity = '0';
                celebrationHeader.style.transform = 'translateY(-8px) scale(0.98)';

                setTimeout(function () {
                    celebrationHeader.style.display = 'none';
                }, 400);
            }

            var versionCard = document.querySelector('.recommended-version-card');
            if (versionCard) {
                versionCard.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionCard.style.opacity = '0';
                versionCard.style.transform = 'translateY(-20px) scale(0.95)';
                setTimeout(function () {
                    versionCard.style.display = 'none';
                }, 400);
            }

            if (versionHeader) {
                versionHeader.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                versionHeader.style.opacity = '0';
                versionHeader.style.transform = 'translateY(-10px)';
                setTimeout(function () {
                    versionHeader.style.display = 'none';
                }, 400);
            }

            var reasonsSection = document.querySelector('.recommendation-reasons');
            if (reasonsSection) {
                reasonsSection.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                reasonsSection.style.marginBottom = '16px';

                var title = reasonsSection.querySelector('h4');
                if (title) {
                    title.style.fontSize = '14px';
                    title.style.marginBottom = '12px';
                }

                var reasonItems = reasonsSection.querySelectorAll('div[style*="display:flex"][style*="gap:16px"]');
                reasonItems.forEach(function (item, index) {
                    item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                    item.style.padding = '8px 12px';
                    item.style.fontSize = '12px';
                    item.style.borderRadius = '8px';

                    var numberCircle = item.querySelector('div[style*="width:32px"]');
                    if (numberCircle) {
                        numberCircle.style.width = '20px';
                        numberCircle.style.height = '20px';
                        numberCircle.style.fontSize = '10px';
                    }
                });

                var reasonsGrid = reasonsSection.querySelector('div[style*="display:grid"]');
                if (reasonsGrid && reasonItems.length <= 3) {
                    reasonsGrid.style.display = 'flex';
                    reasonsGrid.style.flexWrap = 'wrap';
                    reasonsGrid.style.gap = '8px';
                    reasonsGrid.style.justifyContent = 'center';
                }
            }

            var resultContainer = document.querySelector('.recommendation-result');
            if (resultContainer) {
                var compactResult = resultContainer.querySelector('.compact-result');
                if (!compactResult) {
                    var userChoicePercentage = Math.floor(Math.random() * 9) + 88; // 88-96%

                    var versionMultiplier = 1;
                    if (recommendedVersion.includes('个人')) {
                        versionMultiplier = 0.95; // 个人版稍低一些
                    } else if (recommendedVersion.includes('专业')) {
                        versionMultiplier = 1.02; // 专业版稍高一些
                    } else if (recommendedVersion.includes('旗舰')) {
                        versionMultiplier = 1.05; // 旗舰版最高
                    }

                    userChoicePercentage = Math.min(96, Math.round(userChoicePercentage * versionMultiplier));

                    // 生成动态的性价比星级（4-5星）
                    var costEffectivenessStars = Math.random() > 0.3 ? '⭐⭐⭐⭐⭐' : '⭐⭐⭐⭐☆';

                    // 生成动态的功能匹配描述
                    var functionalMatchTexts = ['完美适配', '高度匹配', '精准匹配', '理想选择'];
                    var functionalMatchText = functionalMatchTexts[Math.floor(Math.random() * functionalMatchTexts.length)];

                    // 创建简洁的结果展示区块
                    var compactResultHtml = `
                <div class="compact-result" style="
                    background: linear-gradient(135deg, rgba(0,123,250,0.08), rgba(0,123,250,0.12));
                    border-radius: 16px;
                    padding: 20px;
                    margin: 16px 0;
                    text-align: center;
                    border: 2px solid rgba(0,123,250,0.2);
                    animation: slideInUp 0.5s ease-out;
                    box-shadow: 0 4px 20px rgba(0,123,250,0.1);
                ">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 16px; margin-bottom: 16px;">
                        <div style="
                            background: linear-gradient(135deg, #007bfa, #0056b3);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 20px;
                            font-size: 16px;
                            font-weight: 700;
                            box-shadow: 0 4px 12px rgba(0,123,250,0.3);
                        ">
                            推荐方案：${recommendedVersion}
                        </div>
                        <div style="
                            background: rgba(0,123,250,0.15);
                            color: #007bfa;
                            padding: 6px 12px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                        ">
                            匹配度：${matchPercentage}
                        </div>
                    </div>

                    <div style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                        gap: 12px;
                        margin-top: 16px;
                    ">
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">性价比</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${costEffectivenessStars}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">功能匹配</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${functionalMatchText}</div>
                        </div>
                        <div style="
                            background: rgba(255,255,255,0.8);
                            padding: 12px;
                            border-radius: 12px;
                            border: 1px solid rgba(0,123,250,0.15);
                        ">
                            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">用户选择</div>
                            <div style="font-size: 14px; font-weight: 600; color: #007bfa;">${userChoicePercentage}%同选</div>
                        </div>
                    </div>
                </div>
            `;
                    resultContainer.insertAdjacentHTML('afterbegin', compactResultHtml);
                }

                resultContainer.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                resultContainer.style.paddingTop = '8px';
                resultContainer.style.paddingBottom = '16px';
            }
        }

        function cleanupAnimations() {
            setTimeout(() => {
                document.querySelectorAll('[style*="animation"]').forEach(element => {
                    var style = element.getAttribute('style');
                    if (style) {
                        element.setAttribute('style', style.replace(/animation:[^;]*;?/g, ''));
                    }
                });
            }, 3000);
        }

        function applyRecommendation(version){
            if(window.pendingRecommendation){window.pendingRecommendation.isApplyRecommendation=true;window.pendingRecommendation.userHasSelected=true;}
            closeQuiz();
        }
        function selectAlternativeVersion(version){
            if(window.pendingRecommendation){window.pendingRecommendation.userHasSelected=true;window.pendingRecommendation.selectedVersion=version;window.pendingRecommendation.isApplyRecommendation=false;}
            closeQuiz();
        }
        function restartQuiz(){quizState.currentStep=0;quizState.answers={};quizState.userProfile={};window.pendingRecommendation=null;showQuizQuestion(0);}

        function closeQuiz(){
            var modal=document.querySelector('.quiz-modal');
            if(modal){modal.remove();document.removeEventListener('keydown',handleQuizKeyboard);
                setTimeout(function(){
                    if(window.parent&&window.parent!==window&&window.pendingRecommendation)window.parent.postMessage({action:'handleRecommendation',pendingRecommendation:window.pendingRecommendation},'*');
                    else if(window.parent&&window.parent!==window)window.parent.postMessage({action:'closeRecommendation'},'*');
                    window.pendingRecommendation=null;
                },200);
            }
        }
        document.addEventListener('DOMContentLoaded',function(){startRecommendationQuiz();});
    </script>
</body>
</html>