!function(t,e){"object"==typeof module&&module.exports?(e["default"]=e,module.exports=t.document?e(t):e):"function"==typeof define&&define.amd?define("highcharts/highstock",function(){return e(t)}):(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e(t))}("undefined"!=typeof window?window:this,function(t){function e(t,e,i,s){t.hasOwnProperty(e)||(t[e]=s.apply(null,i))}var i={};return e(i,"parts/Globals.js",[],function(){var e=void 0===t?"undefined"!=typeof window?window:{}:t,i=e.document,s=e.navigator&&e.navigator.userAgent||"",o=i&&i.createElementNS&&!!i.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,n=/(edge|msie|trident)/i.test(s)&&!e.opera,r=-1!==s.indexOf("Firefox"),a=-1!==s.indexOf("Chrome"),h=r&&4>parseInt(s.split("Firefox/")[1],10);return{product:"Highcharts",version:"7.1.1",deg2rad:2*Math.PI/360,doc:i,hasBidiBug:h,hasTouch:i&&void 0!==i.documentElement.ontouchstart,isMS:n,isWebKit:-1!==s.indexOf("AppleWebKit"),isFirefox:r,isChrome:a,isSafari:!a&&-1!==s.indexOf("Safari"),isTouchDevice:/(Mobile|Android|Windows Phone)/.test(s),SVG_NS:"http://www.w3.org/2000/svg",chartCount:0,seriesTypes:{},symbolSizes:{},svg:o,win:e,marginNames:["plotTop","marginRight","marginBottom","plotLeft"],noop:function(){},charts:[],dateFormats:{}}}),e(i,"parts/Utilities.js",[i["parts/Globals.js"]],function(t){t.timers=[];var e=t.charts,i=t.doc,s=t.win;t.error=function(e,i,o){var n=t.isNumber(e)?"Highcharts error #"+e+": www.highcharts.com/errors/"+e:e,r=function(){if(i)throw Error(n);s.console&&console.log(n)};o?t.fireEvent(o,"displayError",{code:e,message:n},r):r()},t.Fx=function(t,e,i){this.options=e,this.elem=t,this.prop=i},t.Fx.prototype={dSetter:function(){var t,e=this.paths[0],i=this.paths[1],s=[],o=this.now,n=e.length;if(1===o)s=this.toD;else if(n===i.length&&1>o)for(;n--;)t=parseFloat(e[n]),s[n]=isNaN(t)?i[n]:o*parseFloat(i[n]-t)+t;else s=i;this.elem.attr("d",s,null,!0)},update:function(){var t=this.elem,e=this.prop,i=this.now,s=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,s&&s.call(t,i,this)},run:function(e,i,o){var n=this,r=n.options,a=function(t){return!a.stopped&&n.step(t)},h=s.requestAnimationFrame||function(t){setTimeout(t,13)},l=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&h(l)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,a.elem=this.elem,a.prop=this.prop,a()&&1===t.timers.push(a)&&h(l)):(delete r.curAnim[this.prop],r.complete&&0===Object.keys(r.curAnim).length&&r.complete.call(this.elem))},step:function(e){var i,s=+new Date,o=this.options,n=this.elem,r=o.complete,a=o.duration,h=o.curAnim;return n.attr&&!n.element?e=!1:e||s>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),i=h[this.prop]=!0,t.objectEach(h,function(t){!0!==t&&(i=!1)}),i&&r&&r.call(n),e=!1):(this.pos=o.easing((s-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},initPath:function(e,i,s){function o(t){var e,i;for(c=t.length;c--;)e="M"===t[c]||"L"===t[c],i=/[a-zA-Z]/.test(t[c+3]),e&&i&&t.splice(c+1,0,t[c+1],t[c+2],t[c+1],t[c+2])}function n(t,e){for(;t.length<h;){t[0]=e[h-t.length];var i=t.slice(0,f);[].splice.apply(t,[0,0].concat(i)),m&&(i=t.slice(t.length-f),[].splice.apply(t,[t.length,0].concat(i)),c--)}t[0]="M"}function r(t,e){for(var i=(h-t.length)/f;0<i&&i--;)(l=t.slice().splice(t.length/x-f,f*x))[0]=e[h-f-i*f],u&&(l[f-6]=l[f-2],l[f-5]=l[f-1]),[].splice.apply(t,[t.length/x,0].concat(l)),m&&i--}i=i||"";var a,h,l,c,d=e.startX,p=e.endX,u=-1<i.indexOf("C"),f=u?7:3;i=i.split(" "),s=s.slice();var g,m=e.isArea,x=m?2:1;if(u&&(o(i),o(s)),d&&p){for(c=0;c<d.length;c++){if(d[c]===p[0]){a=c;break}if(d[0]===p[p.length-d.length+c]){a=c,g=!0;break}}void 0===a&&(i=[])}return i.length&&t.isNumber(a)&&(h=s.length+a*x*f,g?(n(i,s),r(s,i)):(n(s,i),r(i,s))),[i,s]},fillSetter:function(){t.Fx.prototype.strokeSetter.apply(this,arguments)},strokeSetter:function(){this.elem.attr(this.prop,t.color(this.start).tweenTo(t.color(this.end),this.pos),null,!0)}},t.merge=function(){var e,i,s=arguments,o={},n=function(e,i){return"object"!=typeof e&&(e={}),t.objectEach(i,function(s,o){!t.isObject(s,!0)||t.isClass(s)||t.isDOMElement(s)?e[o]=i[o]:e[o]=n(e[o]||{},s)}),e};for(!0===s[0]&&(o=s[1],s=Array.prototype.slice.call(s,2)),i=s.length,e=0;e<i;e++)o=n(o,s[e]);return o},t.pInt=function(t,e){return parseInt(t,e||10)},t.isString=function(t){return"string"==typeof t},t.isArray=function(t){return"[object Array]"===(t=Object.prototype.toString.call(t))||"[object Array Iterator]"===t},t.isObject=function(e,i){return!(!e||"object"!=typeof e||i&&t.isArray(e))},t.isDOMElement=function(e){return t.isObject(e)&&"number"==typeof e.nodeType},t.isClass=function(e){var i=e&&e.constructor;return!(!t.isObject(e,!0)||t.isDOMElement(e)||!i||!i.name||"Object"===i.name)},t.isNumber=function(t){return"number"==typeof t&&!isNaN(t)&&Infinity>t&&-Infinity<t},t.erase=function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},t.defined=function(t){return null!=t},t.attr=function(e,i,s){var o;return t.isString(i)?t.defined(s)?e.setAttribute(i,s):e&&e.getAttribute&&((o=e.getAttribute(i))||"class"!==i||(o=e.getAttribute(i+"Name"))):t.defined(i)&&t.isObject(i)&&t.objectEach(i,function(t,i){e.setAttribute(i,t)}),o},t.splat=function(e){return t.isArray(e)?e:[e]},t.syncTimeout=function(t,e,i){if(e)return setTimeout(t,e,i);t.call(0,i)},t.clearTimeout=function(e){t.defined(e)&&clearTimeout(e)},t.extend=function(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t},t.pick=function(){var t,e,i=arguments,s=i.length;for(t=0;t<s;t++)if(null!=(e=i[t]))return e},t.css=function(e,i){t.isMS&&!t.svg&&i&&void 0!==i.opacity&&(i.filter="alpha(opacity="+100*i.opacity+")"),t.extend(e.style,i)},t.createElement=function(e,s,o,n,r){e=i.createElement(e);var a=t.css;return s&&t.extend(e,s),r&&a(e,{padding:0,border:"none",margin:0}),o&&a(e,o),n&&n.appendChild(e),e},t.extendClass=function(e,i){var s=function(){};return s.prototype=new e,t.extend(s.prototype,i),s},t.pad=function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||0)+t},t.relativeLength=function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},t.wrap=function(t,e,i){var s=t[e];t[e]=function(){var t=Array.prototype.slice.call(arguments),e=arguments,o=this;return o.proceed=function(){s.apply(o,arguments.length?arguments:e)},t.unshift(s),t=i.apply(this,t),o.proceed=null,t}},t.datePropsToTimestamps=function(e){t.objectEach(e,function(i,s){t.isObject(i)&&"function"==typeof i.getTime?e[s]=i.getTime():(t.isObject(i)||t.isArray(i))&&t.datePropsToTimestamps(i)})},t.formatSingle=function(e,i,s){var o=/\.([0-9])/,n=t.defaultOptions.lang;return/f$/.test(e)?(s=(s=e.match(o))?s[1]:-1,null!==i&&(i=t.numberFormat(i,s,n.decimalPoint,-1<e.indexOf(",")?n.thousandsSep:""))):i=(s||t.time).dateFormat(e,i),i},t.format=function(e,i,s){for(var o,n,r,a,h,l="{",c=!1,d=[];e&&-1!==(l=e.indexOf(l));){if(o=e.slice(0,l),c){for(a=(n=(o=o.split(":")).shift().split(".")).length,h=i,r=0;r<a;r++)h&&(h=h[n[r]]);o.length&&(h=t.formatSingle(o.join(":"),h,s)),d.push(h)}else d.push(o);e=e.slice(l+1),l=(c=!c)?"}":"{"}return d.push(e),d.join("")},t.getMagnitude=function(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))},t.normalizeTickInterval=function(e,i,s,o,n){var r,a=e;for(r=e/(s=t.pick(s,1)),i||(i=n?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===s?i=i.filter(function(t){return 0==t%1}):.1>=s&&(i=[1/s]))),o=0;o<i.length&&(a=i[o],!(n&&a*s>=e||!n&&r<=(i[o]+(i[o+1]||i[o]))/2));o++);return t.correctFloat(a*s,-Math.round(Math.log(.001)/Math.LN10))},t.stableSort=function(t,e){var i,s,o=t.length;for(s=0;s<o;s++)t[s].safeI=s;for(t.sort(function(t,s){return 0===(i=e(t,s))?t.safeI-s.safeI:i}),s=0;s<o;s++)delete t[s].safeI},t.arrayMin=function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},t.arrayMax=function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},t.destroyObjectProperties=function(e,i){t.objectEach(e,function(t,s){t&&t!==i&&t.destroy&&t.destroy(),delete e[s]})},t.discardElement=function(e){var i=t.garbageBin;i||(i=t.createElement("div")),e&&i.appendChild(e),i.innerHTML=""},t.correctFloat=function(t,e){return parseFloat(t.toPrecision(e||14))},t.setAnimation=function(e,i){i.renderer.globalAnimation=t.pick(e,i.options.chart.animation,!0)},t.animObject=function(e){return t.isObject(e)?t.merge(e):{duration:e?500:0}},t.timeUnits={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},t.numberFormat=function(e,i,s,o){e=+e||0,i=+i;var n,r,a=t.defaultOptions.lang,h=(e.toString().split(".")[1]||"").split("e")[0].length,l=e.toString().split("e");return-1===i?i=Math.min(h,20):t.isNumber(i)?i&&l[1]&&0>l[1]&&(0<=(n=i+ +l[1])?(l[0]=(+l[0]).toExponential(n).split("e")[0],i=n):(l[0]=l[0].split(".")[0]||0,e=20>i?(l[0]*Math.pow(10,l[1])).toFixed(i):0,l[1]=0)):i=2,r=(Math.abs(l[1]?l[0]:e)+Math.pow(10,-Math.max(i,h)-1)).toFixed(i),n=3<(h=String(t.pInt(r))).length?h.length%3:0,s=t.pick(s,a.decimalPoint),o=t.pick(o,a.thousandsSep),e=(0>e?"-":"")+(n?h.substr(0,n)+o:""),e+=h.substr(n).replace(/(\d{3})(?=\d)/g,"$1"+o),i&&(e+=s+r.slice(-i)),l[1]&&0!=+e&&(e+="e"+l[1]),e},Math.easeInOutSine=function(t){return-.5*(Math.cos(Math.PI*t)-1)},t.getStyle=function(e,i,o){return"width"===i?Math.max(0,Math.min(e.offsetWidth,e.scrollWidth,e.getBoundingClientRect&&"none"===t.getStyle(e,"transform",!1)?Math.floor(e.getBoundingClientRect().width):Infinity)-t.getStyle(e,"padding-left")-t.getStyle(e,"padding-right")):"height"===i?Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-t.getStyle(e,"padding-top")-t.getStyle(e,"padding-bottom")):(s.getComputedStyle||t.error(27,!0),(e=s.getComputedStyle(e,void 0))&&(e=e.getPropertyValue(i),t.pick(o,"opacity"!==i)&&(e=t.pInt(e))),e)},t.inArray=function(t,e,i){return e.indexOf(t,i)},t.find=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,s=t.length;for(i=0;i<s;i++)if(e(t[i],i))return t[i]},t.keys=Object.keys,t.offset=function(t){var e=i.documentElement;return{top:(t=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0}).top+(s.pageYOffset||e.scrollTop)-(e.clientTop||0),left:t.left+(s.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}},t.stop=function(e,i){for(var s=t.timers.length;s--;)t.timers[s].elem!==e||i&&i!==t.timers[s].prop||(t.timers[s].stopped=!0)},t.objectEach=function(t,e,i){for(var s in t)t.hasOwnProperty(s)&&e.call(i||t[s],t[s],s,t)},t.objectEach({map:"map",each:"forEach",grep:"filter",reduce:"reduce",some:"some"},function(e,i){t[i]=function(t){return Array.prototype[e].apply(t,[].slice.call(arguments,1))}}),t.addEvent=function(e,i,s,o){var n,r=e.addEventListener||t.addEventListenerPolyfill;return n="function"==typeof e&&e.prototype?e.prototype.protoEvents=e.prototype.protoEvents||{}:e.hcEvents=e.hcEvents||{},t.Point&&e instanceof t.Point&&e.series&&e.series.chart&&(e.series.chart.runTrackerClick=!0),r&&r.call(e,i,s,!1),n[i]||(n[i]=[]),n[i].push(s),o&&t.isNumber(o.order)&&(s.order=o.order,n[i].sort(function(t,e){return t.order-e.order})),function(){t.removeEvent(e,i,s)}},t.removeEvent=function(e,i,s){function o(i,s){var o=e.removeEventListener||t.removeEventListenerPolyfill;o&&o.call(e,i,s,!1)}function n(s){var n,r;e.nodeName&&(i?(n={})[i]=!0:n=s,t.objectEach(n,function(t,e){if(s[e])for(r=s[e].length;r--;)o(e,s[e][r])}))}var r,a;["protoEvents","hcEvents"].forEach(function(t){var h=e[t];h&&(i?(r=h[i]||[],s?(-1<(a=r.indexOf(s))&&(r.splice(a,1),h[i]=r),o(i,s)):(n(h),h[i]=[])):(n(h),e[t]={}))})},t.fireEvent=function(e,s,o,n){var r,a,h,l,c;o=o||{},i.createEvent&&(e.dispatchEvent||e.fireEvent)?((r=i.createEvent("Events")).initEvent(s,!0,!0),t.extend(r,o),e.dispatchEvent?e.dispatchEvent(r):e.fireEvent(s,r)):["protoEvents","hcEvents"].forEach(function(i){if(e[i])for(a=e[i][s]||[],h=a.length,o.target||t.extend(o,{preventDefault:function(){o.defaultPrevented=!0},target:e,type:s}),l=0;l<h;l++)(c=a[l])&&!1===c.call(e,o)&&o.preventDefault()}),n&&!o.defaultPrevented&&n.call(e,o)},t.animate=function(e,i,s){var o,n,r,a,h="";t.isObject(s)||(s={duration:(a=arguments)[2],easing:a[3],complete:a[4]}),t.isNumber(s.duration)||(s.duration=400),s.easing="function"==typeof s.easing?s.easing:Math[s.easing]||Math.easeInOutSine,s.curAnim=t.merge(i),t.objectEach(i,function(a,l){t.stop(e,l),r=new t.Fx(e,s,l),n=null,"d"===l?(r.paths=r.initPath(e,e.d,i.d),r.toD=i.d,o=0,n=1):e.attr?o=e.attr(l):(o=parseFloat(t.getStyle(e,l))||0,"opacity"!==l&&(h="px")),n||(n=a),n&&n.match&&n.match("px")&&(n=n.replace(/px/g,"")),r.run(o,n,h)})},t.seriesType=function(e,i,s,o,n){var r=t.getOptions(),a=t.seriesTypes;return r.plotOptions[e]=t.merge(r.plotOptions[i],s),a[e]=t.extendClass(a[i]||function(){},o),a[e].prototype.type=e,n&&(a[e].prototype.pointClass=t.extendClass(t.Point,n)),a[e]},t.uniqueKey=function(){var t=Math.random().toString(36).substring(2,9),e=0;return function(){return"highcharts-"+t+"-"+e++}}(),t.isFunction=function(t){return"function"==typeof t},s.jQuery&&(s.jQuery.fn.highcharts=function(){var i=[].slice.call(arguments);if(this[0])return i[0]?(new(t[t.isString(i[0])?i.shift():"Chart"])(this[0],i[0],i[1]),this):e[t.attr(this[0],"data-highcharts-chart")]})}),e(i,"parts/Color.js",[i["parts/Globals.js"]],function(t){var e=t.isNumber,i=t.merge,s=t.pInt;t.Color=function(e){if(!(this instanceof t.Color))return new t.Color(e);this.init(e)},t.Color.prototype={parsers:[{regex:/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/,parse:function(t){return[s(t[1]),s(t[2]),s(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/,parse:function(t){return[s(t[1]),s(t[2]),s(t[3]),1]}}],names:{white:"#ffffff",black:"#000000"},init:function(e){var i,s,o,n;if((this.input=e=this.names[e&&e.toLowerCase?e.toLowerCase():""]||e)&&e.stops)this.stops=e.stops.map(function(e){return new t.Color(e[1])});else if(e&&e.charAt&&"#"===e.charAt()&&(i=e.length,e=parseInt(e.substr(1),16),7===i?s=[(16711680&e)>>16,(65280&e)>>8,255&e,1]:4===i&&(s=[(3840&e)>>4|(3840&e)>>8,(240&e)>>4|240&e,(15&e)<<4|15&e,1])),!s)for(o=this.parsers.length;o--&&!s;)(i=(n=this.parsers[o]).regex.exec(e))&&(s=n.parse(i));this.rgba=s||[]},get:function(t){var s,o=this.input,n=this.rgba;return this.stops?((s=i(o)).stops=[].concat(s.stops),this.stops.forEach(function(e,i){s.stops[i]=[s.stops[i][0],e.get(t)]})):s=n&&e(n[0])?"rgb"===t||!t&&1===n[3]?"rgb("+n[0]+","+n[1]+","+n[2]+")":"a"===t?n[3]:"rgba("+n.join(",")+")":o,s},brighten:function(t){var i,o=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(e(t)&&0!==t)for(i=0;3>i;i++)o[i]+=s(255*t),0>o[i]&&(o[i]=0),255<o[i]&&(o[i]=255);return this},setOpacity:function(t){return this.rgba[3]=t,this},tweenTo:function(t,e){var i=this.rgba,s=t.rgba;return s.length&&i&&i.length?e=((t=1!==s[3]||1!==i[3])?"rgba(":"rgb(")+Math.round(s[0]+(i[0]-s[0])*(1-e))+","+Math.round(s[1]+(i[1]-s[1])*(1-e))+","+Math.round(s[2]+(i[2]-s[2])*(1-e))+(t?","+(s[3]+(i[3]-s[3])*(1-e)):"")+")":e=t.input||"none",e}},t.color=function(e){return new t.Color(e)}}),e(i,"parts/SvgRenderer.js",[i["parts/Globals.js"]],function(t){var e,i,s=t.addEvent,o=t.animate,n=t.attr,r=t.charts,a=t.color,h=t.css,l=t.createElement,c=t.defined,d=t.deg2rad,p=t.destroyObjectProperties,u=t.doc,f=t.extend,g=t.erase,m=t.hasTouch,x=t.isArray,v=t.isFirefox,y=t.isMS,b=t.isObject,M=t.isString,k=t.isWebKit,w=t.merge,S=t.noop,A=t.objectEach,T=t.pick,P=t.pInt,E=t.removeEvent,C=t.splat,L=t.stop,O=t.svg,D=t.SVG_NS,I=t.symbolSizes,B=t.win;e=t.SVGElement=function(){return this},f(e.prototype,{opacity:1,SVG_NS:D,textProps:"direction fontSize fontWeight fontFamily fontStyle color lineHeight width textAlign textDecoration textOverflow textOutline cursor".split(" "),init:function(e,i){this.element="span"===i?l(i):u.createElementNS(this.SVG_NS,i),this.renderer=e,t.fireEvent(this,"afterInit")},animate:function(e,i,s){var n=t.animObject(T(i,this.renderer.globalAnimation,!0));return T(u.hidden,u.msHidden,u.webkitHidden,!1)&&(n.duration=0),0!==n.duration?(s&&(n.complete=s),o(this,e,n)):(this.attr(e,null,s),t.objectEach(e,function(t,e){n.step&&n.step.call(this,t,{prop:e,pos:1})},this)),this},complexColor:function(e,i,s){var o,n,r,a,h,l,d,p,u,f,g,m,v=this.renderer,y=[];t.fireEvent(this.renderer,"complexColor",{args:arguments},function(){e.radialGradient?n="radialGradient":e.linearGradient&&(n="linearGradient"),n&&(r=e[n],h=v.gradients,d=e.stops,f=s.radialReference,x(r)&&(e[n]=r={x1:r[0],y1:r[1],x2:r[2],y2:r[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===n&&f&&!c(r.gradientUnits)&&(a=r,r=w(r,v.getRadialAttr(f,a),{gradientUnits:"userSpaceOnUse"})),A(r,function(t,e){"id"!==e&&y.push(e,t)}),A(d,function(t){y.push(t)}),y=y.join(","),h[y]?g=h[y].attr("id"):(r.id=g=t.uniqueKey(),h[y]=l=v.createElement(n).attr(r).add(v.defs),l.radAttr=a,l.stops=[],d.forEach(function(e){0===e[1].indexOf("rgba")?(o=t.color(e[1]),p=o.get("rgb"),u=o.get("a")):(p=e[1],u=1),e=v.createElement("stop").attr({offset:e[0],"stop-color":p,"stop-opacity":u}).add(l),l.stops.push(e)})),m="url("+v.url+"#"+g+")",s.setAttribute(i,m),s.gradient=y,e.toString=function(){return m})})},applyTextOutline:function(e){var i,s,o,r=this.element;-1!==e.indexOf("contrast")&&(e=e.replace(/contrast/g,this.renderer.getContrast(r.style.fill))),e=e.split(" "),i=e[e.length-1],(s=e[0])&&"none"!==s&&t.svg&&(this.fakeTS=!0,e=[].slice.call(r.getElementsByTagName("tspan")),this.ySetter=this.xSetter,s=s.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*e+i}),this.removeTextOutline(e),o=r.firstChild,e.forEach(function(t,e){0===e&&(t.setAttribute("x",r.getAttribute("x")),e=r.getAttribute("y"),t.setAttribute("y",e||0),null===e&&r.setAttribute("y",0)),t=t.cloneNode(1),n(t,{"class":"highcharts-text-outline",fill:i,stroke:i,"stroke-width":s,"stroke-linejoin":"round"}),r.insertBefore(t,o)}))},removeTextOutline:function(t){for(var e,i=t.length;i--;)"highcharts-text-outline"===(e=t[i]).getAttribute("class")&&g(t,this.element.removeChild(e))},symbolCustomAttribs:"x y width height r start end innerR anchorX anchorY rounded".split(" "),attr:function(e,i,s,o){var n,r,a,h,l=this.element,c=this,d=this.symbolCustomAttribs;return"string"==typeof e&&void 0!==i&&(n=e,(e={})[n]=i),"string"==typeof e?c=(this[e+"Getter"]||this._defaultGetter).call(this,e,l):(A(e,function(i,s){a=!1,o||L(this,s),this.symbolName&&-1!==t.inArray(s,d)&&(r||(this.symbolAttr(e),r=!0),a=!0),!this.rotation||"x"!==s&&"y"!==s||(this.doTransform=!0),a||((h=this[s+"Setter"]||this._defaultSetter).call(this,i,s,l),!this.styledMode&&this.shadows&&/^(width|height|visibility|x|y|d|transform|cx|cy|r)$/.test(s)&&this.updateShadows(s,i,h))},this),this.afterSetters()),s&&s.call(this),c},afterSetters:function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},updateShadows:function(t,e,i){for(var s=this.shadows,o=s.length;o--;)i.call(s[o],"height"===t?Math.max(e-(s[o].cutHeight||0),0):"d"===t?this.d:e,t,s[o])},addClass:function(t,e){var i=this.attr("class")||"";return e||(t=(t||"").split(/ /g).reduce(function(t,e){return-1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" ")),t!==i&&this.attr("class",t),this},hasClass:function(t){return-1!==(this.attr("class")||"").split(" ").indexOf(t)},removeClass:function(t){return this.attr("class",(this.attr("class")||"").replace(t,""))},symbolAttr:function(t){var e=this;"x y r start end width height innerR anchorX anchorY clockwise".split(" ").forEach(function(i){e[i]=T(t[i],e[i])}),e.attr({d:e.renderer.symbols[e.symbolName](e.x,e.y,e.width,e.height,e)})},clip:function(t){return this.attr("clip-path",t?"url("+this.renderer.url+"#"+t.id+")":"none")},crisp:function(t,e){var i;return e=e||t.strokeWidth||0,i=Math.round(e)%2/2,t.x=Math.floor(t.x||this.x||0)+i,t.y=Math.floor(t.y||this.y||0)+i,t.width=Math.floor((t.width||this.width||0)-2*i),t.height=Math.floor((t.height||this.height||0)-2*i),c(t.strokeWidth)&&(t.strokeWidth=e),t},css:function(t){var e,i,s=this.styles,o={},r=this.element,a="",l=!s,c=["textOutline","textOverflow","width"];return t&&t.color&&(t.fill=t.color),s&&A(t,function(t,e){t!==s[e]&&(o[e]=t,l=!0)}),l&&(s&&(t=f(s,o)),t&&(null===t.width||"auto"===t.width?delete this.textWidth:"text"===r.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=P(t.width))),this.styles=t,e&&!O&&this.renderer.forExport&&delete t.width,r.namespaceURI===this.SVG_NS?(i=function(t,e){return"-"+e.toLowerCase()},A(t,function(t,e){-1===c.indexOf(e)&&(a+=e.replace(/([A-Z])/g,i)+":"+t+";")}),a&&n(r,"style",a)):h(r,t),this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t&&t.textOutline&&this.applyTextOutline(t.textOutline))),this},getStyle:function(t){return B.getComputedStyle(this.element||this,"").getPropertyValue(t)},strokeWidth:function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width");return e.indexOf("px")===e.length-2?e=P(e):(t=u.createElementNS(D,"rect"),n(t,{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),e=t.getBBox().width,t.parentNode.removeChild(t)),e},on:function(t,e){var i=this,s=i.element;return m&&"click"===t?(s.ontouchstart=function(t){i.touchEventFired=Date.now(),t.preventDefault(),e.call(s,t)},s.onclick=function(t){(-1===B.navigator.userAgent.indexOf("Android")||1100<Date.now()-(i.touchEventFired||0))&&e.call(s,t)}):s["on"+t]=e,this},setRadialReference:function(t){var e=this.renderer.gradients[this.element.gradient];return this.element.radialReference=t,e&&e.radAttr&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},translate:function(t,e){return this.attr({translateX:t,translateY:e})},invert:function(t){return this.inverted=t,this.updateTransform(),this},updateTransform:function(){var t=this.translateX||0,e=this.translateY||0,i=this.scaleX,s=this.scaleY,o=this.inverted,n=this.rotation,r=this.matrix,a=this.element;o&&(t+=this.width,e+=this.height),t=["translate("+t+","+e+")"],c(r)&&t.push("matrix("+r.join(",")+")"),o?t.push("rotate(90) scale(-1,1)"):n&&t.push("rotate("+n+" "+T(this.rotationOriginX,a.getAttribute("x"),0)+" "+T(this.rotationOriginY,a.getAttribute("y")||0)+")"),(c(i)||c(s))&&t.push("scale("+T(i,1)+" "+T(s,1)+")"),t.length&&a.setAttribute("transform",t.join(" "))},toFront:function(){var t=this.element;return t.parentNode.appendChild(t),this},align:function(t,e,i){var s,o,n,r,a,h,l={};return n=(o=this.renderer).alignedObjects,t?(this.alignOptions=t,this.alignByTranslate=e,(!i||M(i))&&(this.alignTo=s=i||"renderer",g(n,this),n.push(this),i=null)):(t=this.alignOptions,e=this.alignByTranslate,s=this.alignTo),i=T(i,o[s],o),s=t.align,o=t.verticalAlign,n=(i.x||0)+(t.x||0),r=(i.y||0)+(t.y||0),"right"===s?a=1:"center"===s&&(a=2),a&&(n+=(i.width-(t.width||0))/a),l[e?"translateX":"x"]=Math.round(n),"bottom"===o?h=1:"middle"===o&&(h=2),h&&(r+=(i.height-(t.height||0))/h),l[e?"translateY":"y"]=Math.round(r),this[this.placed?"animate":"attr"](l),this.placed=!0,this.alignAttr=l,this},getBBox:function(t,i){var s,o,n,r,a,h=this.renderer,l=this.element,p=this.styles,u=this.textStr,g=h.cache,m=h.cacheKeys,x=l.namespaceURI===this.SVG_NS;if(o=(i=T(i,this.rotation))*d,n=h.styledMode?l&&e.prototype.getStyle.call(l,"font-size"):p&&p.fontSize,c(u)&&(-1===(a=u.toString()).indexOf("<")&&(a=a.replace(/[0-9]/g,"0")),a+=["",i||0,n,this.textWidth,p&&p.textOverflow].join()),a&&!t&&(s=g[a]),!s){if(x||h.forExport){try{(r=this.fakeTS&&function(t){[].forEach.call(l.querySelectorAll(".highcharts-text-outline"),function(e){e.style.display=t})})&&r("none"),s=l.getBBox?f({},l.getBBox()):{width:l.offsetWidth,height:l.offsetHeight},r&&r("")}catch(v){}(!s||0>s.width)&&(s={width:0,height:0})}else s=this.htmlGetBBox();if(h.isSVG&&(t=s.width,h=s.height,x&&(s.height=h={"11px,17":14,"13px,20":16}[p&&p.fontSize+","+Math.round(h)]||h),i&&(s.width=Math.abs(h*Math.sin(o))+Math.abs(t*Math.cos(o)),s.height=Math.abs(h*Math.cos(o))+Math.abs(t*Math.sin(o)))),a&&0<s.height){for(;250<m.length;)delete g[m.shift()];g[a]||m.push(a),g[a]=s}}return s},show:function(t){return this.attr({visibility:t?"inherit":"visible"})},hide:function(){return this.attr({visibility:"hidden"})},fadeOut:function(t){var e=this;e.animate({opacity:0},{duration:t||150,complete:function(){e.attr({y:-9999})}})},add:function(t){var e,i=this.renderer,s=this.element;return t&&(this.parentGroup=t),this.parentInverted=t&&t.inverted,void 0!==this.textStr&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(s),this.onAdd&&this.onAdd(),this},safeRemoveChild:function(t){var e=t.parentNode;e&&e.removeChild(t)},destroy:function(){var t=this,e=t.element||{},i=t.renderer,s=i.isSVG&&"SPAN"===e.nodeName&&t.parentGroup,o=e.ownerSVGElement,n=t.clipPath;if(e.onclick=e.onmouseout=e.onmouseover=e.onmousemove=e.point=null,L(t),n&&o&&([].forEach.call(o.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){-1<t.getAttribute("clip-path").indexOf(n.element.id)&&t.removeAttribute("clip-path")}),t.clipPath=n.destroy()),t.stops){for(o=0;o<t.stops.length;o++)t.stops[o]=t.stops[o].destroy();t.stops=null}for(t.safeRemoveChild(e),i.styledMode||t.destroyShadows();s&&s.div&&0===s.div.childNodes.length;)e=s.parentGroup,t.safeRemoveChild(s.div),delete s.div,s=e;return t.alignTo&&g(i.alignedObjects,t),A(t,function(e,i){delete t[i]}),null},shadow:function(t,e,i){var s,o,r,a,h,l,c=[],d=this.element;if(t){if(!this.shadows){for(a=T(t.width,3),h=(t.opacity||.15)/a,l=this.parentInverted?"(-1,-1)":"("+T(t.offsetX,1)+", "+T(t.offsetY,1)+")",s=1;s<=a;s++)o=d.cloneNode(0),r=2*a+1-2*s,n(o,{stroke:t.color||"#000000","stroke-opacity":h*s,"stroke-width":r,transform:"translate"+l,fill:"none"}),o.setAttribute("class",(o.getAttribute("class")||"")+" highcharts-shadow"),i&&(n(o,"height",Math.max(n(o,"height")-r,0)),o.cutHeight=r),e?e.element.appendChild(o):d.parentNode&&d.parentNode.insertBefore(o,d),c.push(o);this.shadows=c}}else this.destroyShadows();return this},destroyShadows:function(){(this.shadows||[]).forEach(function(t){this.safeRemoveChild(t)},this),this.shadows=void 0},xGetter:function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},_defaultGetter:function(t){return t=T(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0),/^[\-0-9\.]+$/.test(t)&&(t=parseFloat(t)),t},dSetter:function(t,e,i){t&&t.join&&(t=t.join(" ")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},dashstyleSetter:function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t=t&&t.toLowerCase()){for(e=(t=t.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",")).length;e--;)t[e]=P(t[e])*i;t=t.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},alignSetter:function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},opacitySetter:function(t,e,i){this[e]=t,i.setAttribute(e,t)},titleSetter:function(t){var e=this.element.getElementsByTagName("title")[0];e||(e=u.createElementNS(this.SVG_NS,"title"),this.element.appendChild(e)),e.firstChild&&e.removeChild(e.firstChild),e.appendChild(u.createTextNode(String(T(t),"").replace(/<[^>]*>/g,"").replace(/&lt;/g,"<").replace(/&gt;/g,">")))},textSetter:function(t){t!==this.textStr&&(delete this.bBox,this.textStr=t,this.added&&this.renderer.buildText(this))},setTextPath:function(e,i){var s,o,n=this.element,r={textAnchor:"text-anchor"},a=!1,h=this.textPathWrapper,l=!h;if(s=(i=w(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},i)).attributes,e&&i&&i.enabled){if(this.options&&this.options.padding&&(s.dx=-this.options.padding),h||(this.textPathWrapper=h=this.renderer.createElement("textPath"),a=!0),o=h.element,(i=e.element.getAttribute("id"))||e.element.setAttribute("id",i=t.uniqueKey()),l)for(e=n.getElementsByTagName("tspan");e.length;)e[0].setAttribute("y",0),o.appendChild(e[0]);a&&h.add({element:this.text?this.text.element:n}),o.setAttributeNS("http://www.w3.org/1999/xlink","href",this.renderer.url+"#"+i),c(s.dy)&&(o.parentNode.setAttribute("dy",s.dy),delete s.dy),c(s.dx)&&(o.parentNode.setAttribute("dx",s.dx),delete s.dx),t.objectEach(s,function(t,e){o.setAttribute(r[e]||e,t)}),n.removeAttribute("transform"),this.removeTextOutline.call(h,[].slice.call(n.getElementsByTagName("tspan"))),this.applyTextOutline=this.updateTransform=S}else h&&(delete this.updateTransform,delete this.applyTextOutline,this.destroyTextPath(n,e));return this},destroyTextPath:function(t,e){var i;for(e.element.setAttribute("id",""),i=this.textPathWrapper.element.childNodes;i.length;)t.firstChild.appendChild(i[0]);t.firstChild.removeChild(this.textPathWrapper.element),delete e.textPathWrapper},fillSetter:function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},visibilitySetter:function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},zIndexSetter:function(t,e){var i,s,o,n,r=this.renderer,a=this.parentGroup,h=(a||r).element||r.box,l=this.element;r=h===r.box;if(i=this.added,c(t)?(l.setAttribute("data-z-index",t),t=+t,this[e]===t&&(i=!1)):c(this[e])&&l.removeAttribute("data-z-index"),this[e]=t,i){for((t=this.zIndex)&&a&&(a.handleZ=!0),n=(e=h.childNodes).length-1;0<=n&&!s;n--)i=(a=e[n]).getAttribute("data-z-index"),o=!c(i),a!==l&&(0>t&&o&&!r&&!n?(h.insertBefore(l,e[n]),s=!0):(P(i)<=t||o&&(!c(t)||0<=t))&&(h.insertBefore(l,e[n+1]||null),s=!0));s||(h.insertBefore(l,e[r?3:0]||null),s=!0)}return s},_defaultSetter:function(t,e,i){i.setAttribute(e,t)}}),e.prototype.yGetter=e.prototype.xGetter,e.prototype.translateXSetter=e.prototype.translateYSetter=e.prototype.rotationSetter=e.prototype.verticalAlignSetter=e.prototype.rotationOriginXSetter=e.prototype.rotationOriginYSetter=e.prototype.scaleXSetter=e.prototype.scaleYSetter=e.prototype.matrixSetter=function(t,e){this[e]=t,this.doTransform=!0},e.prototype["stroke-widthSetter"]=e.prototype.strokeSetter=function(t,i,s){this[i]=t,this.stroke&&this["stroke-width"]?(e.prototype.fillSetter.call(this,this.stroke,"stroke",s),s.setAttribute("stroke-width",this["stroke-width"]),this.hasStroke=!0):"stroke-width"===i&&0===t&&this.hasStroke&&(s.removeAttribute("stroke"),this.hasStroke=!1)},i=t.SVGRenderer=function(){this.init.apply(this,arguments)},f(i.prototype,{Element:e,SVG_NS:D,init:function(t,e,i,o,r,a,l){var c,d;c=this.createElement("svg").attr({version:"1.1","class":"highcharts-root"}),l||c.css(this.getStyle(o)),o=c.element,t.appendChild(o),n(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&n(o,"xmlns",this.SVG_NS),this.isSVG=!0,this.box=o,this.boxWrapper=c,this.alignedObjects=[],this.url=(v||k)&&u.getElementsByTagName("base").length?B.location.href.split("#")[0].replace(/<[^>]*>/g,"").replace(/([\('\)])/g,"\\$1").replace(/ /g,"%20"):"",this.createElement("desc").add().element.appendChild(u.createTextNode("Created with Highcharts 7.1.1")),this.defs=this.createElement("defs").add(),this.allowHTML=a,this.forExport=r,this.styledMode=l,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.setSize(e,i,!1),v&&t.getBoundingClientRect&&((e=function(){h(t,{left:0,top:0}),d=t.getBoundingClientRect(),h(t,{left:Math.ceil(d.left)-d.left+"px",top:Math.ceil(d.top)-d.top+"px"})})(),this.unSubPixelFix=s(B,"resize",e))},definition:function(t){function e(t,s){var o;return C(t).forEach(function(t){var n=i.createElement(t.tagName),r={};A(t,function(t,e){"tagName"!==e&&"children"!==e&&"textContent"!==e&&(r[e]=t)}),n.attr(r),n.add(s||i.defs),t.textContent&&n.element.appendChild(u.createTextNode(t.textContent)),e(t.children||[],n),o=n}),o}var i=this;return e(t)},getStyle:function(t){return this.style=f({fontFamily:'"Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif',fontSize:"12px"},t)},setStyle:function(t){this.boxWrapper.css(this.getStyle(t))},isHidden:function(){return!this.boxWrapper.getBBox().width},destroy:function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),p(this.gradients||{}),this.gradients=null,t&&(this.defs=t.destroy()),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null},createElement:function(t){var e=new this.Element;return e.init(this,t),e},draw:S,getRadialAttr:function(t,e){return{cx:t[0]-t[2]/2+e.cx*t[2],cy:t[1]-t[2]/2+e.cy*t[2],r:e.r*t[2]}},truncate:function(t,e,i,s,o,n,r){var a,h,l,c=this,d=t.rotation,p=s?1:0,f=(i||s).length,g=f,m=[],x=function(t){
e.firstChild&&e.removeChild(e.firstChild),t&&e.appendChild(u.createTextNode(t))},v=function(n,a){if(void 0===m[a=a||n])if(e.getSubStringLength)try{m[a]=o+e.getSubStringLength(0,s?a+1:a)}catch(h){}else c.getSpanWidth&&(x(r(i||s,n)),m[a]=o+c.getSpanWidth(t,e));return m[a]};if(t.rotation=0,h=v(e.textContent.length),l=o+h>n){for(;p<=f;)g=Math.ceil((p+f)/2),s&&(a=r(s,g)),h=v(g,a&&a.length-1),p===f?p=f+1:h>n?f=g-1:p=g;0===f?x(""):i&&f===i.length-1||x(a||r(i||s,g))}return s&&s.splice(0,g),t.actualWidth=h,t.rotation=d,l},escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},buildText:function(t){var e,i,s,o=t.element,r=this,a=r.forExport,l=T(t.textStr,"").toString(),c=-1!==l.indexOf("<"),d=o.childNodes,p=n(o,"x"),f=t.styles,g=t.textWidth,m=f&&f.lineHeight,x=f&&f.textOutline,v=f&&"ellipsis"===f.textOverflow,y=f&&"nowrap"===f.whiteSpace,b=f&&f.fontSize,M=d.length,k=(f=g&&!t.added&&this.box,function(t){var e;return r.styledMode||(e=/(px|em)$/.test(t&&t.style.fontSize)?t.style.fontSize:b||r.style.fontSize||12),m?P(m):r.fontMetrics(e,t.getAttribute("style")?t:o).h}),w=function(t,e){return A(r.escapes,function(i,s){e&&-1!==e.indexOf(i)||(t=t.toString().replace(new RegExp(i,"g"),s))}),t},S=function(t,e){var i;if(i=t.indexOf("<"),-1!==(i=(t=t.substring(i,t.indexOf(">")-i)).indexOf(e+"="))&&(i=i+e.length+1,'"'===(e=t.charAt(i))||"'"===e))return(t=t.substring(i+1)).substring(0,t.indexOf(e))};if((i=[l,v,y,m,x,b,g].join())!==t.textCache){for(t.textCache=i;M--;)o.removeChild(d[M]);c||x||v||g||-1!==l.indexOf(" ")?(f&&f.appendChild(o),c?l=(l=r.styledMode?l.replace(/<(b|strong)>/g,'<span class="highcharts-strong">').replace(/<(i|em)>/g,'<span class="highcharts-emphasized">'):l.replace(/<(b|strong)>/g,'<span style="font-weight:bold">').replace(/<(i|em)>/g,'<span style="font-style:italic">')).replace(/<a/g,"<span").replace(/<\/(b|strong|i|em|a)>/g,"</span>").split(/<br.*?>/g):l=[l],(l=l.filter(function(t){return""!==t})).forEach(function(i,l){var c,d=0,f=0;i=i.replace(/^\s+|\s+$/g,"").replace(/<span/g,"|||<span").replace(/<\/span>/g,"</span>|||"),(c=i.split("|||")).forEach(function(i){if(""!==i||1===c.length){var m,x,M={},A=u.createElementNS(r.SVG_NS,"tspan");if((m=S(i,"class"))&&n(A,"class",m),(m=S(i,"style"))&&(m=m.replace(/(;| |^)color([ :])/,"$1fill$2"),n(A,"style",m)),(x=S(i,"href"))&&!a&&(n(A,"onclick",'location.href="'+x+'"'),n(A,"class","highcharts-anchor"),r.styledMode||h(A,{cursor:"pointer"}))," "!==(i=w(i.replace(/<[a-zA-Z\/](.|\n)*?>/g,"")||" "))){if(A.appendChild(u.createTextNode(i)),d?M.dx=0:l&&null!==p&&(M.x=p),n(A,M),o.appendChild(A),!d&&s&&(!O&&a&&h(A,{display:"block"}),n(A,"dy",k(A))),g){var T=i.replace(/([^\^])-/g,"$1- ").split(" ");M=!y&&(1<c.length||l||1<T.length);x=0;var P=k(A);if(v)e=r.truncate(t,A,i,void 0,0,Math.max(0,g-parseInt(b||12,10)),function(t,e){return t.substring(0,e)+"\u2026"});else if(M)for(;T.length;)T.length&&!y&&0<x&&(A=u.createElementNS(D,"tspan"),n(A,{dy:P,x:p}),m&&n(A,"style",m),A.appendChild(u.createTextNode(T.join(" ").replace(/- /g,"-"))),o.appendChild(A)),r.truncate(t,A,null,T,0===x?f:0,g,function(t,e){return T.slice(0,e).join(" ").replace(/- /g,"-")}),f=t.actualWidth,x++}d++}}}),s=s||o.childNodes.length}),v&&e&&t.attr("title",w(t.textStr,["&lt;","&gt;"])),f&&f.removeChild(o),x&&t.applyTextOutline&&t.applyTextOutline(x)):o.appendChild(u.createTextNode(w(l)))}},getContrast:function(t){return(t=a(t).rgba)[0]*=1,t[1]*=1.2,t[2]*=.5,459<t[0]+t[1]+t[2]?"#000000":"#FFFFFF"},button:function(t,e,i,o,n,r,a,h,l,c){var d,p,u,g,m=this.label(t,e,i,l,null,null,c,null,"button"),x=0,v=this.styledMode;(m.attr(w({padding:8,r:2},n)),v)||(n=w({fill:"#f7f7f7",stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontWeight:"normal"}},n),d=n.style,delete n.style,r=w(n,{fill:"#e6e6e6"},r),p=r.style,delete r.style,a=w(n,{fill:"#e6ebf5",style:{color:"#000000",fontWeight:"bold"}},a),u=a.style,delete a.style,h=w(n,{style:{color:"#cccccc"}},h),g=h.style,delete h.style);return s(m.element,y?"mouseover":"mouseenter",function(){3!==x&&m.setState(1)}),s(m.element,y?"mouseout":"mouseleave",function(){3!==x&&m.setState(x)}),m.setState=function(t){1!==t&&(m.state=x=t),m.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t||0]),v||m.attr([n,r,a,h][t||0]).css([d,p,u,g][t||0])},v||m.attr(n).css(f({cursor:"default"},d)),m.on("click",function(t){3!==x&&o.call(m,t)})},crispLine:function(t,e){return t[1]===t[4]&&(t[1]=t[4]=Math.round(t[1])-e%2/2),t[2]===t[5]&&(t[2]=t[5]=Math.round(t[2])+e%2/2),t},path:function(t){var e=this.styledMode?{}:{fill:"none"};return x(t)?e.d=t:b(t)&&f(e,t),this.createElement("path").attr(e)},circle:function(t,e,i){return t=b(t)?t:void 0===t?{}:{x:t,y:e,r:i},(e=this.createElement("circle")).xSetter=e.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},e.attr(t)},arc:function(t,e,i,s,o,n){return b(t)?(e=(s=t).y,i=s.r,t=s.x):s={innerR:s,start:o,end:n},(t=this.symbol("arc",t,e,i,i,s)).r=i,t},rect:function(t,e,i,s,o,r){o=b(t)?t.r:o;var a=this.createElement("rect");return t=b(t)?t:void 0===t?{}:{x:t,y:e,width:Math.max(i,0),height:Math.max(s,0)},this.styledMode||(void 0!==r&&(t.strokeWidth=r,t=a.crisp(t)),t.fill="none"),o&&(t.r=o),a.rSetter=function(t,e,i){a.r=t,n(i,{rx:t,ry:t})},a.rGetter=function(){return a.r},a.attr(t)},setSize:function(t,e,i){var s=this.alignedObjects,o=s.length;for(this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:T(i,!0)?void 0:0});o--;)s[o].align()},g:function(t){var e=this.createElement("g");return t?e.attr({"class":"highcharts-"+t}):e},image:function(t,e,i,o,n,r){var a,h={preserveAspectRatio:"none"},l=function(t,e){t.setAttributeNS?t.setAttributeNS("http://www.w3.org/1999/xlink","href",e):t.setAttribute("hc-svg-href",e)},c=function(e){l(a.element,t),r.call(a,e)};return 1<arguments.length&&f(h,{x:e,y:i,width:o,height:n}),a=this.createElement("image").attr(h),r?(l(a.element,"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="),h=new B.Image,s(h,"load",c),h.src=t,h.complete&&c({})):l(a.element,t),a},symbol:function(t,e,i,s,o,n){var a,d,p,g=this,m=/^url\((.*?)\)$/,x=m.test(t),v=!x&&(this.symbols[t]?t:"circle"),y=v&&this.symbols[v],b=c(e)&&y&&y.call(this.symbols,Math.round(e),Math.round(i),s,o,n);return y?(a=this.path(b),g.styledMode||a.attr("fill","none"),f(a,{symbolName:v,x:e,y:i,width:s,height:o}),n&&f(a,n)):x&&(d=t.match(m)[1],(a=this.image(d)).imgwidth=T(I[d]&&I[d].width,n&&n.width),a.imgheight=T(I[d]&&I[d].height,n&&n.height),p=function(){a.attr({width:a.width,height:a.height})},["width","height"].forEach(function(t){a[t+"Setter"]=function(t,e){var i={},s=this["img"+e],o="width"===e?"translateX":"translateY";this[e]=t,c(s)&&(n&&"within"===n.backgroundSize&&this.width&&this.height&&(s=Math.round(s*Math.min(this.width/this.imgwidth,this.height/this.imgheight))),this.element&&this.element.setAttribute(e,s),this.alignByTranslate||(i[o]=((this[e]||0)-s)/2,this.attr(i)))}}),c(e)&&a.attr({x:e,y:i}),a.isImg=!0,c(a.imgwidth)&&c(a.imgheight)?p():(a.attr({width:0,height:0}),l("img",{onload:function(){var t=r[g.chartIndex];0===this.width&&(h(this,{position:"absolute",top:"-999em"}),u.body.appendChild(this)),I[d]={width:this.width,height:this.height},a.imgwidth=this.width,a.imgheight=this.height,a.element&&p(),this.parentNode&&this.parentNode.removeChild(this),g.imgCount--,!g.imgCount&&t&&t.onload&&t.onload()},src:d}),this.imgCount++)),a},symbols:{circle:function(t,e,i,s){return this.arc(t+i/2,e+s/2,i/2,s/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},square:function(t,e,i,s){return["M",t,e,"L",t+i,e,t+i,e+s,t,e+s,"Z"]},triangle:function(t,e,i,s){return["M",t+i/2,e,"L",t+i,e+s,t,e+s,"Z"]},"triangle-down":function(t,e,i,s){return["M",t,e,"L",t+i,e,t+i/2,e+s,"Z"]},diamond:function(t,e,i,s){return["M",t+i/2,e,"L",t+i,e+s/2,t+i/2,e+s,t,e+s/2,"Z"]},arc:function(t,e,i,s,o){var n=o.start,r=o.r||i,a=o.r||s||i,h=o.end-.001;i=o.innerR,s=T(o.open,.001>Math.abs(o.end-o.start-2*Math.PI));var l=Math.cos(n),d=Math.sin(n),p=Math.cos(h);h=Math.sin(h);return o=["M",t+r*l,e+a*d,"A",r,a,0,n=.001>o.end-n-Math.PI?0:1,T(o.clockwise,1),t+r*p,e+a*h],c(i)&&o.push(s?"M":"L",t+i*p,e+i*h,"A",i,i,0,n,0,t+i*l,e+i*d),o.push(s?"":"Z"),o},callout:function(t,e,i,s,o){var n,r=Math.min(o&&o.r||0,i,s),a=r+6,h=o&&o.anchorX;return o=o&&o.anchorY,n=["M",t+r,e,"L",t+i-r,e,"C",t+i,e,t+i,e,t+i,e+r,"L",t+i,e+s-r,"C",t+i,e+s,t+i,e+s,t+i-r,e+s,"L",t+r,e+s,"C",t,e+s,t,e+s,t,e+s-r,"L",t,e+r,"C",t,e,t,e,t+r,e],h&&h>i?o>e+a&&o<e+s-a?n.splice(13,3,"L",t+i,o-6,t+i+6,o,t+i,o+6,t+i,e+s-r):n.splice(13,3,"L",t+i,s/2,h,o,t+i,s/2,t+i,e+s-r):h&&0>h?o>e+a&&o<e+s-a?n.splice(33,3,"L",t,o+6,t-6,o,t,o-6,t,e+r):n.splice(33,3,"L",t,s/2,h,o,t,s/2,t,e+r):o&&o>s&&h>t+a&&h<t+i-a?n.splice(23,3,"L",h+6,e+s,h,e+s+6,h-6,e+s,t+r,e+s):o&&0>o&&h>t+a&&h<t+i-a&&n.splice(3,3,"L",h-6,e,h,e-6,h+6,e,i-r,e),n}},clipRect:function(e,i,s,o){var n=t.uniqueKey()+"-",r=this.createElement("clipPath").attr({id:n}).add(this.defs);return(e=this.rect(e,i,s,o,0).add(r)).id=n,e.clipPath=r,e.count=0,e},text:function(t,e,i,s){var o={};return!s||!this.allowHTML&&this.forExport?(o.x=Math.round(e||0),i&&(o.y=Math.round(i)),c(t)&&(o.text=t),t=this.createElement("text").attr(o),s||(t.xSetter=function(t,e,i){var s,o,n=i.getElementsByTagName("tspan"),r=i.getAttribute(e);for(o=0;o<n.length;o++)(s=n[o]).getAttribute(e)===r&&s.setAttribute(e,t);i.setAttribute(e,t)}),t):this.html(t,e,i)},fontMetrics:function(t,i){return t=!this.styledMode&&/px/.test(t)||!B.getComputedStyle?t||i&&i.style&&i.style.fontSize||this.style&&this.style.fontSize:i&&e.prototype.getStyle.call(i,"font-size"),{h:i=24>(t=/px/.test(t)?P(t):12)?t+3:Math.round(1.2*t),b:Math.round(.8*i),f:t}},rotCorr:function(t,e,i){var s=t;return e&&i&&(s=Math.max(s*Math.cos(e*d),4)),{x:-t/3*Math.sin(e*d),y:s}},label:function(i,s,o,n,r,a,h,l,d){var p,u,g,m,x,v,y,b,M,k,S,A,T=this,P=T.styledMode,C=T.g("button"!==d&&"label"),L=C.text=T.text("",0,0,h).attr({zIndex:1}),O=0,D=3,I=0,B={},z=/^url\((.*?)\)$/.test(n),R=P||z,G=function(){return P?p.strokeWidth()%2/2:(b?parseInt(b,10):0)%2/2};d&&C.addClass("highcharts-"+d),k=function(){var t=L.element.style,e={};u=(void 0===g||void 0===m||y)&&c(L.textStr)&&L.getBBox(),C.width=(g||u.width||0)+2*D+I,C.height=(m||u.height||0)+2*D,M=D+Math.min(T.fontMetrics(t&&t.fontSize,L).b,u?u.height:Infinity),R&&(p||(C.box=p=T.symbols[n]||z?T.symbol(n):T.rect(),p.addClass(("button"===d?"":"highcharts-label-box")+(d?" highcharts-"+d+"-box":"")),p.add(C),t=G(),e.x=t,e.y=(l?-M:0)+t),e.width=Math.round(C.width),e.height=Math.round(C.height),p.attr(f(e,B)),B={})},S=function(){var t,e=I+D;t=l?0:M,c(g)&&u&&("center"===y||"right"===y)&&(e+={center:.5,right:1}[y]*(g-u.width)),e===L.x&&t===L.y||(L.attr("x",e),L.hasBoxWidthChanged&&(u=L.getBBox(!0),k()),void 0!==t&&L.attr("y",t)),L.x=e,L.y=t},A=function(t,e){p?p.attr(t,e):B[t]=e},C.onAdd=function(){L.add(C),C.attr({text:i||0===i?i:"",x:s,y:o}),p&&c(r)&&C.attr({anchorX:r,anchorY:a})},C.widthSetter=function(e){g=t.isNumber(e)?e:null},C.heightSetter=function(t){m=t},C["text-alignSetter"]=function(t){y=t},C.paddingSetter=function(t){c(t)&&t!==D&&(D=C.padding=t,S())},C.paddingLeftSetter=function(t){c(t)&&t!==I&&(I=t,S())},C.alignSetter=function(t){(t={left:0,center:.5,right:1}[t])!==O&&(O=t,u&&C.attr({x:x}))},C.textSetter=function(t){void 0!==t&&L.attr({text:t}),k(),S()},C["stroke-widthSetter"]=function(t,e){t&&(R=!0),b=this["stroke-width"]=t,A(e,t)},P?C.rSetter=function(t,e){A(e,t)}:C.strokeSetter=C.fillSetter=C.rSetter=function(t,e){"r"!==e&&("fill"===e&&t&&(R=!0),C[e]=t),A(e,t)},C.anchorXSetter=function(t,e){r=C.anchorX=t,A(e,Math.round(t)-G()-x)},C.anchorYSetter=function(t,e){a=C.anchorY=t,A(e,t-v)},C.xSetter=function(t){C.x=t,O&&(t-=O*((g||u.width)+2*D),C["forceAnimate:x"]=!0),x=Math.round(t),C.attr("translateX",x)},C.ySetter=function(t){v=C.y=Math.round(t),C.attr("translateY",v)};var W=C.css;return h={css:function(t){if(t){var e={};t=w(t),C.textProps.forEach(function(i){void 0!==t[i]&&(e[i]=t[i],delete t[i])}),L.css(e),"width"in e&&k(),"fontSize"in e&&(k(),S())}return W.call(C,t)},getBBox:function(){return{width:u.width+2*D,height:u.height+2*D,x:u.x-D,y:u.y-D}},destroy:function(){E(C.element,"mouseenter"),E(C.element,"mouseleave"),L&&(L=L.destroy()),p&&(p=p.destroy()),e.prototype.destroy.call(C),C=T=k=S=A=null}},P||(h.shadow=function(t){return t&&(k(),p&&p.shadow(t)),C}),f(C,h)}}),t.Renderer=i}),e(i,"parts/Html.js",[i["parts/Globals.js"]],function(t){var e=t.attr,i=t.createElement,s=t.css,o=t.defined,n=t.extend,r=t.isFirefox,a=t.isMS,h=t.isWebKit,l=t.pick,c=t.pInt,d=t.SVGElement,p=t.SVGRenderer,u=t.win;n(d.prototype,{htmlCss:function(t){var e,i="SPAN"===this.element.tagName&&t&&"width"in t,o=l(i&&t.width,void 0);return i&&(delete t.width,this.textWidth=o,e=!0),t&&"ellipsis"===t.textOverflow&&(t.whiteSpace="nowrap",t.overflow="hidden"),this.styles=n(this.styles,t),s(this.element,t),e&&this.htmlUpdateTransform(),this},htmlGetBBox:function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},htmlUpdateTransform:function(){if(this.added){var t=this.renderer,e=this.element,i=this.translateX||0,n=this.translateY||0,r=this.x||0,a=this.y||0,h=this.textAlign||"left",l={left:0,center:.5,right:1}[h],d=(u=this.styles)&&u.whiteSpace;if(s(e,{marginLeft:i,marginTop:n}),!t.styledMode&&this.shadows&&this.shadows.forEach(function(t){s(t,{marginLeft:i+1,marginTop:n+1})}),this.inverted&&[].forEach.call(e.childNodes,function(i){t.invertChild(i,e)}),"SPAN"===e.tagName){var p,u=this.rotation,f=this.textWidth&&c(this.textWidth),g=[u,h,e.innerHTML,this.textWidth,this.textAlign].join();(p=f!==this.oldTextWidth)&&!(p=f>this.oldTextWidth)&&((p=this.textPxLength)||(s(e,{width:"",whiteSpace:d||"nowrap"}),p=e.offsetWidth),p=p>f),p&&(/[ \-]/.test(e.textContent||e.innerText)||"ellipsis"===e.style.textOverflow)?(s(e,{width:f+"px",display:"block",whiteSpace:d||"normal"}),this.oldTextWidth=f,this.hasBoxWidthChanged=!0):this.hasBoxWidthChanged=!1,g!==this.cTT&&(d=t.fontMetrics(e.style.fontSize,e).b,!o(u)||u===(this.oldRotation||0)&&h===this.oldAlign||this.setSpanRotation(u,l,d),this.getSpanCorrection(!o(u)&&this.textPxLength||e.offsetWidth,d,l,u,h)),s(e,{left:r+(this.xCorr||0)+"px",top:a+(this.yCorr||0)+"px"}),this.cTT=g,this.oldRotation=u,this.oldAlign=h}}else this.alignOnAdd=!0},setSpanRotation:function(t,e,i){var o={},n=this.renderer.getTransformKey();o[n]=o.transform="rotate("+t+"deg)",o[n+(r?"Origin":"-origin")]=o.transformOrigin=100*e+"% "+i+"px",s(this.element,o)},getSpanCorrection:function(t,e,i){this.xCorr=-t*i,this.yCorr=-e}}),n(p.prototype,{getTransformKey:function(){return a&&!/Edge/.test(u.navigator.userAgent)?"-ms-transform":h?"-webkit-transform":r?"MozTransform":u.opera?"-o-transform":""},html:function(s,o,r){var a=this.createElement("span"),h=a.element,c=a.renderer,p=c.isSVG,u=function(t,e){["opacity","visibility"].forEach(function(i){t[i+"Setter"]=function(s,o,n){var r=t.div?t.div.style:e;d.prototype[i+"Setter"].call(this,s,o,n),r&&(r[o]=s)}}),t.addedSetters=!0},f=(f=t.charts[c.chartIndex])&&f.styledMode;return a.textSetter=function(t){t!==h.innerHTML&&(delete this.bBox,delete this.oldTextWidth),this.textStr=t,h.innerHTML=l(t,""),a.doTransform=!0},p&&u(a,a.element.style),a.xSetter=a.ySetter=a.alignSetter=a.rotationSetter=function(t,e){"align"===e&&(e="textAlign"),a[e]=t,a.doTransform=!0},a.afterSetters=function(){this.doTransform&&(this.htmlUpdateTransform(),this.doTransform=!1)},a.attr({text:s,x:Math.round(o),y:Math.round(r)}).css({position:"absolute"}),f||a.css({fontFamily:this.style.fontFamily,fontSize:this.style.fontSize}),h.style.whiteSpace="nowrap",a.css=a.htmlCss,p&&(a.add=function(t){var s,o=c.box.parentNode,r=[];if(this.parentGroup=t){if(!(s=t.div)){for(;t;)r.push(t),t=t.parentGroup;r.reverse().forEach(function(t){function h(e,i){t[i]=e,"translateX"===i?l.left=e+"px":l.top=e+"px",t.doTransform=!0}var l,c=e(t.element,"class");c&&(c={className:c}),s=t.div=t.div||i("div",c,{position:"absolute",left:(t.translateX||0)+"px",top:(t.translateY||0)+"px",display:t.display,opacity:t.opacity,pointerEvents:t.styles&&t.styles.pointerEvents},s||o),l=s.style,n(t,{classSetter:function(t){return function(e){this.element.setAttribute("class",e),t.className=e}}(s),on:function(){return r[0].div&&a.on.apply({element:r[0].div},arguments),t},translateXSetter:h,translateYSetter:h}),t.addedSetters||u(t)})}}else s=o;return s.appendChild(h),a.added=!0,a.alignOnAdd&&a.htmlUpdateTransform(),a}),a}})}),e(i,"parts/Time.js",[i["parts/Globals.js"]],function(t){var e=t.defined,i=t.extend,s=t.merge,o=t.pick,n=t.timeUnits,r=t.win;t.Time=function(t){this.update(t,!1)},t.Time.prototype={defaultOptions:{},update:function(t){var e=o(t&&t.useUTC,!0),i=this;this.options=t=s(!0,this.options||{},t),this.Date=t.Date||r.Date||Date,this.timezoneOffset=(this.useUTC=e)&&t.timezoneOffset,this.getTimezoneOffset=this.timezoneOffsetFunction(),(this.variableTimezone=!(e&&!t.getTimezoneOffset&&!t.timezone))||this.timezoneOffset?(this.get=function(t,e){var s=e.getTime(),o=s-i.getTimezoneOffset(e);return e.setTime(o),t=e["getUTC"+t](),e.setTime(s),t},this.set=function(t,e,s){var o;"Milliseconds"===t||"Seconds"===t||"Minutes"===t&&0==e.getTimezoneOffset()%60?e["set"+t](s):(o=i.getTimezoneOffset(e),o=e.getTime()-o,e.setTime(o),e["setUTC"+t](s),t=i.getTimezoneOffset(e),o=e.getTime()+t,e.setTime(o))}):e?(this.get=function(t,e){return e["getUTC"+t]()},this.set=function(t,e,i){return e["setUTC"+t](i)}):(this.get=function(t,e){return e["get"+t]()},this.set=function(t,e,i){return e["set"+t](i)})},makeTime:function(e,i,s,n,r,a){var h,l,c;return this.useUTC?(h=this.Date.UTC.apply(0,arguments),h+=l=this.getTimezoneOffset(h),l!==(c=this.getTimezoneOffset(h))?h+=c-l:l-36e5!==this.getTimezoneOffset(h-36e5)||t.isSafari||(h-=36e5)):h=new this.Date(e,i,o(s,1),o(n,0),o(r,0),o(a,0)).getTime(),h},timezoneOffsetFunction:function(){var e=this,i=this.options,s=r.moment;if(!this.useUTC)return function(t){return 6e4*new Date(t).getTimezoneOffset()};if(i.timezone){if(s)return function(t){return 6e4*-s.tz(t,i.timezone).utcOffset()};t.error(25)}return this.useUTC&&i.getTimezoneOffset?function(t){return 6e4*i.getTimezoneOffset(t)}:function(){return 6e4*(e.timezoneOffset||0)}},dateFormat:function(e,i,s){if(!t.defined(i)||isNaN(i))return t.defaultOptions.lang.invalidDate||"";e=t.pick(e,"%Y-%m-%d %H:%M:%S");var o=this,n=new this.Date(i),r=this.get("Hours",n),a=this.get("Day",n),h=this.get("Date",n),l=this.get("Month",n),c=this.get("FullYear",n),d=t.defaultOptions.lang,p=d.weekdays,u=d.shortWeekdays,f=t.pad;n=t.extend({a:u?u[a]:p[a].substr(0,3),A:p[a],d:f(h),e:f(h,2," "),w:a,b:d.shortMonths[l],B:d.months[l],m:f(l+1),o:l+1,y:c.toString().substr(2,2),Y:c,H:f(r),k:r,I:f(r%12||12),l:r%12||12,M:f(o.get("Minutes",n)),p:12>r?"AM":"PM",P:12>r?"am":"pm",S:f(n.getSeconds()),L:f(Math.floor(i%1e3),3)},t.dateFormats);return t.objectEach(n,function(t,s){for(;-1!==e.indexOf("%"+s);)e=e.replace("%"+s,"function"==typeof t?t.call(o,i):t)}),s?e.substr(0,1).toUpperCase()+e.substr(1):e},resolveDTLFormat:function(e){return t.isObject(e,!0)?e:{main:(e=t.splat(e))[0],from:e[1],to:e[2]}},getTimeTicks:function(t,s,r,a){var h,l,c=this,d=[],p={};h=new c.Date(s);var u,f=t.unitRange,g=t.count||1;if(a=o(a,1),e(s)){c.set("Milliseconds",h,f>=n.second?0:g*Math.floor(c.get("Milliseconds",h)/g)),f>=n.second&&c.set("Seconds",h,f>=n.minute?0:g*Math.floor(c.get("Seconds",h)/g)),f>=n.minute&&c.set("Minutes",h,f>=n.hour?0:g*Math.floor(c.get("Minutes",h)/g)),f>=n.hour&&c.set("Hours",h,f>=n.day?0:g*Math.floor(c.get("Hours",h)/g)),f>=n.day&&c.set("Date",h,f>=n.month?1:Math.max(1,g*Math.floor(c.get("Date",h)/g))),f>=n.month&&(c.set("Month",h,f>=n.year?0:g*Math.floor(c.get("Month",h)/g)),l=c.get("FullYear",h)),f>=n.year&&c.set("FullYear",h,l-l%g),f===n.week&&(l=c.get("Day",h),c.set("Date",h,c.get("Date",h)-l+a+(l<a?-7:0))),l=c.get("FullYear",h),a=c.get("Month",h);var m=c.get("Date",h),x=c.get("Hours",h);for(s=h.getTime(),c.variableTimezone&&(u=r-s>4*n.month||c.getTimezoneOffset(s)!==c.getTimezoneOffset(r)),s=h.getTime(),h=1;s<r;)d.push(s),s=f===n.year?c.makeTime(l+h*g,0):f===n.month?c.makeTime(l,a+h*g):!u||f!==n.day&&f!==n.week?u&&f===n.hour&&1<g?c.makeTime(l,a,m,x+h*g):s+f*g:c.makeTime(l,a,m+h*g*(f===n.day?1:7)),h++;d.push(s),f<=n.hour&&1e4>d.length&&d.forEach(function(t){0==t%18e5&&"000000000"===c.dateFormat("%H%M%S%L",t)&&(p[t]="day")})}return d.info=i(t,{higherRanks:p,totalRange:f*g}),d}}}),e(i,"parts/Options.js",[i["parts/Globals.js"]],function(t){var e=t.color,i=t.merge;t.defaultOptions={colors:"#7cb5ec #434348 #90ed7d #f7a35c #8085e9 #f15c80 #e4d354 #2b908f #f45b5b #91e8e1".split(" "),symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:"January February March April May June July August September October November December".split(" "),shortMonths:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),weekdays:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),decimalPoint:".",numericSymbols:"kMGTPE".split(""),resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",thousandsSep:" "},global:{},time:t.Time.prototype.defaultOptions,chart:{styledMode:!1,borderRadius:0,colorCount:10,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}},width:null,height:null,borderColor:"#335cad",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{text:"Chart title",align:"center",margin:15,widthAdjust:-44},subtitle:{text:"",align:"center",widthAdjust:-44},plotOptions:{},labels:{style:{position:"absolute",color:"#333333"}},legend:{enabled:!0,align:"center",alignColumns:!0,layout:"horizontal",labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{activeColor:"#003399",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"12px",fontWeight:"bold",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#cccccc"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:t.svg,borderRadius:3,dateTimeLabelFormats:{millisecond:"%A, %b %e, %H:%M:%S.%L",second:"%A, %b %e, %H:%M:%S",minute:"%A, %b %e, %H:%M",hour:"%A, %b %e, %H:%M",day:"%A, %b %e, %Y",week:"Week from %A, %b %e, %Y",month:"%B %Y",year:"%Y"},footerFormat:"",padding:8,snap:t.isTouchDevice?25:10,headerFormat:'<span style="font-size: 10px">{point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">\u25cf</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:e("#f7f7f7").setOpacity(.85).get(),borderWidth:1,shadow:!0,style:{color:"#333333",cursor:"default",fontSize:"12px",pointerEvents:"none",whiteSpace:"nowrap"}},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"9px"},text:"Highcharts.com"}},t.setOptions=function(e){return t.defaultOptions=i(!0,t.defaultOptions,e),t.time.update(i(t.defaultOptions.global,t.defaultOptions.time),!1),t.defaultOptions},t.getOptions=function(){return t.defaultOptions},t.defaultPlotOptions=t.defaultOptions.plotOptions,t.time=new t.Time(i(t.defaultOptions.global,t.defaultOptions.time)),t.dateFormat=function(e,i,s){return t.time.dateFormat(e,i,s)}}),e(i,"parts/Tick.js",[i["parts/Globals.js"]],function(t){var e=t.correctFloat,i=t.defined,s=t.destroyObjectProperties,o=t.fireEvent,n=t.isNumber,r=t.merge,a=t.pick,h=t.deg2rad;t.Tick=function(t,e,i,s,o){this.axis=t,this.pos=e,this.type=i||"",this.isNewLabel=this.isNew=!0,this.parameters=o||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,i||s||this.addLabel()},t.Tick.prototype={addLabel:function(){var s,o,n,h,l=this,c=l.axis,d=c.options,p=c.chart,u=c.categories,f=c.names,g=l.pos,m=a(l.options&&l.options.labels,d.labels),x=g===(b=c.tickPositions)[0],v=g===b[b.length-1],y=(u=this.parameters.category||(u?a(u[g],f[g],g):g),l.label),b=b.info;c.isDatetimeAxis&&b&&(s=(o=p.time.resolveDTLFormat(d.dateTimeLabelFormats[!d.grid&&b.higherRanks[g]||b.unitName])).main),l.isFirst=x,l.isLast=v,l.formatCtx={axis:c,chart:p,isFirst:x,isLast:v,dateTimeLabelFormat:s,tickPositionInfo:b,value:c.isLog?e(c.lin2log(u)):u,pos:g},d=c.labelFormatter.call(l.formatCtx,this.formatCtx),(h=o&&o.list)&&(l.shortenLabel=function(){for(n=0;n<h.length;n++)if(y.attr({text:c.labelFormatter.call(t.extend(l.formatCtx,{dateTimeLabelFormat:h[n]}))}),y.getBBox().width<c.getSlotWidth(l)-2*a(m.padding,5))return;y.attr({text:""})}),i(y)?y&&y.textStr!==d&&(!y.textWidth||m.style&&m.style.width||y.styles.width||y.css({width:null}),y.attr({text:d})):((l.label=y=i(d)&&m.enabled?p.renderer.text(d,0,0,m.useHTML).add(c.labelGroup):null)&&(p.styledMode||y.css(r(m.style)),y.textPxLength=y.getBBox().width),l.rotation=0)},getLabelSize:function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},handleOverflow:function(t){var e,i=this.axis,s=i.options.labels,o=t.x,n=i.chart.chartWidth,r=i.chart.spacing,l=a(i.labelLeft,Math.min(i.pos,r[3])),c=(r=a(i.labelRight,Math.max(i.isRadial?0:i.pos+i.len,n-r[1])),this.label),d=this.rotation,p={left:0,center:.5,right:1}[i.labelAlign||c.attr("align")],u=c.getBBox().width,f=i.getSlotWidth(this),g=f,m=1,x={};d||"justify"!==a(s.overflow,"justify")?0>d&&o-p*u<l?e=Math.round(o/Math.cos(d*h)-l):0<d&&o+p*u>r&&(e=Math.round((n-o)/Math.cos(d*h))):(n=o+(1-p)*u,o-p*u<l?g=t.x+g*(1-p)-l:n>r&&(g=r-t.x+g*p,m=-1),(g=Math.min(f,g))<f&&"center"===i.labelAlign&&(t.x+=m*(f-g-p*(f-Math.min(u,g)))),(u>g||i.autoRotation&&(c.styles||{}).width)&&(e=g)),e&&(this.shortenLabel?this.shortenLabel():(x.width=Math.floor(e),(s.style||{}).textOverflow||(x.textOverflow="ellipsis"),c.css(x)))},getPosition:function(e,i,s,n){var r=this.axis,a=r.chart,h=n&&a.oldChartHeight||a.chartHeight;return e={x:e?t.correctFloat(r.translate(i+s,null,null,n)+r.transB):r.left+r.offset+(r.opposite?(n&&a.oldChartWidth||a.chartWidth)-r.right-r.left:0),y:e?h-r.bottom+r.offset-(r.opposite?r.height:0):t.correctFloat(h-r.translate(i+s,null,null,n)-r.transB)},o(this,"afterGetPosition",{pos:e}),e},getLabelPosition:function(t,e,s,n,r,a,l,c){var d=this.axis,p=d.transA,u=d.reversed,f=d.staggerLines,g=d.tickRotCorr||{x:0,y:0},m=r.y,x=n||d.reserveSpaceDefault?0:-d.labelOffset*("center"===d.labelAlign?.5:1),v={};return i(m)||(m=0===d.side?s.rotation?-8:-s.getBBox().height:2===d.side?g.y+8:Math.cos(s.rotation*h)*(g.y-s.getBBox(!1,0).height/2)),t=t+r.x+x+g.x-(a&&n?a*p*(u?-1:1):0),e=e+m-(a&&!n?a*p*(u?1:-1):0),f&&(s=l/(c||1)%f,d.opposite&&(s=f-s-1),e+=d.labelOffset/f*s),v.x=t,v.y=Math.round(e),o(this,"afterGetLabelPosition",{pos:v,tickmarkOffset:a,index:l}),v},getMarkPath:function(t,e,i,s,o,n){return n.crispLine(["M",t,e,"L",t+(o?0:-i),e+(o?i:0)],s)},renderGridLine:function(t,e,i){var s=this.axis,o=s.options,n=this.gridLine,r={},h=this.pos,l=this.type,c=a(this.tickmarkOffset,s.tickmarkOffset),d=s.chart.renderer,p=l?l+"Grid":"grid",u=o[p+"LineWidth"],f=o[p+"LineColor"];o=o[p+"LineDashStyle"];n||(s.chart.styledMode||(r.stroke=f,r["stroke-width"]=u,o&&(r.dashstyle=o)),l||(r.zIndex=1),t&&(e=0),this.gridLine=n=d.path().attr(r).addClass("highcharts-"+(l?l+"-":"")+"grid-line").add(s.gridGroup)),n&&(i=s.getPlotLinePath(h+c,n.strokeWidth()*i,t,"pass"))&&n[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},renderMark:function(t,e,i){var s=this.axis,o=s.options,n=s.chart.renderer,r=this.type,h=r?r+"Tick":"tick",l=s.tickSize(h),c=this.mark,d=!c,p=t.x;t=t.y;var u=a(o[h+"Width"],!r&&s.isXAxis?1:0);o=o[h+"Color"];l&&(s.opposite&&(l[0]=-l[0]),d&&(this.mark=c=n.path().addClass("highcharts-"+(r?r+"-":"")+"tick").add(s.axisGroup),s.chart.styledMode||c.attr({stroke:o,"stroke-width":u})),c[d?"attr":"animate"]({d:this.getMarkPath(p,t,l[0],c.strokeWidth()*i,s.horiz,n),opacity:e}))},renderLabel:function(t,e,i,s){var o=(d=this.axis).horiz,r=d.options,h=this.label,l=r.labels,c=l.step,d=a(this.tickmarkOffset,d.tickmarkOffset),p=!0,u=t.x;t=t.y,h&&n(u)&&(h.xy=t=this.getLabelPosition(u,t,h,o,l,d,s,c),this.isFirst&&!this.isLast&&!a(r.showFirstLabel,1)||this.isLast&&!this.isFirst&&!a(r.showLastLabel,1)?p=!1:!o||l.step||l.rotation||e||0===i||this.handleOverflow(t),c&&s%c&&(p=!1),p&&n(t.y)?(t.opacity=i,h[this.isNewLabel?"attr":"animate"](t),this.isNewLabel=!1):(h.attr("y",-9999),this.isNewLabel=!0))},render:function(e,i,s){var o=(l=this.axis).horiz,n=this.pos,r=a(this.tickmarkOffset,l.tickmarkOffset),h=(r=(n=this.getPosition(o,n,r,i)).x,n.y),l=o&&r===l.pos+l.len||!o&&h===l.pos?-1:1;s=a(s,1),this.isActive=!0,this.renderGridLine(i,s,l),this.renderMark(n,s,l),this.renderLabel(n,i,s,e),this.isNew=!1,t.fireEvent(this,"afterRender")},destroy:function(){s(this,this.axis)}}}),e(i,"parts/Axis.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.animObject,s=t.arrayMax,o=t.arrayMin,n=t.color,r=t.correctFloat,a=t.defaultOptions,h=t.defined,l=t.deg2rad,c=t.destroyObjectProperties,d=t.extend,p=t.fireEvent,u=t.format,f=t.getMagnitude,g=t.isArray,m=t.isNumber,x=t.isString,v=t.merge,y=t.normalizeTickInterval,b=t.objectEach,M=t.pick,k=t.removeEvent,w=t.seriesTypes,S=t.splat,A=t.syncTimeout,T=t.Tick,P=function(){this.init.apply(this,arguments)};return t.extend(P.prototype,{defaultOptions:{dateTimeLabelFormats:{millisecond:{main:"%H:%M:%S.%L",range:!1},second:{main:"%H:%M:%S",range:!1},minute:{main:"%H:%M",range:!1},hour:{main:"%H:%M",range:!1},day:{main:"%e. %b"},week:{main:"%e. %b"},month:{main:"%b '%y"},year:{main:"%Y"}},endOnTick:!1,labels:{enabled:!0,indentation:10,x:0,style:{color:"#666666",cursor:"default",fontSize:"11px"}},maxPadding:.01,minorTickLength:2,minorTickPosition:"outside",minPadding:.01,showEmpty:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",style:{color:"#666666"}},type:"linear",minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#ccd6eb",lineWidth:1,gridLineColor:"#e6e6e6",tickColor:"#ccd6eb"},defaultYAxisOptions:{endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:-8},startOnTick:!0,title:{rotation:270,text:"Values"},stackLabels:{allowOverlap:!1,enabled:!1,formatter:function(){return t.numberFormat(this.total,-1)},style:{color:"#000000",fontSize:"11px",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0},defaultLeftAxisOptions:{labels:{x:-15},title:{rotation:270}},defaultRightAxisOptions:{labels:{x:15},title:{rotation:90}},defaultBottomAxisOptions:{labels:{autoRotation:[-45],x:0},margin:15,title:{rotation:0}},defaultTopAxisOptions:{labels:{autoRotation:[-45],x:0},margin:15,title:{rotation:0}},init:function(t,i){var s=i.isX,o=this;o.chart=t,o.horiz=t.inverted&&!o.isZAxis?!s:s,o.isXAxis=s,o.coll=o.coll||(s?"xAxis":"yAxis"),p(this,"init",{userOptions:i}),o.opposite=i.opposite,o.side=i.side||(o.horiz?o.opposite?0:2:o.opposite?1:3),o.setOptions(i);var n=this.options,r=n.type;o.labelFormatter=n.labels.formatter||o.defaultLabelFormatter,o.userOptions=i,o.minPixelPadding=0,o.reversed=n.reversed,o.visible=!1!==n.visible,o.zoomEnabled=!1!==n.zoomEnabled,o.hasNames="category"===r||!0===n.categories,o.categories=n.categories||o.hasNames,o.names||(o.names=[],o.names.keys={}),o.plotLinesAndBandsGroups={},o.isLog="logarithmic"===r,o.isDatetimeAxis="datetime"===r,o.positiveValuesOnly=o.isLog&&!o.allowNegativeLog,o.isLinked=h(n.linkedTo),o.ticks={},o.labelEdge=[],o.minorTicks={},o.plotLinesAndBands=[],o.alternateBands={},o.len=0,o.minRange=o.userMinRange=n.minRange||n.maxZoom,o.range=n.range,o.offset=n.offset||0,o.stacks={},o.oldStacks={},o.stacksTouched=0,o.max=null,o.min=null,o.crosshair=M(n.crosshair,S(t.options.tooltip.crosshairs)[s?0:1],!1),i=o.options.events,-1===t.axes.indexOf(o)&&(s?t.axes.splice(t.xAxis.length,0,o):t.axes.push(o),t[o.coll].push(o)),o.series=o.series||[],t.inverted&&!o.isZAxis&&s&&void 0===o.reversed&&(o.reversed=!0),b(i,function(t,i){e(o,i,t)}),o.lin2log=n.linearToLogConverter||o.lin2log,o.isLog&&(o.val2lin=o.log2lin,o.lin2val=o.lin2log),p(this,"afterInit")},setOptions:function(t){this.options=v(this.defaultOptions,"yAxis"===this.coll&&this.defaultYAxisOptions,[this.defaultTopAxisOptions,this.defaultRightAxisOptions,this.defaultBottomAxisOptions,this.defaultLeftAxisOptions][this.side],v(a[this.coll],t)),p(this,"afterSetOptions",{
userOptions:t})},defaultLabelFormatter:function(){var e,i=this.axis,s=this.value,o=i.chart.time,n=i.categories,r=this.dateTimeLabelFormat,h=(l=a.lang).numericSymbols,l=l.numericSymbolMagnitude||1e3,c=h&&h.length,d=i.options.labels.format;i=i.isLog?Math.abs(s):i.tickInterval;if(d)e=u(d,this,o);else if(n)e=s;else if(r)e=o.dateFormat(r,s);else if(c&&1e3<=i)for(;c--&&void 0===e;)i>=(o=Math.pow(l,c+1))&&0==10*s%o&&null!==h[c]&&0!==s&&(e=t.numberFormat(s/o,-1)+h[c]);return void 0===e&&(e=1e4<=Math.abs(s)?t.numberFormat(s,-1):t.numberFormat(s,-1,void 0,"")),e},getSeriesExtremes:function(){var t,e=this,i=e.chart;p(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=null,e.softThreshold=!e.isXAxis,e.buildStacks&&e.buildStacks(),e.series.forEach(function(s){if(s.visible||!i.options.chart.ignoreHiddenSeries){var o,n,r=s.options,a=r.threshold;e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=a&&(a=null),e.isXAxis?(r=s.xData).length&&(o=(t=s.getXExtremes(r)).min,n=t.max,m(o)||o instanceof Date||(r=r.filter(m),o=(t=s.getXExtremes(r)).min,n=t.max),r.length&&(e.dataMin=Math.min(M(e.dataMin,o),o),e.dataMax=Math.max(M(e.dataMax,n),n))):(s.getExtremes(),n=s.dataMax,o=s.dataMin,h(o)&&h(n)&&(e.dataMin=Math.min(M(e.dataMin,o),o),e.dataMax=Math.max(M(e.dataMax,n),n)),h(a)&&(e.threshold=a),(!r.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1))}})}),p(this,"afterGetSeriesExtremes")},translate:function(t,e,i,s,o,n){var r=this.linkedParent||this,a=1,h=0,l=s?r.oldTransA:r.transA;s=s?r.oldMin:r.min;var c=r.minPixelPadding;return o=(r.isOrdinal||r.isBroken||r.isLog&&o)&&r.lin2val,l||(l=r.transA),i&&(a*=-1,h=r.len),r.reversed&&(h-=(a*=-1)*(r.sector||r.len)),e?(t=(t*a+h-c)/l+s,o&&(t=r.lin2val(t))):(o&&(t=r.val2lin(t)),t=m(s)?a*(t-s)*l+h+a*c+(m(n)?l*n:0):void 0),t},toPixels:function(t,e){return this.translate(t,!1,!this.horiz,null,!0)+(e?0:this.pos)},toValue:function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,null,!0)},getPlotLinePath:function(t,e,i,s,o){var n,r,a,h,l,c,d=this,u=d.chart,f=d.left,g=d.top,x=i&&u.oldChartHeight||u.chartHeight,v=i&&u.oldChartWidth||u.chartWidth,y=d.transB,b=function(t,e,i){return("pass"!==s&&t<e||t>i)&&(s?t=Math.min(Math.max(e,t),i):l=!0),t};return p(this,"getPlotLinePath",c={value:t,lineWidth:e,old:i,force:s,translatedValue:o},function(c){o=M(o,d.translate(t,null,null,i)),o=Math.min(Math.max(-1e5,o),1e5),n=a=Math.round(o+y),r=h=Math.round(x-o-y),m(o)?d.horiz?(r=g,h=x-d.bottom,n=a=b(n,f,f+d.width)):(n=f,a=v-d.right,r=h=b(r,g,g+d.height)):(l=!0,s=!1),c.path=l&&!s?null:u.renderer.crispLine(["M",n,r,"L",a,h],e||1)}),c.path},getLinearTickPositions:function(t,e,i){var s,o=r(Math.floor(e/t)*t);i=r(Math.ceil(i/t)*t);var n,a=[];if(r(o+t)===o&&(n=20),this.single)return[e];for(e=o;e<=i&&(a.push(e),(e=r(e+t,n))!==s);)s=e;return a},getMinorTickInterval:function(){var t=this.options;return!0===t.minorTicks?M(t.minorTickInterval,"auto"):!1===t.minorTicks?null:t.minorTickInterval},getMinorTickPositions:function(){var t=this,e=t.options,i=t.tickPositions,s=t.minorTickInterval,o=[],n=t.pointRangePadding||0,r=t.min-n,a=(n=t.max+n)-r;if(a&&a/s<t.len/3)if(t.isLog)this.paddedTicks.forEach(function(e,i,n){i&&o.push.apply(o,t.getLogTickPositions(s,n[i-1],n[i],!0))});else if(t.isDatetimeAxis&&"auto"===this.getMinorTickInterval())o=o.concat(t.getTimeTicks(t.normalizeTimeTickInterval(s),r,n,e.startOfWeek));else for(e=r+(i[0]-r)%s;e<=n&&e!==o[0];e+=s)o.push(e);return 0!==o.length&&t.trimTicks(o),o},adjustForMinRange:function(){var t,e,i,n,r,a,l,c=this.options,d=this.min,p=this.max;this.isXAxis&&void 0===this.minRange&&!this.isLog&&(h(c.min)||h(c.max)?this.minRange=null:(this.series.forEach(function(t){for(a=t.xData,n=t.xIncrement?1:a.length-1;0<n;n--)r=a[n]-a[n-1],(void 0===i||r<i)&&(i=r)}),this.minRange=Math.min(5*i,this.dataMax-this.dataMin))),p-d<this.minRange&&(e=this.dataMax-this.dataMin>=this.minRange,t=[d-(t=((l=this.minRange)-p+d)/2),M(c.min,d-t)],e&&(t[2]=this.isLog?this.log2lin(this.dataMin):this.dataMin),p=[(d=s(t))+l,M(c.max,d+l)],e&&(p[2]=this.isLog?this.log2lin(this.dataMax):this.dataMax),(p=o(p))-d<l&&(t[0]=p-l,t[1]=M(c.min,p-l),d=s(t))),this.min=d,this.max=p},getClosest:function(){var t;return this.categories?t=1:this.series.forEach(function(e){var i=e.closestPointRange,s=e.visible||!e.chart.options.chart.ignoreHiddenSeries;!e.noSharedTooltip&&h(i)&&s&&(t=h(t)?Math.min(t,i):i)}),t},nameToX:function(t){var e,i=g(this.categories),s=i?this.categories:this.names,o=t.options.x;return t.series.requireSorting=!1,h(o)||(o=!1===this.options.uniqueNames?t.series.autoIncrement():i?s.indexOf(t.name):M(s.keys[t.name],-1)),-1===o?i||(e=s.length):e=o,void 0!==e&&(this.names[e]=t.name,this.names.keys[t.name]=e),e},updateNames:function(){var t=this,e=this.names;0<e.length&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,e.points&&!e.isDirtyData||(t.max=Math.max(t.max,e.xData.length-1),e.processData(),e.generatePoints()),e.data.forEach(function(i,s){var o;i&&i.options&&void 0!==i.name&&(void 0!==(o=t.nameToX(i))&&o!==i.x&&(i.x=o,e.xData[s]=o))})}))},setAxisTranslation:function(t){var e,i=this,s=i.max-i.min,o=i.axisPointRange||0,n=0,r=0,a=i.linkedParent,h=!!i.categories,l=i.transA,c=i.isXAxis;(c||h||o)&&(e=i.getClosest(),a?(n=a.minPointOffset,r=a.pointRangePadding):i.series.forEach(function(t){var s=h?1:c?M(t.options.pointRange,e,0):i.axisPointRange||0,a=t.options.pointPlacement;o=Math.max(o,s),i.single&&!h||(t=w.xrange&&t instanceof w.xrange?!c:c,n=Math.max(n,t&&x(a)?0:s/2),r=Math.max(r,t&&"on"===a?0:s))}),a=i.ordinalSlope&&e?i.ordinalSlope/e:1,i.minPointOffset=n*=a,i.pointRangePadding=r*=a,i.pointRange=Math.min(o,s),c&&(i.closestPointRange=e)),t&&(i.oldTransA=l),i.translationSlope=i.transA=l=i.staticScale||i.len/(s+r||1),i.transB=i.horiz?i.left:i.bottom,i.minPixelPadding=l*n,p(this,"afterSetAxisTranslation")},minFromRange:function(){return this.max-this.range},setTickInterval:function(e){var i,s,o,n,a=this,l=a.chart,c=a.options,d=a.isLog,u=a.isDatetimeAxis,g=a.isXAxis,x=a.isLinked,v=c.maxPadding,b=c.minPadding,k=c.tickInterval,w=c.tickPixelInterval,S=a.categories,A=m(a.threshold)?a.threshold:null,T=a.softThreshold;u||S||x||this.getTickAmount(),o=M(a.userMin,c.min),n=M(a.userMax,c.max),x?(a.linkedParent=l[a.coll][c.linkedTo],i=a.linkedParent.getExtremes(),a.min=M(i.min,i.dataMin),a.max=M(i.max,i.dataMax),c.type!==a.linkedParent.options.type&&t.error(11,1,l)):(!T&&h(A)&&(a.dataMin>=A?(i=A,b=0):a.dataMax<=A&&(s=A,v=0)),a.min=M(o,i,a.dataMin),a.max=M(n,s,a.dataMax)),d&&(a.positiveValuesOnly&&!e&&0>=Math.min(a.min,M(a.dataMin,a.min))&&t.error(10,1,l),a.min=r(a.log2lin(a.min),15),a.max=r(a.log2lin(a.max),15)),a.range&&h(a.max)&&(a.userMin=a.min=o=Math.max(a.dataMin,a.minFromRange()),a.userMax=n=a.max,a.range=null),p(a,"foundExtremes"),a.beforePadding&&a.beforePadding(),a.adjustForMinRange(),!(S||a.axisPointRange||a.usePercentage||x)&&h(a.min)&&h(a.max)&&(l=a.max-a.min)&&(!h(o)&&b&&(a.min-=l*b),!h(n)&&v&&(a.max+=l*v)),m(c.softMin)&&!m(a.userMin)&&c.softMin<a.min&&(a.min=o=c.softMin),m(c.softMax)&&!m(a.userMax)&&c.softMax>a.max&&(a.max=n=c.softMax),m(c.floor)&&(a.min=Math.min(Math.max(a.min,c.floor),Number.MAX_VALUE)),m(c.ceiling)&&(a.max=Math.max(Math.min(a.max,c.ceiling),M(a.userMax,-Number.MAX_VALUE))),T&&h(a.dataMin)&&(A=A||0,!h(o)&&a.min<A&&a.dataMin>=A?a.min=a.options.minRange?Math.min(A,a.max-a.minRange):A:!h(n)&&a.max>A&&a.dataMax<=A&&(a.max=a.options.minRange?Math.max(A,a.min+a.minRange):A)),a.tickInterval=a.min===a.max||void 0===a.min||void 0===a.max?1:x&&!k&&w===a.linkedParent.options.tickPixelInterval?k=a.linkedParent.tickInterval:M(k,this.tickAmount?(a.max-a.min)/Math.max(this.tickAmount-1,1):void 0,S?1:(a.max-a.min)*w/Math.max(a.len,w)),g&&!e&&a.series.forEach(function(t){t.processData(a.min!==a.oldMin||a.max!==a.oldMax)}),a.setAxisTranslation(!0),a.beforeSetTickPositions&&a.beforeSetTickPositions(),a.postProcessTickInterval&&(a.tickInterval=a.postProcessTickInterval(a.tickInterval)),a.pointRange&&!k&&(a.tickInterval=Math.max(a.pointRange,a.tickInterval)),e=M(c.minTickInterval,a.isDatetimeAxis&&a.closestPointRange),!k&&a.tickInterval<e&&(a.tickInterval=e),u||d||k||(a.tickInterval=y(a.tickInterval,null,f(a.tickInterval),M(c.allowDecimals,!(.5<a.tickInterval&&5>a.tickInterval&&1e3<a.max&&9999>a.max)),!!this.tickAmount)),this.tickAmount||(a.tickInterval=a.unsquish()),this.setTickPositions()},setTickPositions:function(){var e,i=this.options,s=i.tickPositions;e=this.getMinorTickInterval();var o=i.tickPositioner,n=i.startOnTick,r=i.endOnTick;this.tickmarkOffset=this.categories&&"between"===i.tickmarkPlacement&&1===this.tickInterval?.5:0,this.minorTickInterval="auto"===e&&this.tickInterval?this.tickInterval/5:e,this.single=this.min===this.max&&h(this.min)&&!this.tickAmount&&(parseInt(this.min,10)===this.min||!1!==i.allowDecimals),this.tickPositions=e=s&&s.slice(),!e&&(!this.ordinalPositions&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200)?(e=[this.min,this.max],t.error(19,!1,this.chart)):e=this.isDatetimeAxis?this.getTimeTicks(this.normalizeTimeTickInterval(this.tickInterval,i.units),this.min,this.max,i.startOfWeek,this.ordinalPositions,this.closestPointRange,!0):this.isLog?this.getLogTickPositions(this.tickInterval,this.min,this.max):this.getLinearTickPositions(this.tickInterval,this.min,this.max),e.length>this.len&&((e=[e[0],e.pop()])[0]===e[1]&&(e.length=1)),this.tickPositions=e,o&&(o=o.apply(this,[this.min,this.max])))&&(this.tickPositions=e=o),this.paddedTicks=e.slice(0),this.trimTicks(e,n,r),this.isLinked||(this.single&&2>e.length&&!this.categories&&(this.min-=.5,this.max+=.5),s||o||this.adjustTickAmount()),p(this,"afterSetTickPositions")},trimTicks:function(t,e,i){var s=t[0],o=t[t.length-1],n=this.minPointOffset||0;if(p(this,"trimTicks"),!this.isLinked){if(e&&-Infinity!==s)this.min=s;else for(;this.min-n>t[0];)t.shift();if(i)this.max=o;else for(;this.max+n<t[t.length-1];)t.pop();0===t.length&&h(s)&&!this.options.tickPositions&&t.push((o+s)/2)}},alignToOthers:function(){var t,e={},i=this.options;return!1===this.chart.options.chart.alignTicks||!1===i.alignTicks||!1===i.startOnTick||!1===i.endOnTick||this.isLog||this.chart[this.coll].forEach(function(i){var s=i.options;s=[i.horiz?s.left:s.top,s.width,s.height,s.pane].join();i.series.length&&(e[s]?t=!0:e[s]=1)}),t},getTickAmount:function(){var t=this.options,e=t.tickAmount,i=t.tickPixelInterval;!h(t.tickInterval)&&this.len<i&&!this.isRadial&&!this.isLog&&t.startOnTick&&t.endOnTick&&(e=2),!e&&this.alignToOthers()&&(e=Math.ceil(this.len/i)+1),4>e&&(this.finalTickAmt=e,e=5),this.tickAmount=e},adjustTickAmount:function(){var t,e=this.options,i=this.tickInterval,s=this.tickPositions,o=this.tickAmount,n=this.finalTickAmt,a=s&&s.length,l=M(this.threshold,this.softThreshold?0:null);if(this.hasData()){if(a<o){for(t=this.min;s.length<o;)s.length%2||t===l?s.push(r(s[s.length-1]+i)):s.unshift(r(s[0]-i));this.transA*=(a-1)/(o-1),this.min=e.startOnTick?s[0]:Math.min(this.min,s[0]),this.max=e.endOnTick?s[s.length-1]:Math.max(this.max,s[s.length-1])}else a>o&&(this.tickInterval*=2,this.setTickPositions());if(h(n)){for(i=e=s.length;i--;)(3===n&&1==i%2||2>=n&&0<i&&i<e-1)&&s.splice(i,1);this.finalTickAmt=void 0}}},setScale:function(){var t,e=this.series.some(function(t){return t.isDirtyData||t.isDirty||t.xAxis.isDirty});this.oldMin=this.min,this.oldMax=this.max,this.oldAxisLength=this.len,this.setAxisSize(),(t=this.len!==this.oldAxisLength)||e||this.isLinked||this.forceRedraw||this.userMin!==this.oldUserMin||this.userMax!==this.oldUserMax||this.alignToOthers()?(this.resetStacks&&this.resetStacks(),this.forceRedraw=!1,this.getSeriesExtremes(),this.setTickInterval(),this.oldUserMin=this.userMin,this.oldUserMax=this.userMax,this.isDirty||(this.isDirty=t||this.min!==this.oldMin||this.max!==this.oldMax)):this.cleanStacks&&this.cleanStacks(),p(this,"afterSetScale")},setExtremes:function(t,e,i,s,o){var n=this,r=n.chart;i=M(i,!0),n.series.forEach(function(t){delete t.kdTree}),o=d(o,{min:t,max:e}),p(n,"setExtremes",o,function(){n.userMin=t,n.userMax=e,n.eventArgs=o,i&&r.redraw(s)})},zoom:function(t,e){var i=this.dataMin,s=this.dataMax,o=this.options,n=Math.min(i,M(o.min,i)),r=Math.max(s,M(o.max,s));return p(this,"zoom",t={newMin:t,newMax:e},function(t){var e=t.newMin,o=t.newMax;e===this.min&&o===this.max||(this.allowZoomOutside||(h(i)&&(e<n&&(e=n),e>r&&(e=r)),h(s)&&(o<n&&(o=n),o>r&&(o=r))),this.displayBtn=void 0!==e||void 0!==o,this.setExtremes(e,o,!1,void 0,{trigger:"zoom"})),t.zoomed=!0}),t.zoomed},setAxisSize:function(){var e=this.chart,i=(a=this.options).offsets||[0,0,0,0],s=this.horiz,o=this.width=Math.round(t.relativeLength(M(a.width,e.plotWidth-i[3]+i[1]),e.plotWidth)),n=this.height=Math.round(t.relativeLength(M(a.height,e.plotHeight-i[0]+i[2]),e.plotHeight)),r=this.top=Math.round(t.relativeLength(M(a.top,e.plotTop+i[0]),e.plotHeight,e.plotTop)),a=this.left=Math.round(t.relativeLength(M(a.left,e.plotLeft+i[3]),e.plotWidth,e.plotLeft));this.bottom=e.chartHeight-n-r,this.right=e.chartWidth-o-a,this.len=Math.max(s?o:n,0),this.pos=s?a:r},getExtremes:function(){var t=this.isLog;return{min:t?r(this.lin2log(this.min)):this.min,max:t?r(this.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},getThreshold:function(t){var e=(i=this.isLog)?this.lin2log(this.min):this.min,i=i?this.lin2log(this.max):this.max;return null===t||-Infinity===t?t=e:Infinity===t?t=i:e>t?t=e:i<t&&(t=i),this.translate(t,0,1,0,1)},autoLabelAlign:function(t){var e=(M(t,0)-90*this.side+720)%360;return p(this,"autoLabelAlign",t={align:"center"},function(t){15<e&&165>e?t.align="right":195<e&&345>e&&(t.align="left")}),t.align},tickSize:function(t){var e,i=this.options,s=i[t+"Length"],o=M(i[t+"Width"],"tick"===t&&this.isXAxis&&!this.categories?1:0);return o&&s&&("inside"===i[t+"Position"]&&(s=-s),e=[s,o]),p(this,"afterTickSize",t={tickSize:e}),t.tickSize},labelMetrics:function(){var t=this.tickPositions&&this.tickPositions[0]||0;return this.chart.renderer.fontMetrics(this.options.labels.style&&this.options.labels.style.fontSize,this.ticks[t]&&this.ticks[t].label)},unsquish:function(){var t,e,i,s=this.options.labels,o=this.horiz,n=this.tickInterval,a=n,c=this.len/(((this.categories?1:0)+this.max-this.min)/n),d=s.rotation,p=this.labelMetrics(),u=Number.MAX_VALUE,f=this.max-this.min,g=function(t){var e;return(e=1<(e=t/(c||1))?Math.ceil(e):1)*n>f&&Infinity!==t&&Infinity!==c&&(e=Math.ceil(f/n)),r(e*n)};return o?(i=!s.staggerLines&&!s.step&&(h(d)?[d]:c<M(s.autoRotationLimit,80)&&s.autoRotation))&&i.forEach(function(i){var s;(i===d||i&&-90<=i&&90>=i)&&((s=(e=g(Math.abs(p.h/Math.sin(l*i))))+Math.abs(i/360))<u&&(u=s,t=i,a=e))}):s.step||(a=g(p.h)),this.autoRotation=i,this.labelRotation=M(t,d),a},getSlotWidth:function(t){var e=this.chart,i=this.horiz,s=this.options.labels,o=Math.max(this.tickPositions.length-(this.categories?0:1),1),n=e.margin[3];return t&&t.slotWidth||i&&2>(s.step||0)&&!s.rotation&&(this.staggerLines||1)*this.len/o||!i&&(s.style&&parseInt(s.style.width,10)||n&&n-e.spacing[3]||.33*e.chartWidth)},renderUnsquish:function(){var t,e,i,s=this.chart,o=s.renderer,n=this.tickPositions,r=this.ticks,a=this.options.labels,h=a&&a.style||{},l=this.horiz,c=this.getSlotWidth(),d=Math.max(1,Math.round(c-2*(a.padding||5))),p={},u=this.labelMetrics(),f=a.style&&a.style.textOverflow,g=0;if(x(a.rotation)||(p.rotation=a.rotation||0),n.forEach(function(t){(t=r[t])&&t.label&&t.label.textPxLength>g&&(g=t.label.textPxLength)}),this.maxLabelLength=g,this.autoRotation)g>d&&g>u.h?p.rotation=this.labelRotation:this.labelRotation=0;else if(c&&(t=d,!f))for(e="clip",d=n.length;!l&&d--;)i=n[d],(i=r[i].label)&&(i.styles&&"ellipsis"===i.styles.textOverflow?i.css({textOverflow:"clip"}):i.textPxLength>c&&i.css({width:c+"px"}),i.getBBox().height>this.len/n.length-(u.h-u.f)&&(i.specificTextOverflow="ellipsis"));p.rotation&&(t=g>.5*s.chartHeight?.33*s.chartHeight:g,f||(e="ellipsis")),(this.labelAlign=a.align||this.autoLabelAlign(this.labelRotation))&&(p.align=this.labelAlign),n.forEach(function(i){var s=(i=r[i])&&i.label,o=h.width,n={};s&&(s.attr(p),i.shortenLabel?i.shortenLabel():t&&!o&&"nowrap"!==h.whiteSpace&&(t<s.textPxLength||"SPAN"===s.element.tagName)?(n.width=t,f||(n.textOverflow=s.specificTextOverflow||e),s.css(n)):s.styles&&s.styles.width&&!n.width&&!o&&s.css({width:null}),delete s.specificTextOverflow,i.rotation=p.rotation)},this),this.tickRotCorr=o.rotCorr(u.b,this.labelRotation||0,0!==this.side)},hasData:function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&h(this.min)&&h(this.max)},addTitle:function(t){var e,i=this.chart.renderer,s=this.horiz,o=this.opposite,n=this.options.title,r=this.chart.styledMode;this.axisTitle||((e=n.textAlign)||(e=(s?{low:"left",middle:"center",high:"right"}:{low:o?"right":"left",middle:"center",high:o?"left":"right"})[n.align]),this.axisTitle=i.text(n.text,0,0,n.useHTML).attr({zIndex:7,rotation:n.rotation||0,align:e}).addClass("highcharts-axis-title"),r||this.axisTitle.css(v(n.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),r||n.style.width||this.isRadial||this.axisTitle.css({width:this.len}),this.axisTitle[t?"show":"hide"](!0)},generateTick:function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new T(this,t)},getOffset:function(){var t,e,i,s=this,o=(y=s.chart).renderer,n=s.options,r=s.tickPositions,a=s.ticks,l=s.horiz,c=s.side,d=y.inverted&&!s.isZAxis?[1,0,3,2][c]:c,u=0,f=0,g=n.title,m=n.labels,x=0,v=y.axisOffset,y=y.clipOffset,k=[-1,1,1,-1][c],w=n.className,S=s.axisParent;t=s.hasData(),s.showAxis=e=t||M(n.showEmpty,!0),s.staggerLines=s.horiz&&m.staggerLines,s.axisGroup||(s.gridGroup=o.g("grid").attr({zIndex:n.gridZIndex||1}).addClass("highcharts-"+this.coll.toLowerCase()+"-grid "+(w||"")).add(S),s.axisGroup=o.g("axis").attr({zIndex:n.zIndex||2}).addClass("highcharts-"+this.coll.toLowerCase()+" "+(w||"")).add(S),s.labelGroup=o.g("axis-labels").attr({zIndex:m.zIndex||7}).addClass("highcharts-"+s.coll.toLowerCase()+"-labels "+(w||"")).add(S)),t||s.isLinked?(r.forEach(function(t,e){s.generateTick(t,e)}),s.renderUnsquish(),s.reserveSpaceDefault=0===c||2===c||{1:"left",3:"right"}[c]===s.labelAlign,M(m.reserveSpace,"center"===s.labelAlign||null,s.reserveSpaceDefault)&&r.forEach(function(t){x=Math.max(a[t].getLabelSize(),x)}),s.staggerLines&&(x*=s.staggerLines),s.labelOffset=x*(s.opposite?-1:1)):b(a,function(t,e){t.destroy(),delete a[e]}),g&&g.text&&!1!==g.enabled&&(s.addTitle(e),e&&!1!==g.reserveSpace&&(s.titleOffset=u=s.axisTitle.getBBox()[l?"height":"width"],i=g.offset,f=h(i)?0:M(g.margin,l?5:10))),s.renderLine(),s.offset=k*M(n.offset,v[c]?v[c]+(n.margin||0):0),s.tickRotCorr=s.tickRotCorr||{x:0,y:0},o=0===c?-s.labelMetrics().h:2===c?s.tickRotCorr.y:0,f=Math.abs(x)+f,x&&(f=f-o+k*(l?M(m.y,s.tickRotCorr.y+8*k):m.x)),s.axisTitleMargin=M(i,f),s.getMaxLabelDimensions&&(s.maxLabelDimensions=s.getMaxLabelDimensions(a,r)),l=this.tickSize("tick"),v[c]=Math.max(v[c],s.axisTitleMargin+u+k*s.offset,f,r&&r.length&&l?l[0]+k*s.offset:0),n=n.offset?0:2*Math.floor(s.axisLine.strokeWidth()/2),y[d]=Math.max(y[d],n),p(this,"afterGetOffset")},getLinePath:function(t){var e=this.chart,i=this.opposite,s=this.offset,o=this.horiz,n=this.left+(i?this.width:0)+s;s=e.chartHeight-this.bottom-(i?this.height:0)+s;return i&&(t*=-1),e.renderer.crispLine(["M",o?this.left:n,o?s:this.top,"L",o?e.chartWidth-this.right:n,o?s:e.chartHeight-this.bottom],t)},renderLine:function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},getTitlePosition:function(){var t=this.horiz,e=this.left,i=this.top,s=this.len,o=this.options.title,n=t?e:i,r=this.opposite,a=this.offset,h=o.x||0,l=o.y||0,c=this.axisTitle,d=this.chart.renderer.fontMetrics(o.style&&o.style.fontSize,c);c=Math.max(c.getBBox(null,0).height-d.h-1,0),s={low:n+(t?0:s),middle:n+s/2,high:n+(t?s:0)}[o.align],e=(t?i+this.height:e)+(t?1:-1)*(r?-1:1)*this.axisTitleMargin+[-c,c,d.f,-c][this.side],t={x:t?s+h:e+(r?this.width:0)+a+h,y:t?e+l-(r?this.height:0)+a:s+l};return p(this,"afterGetTitlePosition",{titlePosition:t}),t},renderMinorTick:function(t){var e=this.chart.hasRendered&&m(this.oldMin),i=this.minorTicks;i[t]||(i[t]=new T(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},renderTick:function(t,e){var i=this.isLinked,s=this.ticks,o=this.chart.hasRendered&&m(this.oldMin);(!i||t>=this.min&&t<=this.max)&&(s[t]||(s[t]=new T(this,t)),o&&s[t].isNew&&s[t].render(e,!0,-1),s[t].render(e))},render:function(){var e,s,o=this,n=o.chart,r=o.options,a=o.isLog,h=o.isLinked,l=o.tickPositions,c=o.axisTitle,d=o.ticks,u=o.minorTicks,f=o.alternateBands,g=r.stackLabels,x=r.alternateGridColor,v=o.tickmarkOffset,y=o.axisLine,M=o.showAxis,k=i(n.renderer.globalAnimation);o.labelEdge.length=0,o.overlap=!1,[d,u,f].forEach(function(t){b(t,function(t){t.isActive=!1})}),(o.hasData()||h)&&(o.minorTickInterval&&!o.categories&&o.getMinorTickPositions().forEach(function(t){o.renderMinorTick(t)}),l.length&&(l.forEach(function(t,e){o.renderTick(t,e)}),v&&(0===o.min||o.single)&&(d[-1]||(d[-1]=new T(o,-1,null,!0)),d[-1].render(-1))),x&&l.forEach(function(i,r){s=void 0!==l[r+1]?l[r+1]+v:o.max-v,0==r%2&&i<o.max&&s<=o.max+(n.polar?-v:v)&&(f[i]||(f[i]=new t.PlotLineOrBand(o)),e=i+v,f[i].options={from:a?o.lin2log(e):e,to:a?o.lin2log(s):s,color:x},f[i].render(),f[i].isActive=!0)}),o._addedPlotLB||((r.plotLines||[]).concat(r.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)}),o._addedPlotLB=!0)),[d,u,f].forEach(function(t){var e,i=[],s=k.duration;b(t,function(t,e){t.isActive||(t.render(e,!1,0),t.isActive=!1,i.push(e))}),A(function(){for(e=i.length;e--;)t[i[e]]&&!t[i[e]].isActive&&(t[i[e]].destroy(),delete t[i[e]])},t!==f&&n.hasRendered&&s?s:0)}),y&&(y[y.isPlaced?"animate":"attr"]({d:this.getLinePath(y.strokeWidth())}),y.isPlaced=!0,y[M?"show":"hide"](!0)),c&&M&&(r=o.getTitlePosition(),m(r.y)?(c[c.isNew?"attr":"animate"](r),c.isNew=!1):(c.attr("y",-9999),c.isNew=!0)),g&&g.enabled&&o.renderStackTotals(),o.isDirty=!1,p(this,"afterRender")},redraw:function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},keepProps:"extKey hcEvents names series userMax userMin".split(" "),destroy:function(t){var e,i=this,s=i.stacks,o=i.plotLinesAndBands;if(p(this,"destroy",{keepEvents:t}),t||k(i),b(s,function(t,e){c(t),s[e]=null}),[i.ticks,i.minorTicks,i.alternateBands].forEach(function(t){c(t)}),o)for(t=o.length;t--;)o[t].destroy();for(e in"stackTotalGroup axisLine axisTitle axisGroup gridGroup labelGroup cross scrollbar".split(" ").forEach(function(t){i[t]&&(i[t]=i[t].destroy())}),i.plotLinesAndBandsGroups)i.plotLinesAndBandsGroups[e]=i.plotLinesAndBandsGroups[e].destroy();b(i,function(t,e){-1===i.keepProps.indexOf(e)&&delete i[e]})},drawCrosshair:function(t,e){var i,s,o=this.crosshair,r=M(o.snap,!0),a=this.cross;if(p(this,"drawCrosshair",{e:t,point:e}),t||(t=this.cross&&this.cross.e),this.crosshair&&!1!==(h(e)||!r)){if(r?h(e)&&(s=M(e.crosshairPos,this.isXAxis?e.plotX:this.len-e.plotY)):s=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),h(s)&&(i=this.getPlotLinePath(e&&(this.isXAxis?e.x:M(e.stackY,e.y)),null,null,null,s)||null),!h(i))return void this.hideCrosshair();r=this.categories&&!this.isRadial,a||(this.cross=a=this.chart.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(r?"category ":"thin ")+o.className).attr({zIndex:M(o.zIndex,2)}).add(),this.chart.styledMode||(a.attr({stroke:o.color||(r?n("#ccd6eb").setOpacity(.25).get():"#cccccc"),"stroke-width":M(o.width,1)}).css({"pointer-events":"none"}),o.dashStyle&&a.attr({dashstyle:o.dashStyle}))),a.show().attr({d:i}),r&&!o.width&&a.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();p(this,"afterDrawCrosshair",{e:t,point:e})},hideCrosshair:function(){this.cross&&this.cross.hide(),p(this,"afterHideCrosshair")}}),t.Axis=P}),e(i,"parts/DateTimeAxis.js",[i["parts/Globals.js"]],function(t){var e=t.Axis,i=t.getMagnitude,s=t.normalizeTickInterval,o=t.timeUnits;e.prototype.getTimeTicks=function(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)},e.prototype.normalizeTimeTickInterval=function(t,e){var n=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]];e=n[n.length-1];var r,a=o[e[0]],h=e[1];for(r=0;r<n.length&&(e=n[r],a=o[e[0]],h=e[1],!(n[r+1]&&t<=(a*h[h.length-1]+o[n[r+1][0]])/2));r++);return a===o.year&&t<5*a&&(h=[1,2,5]),{unitRange:a,count:t=s(t/a,h,"year"===e[0]?Math.max(i(t/a),1):1),unitName:e[0]}}}),e(i,"parts/LogarithmicAxis.js",[i["parts/Globals.js"]],function(t){var e=t.Axis,i=t.getMagnitude,s=t.normalizeTickInterval,o=t.pick;e.prototype.getLogTickPositions=function(t,e,n,r){var a=this.options,h=this.len,l=[];if(r||(this._minorAutoInterval=null),.5<=t)t=Math.round(t),l=this.getLinearTickPositions(t,e,n);else if(.08<=t){var c,d,p,u,f;for(h=Math.floor(e),a=.3<t?[1,2,4]:.15<t?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9];h<n+1&&!f;h++)for(d=a.length,c=0;c<d&&!f;c++)(p=this.log2lin(this.lin2log(h)*a[c]))>e&&(!r||u<=n)&&void 0!==u&&l.push(u),u>n&&(f=!0),u=p}else e=this.lin2log(e),n=this.lin2log(n),t=r?this.getMinorTickInterval():a.tickInterval,t=o("auto"===t?null:t,this._minorAutoInterval,a.tickPixelInterval/(r?5:1)*(n-e)/((r?h/this.tickPositions.length:h)||1)),t=s(t,null,i(t)),l=this.getLinearTickPositions(t,e,n).map(this.log2lin),r||(this._minorAutoInterval=t/5);return r||(this.tickInterval=t),l},e.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},e.prototype.lin2log=function(t){return Math.pow(10,t)}}),e(i,"parts/PlotLineOrBand.js",[i["parts/Globals.js"],i["parts/Axis.js"]],function(t,e){var i=t.arrayMax,s=t.arrayMin,o=t.defined,n=t.destroyObjectProperties,r=t.erase,a=t.merge,h=t.pick;t.PlotLineOrBand=function(t,e){this.axis=t,e&&(this.options=e,this.id=e.id)},t.PlotLineOrBand.prototype={render:function(){t.fireEvent(this,"render");var e=this,i=e.axis,s=i.horiz,n=e.options,r=n.label,l=e.label,c=n.to,d=n.from,p=n.value,u=o(d)&&o(c),f=o(p),g=e.svgElem,m=!g,x=[],v=n.color,y=h(n.zIndex,0),b=n.events,M=(x={"class":"highcharts-plot-"+(u?"band ":"line ")+(n.className||"")},{}),k=i.chart.renderer,w=u?"bands":"lines";if(i.isLog&&(d=i.log2lin(d),c=i.log2lin(c),p=i.log2lin(p)),i.chart.styledMode||(f?(x.stroke=v,x["stroke-width"]=n.width,n.dashStyle&&(x.dashstyle=n.dashStyle)):u&&(v&&(x.fill=v),n.borderWidth&&(x.stroke=n.borderColor,x["stroke-width"]=n.borderWidth))),M.zIndex=y,w+="-"+y,(v=i.plotLinesAndBandsGroups[w])||(i.plotLinesAndBandsGroups[w]=v=k.g("plot-"+w).attr(M).add()),m&&(e.svgElem=g=k.path().attr(x).add(v)),f)x=i.getPlotLinePath(p,g.strokeWidth());else{if(!u)return;x=i.getPlotBandPath(d,c,n)}return(m||!g.d)&&x&&x.length?(g.attr({d:x}),b&&t.objectEach(b,function(t,i){g.on(i,function(t){b[i].apply(e,[t])})})):g&&(x?(g.show(!0),g.animate({d:x})):g.d&&(g.hide(),l&&(e.label=l=l.destroy()))),r&&o(r.text)&&x&&x.length&&0<i.width&&0<i.height&&!x.isFlat?(r=a({align:s&&u&&"center",x:s?!u&&4:10,verticalAlign:!s&&u&&"middle",y:s?u?16:10:u?6:-4,rotation:s&&!u&&90},r),this.renderLabel(r,x,u,y)):l&&l.hide(),e},renderLabel:function(t,e,o,n){var r=this.label,a=this.axis.chart.renderer;r||((r={align:t.textAlign||t.align,rotation:t.rotation,"class":"highcharts-plot-"+(o?"band":"line")+"-label "+(t.className||"")}).zIndex=n,this.label=r=a.text(t.text,0,0,t.useHTML).attr(r).add(),this.axis.chart.styledMode||r.css(t.style)),n=e.xBounds||[e[1],e[4],o?e[6]:e[1]],e=e.yBounds||[e[2],e[5],o?e[7]:e[2]],o=s(n),a=s(e),r.align(t,!1,{x:o,y:a,width:i(n)-o,height:i(e)-a}),r.show(!0)},destroy:function(){r(this.axis.plotLinesAndBands,this),delete this.axis,n(this)}},t.extend(e.prototype,{getPlotBandPath:function(t,e){var i,s=this.getPlotLinePath(e,null,null,!0),o=this.getPlotLinePath(t,null,null,!0),n=[],r=this.horiz,a=1;if(t=t<this.min&&e<this.min||t>this.max&&e>this.max,o&&s)for(t&&(i=o.toString()===s.toString(),a=0),t=0;t<o.length;t+=6)r&&s[t+1]===o[t+1]?(s[t+1]+=a,s[t+4]+=a):r||s[t+2]!==o[t+2]||(s[t+2]+=a,s[t+5]+=a),n.push("M",o[t+1],o[t+2],"L",o[t+4],o[t+5],s[t+4],s[t+5],s[t+1],s[t+2],"z"),n.isFlat=i;return n},addPlotBand:function(t){return this.addPlotBandOrLine(t,"plotBands")},addPlotLine:function(t){return this.addPlotBandOrLine(t,"plotLines")},addPlotBandOrLine:function(e,i){var s=new t.PlotLineOrBand(this,e).render(),o=this.userOptions;return s&&(i&&(o[i]=o[i]||[],o[i].push(e)),this.plotLinesAndBands.push(s)),s},removePlotBandOrLine:function(t){for(var e=this.plotLinesAndBands,i=this.options,s=this.userOptions,o=e.length;o--;)e[o].id===t&&e[o].destroy();[i.plotLines||[],s.plotLines||[],i.plotBands||[],s.plotBands||[]].forEach(function(e){for(o=e.length;o--;)e[o].id===t&&r(e,e[o])})},removePlotBand:function(t){this.removePlotBandOrLine(t)},removePlotLine:function(t){this.removePlotBandOrLine(t)}})}),e(i,"parts/Tooltip.js",[i["parts/Globals.js"]],function(t){var e=t.doc,i=t.extend,s=t.format,o=t.isNumber,n=t.merge,r=t.pick,a=t.splat,h=t.syncTimeout,l=t.timeUnits;t.Tooltip=function(){this.init.apply(this,arguments)},t.Tooltip.prototype={init:function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.now={x:0,y:0},this.isHidden=!0,this.split=e.split&&!t.inverted,this.shared=e.shared||this.split,this.outside=e.outside&&!this.split},cleanSplit:function(t){this.chart.series.forEach(function(e){var i=e&&e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},applyFilter:function(){var t=this.chart;t.renderer.definition({tagName:"filter",id:"drop-shadow-"+t.index,opacity:.5,children:[{tagName:"feGaussianBlur","in":"SourceAlpha",stdDeviation:1},{tagName:"feOffset",dx:1,dy:1},{tagName:"feComponentTransfer",children:[{tagName:"feFuncA",type:"linear",slope:.3}]},{tagName:"feMerge",children:[{tagName:"feMergeNode"},{tagName:"feMergeNode","in":"SourceGraphic"}]}]}),t.renderer.definition({tagName:"style",textContent:".highcharts-tooltip-"+t.index+"{filter:url(#drop-shadow-"+t.index+")}"})},getLabel:function(){var e,i,s=this,o=this.chart.renderer,n=this.chart.styledMode,r=this.options;return this.label||(this.outside&&(this.container=e=t.doc.createElement("div"),e.className="highcharts-tooltip-container",t.css(e,{position:"absolute",top:"1px",pointerEvents:r.style&&r.style.pointerEvents}),t.doc.body.appendChild(e),this.renderer=o=new t.Renderer(e,0,0)),this.split?this.label=o.g("tooltip"):(this.label=o.label("",0,0,r.shape||"callout",null,null,r.useHTML,null,"tooltip").attr({padding:r.padding,r:r.borderRadius}),n||this.label.attr({fill:r.backgroundColor,"stroke-width":r.borderWidth}).css(r.style).shadow(r.shadow)),n&&(this.applyFilter(),this.label.addClass("highcharts-tooltip-"+this.chart.index)),this.outside&&(i={x:this.label.xSetter,y:this.label.ySetter},this.label.xSetter=function(t,o){i[o].call(this.label,s.distance),e.style.left=t+"px"},this.label.ySetter=function(t,o){i[o].call(this.label,s.distance),e.style.top=t+"px"}),this.label.attr({zIndex:8}).add()),this.label},update:function(t){this.destroy(),n(!0,this.chart.options.tooltip.userOptions,t),this.init(this.chart,n(!0,this.options,t))},destroy:function(){this.label&&(this.label=this.label.destroy()),this.split&&this.tt&&(this.cleanSplit(this.chart,!0),this.tt=this.tt.destroy()),this.renderer&&(this.renderer=this.renderer.destroy(),t.discardElement(this.container)),t.clearTimeout(this.hideTimer),t.clearTimeout(this.tooltipTimeout)},move:function(e,s,o,n){var r=this,a=r.now,h=!1!==r.options.animation&&!r.isHidden&&(1<Math.abs(e-a.x)||1<Math.abs(s-a.y)),l=r.followPointer||1<r.len;i(a,{x:h?(2*a.x+e)/3:e,y:h?(a.y+s)/2:s,anchorX:l?void 0:h?(2*a.anchorX+o)/3:o,anchorY:l?void 0:h?(a.anchorY+n)/2:n}),r.getLabel().attr(a),h&&(t.clearTimeout(this.tooltipTimeout),this.tooltipTimeout=setTimeout(function(){r&&r.move(e,s,o,n)},32))},hide:function(e){var i=this;t.clearTimeout(this.hideTimer),e=r(e,this.options.hideDelay,500),this.isHidden||(this.hideTimer=h(function(){i.getLabel()[e?"fadeOut":"hide"](),i.isHidden=!0},e))},getAnchor:function(t,e){var i,s,o=this.chart,n=o.pointer,r=o.inverted,h=o.plotTop,l=o.plotLeft,c=0,d=0;return t=a(t),this.followPointer&&e?(void 0===e.chartX&&(e=n.normalize(e)),t=[e.chartX-o.plotLeft,e.chartY-h]):t[0].tooltipPos?t=t[0].tooltipPos:(t.forEach(function(t){i=t.series.yAxis,s=t.series.xAxis,c+=t.plotX+(!r&&s?s.left-l:0),d+=(t.plotLow?(t.plotLow+t.plotHigh)/2:t.plotY)+(!r&&i?i.top-h:0)}),c/=t.length,d/=t.length,
t=[r?o.plotWidth-d:c,this.shared&&!r&&1<t.length&&e?e.chartY-h:r?o.plotHeight-c:d]),t.map(Math.round)},getPosition:function(t,i,s){var o,n=this.chart,a=this.distance,h={},l=n.inverted&&s.h||0,c=this.outside,d=c?e.documentElement.clientWidth-2*a:n.chartWidth,p=c?Math.max(e.body.scrollHeight,e.documentElement.scrollHeight,e.body.offsetHeight,e.documentElement.offsetHeight,e.documentElement.clientHeight):n.chartHeight,u=n.pointer.chartPosition,f=["y",p,i,(c?u.top-a:0)+s.plotY+n.plotTop,c?0:n.plotTop,c?p:n.plotTop+n.plotHeight],g=["x",d,t,(c?u.left-a:0)+s.plotX+n.plotLeft,c?0:n.plotLeft,c?d:n.plotLeft+n.plotWidth],m=!this.followPointer&&r(s.ttBelow,!n.inverted==!!s.negative),x=function(t,e,i,s,o,n){var r=i<s-a,c=s+a+i<e,d=s-a-i;if(s+=a,m&&c)h[t]=s;else if(!m&&r)h[t]=d;else if(r)h[t]=Math.min(n-i,0>d-l?d:d-l);else{if(!c)return!1;h[t]=Math.max(o,s+l+i>e?s:s+l)}},v=function(t,e,i,s){var o;return s<a||s>e-a?o=!1:h[t]=s<i/2?1:s>e-i/2?e-i-2:s-i/2,o},y=function(t){var e=f;f=g,g=e,o=t},b=function(){!1!==x.apply(0,f)?!1!==v.apply(0,g)||o||(y(!0),b()):o?h.x=h.y=0:(y(!0),b())};return(n.inverted||1<this.len)&&y(),b(),h},defaultFormatter:function(t){var e,i=this.points||a(this);return(e=(e=[t.tooltipFooterHeaderFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.tooltipFooterHeaderFormatter(i[0],!0)),e},refresh:function(e,i){var s,o,n,h=this.chart,l=this.options,c=e,d={},p=[];n=l.formatter||this.defaultFormatter;d=this.shared;var u=h.styledMode,f=[];l.enabled&&(t.clearTimeout(this.hideTimer),this.followPointer=a(c)[0].series.tooltipOptions.followPointer,i=(o=this.getAnchor(c,i))[0],s=o[1],!d||c.series&&c.series.noSharedTooltip?d=c.getLabelConfig():(f=h.pointer.getActiveSeries(c),h.series.forEach(function(t){(t.options.inactiveOtherPoints||-1===f.indexOf(t))&&t.setState("inactive",!0)}),c.forEach(function(t){t.setState("hover"),p.push(t.getLabelConfig())}),(d={x:c[0].category,y:c[0].y}).points=p,c=c[0]),this.len=p.length,n=n.call(d,this),d=c.series,this.distance=r(d.tooltipOptions.distance,16),!1===n?this.hide():(h=this.getLabel(),this.isHidden&&h.attr({opacity:1}).show(),this.split?this.renderSplit(n,a(e)):(l.style.width&&!u||h.css({width:this.chart.spacingBox.width}),h.attr({text:n&&n.join?n.join(""):n}),h.removeClass(/highcharts-color-[\d]+/g).addClass("highcharts-color-"+r(c.colorIndex,d.colorIndex)),u||h.attr({stroke:l.borderColor||c.color||d.color||"#666666"}),this.updatePosition({plotX:i,plotY:s,negative:c.negative,ttBelow:c.ttBelow,h:o[2]||0})),this.isHidden=!1),t.fireEvent(this,"refresh"))},renderSplit:function(e,i){var s,o=this,n=[],a=this.chart,h=a.renderer,l=!0,c=this.options,d=0,p=this.getLabel(),u=a.plotTop;t.isString(e)&&(e=[!1,e]),e.slice(0,i.length+1).forEach(function(t,e){if(!1!==t&&""!==t){var f=(e=i[e-1]||{isHeader:!0,plotX:i[0].plotX,plotY:a.plotHeight}).series||o,g=f.tt,m=e.series||{},x="highcharts-color-"+r(e.colorIndex,m.colorIndex,"none");g||(g={padding:c.padding,r:c.borderRadius},a.styledMode||(g.fill=c.backgroundColor,g.stroke=c.borderColor||e.color||m.color||"#333333",g["stroke-width"]=c.borderWidth),f.tt=g=h.label(null,null,null,(e.isHeader?c.headerShape:c.shape)||"callout",null,null,c.useHTML).addClass("highcharts-tooltip-box "+x).attr(g).add(p)),g.isActive=!0,g.attr({text:t}),a.styledMode||g.css(c.style).shadow(c.shadow),m=(t=g.getBBox()).width+g.strokeWidth(),e.isHeader?(d=t.height,a.xAxis[0].opposite&&(s=!0,u-=d),m=Math.max(0,Math.min(e.plotX+a.plotLeft-m/2,a.chartWidth+(a.scrollablePixels?a.scrollablePixels-a.marginRight:0)-m))):m=e.plotX+a.plotLeft-r(c.distance,16)-m,0>m&&(l=!1),t=(e.series&&e.series.yAxis&&e.series.yAxis.pos)+(e.plotY||0),t-=u,e.isHeader&&(t=s?-d:a.plotHeight+d),n.push({target:t,rank:e.isHeader?1:0,size:f.tt.getBBox().height+1,point:e,x:m,tt:g})}}),this.cleanSplit(),c.positioner&&n.forEach(function(t){var e=c.positioner.call(o,t.tt.getBBox().width,t.size,t.point);t.x=e.x,t.align=0,t.target=e.y,t.rank=r(e.rank,t.rank)}),t.distribute(n,a.plotHeight+d),n.forEach(function(t){var e=t.point,i=e.series;t.tt.attr({visibility:void 0===t.pos?"hidden":"inherit",x:l||e.isHeader||c.positioner?t.x:e.plotX+a.plotLeft+o.distance,y:t.pos+u,anchorX:e.isHeader?e.plotX+a.plotLeft:e.plotX+i.xAxis.pos,anchorY:e.isHeader?a.plotTop+a.plotHeight/2:e.plotY+i.yAxis.pos})})},updatePosition:function(t){var e,i=this.chart,s=this.getLabel(),o=(this.options.positioner||this.getPosition).call(this,s.width,s.height,t),n=t.plotX+i.plotLeft;t=t.plotY+i.plotTop,this.outside&&(e=(this.options.borderWidth||0)+2*this.distance,this.renderer.setSize(s.width+e,s.height+e,!1),n+=i.pointer.chartPosition.left-o.x,t+=i.pointer.chartPosition.top-o.y),this.move(Math.round(o.x),Math.round(o.y||0),n,t)},getDateFormat:function(t,e,i,s){var o,n,r=this.chart.time,a=r.dateFormat("%m-%d %H:%M:%S.%L",e),h={millisecond:15,second:12,minute:9,hour:6,day:3},c="millisecond";for(n in l){if(t===l.week&&+r.dateFormat("%w",e)===i&&"00:00:00.000"===a.substr(6)){n="week";break}if(l[n]>t){n=c;break}if(h[n]&&a.substr(h[n])!=="01-01 00:00:00.000".substr(h[n]))break;"week"!==n&&(c=n)}return n&&(o=r.resolveDTLFormat(s[n]).main),o},getXDateFormat:function(t,e,i){e=e.dateTimeLabelFormats;var s=i&&i.closestPointRange;return(s?this.getDateFormat(s,t.x,i.options.startOfWeek,e):e.day)||e.year},tooltipFooterHeaderFormatter:function(e,i){var n=i?"footer":"header",r=e.series,a=r.tooltipOptions,h=a.xDateFormat,l=r.xAxis,c=l&&"datetime"===l.options.type&&o(e.key),d=a[n+"Format"];return i={isFooter:i,labelConfig:e},t.fireEvent(this,"headerFormatter",i,function(t){c&&!h&&(h=this.getXDateFormat(e,a,l)),c&&h&&(e.point&&e.point.tooltipDateKeys||["key"]).forEach(function(t){d=d.replace("{point."+t+"}","{point."+t+":"+h+"}")}),r.chart.styledMode&&(d=this.styledModeFormat(d)),t.text=s(d,{point:e,series:r},this.chart.time)}),i.text},bodyFormatter:function(t){return t.map(function(t){var e=t.series.tooltipOptions;return(e[(t.point.formatPrefix||"point")+"Formatter"]||t.point.tooltipFormatter).call(t.point,e[(t.point.formatPrefix||"point")+"Format"]||"")})},styledModeFormat:function(t){return t.replace('style="font-size: 10px"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex}"')}}}),e(i,"parts/Pointer.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.attr,s=t.charts,o=t.color,n=t.css,r=t.defined,a=t.extend,h=t.find,l=t.fireEvent,c=t.isNumber,d=t.isObject,p=t.offset,u=t.pick,f=t.splat,g=t.Tooltip;t.Pointer=function(t,e){this.init(t,e)},t.Pointer.prototype={init:function(t,e){this.options=e,this.chart=t,this.runChartClick=e.chart.events&&!!e.chart.events.click,this.pinchDown=[],this.lastValidTouch={},g&&(t.tooltip=new g(t,e.tooltip),this.followTouchMove=u(e.tooltip.followTouchMove,!0)),this.setDOMEvents()},zoomOption:function(t){var e=(s=this.chart).options.chart,i=e.zoomType||"",s=s.inverted;/touch/.test(t.type)&&(i=u(e.pinchType,i)),this.zoomX=t=/x/.test(i),this.zoomY=i=/y/.test(i),this.zoomHor=t&&!s||i&&s,this.zoomVert=i&&!s||t&&s,this.hasZoom=t||i},normalize:function(t,e){var i;return i=t.touches?t.touches.length?t.touches.item(0):t.changedTouches[0]:t,e||(this.chartPosition=e=p(this.chart.container)),a(t,{chartX:Math.round(i.pageX-e.left),chartY:Math.round(i.pageY-e.top)})},getCoordinates:function(t){var e={xAxis:[],yAxis:[]};return this.chart.axes.forEach(function(i){e[i.isXAxis?"xAxis":"yAxis"].push({axis:i,value:i.toValue(t[i.horiz?"chartX":"chartY"])})}),e},findNearestKDPoint:function(t,e,i){var s;return t.forEach(function(t){var o=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y");if(t=t.searchPoint(i,o),(o=d(t,!0))&&!(o=!d(s,!0))){o=s.distX-t.distX;var n=s.dist-t.dist,r=(t.series.group&&t.series.group.zIndex)-(s.series.group&&s.series.group.zIndex);o=0<(0!==o&&e?o:0!==n?n:0!==r?r:s.series.index>t.series.index?-1:1)}o&&(s=t)}),s},getPointFromEvent:function(t){t=t.target;for(var e;t&&!e;)e=t.point,t=t.parentNode;return e},getChartCoordinatesFromPoint:function(t,e){var i=(s=t.series).xAxis,s=s.yAxis,o=u(t.clientX,t.plotX),n=t.shapeArgs;return i&&s?e?{chartX:i.len+i.pos-o,chartY:s.len+s.pos-t.plotY}:{chartX:o+i.pos,chartY:t.plotY+s.pos}:n&&n.x&&n.y?{chartX:n.x,chartY:n.y}:void 0},getHoverData:function(t,e,i,s,o,n){var r,a=[];s=!(!s||!t);var l=e&&!e.stickyTracking?[e]:i.filter(function(t){return t.visible&&!(!o&&t.directTouch)&&u(t.options.enableMouseTracking,!0)&&t.stickyTracking});return e=(r=s?t:this.findNearestKDPoint(l,o,n))&&r.series,r&&(o&&!e.noSharedTooltip?(l=i.filter(function(t){return t.visible&&!(!o&&t.directTouch)&&u(t.options.enableMouseTracking,!0)&&!t.noSharedTooltip})).forEach(function(t){var e=h(t.points,function(t){return t.x===r.x&&!t.isNull});d(e)&&(t.chart.isBoosting&&(e=t.getPoint(e)),a.push(e))}):a.push(r)),{hoverPoint:r,hoverSeries:e,hoverPoints:a}},runPointActions:function(i,o){var n,r=this.chart,a=r.tooltip&&r.tooltip.options.enabled?r.tooltip:void 0,h=!!a&&a.shared,l=(d=o||r.hoverPoint)&&d.series||r.hoverSeries,c=(l=this.getHoverData(d,l,r.series,"touchmove"!==i.type&&(!!o||l&&l.directTouch&&this.isDirectTouch),h,i),[]),d=l.hoverPoint;if(n=l.hoverPoints,o=(l=l.hoverSeries)&&l.tooltipOptions.followPointer,h=h&&l&&!l.noSharedTooltip,d&&(d!==r.hoverPoint||a&&a.isHidden)){if((r.hoverPoints||[]).forEach(function(t){-1===n.indexOf(t)&&t.setState()}),r.hoverSeries!==l&&l.onMouseOver(),c=this.getActiveSeries(n),r.series.forEach(function(t){(t.options.inactiveOtherPoints||-1===c.indexOf(t))&&t.setState("inactive",!0)}),(n||[]).forEach(function(t){t.setState("hover")}),r.hoverPoint&&r.hoverPoint.firePointEvent("mouseOut"),!d.series)return;d.firePointEvent("mouseOver"),r.hoverPoints=n,r.hoverPoint=d,a&&a.refresh(h?n:d,i)}else o&&a&&!a.isHidden&&(d=a.getAnchor([{}],i),a.updatePosition({plotX:d[0],plotY:d[1]}));this.unDocMouseMove||(this.unDocMouseMove=e(r.container.ownerDocument,"mousemove",function(e){var i=s[t.hoverChartIndex];i&&i.pointer.onDocumentMouseMove(e)})),r.axes.forEach(function(e){var s=u(e.crosshair.snap,!0),o=s?t.find(n,function(t){return t.series[e.coll]===e}):void 0;o||!s?e.drawCrosshair(i,o):e.hideCrosshair()})},getActiveSeries:function(t){var e,i=[];return(t||[]).forEach(function(t){e=t.series,i.push(e),e.linkedParent&&i.push(e.linkedParent),e.linkedSeries&&(i=i.concat(e.linkedSeries)),e.navigatorSeries&&i.push(e.navigatorSeries)}),i},reset:function(t,e){var i=this.chart,s=i.hoverSeries,o=i.hoverPoint,n=i.hoverPoints,r=i.tooltip,a=r&&r.shared?n:o;t&&a&&f(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?r&&a&&f(a).length&&(r.refresh(a),r.shared&&n?n.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):o&&(o.setState(o.state,!0),i.axes.forEach(function(t){t.crosshair&&t.drawCrosshair(null,o)}))):(o&&o.onMouseOut(),n&&n.forEach(function(t){t.setState()}),s&&s.onMouseOut(),r&&r.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),this.hoverX=i.hoverPoints=i.hoverPoint=null)},scaleGroups:function(t,e){var i,s=this.chart;s.series.forEach(function(o){i=t||o.getPlotBox(),o.xAxis&&o.xAxis.zoomEnabled&&o.group&&(o.group.attr(i),o.markerGroup&&(o.markerGroup.attr(i),o.markerGroup.clip(e?s.clipRect:null)),o.dataLabelsGroup&&o.dataLabelsGroup.attr(i))}),s.clipRect.attr(e||s.clipBox)},dragStart:function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=this.mouseDownX=t.chartX,e.mouseDownY=this.mouseDownY=t.chartY},drag:function(t){var e,i=this.chart,s=i.options.chart,n=t.chartX,r=t.chartY,a=this.zoomHor,h=this.zoomVert,l=i.plotLeft,c=i.plotTop,d=i.plotWidth,p=i.plotHeight,u=this.selectionMarker,f=this.mouseDownX,g=this.mouseDownY,m=s.panKey&&t[s.panKey+"Key"];u&&u.touch||(n<l?n=l:n>l+d&&(n=l+d),r<c?r=c:r>c+p&&(r=c+p),this.hasDragged=Math.sqrt(Math.pow(f-n,2)+Math.pow(g-r,2)),10<this.hasDragged&&(e=i.isInsidePlot(f-l,g-c),i.hasCartesianSeries&&(this.zoomX||this.zoomY)&&e&&!m&&!u&&(this.selectionMarker=u=i.renderer.rect(l,c,a?1:d,h?1:p,0).attr({"class":"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||u.attr({fill:s.selectionMarkerFill||o("#335cad").setOpacity(.25).get()})),u&&a&&(n-=f,u.attr({width:Math.abs(n),x:(0<n?0:n)+f})),u&&h&&(n=r-g,u.attr({height:Math.abs(n),y:(0<n?0:n)+g})),e&&!u&&s.panning&&i.pan(t,s.panning)))},drop:function(t){var e=this,i=this.chart,s=this.hasPinched;if(this.selectionMarker){var o,h={originalEvent:t,xAxis:[],yAxis:[]},d=this.selectionMarker,p=d.attr?d.attr("x"):d.x,u=d.attr?d.attr("y"):d.y,f=d.attr?d.attr("width"):d.width,g=d.attr?d.attr("height"):d.height;(this.hasDragged||s)&&(i.axes.forEach(function(i){if(i.zoomEnabled&&r(i.min)&&(s||e[{xAxis:"zoomX",yAxis:"zoomY"}[i.coll]])){var n=i.horiz,a="touchend"===t.type?i.minPixelPadding:0,l=i.toValue((n?p:u)+a);n=i.toValue((n?p+f:u+g)-a);h[i.coll].push({axis:i,min:Math.min(l,n),max:Math.max(l,n)}),o=!0}}),o&&l(i,"selection",h,function(t){i.zoom(a(t,s?{animation:!1}:null))})),c(i.index)&&(this.selectionMarker=this.selectionMarker.destroy()),s&&this.scaleGroups()}i&&c(i.index)&&(n(i.container,{cursor:i._cursor}),i.cancelClick=10<this.hasDragged,i.mouseIsDown=this.hasDragged=this.hasPinched=!1,this.pinchDown=[])},onContainerMouseDown:function(t){2!==(t=this.normalize(t)).button&&(this.zoomOption(t),t.preventDefault&&t.preventDefault(),this.dragStart(t))},onDocumentMouseUp:function(e){s[t.hoverChartIndex]&&s[t.hoverChartIndex].pointer.drop(e)},onDocumentMouseMove:function(t){var e=this.chart,i=this.chartPosition;t=this.normalize(t,i),!i||this.inClass(t.target,"highcharts-tracker")||e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)||this.reset()},onContainerMouseLeave:function(e){var i=s[t.hoverChartIndex];i&&(e.relatedTarget||e.toElement)&&(i.pointer.reset(),i.pointer.chartPosition=null)},onContainerMouseMove:function(e){var i=this.chart;r(t.hoverChartIndex)&&s[t.hoverChartIndex]&&s[t.hoverChartIndex].mouseIsDown||(t.hoverChartIndex=i.index),(e=this.normalize(e)).preventDefault||(e.returnValue=!1),"mousedown"===i.mouseIsDown&&this.drag(e),!this.inClass(e.target,"highcharts-tracker")&&!i.isInsidePlot(e.chartX-i.plotLeft,e.chartY-i.plotTop)||i.openMenu||this.runPointActions(e)},inClass:function(t,e){for(var s;t;){if(s=i(t,"class")){if(-1!==s.indexOf(e))return!0;if(-1!==s.indexOf("highcharts-container"))return!1}t=t.parentNode}},onTrackerMouseOut:function(t){var e=this.chart.hoverSeries;t=t.relatedTarget||t.toElement,this.isDirectTouch=!1,!e||!t||e.stickyTracking||this.inClass(t,"highcharts-tooltip")||this.inClass(t,"highcharts-series-"+e.index)&&this.inClass(t,"highcharts-tracker")||e.onMouseOut()},onContainerClick:function(t){var e=this.chart,i=e.hoverPoint,s=e.plotLeft,o=e.plotTop;t=this.normalize(t),e.cancelClick||(i&&this.inClass(t.target,"highcharts-tracker")?(l(i.series,"click",a(t,{point:i})),e.hoverPoint&&i.firePointEvent("click",t)):(a(t,this.getCoordinates(t)),e.isInsidePlot(t.chartX-s,t.chartY-o)&&l(e,"click",t)))},setDOMEvents:function(){var i=this,s=i.chart.container,o=s.ownerDocument;s.onmousedown=function(t){i.onContainerMouseDown(t)},s.onmousemove=function(t){i.onContainerMouseMove(t)},s.onclick=function(t){i.onContainerClick(t)},this.unbindContainerMouseLeave=e(s,"mouseleave",i.onContainerMouseLeave),t.unbindDocumentMouseUp||(t.unbindDocumentMouseUp=e(o,"mouseup",i.onDocumentMouseUp)),t.hasTouch&&(s.ontouchstart=function(t){i.onContainerTouchStart(t)},s.ontouchmove=function(t){i.onContainerTouchMove(t)},t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=e(o,"touchend",i.onDocumentTouchEnd)))},destroy:function(){var e=this;e.unDocMouseMove&&e.unDocMouseMove(),this.unbindContainerMouseLeave(),t.chartCount||(t.unbindDocumentMouseUp&&(t.unbindDocumentMouseUp=t.unbindDocumentMouseUp()),t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),t.objectEach(e,function(t,i){e[i]=null})}}}),e(i,"parts/TouchPointer.js",[i["parts/Globals.js"]],function(t){var e=t.charts,i=t.extend,s=t.noop,o=t.pick;i(t.Pointer.prototype,{pinchTranslate:function(t,e,i,s,o,n){this.zoomHor&&this.pinchTranslateDirection(!0,t,e,i,s,o,n),this.zoomVert&&this.pinchTranslateDirection(!1,t,e,i,s,o,n)},pinchTranslateDirection:function(t,e,i,s,o,n,r,a){var h,l,c,d=this.chart,p=t?"x":"y",u=t?"X":"Y",f="chart"+u,g=t?"width":"height",m=d["plot"+(t?"Left":"Top")],x=a||1,v=d.inverted,y=d.bounds[t?"h":"v"],b=1===e.length,M=e[0][f],k=i[0][f],w=!b&&e[1][f],S=!b&&i[1][f];(i=function(){!b&&20<Math.abs(M-w)&&(x=a||Math.abs(k-S)/Math.abs(M-w)),l=(m-k)/x+M,h=d["plot"+(t?"Width":"Height")]/x})(),(e=l)<y.min?(e=y.min,c=!0):e+h>y.max&&(e=y.max-h,c=!0),c?(k-=.8*(k-r[p][0]),b||(S-=.8*(S-r[p][1])),i()):r[p]=[k,S],v||(n[p]=l-m,n[g]=h),n=v?1/x:x,o[g]=h,o[p]=e,s[v?t?"scaleY":"scaleX":"scale"+u]=x,s["translate"+u]=n*m+(k-n*M)},pinch:function(t){var e=this,n=e.chart,r=e.pinchDown,a=t.touches,h=a.length,l=e.lastValidTouch,c=e.hasZoom,d=e.selectionMarker,p={},u=1===h&&(e.inClass(t.target,"highcharts-tracker")&&n.runTrackerClick||e.runChartClick),f={};1<h&&(e.initiated=!0),c&&e.initiated&&!u&&t.preventDefault(),[].map.call(a,function(t){return e.normalize(t)}),"touchstart"===t.type?([].forEach.call(a,function(t,e){r[e]={chartX:t.chartX,chartY:t.chartY}}),l.x=[r[0].chartX,r[1]&&r[1].chartX],l.y=[r[0].chartY,r[1]&&r[1].chartY],n.axes.forEach(function(t){if(t.zoomEnabled){var e=n.bounds[t.horiz?"h":"v"],i=t.minPixelPadding,s=t.toPixels(o(t.options.min,t.dataMin)),r=t.toPixels(o(t.options.max,t.dataMax)),a=Math.max(s,r);e.min=Math.min(t.pos,Math.min(s,r)-i),e.max=Math.max(t.pos+t.len,a+i)}}),e.res=!0):e.followTouchMove&&1===h?this.runPointActions(e.normalize(t)):r.length&&(d||(e.selectionMarker=d=i({destroy:s,touch:!0},n.plotBox)),e.pinchTranslate(r,a,p,d,f,l),e.hasPinched=c,e.scaleGroups(p,f),e.res&&(e.res=!1,this.reset(!1,0)))},touch:function(e,i){var s,n=this.chart;n.index!==t.hoverChartIndex&&this.onContainerMouseLeave({relatedTarget:!0}),t.hoverChartIndex=n.index,1===e.touches.length?(e=this.normalize(e),n.isInsidePlot(e.chartX-n.plotLeft,e.chartY-n.plotTop)&&!n.openMenu?(i&&this.runPointActions(e),"touchmove"===e.type&&(s=!!(i=this.pinchDown)[0]&&4<=Math.sqrt(Math.pow(i[0].chartX-e.chartX,2)+Math.pow(i[0].chartY-e.chartY,2))),o(s,!0)&&this.pinch(e)):i&&this.reset()):2===e.touches.length&&this.pinch(e)},onContainerTouchStart:function(t){this.zoomOption(t),this.touch(t,!0)},onContainerTouchMove:function(t){this.touch(t)},onDocumentTouchEnd:function(i){e[t.hoverChartIndex]&&e[t.hoverChartIndex].pointer.drop(i)}})}),e(i,"parts/MSPointer.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.charts,s=t.css,o=t.doc,n=t.extend,r=t.noop,a=t.Pointer,h=t.removeEvent,l=t.win,c=t.wrap;if(!t.hasTouch&&(l.PointerEvent||l.MSPointerEvent)){var d={},p=!!l.PointerEvent,u=function(){var e=[];return e.item=function(t){return this[t]},t.objectEach(d,function(t){e.push({pageX:t.pageX,pageY:t.pageY,target:t.target})}),e},f=function(e,s,o,n){"touch"!==e.pointerType&&e.pointerType!==e.MSPOINTER_TYPE_TOUCH||!i[t.hoverChartIndex]||(n(e),(n=i[t.hoverChartIndex].pointer)[s]({type:o,target:e.currentTarget,preventDefault:r,touches:u()}))};n(a.prototype,{onContainerPointerDown:function(t){f(t,"onContainerTouchStart","touchstart",function(t){d[t.pointerId]={pageX:t.pageX,pageY:t.pageY,target:t.currentTarget}})},onContainerPointerMove:function(t){f(t,"onContainerTouchMove","touchmove",function(t){d[t.pointerId]={pageX:t.pageX,pageY:t.pageY},d[t.pointerId].target||(d[t.pointerId].target=t.currentTarget)})},onDocumentPointerUp:function(t){f(t,"onDocumentTouchEnd","touchend",function(t){delete d[t.pointerId]})},batchMSEvents:function(t){t(this.chart.container,p?"pointerdown":"MSPointerDown",this.onContainerPointerDown),t(this.chart.container,p?"pointermove":"MSPointerMove",this.onContainerPointerMove),t(o,p?"pointerup":"MSPointerUp",this.onDocumentPointerUp)}}),c(a.prototype,"init",function(t,e,i){t.call(this,e,i),this.hasZoom&&s(e.container,{"-ms-touch-action":"none","touch-action":"none"})}),c(a.prototype,"setDOMEvents",function(t){t.apply(this),(this.hasZoom||this.followTouchMove)&&this.batchMSEvents(e)}),c(a.prototype,"destroy",function(t){this.batchMSEvents(h),t.call(this)})}}),e(i,"parts/Legend.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.css,s=t.discardElement,o=t.defined,n=t.fireEvent,r=t.isFirefox,a=t.marginNames,h=t.merge,l=t.pick,c=t.setAnimation,d=t.stableSort,p=t.win,u=t.wrap;t.Legend=function(t,e){this.init(t,e)},t.Legend.prototype={init:function(t,i){this.chart=t,this.setOptions(i),i.enabled&&(this.render(),e(this.chart,"endResize",function(){this.legend.positionCheckboxes()}),this.proximate?this.unchartrender=e(this.chart,"render",function(){this.legend.proximatePositions(),this.legend.positionItems()}):this.unchartrender&&this.unchartrender())},setOptions:function(t){var e=l(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=h(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop||0,this.padding=e,this.initialItemY=e-5,this.symbolWidth=l(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted},update:function(t,e){var i=this.chart;this.setOptions(h(!0,this.options,t)),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,l(e,!0)&&i.redraw(),n(this,"afterUpdate")},colorizeItem:function(t,e){if(t.legendGroup[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var i=this.options,s=t.legendItem,o=t.legendLine,r=t.legendSymbol,a=this.itemHiddenStyle.color,h=(i=e?i.itemStyle.color:a,e&&t.color||a),l=t.options&&t.options.marker,c={fill:h};s&&s.css({fill:i,color:i}),o&&o.attr({stroke:h}),r&&(l&&r.isMarker&&(c=t.pointAttribs(),e||(c.stroke=c.fill=a)),r.attr(c))}n(this,"afterColorizeItem",{item:t,visible:e})},positionItems:function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},positionItem:function(t){var e=(i=this.options).symbolPadding,i=!i.rtl,s=(n=t._legendItemPos)[0],n=n[1],r=t.checkbox;(t=t.legendGroup)&&t.element&&t[o(t.translateY)?"animate":"attr"]({translateX:i?s:this.legendWidth-s-2*e-4,translateY:n}),r&&(r.x=s,r.y=n)},destroyItem:function(t){var e=t.checkbox;["legendItem","legendLine","legendSymbol","legendGroup"].forEach(function(e){t[e]&&(t[e]=t[e].destroy())}),e&&s(t.checkbox)},destroy:function(){function t(t){this[t]&&(this[t]=this[t].destroy())}this.getAllItems().forEach(function(e){["legendItem","legendGroup"].forEach(t,e)}),"clipRect up down pager nav box title group".split(" ").forEach(t,this),this.display=null},positionCheckboxes:function(){var t,e=this.group&&this.group.alignAttr,s=this.clipHeight||this.legendHeight,o=this.titleHeight;e&&(t=e.translateY,this.allItems.forEach(function(n){var r,a=n.checkbox;a&&(r=t+o+a.y+(this.scrollOffset||0)+3,i(a,{left:e.translateX+n.checkboxOffset+a.x-20+"px",top:r+"px",display:this.proximate||r>t-6&&r<t+s-6?"":"none"}))},this))},renderTitle:function(){var t=this.options,e=this.padding,i=t.title,s=0;i.text&&(this.title||(this.title=this.chart.renderer.label(i.text,e-3,e-4,null,null,null,t.useHTML,null,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(i.style),this.title.add(this.group)),i.width||this.title.css({width:this.maxLegendWidth+"px"}),s=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:s})),this.titleHeight=s},setText:function(e){var i=this.options;e.legendItem.attr({text:i.labelFormat?t.format(i.labelFormat,e,this.chart.time):i.labelFormatter.call(e)})},renderItem:function(t){var e=this.chart,i=e.renderer,s=this.options,o=this.symbolWidth,n=s.symbolPadding,r=this.itemStyle,a=this.itemHiddenStyle,c="horizontal"===s.layout?l(s.itemDistance,20):0,d=!s.rtl,p=t.legendItem,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,m=(c=o+n+c+((g=this.createCheckboxForItem&&g&&g.showCheckbox)?20:0),s.useHTML),x=t.options.className;p||(t.legendGroup=i.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(x?" "+x:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),t.legendItem=p=i.text("",d?o+n:-n,this.baseline||0,m),e.styledMode||p.css(h(t.visible?r:a)),p.attr({align:d?"left":"right",zIndex:2}).add(t.legendGroup),this.baseline||(this.fontMetrics=i.fontMetrics(e.styledMode?12:r.fontSize,p),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,p.attr("y",this.baseline)),this.symbolHeight=s.symbolHeight||this.fontMetrics.f,f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,p,m)),g&&!t.checkbox&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),!e.styledMode&&r.width||p.css({width:(s.itemWidth||this.widthOption||e.spacingBox.width)-c}),this.setText(t),e=p.getBBox(),t.itemWidth=t.checkboxOffset=s.itemWidth||t.legendItemWidth||e.width+c,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(t.legendItemHeight||e.height||this.symbolHeight)},layoutItem:function(t){var e=this.options,i=this.padding,s="horizontal"===e.layout,o=t.itemHeight,n=e.itemMarginBottom||0,r=this.itemMarginTop,a=s?l(e.itemDistance,20):0,h=this.maxLegendWidth;e=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth;s&&this.itemX-i+e>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=r+this.lastLineHeight+n),this.lastLineHeight=0),this.lastItemY=r+this.itemY+n,this.lastLineHeight=Math.max(o,this.lastLineHeight),t._legendItemPos=[this.itemX,this.itemY],s?this.itemX+=e:(this.itemY+=r+o+n,this.lastLineHeight=o),this.offsetWidth=this.widthOption||Math.max((s?this.itemX-i-(t.checkbox?0:a):e)+i,this.offsetWidth)},getAllItems:function(){var t=[];return this.chart.series.forEach(function(e){var i=e&&e.options;e&&l(i.showInLegend,!o(i.linkedTo)&&void 0,!0)&&(t=t.concat(e.legendItems||("point"===i.legendType?e.data:e)))}),n(this,"afterGetAllItems",{allItems:t}),t},getAlignment:function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},adjustMargins:function(t,e){var i=this.chart,s=this.options,n=this.getAlignment(),r=void 0!==i.options.title.margin?i.titleOffset+i.options.title.margin:0;n&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(h,c){h.test(n)&&!o(t[c])&&(i[a[c]]=Math.max(i[a[c]],i.legend[(c+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][c]*s[c%2?"x":"y"]+l(s.margin,12)+e[c]+(0===c&&(0===i.titleOffset?0:r))))})},proximatePositions:function(){var e=this.chart,i=[],s="left"===this.options.align;this.allItems.forEach(function(o){var n,r,a;r=s,o.yAxis&&o.points&&(o.xAxis.options.reversed&&(r=!r),n=t.find(r?o.points:o.points.slice(0).reverse(),function(e){return t.isNumber(e.plotY)}),r=o.legendGroup.getBBox().height,a=o.yAxis.top-e.plotTop,o.visible?(n=n?n.plotY:o.yAxis.height,n+=a-.3*r):n=a+o.yAxis.height,i.push({target:n,size:r,item:o}))},this),t.distribute(i,e.plotHeight),i.forEach(function(t){t.item._legendItemPos[1]=e.plotTop-e.spacing[0]+t.pos})},render:function(){var e,i,s,o=this.chart,r=o.renderer,a=this.group,l=this.box,c=this.options,p=this.padding;this.itemX=p,this.itemY=this.initialItemY,this.lastItemY=this.offsetWidth=0,this.widthOption=t.relativeLength(c.width,o.spacingBox.width-p),e=o.spacingBox.width-2*p-c.x,-1<["rm","lm"].indexOf(this.getAlignment().substring(0,2))&&(e/=2),this.maxLegendWidth=this.widthOption||e,a||(this.group=a=r.g("legend").attr({zIndex:7}).add(),this.contentGroup=r.g().attr({zIndex:1}).add(a),this.scrollGroup=r.g().add(this.contentGroup)),this.renderTitle(),e=this.getAllItems(),d(e,function(t,e){return(t.options&&t.options.legendIndex||0)-(e.options&&e.options.legendIndex||0)}),c.reversed&&e.reverse(),this.allItems=e,this.display=i=!!e.length,this.itemHeight=this.totalItemWidth=this.maxItemWidth=this.lastLineHeight=0,e.forEach(this.renderItem,this),e.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+p,s=this.lastItemY+this.lastLineHeight+this.titleHeight,s=this.handleOverflow(s),s+=p,l||(this.box=l=r.rect().addClass("highcharts-legend-box").attr({r:c.borderRadius}).add(a),l.isNew=!0),o.styledMode||l.attr({stroke:c.borderColor,"stroke-width":c.borderWidth||0,fill:c.backgroundColor||"none"}).shadow(c.shadow),0<e&&0<s&&(l[l.isNew?"attr":"animate"](l.crisp.call({},{x:0,y:0,width:e,height:s},l.strokeWidth())),l.isNew=!1),l[i?"show":"hide"](),o.styledMode&&"none"===a.getStyle("display")&&(e=s=0),this.legendWidth=e,this.legendHeight=s,i&&(r=o.spacingBox,/(lth|ct|rth)/.test(this.getAlignment())&&(l=r.y+o.titleOffset,r=h(r,{y:0<o.titleOffset?l+=o.options.title.margin:l})),a.align(h(c,{width:e,height:s,verticalAlign:this.proximate?"top":c.verticalAlign}),!0,r)),this.proximate||this.positionItems(),n(this,"afterRender")},handleOverflow:function(t){var e,i,s=this,o=this.chart,n=o.renderer,r=this.options,a=r.y,h=this.padding,c=(a=o.spacingBox.height+("top"===r.verticalAlign?-a:a)-h,r.maxHeight),d=this.clipRect,p=r.navigation,u=l(p.animation,!0),f=p.arrowSize||12,g=this.nav,m=this.pages,x=this.allItems,v=function(t){"number"==typeof t?d.attr({height:t}):d&&(s.clipRect=d.destroy(),s.contentGroup.clip()),s.contentGroup.div&&(s.contentGroup.div.style.clip=t?"rect("+h+"px,9999px,"+(h+t)+"px,0)":"auto")},y=function(t){return s[t]=n.circle(0,0,1.3*f).translate(f/2,f/2).add(g),o.styledMode||s[t].attr("fill","rgba(0,0,0,0.0001)"),s[t]};return"horizontal"!==r.layout||"middle"===r.verticalAlign||r.floating||(a/=2),c&&(a=Math.min(a,c)),m.length=0,t>a&&!1!==p.enabled?(this.clipHeight=e=Math.max(a-20-this.titleHeight-h,0),this.currentPage=l(this.currentPage,1),this.fullHeight=t,x.forEach(function(t,s){var o=t._legendItemPos[1],n=Math.round(t.legendItem.getBBox().height),r=m.length;(!r||o-m[r-1]>e&&(i||o)!==m[r-1])&&(m.push(i||o),r++),t.pageIx=r-1,i&&(x[s-1].pageIx=r-1),s===x.length-1&&o+n-m[r-1]>e&&o!==i&&(m.push(o),t.pageIx=r),o!==i&&(i=o)}),d||(d=s.clipRect=n.clipRect(0,h,9999,0),s.contentGroup.clip(d)),v(e),g||(this.nav=g=n.g().attr({zIndex:1}).add(this.group),this.up=n.symbol("triangle",0,0,f,f).add(g),y("upTracker").on("click",function(){s.scroll(-1,u)}),this.pager=n.text("",15,10).addClass("highcharts-legend-navigation"),o.styledMode||this.pager.css(p.style),this.pager.add(g),this.down=n.symbol("triangle-down",0,0,f,f).add(g),y("downTracker").on("click",function(){s.scroll(1,u)})),s.scroll(0),t=a):g&&(v(),this.nav=g.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},scroll:function(t,e){var i=this.pages,s=i.length,o=this.currentPage+t;t=this.clipHeight;var n=this.options.navigation,r=this.pager,a=this.padding;o>s&&(o=s),0<o&&(void 0!==e&&c(e,this.chart),this.nav.attr({translateX:a,translateY:t+this.padding+7+this.titleHeight,visibility:"visible"}),[this.up,this.upTracker].forEach(function(t){t.attr({"class":1===o?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),r.attr({text:o+"/"+s}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,"class":o===s?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),this.chart.styledMode||(this.up.attr({fill:1===o?n.inactiveColor:n.activeColor}),this.upTracker.css({cursor:1===o?"default":"pointer"}),this.down.attr({fill:o===s?n.inactiveColor:n.activeColor}),this.downTracker.css({cursor:o===s?"default":"pointer"})),this.scrollOffset=-i[o-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=o,this.positionCheckboxes())}},t.LegendSymbolMixin={drawRectangle:function(t,e){var i=t.symbolHeight,s=t.options.squareSymbol;e.legendSymbol=this.chart.renderer.rect(s?(t.symbolWidth-i)/2:0,t.baseline-i+1,s?i:t.symbolWidth,i,l(t.options.symbolRadius,i/2)).addClass("highcharts-point").attr({zIndex:3}).add(e.legendGroup)},drawLineMarker:function(t){var e=this.options,i=e.marker,s=t.symbolWidth,o=t.symbolHeight,n=o/2,r=this.chart.renderer,a=this.legendGroup;t=t.baseline-Math.round(.3*t.fontMetrics.b);var c={};this.chart.styledMode||(c={"stroke-width":e.lineWidth||0},e.dashStyle&&(c.dashstyle=e.dashStyle)),this.legendLine=r.path(["M",0,t,"L",s,t]).addClass("highcharts-graph").attr(c).add(a),i&&!1!==i.enabled&&s&&(e=Math.min(l(i.radius,n),n),0===this.symbol.indexOf("url")&&(i=h(i,{width:o,height:o}),e=0),this.legendSymbol=i=r.symbol(this.symbol,s/2-e,t-e,2*e,2*e,i).addClass("highcharts-point").add(a),i.isMarker=!0)}},(/Trident\/7\.0/.test(p.navigator&&p.navigator.userAgent)||r)&&u(t.Legend.prototype,"positionItem",function(t,e){var i=this,s=function(){e._legendItemPos&&t.call(i,e)};s(),i.bubbleLegend||setTimeout(s)})}),e(i,"parts/Chart.js",[i["parts/Globals.js"]],function(t){
var e=t.addEvent,i=t.animate,s=t.animObject,o=t.attr,n=t.doc,r=t.Axis,a=t.createElement,h=t.defaultOptions,l=t.discardElement,c=t.charts,d=t.css,p=t.defined,u=t.extend,f=t.find,g=t.fireEvent,m=t.isNumber,x=t.isObject,v=t.isString,y=t.Legend,b=t.marginNames,M=t.merge,k=t.objectEach,w=t.Pointer,S=t.pick,A=t.pInt,T=t.removeEvent,P=t.seriesTypes,E=t.splat,C=t.syncTimeout,L=t.win,O=t.Chart=function(){this.getArgs.apply(this,arguments)};t.chart=function(t,e,i){return new O(t,e,i)},u(O.prototype,{callbacks:[],getArgs:function(){var t=[].slice.call(arguments);(v(t[0])||t[0].nodeName)&&(this.renderTo=t.shift()),this.init(t[0],t[1])},init:function(i,s){var o,n=i.series,r=i.plotOptions||{};g(this,"init",{args:arguments},function(){i.series=null,o=M(h,i),k(o.plotOptions,function(t,e){x(t)&&(t.tooltip=r[e]&&M(r[e].tooltip)||void 0)}),o.tooltip.userOptions=i.chart&&i.chart.forExport&&i.tooltip.userOptions||i.tooltip,o.series=i.series=n,this.userOptions=i;var a=o.chart,l=a.events;this.margin=[],this.spacing=[],this.bounds={h:{},v:{}},this.labelCollectors=[],this.callback=s,this.isResizing=0,this.options=o,this.axes=[],this.series=[],this.time=i.time&&Object.keys(i.time).length?new t.Time(i.time):t.time,this.styledMode=a.styledMode,this.hasCartesianSeries=a.showAxes;var d=this;d.index=c.length,c.push(d),t.chartCount++,l&&k(l,function(t,i){e(d,i,t)}),d.xAxis=[],d.yAxis=[],d.pointCount=d.colorCounter=d.symbolCounter=0,g(d,"afterInit"),d.firstRender()})},initSeries:function(e){var i=this.options.chart;return(i=P[e.type||i.type||i.defaultSeriesType])||t.error(17,!0,this),(i=new i).init(this,e),i},orderSeries:function(t){var e=this.series;for(t=t||0;t<e.length;t++)e[t]&&(e[t].index=t,e[t].name=e[t].getName())},isInsidePlot:function(t,e,i){var s=i?e:t;return t=i?t:e,0<=s&&s<=this.plotWidth&&0<=t&&t<=this.plotHeight},redraw:function(e){g(this,"beforeRedraw");var i,s,o,n=this.axes,r=this.series,a=this.pointer,h=this.legend,l=this.userOptions.legend,c=this.isDirtyLegend,d=this.hasCartesianSeries,p=this.isDirtyBox,f=this.renderer,m=f.isHidden(),x=[];for(this.setResponsive&&this.setResponsive(!1),t.setAnimation(e,this),m&&this.temporaryDisplay(),this.layOutTitles(),e=r.length;e--;)if((o=r[e]).options.stacking&&(i=!0,o.isDirty)){s=!0;break}if(s)for(e=r.length;e--;)(o=r[e]).options.stacking&&(o.isDirty=!0);r.forEach(function(t){t.isDirty&&("point"===t.options.legendType?(t.updateTotals&&t.updateTotals(),c=!0):l&&(l.labelFormatter||l.labelFormat)&&(c=!0)),t.isDirtyData&&g(t,"updatedData")}),c&&h&&h.options.enabled&&(h.render(),this.isDirtyLegend=!1),i&&this.getStacks(),d&&n.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),d&&(n.forEach(function(t){t.isDirty&&(p=!0)}),n.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,x.push(function(){g(t,"afterSetExtremes",u(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(p||i)&&t.redraw()})),p&&this.drawChartBox(),g(this,"predraw"),r.forEach(function(t){(p||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),f.draw(),g(this,"redraw"),g(this,"render"),m&&this.temporaryDisplay(!0),x.forEach(function(t){t.call()})},get:function(t){function e(e){return e.id===t||e.options&&e.options.id===t}var i,s,o=this.series;for(i=f(this.axes,e)||f(this.series,e),s=0;!i&&s<o.length;s++)i=f(o[s].points||[],e);return i},getAxes:function(){var t=this,e=(i=this.options).xAxis=E(i.xAxis||{}),i=i.yAxis=E(i.yAxis||{});g(this,"getAxes"),e.forEach(function(t,e){t.index=e,t.isX=!0}),i.forEach(function(t,e){t.index=e}),e.concat(i).forEach(function(e){new r(t,e)}),g(this,"afterGetAxes")},getSelectedPoints:function(){var t=[];return this.series.forEach(function(e){t=t.concat((e[e.hasGroupedData?"points":"data"]||[]).filter(function(t){return t.selected}))}),t},getSelectedSeries:function(){return this.series.filter(function(t){return t.selected})},setTitle:function(t,e,i){var s=this,o=s.options,n=s.styledMode;[["title",t,o.title=M(!n&&{style:{color:"#333333",fontSize:o.isStock?"16px":"18px"}},o.title,t)],["subtitle",e,o=o.subtitle=M(!n&&{style:{color:"#666666"}},o.subtitle,e)]].forEach(function(t,e){var i=t[0],o=s[i],r=t[1];t=t[2],o&&r&&(s[i]=o=o.destroy()),t&&!o&&(s[i]=s.renderer.text(t.text,0,0,t.useHTML).attr({align:t.align,"class":"highcharts-"+i,zIndex:t.zIndex||4}).add(),s[i].update=function(t){s.setTitle(!e&&t,e&&t)},n||s[i].css(t.style))}),s.layOutTitles(i)},layOutTitles:function(t){var e,i=0,s=this.renderer,o=this.spacingBox;["title","subtitle"].forEach(function(t){var e,n=this[t],r=this.options[t];t="title"===t?-3:r.verticalAlign?0:i+2,n&&(this.styledMode||(e=r.style.fontSize),e=s.fontMetrics(e,n).b,n.css({width:(r.width||o.width+r.widthAdjust)+"px"}).align(u({y:t+e},r),!1,"spacingBox"),r.floating||r.verticalAlign||(i=Math.ceil(i+n.getBBox(r.useHTML).height)))},this),e=this.titleOffset!==i,this.titleOffset=i,!this.isDirtyBox&&e&&(this.isDirtyBox=this.isDirtyLegend=e,this.hasRendered&&S(t,!0)&&this.isDirtyBox&&this.redraw())},getChartSize:function(){var e=(i=this.options.chart).width,i=i.height,s=this.renderTo;p(e)||(this.containerWidth=t.getStyle(s,"width")),p(i)||(this.containerHeight=t.getStyle(s,"height")),this.chartWidth=Math.max(0,e||this.containerWidth||600),this.chartHeight=Math.max(0,t.relativeLength(i,this.chartWidth)||(1<this.containerHeight?this.containerHeight:400))},temporaryDisplay:function(e){var i=this.renderTo;if(e)for(;i&&i.style;)i.hcOrigStyle&&(t.css(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(n.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;i&&i.style&&(n.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,n.body.appendChild(i)),("none"===t.getStyle(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),t.css(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==n.body););},setClassName:function(t){this.container.className="highcharts-container "+(t||"")},getContainer:function(){var e,i,s,r=this.options,h=r.chart;e=this.renderTo;var l,p,f=t.uniqueKey();if(e||(this.renderTo=e=h.renderTo),v(e)&&(this.renderTo=e=n.getElementById(e)),e||t.error(13,!0,this),i=A(o(e,"data-highcharts-chart")),m(i)&&c[i]&&c[i].hasRendered&&c[i].destroy(),o(e,"data-highcharts-chart",this.index),e.innerHTML="",h.skipClone||e.offsetWidth||this.temporaryDisplay(),this.getChartSize(),i=this.chartWidth,s=this.chartHeight,d(e,{overflow:"hidden"}),this.styledMode||(l=u({position:"relative",overflow:"hidden",width:i+"px",height:s+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)"},h.style)),this.container=e=a("div",{id:f},l,e),this._cursor=e.style.cursor,this.renderer=new(t[h.renderer]||t.Renderer)(e,i,s,null,h.forExport,r.exporting&&r.exporting.allowHTML,this.styledMode),this.setClassName(h.className),this.styledMode)for(p in r.defs)this.renderer.definition(r.defs[p]);else this.renderer.setStyle(h.style);this.renderer.chartIndex=this.index,g(this,"afterGetContainer")},getMargins:function(t){var e=this.spacing,i=this.margin,s=this.titleOffset;this.resetMargins(),s&&!p(i[0])&&(this.plotTop=Math.max(this.plotTop,s+this.options.title.margin+e[0])),this.legend&&this.legend.display&&this.legend.adjustMargins(i,e),g(this,"getMargins"),t||this.getAxisMargins()},getAxisMargins:function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.margin;t.hasCartesianSeries&&t.axes.forEach(function(t){t.visible&&t.getOffset()}),b.forEach(function(s,o){p(i[o])||(t[s]+=e[o])}),t.setChartSize()},reflow:function(e){var i=this,s=i.options.chart,o=i.renderTo,r=p(s.width)&&p(s.height),a=s.width||t.getStyle(o,"width");s=s.height||t.getStyle(o,"height"),o=e?e.target:L;r||i.isPrinting||!a||!s||o!==L&&o!==n||(a===i.containerWidth&&s===i.containerHeight||(t.clearTimeout(i.reflowTimeout),i.reflowTimeout=C(function(){i.container&&i.setSize(void 0,void 0,!1)},e?100:0)),i.containerWidth=a,i.containerHeight=s)},setReflow:function(t){var i=this;!1===t||this.unbindReflow?!1===t&&this.unbindReflow&&(this.unbindReflow=this.unbindReflow()):(this.unbindReflow=e(L,"resize",function(t){i.reflow(t)}),e(this,"destroy",this.unbindReflow))},setSize:function(e,o,n){var r,a=this,h=a.renderer;a.isResizing+=1,t.setAnimation(n,a),a.oldChartHeight=a.chartHeight,a.oldChartWidth=a.chartWidth,void 0!==e&&(a.options.chart.width=e),void 0!==o&&(a.options.chart.height=o),a.getChartSize(),a.styledMode||((r=h.globalAnimation)?i:d)(a.container,{width:a.chartWidth+"px",height:a.chartHeight+"px"},r),a.setChartSize(!0),h.setSize(a.chartWidth,a.chartHeight,n),a.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),a.isDirtyLegend=!0,a.isDirtyBox=!0,a.layOutTitles(),a.getMargins(),a.redraw(n),a.oldChartHeight=null,g(a,"resize"),C(function(){a&&g(a,"endResize",null,function(){--a.isResizing})},s(r).duration)},setChartSize:function(t){var e,i,s,o,n=this.inverted,r=this.renderer,a=this.chartWidth,h=this.chartHeight,l=this.options.chart,c=this.spacing,d=this.clipOffset;this.plotLeft=e=Math.round(this.plotLeft),this.plotTop=i=Math.round(this.plotTop),this.plotWidth=s=Math.max(0,Math.round(a-e-this.marginRight)),this.plotHeight=o=Math.max(0,Math.round(h-i-this.marginBottom)),this.plotSizeX=n?o:s,this.plotSizeY=n?s:o,this.plotBorderWidth=l.plotBorderWidth||0,this.spacingBox=r.spacingBox={x:c[3],y:c[0],width:a-c[3]-c[1],height:h-c[0]-c[2]},this.plotBox=r.plotBox={x:e,y:i,width:s,height:o},a=2*Math.floor(this.plotBorderWidth/2),n=Math.ceil(Math.max(a,d[3])/2),r=Math.ceil(Math.max(a,d[0])/2),this.clipBox={x:n,y:r,width:Math.floor(this.plotSizeX-Math.max(a,d[1])/2-n),height:Math.max(0,Math.floor(this.plotSizeY-Math.max(a,d[2])/2-r))},t||this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),g(this,"afterSetChartSize",{skipAxes:t})},resetMargins:function(){g(this,"resetMargins");var t=this,e=t.options.chart;["margin","spacing"].forEach(function(i){var s=e[i],o=x(s)?s:[s,s,s,s];["Top","Right","Bottom","Left"].forEach(function(s,n){t[i][n]=S(e[i+s],o[n])})}),b.forEach(function(e,i){t[e]=S(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[0,0,0,0]},drawChartBox:function(){var t,e,i=this.options.chart,s=this.renderer,o=this.chartWidth,n=this.chartHeight,r=this.chartBackground,a=this.plotBackground,h=this.plotBorder,l=this.styledMode,c=this.plotBGImage,d=i.backgroundColor,p=i.plotBackgroundColor,u=i.plotBackgroundImage,f=this.plotLeft,m=this.plotTop,x=this.plotWidth,v=this.plotHeight,y=this.plotBox,b=this.clipRect,M=this.clipBox,k="animate";r||(this.chartBackground=r=s.rect().addClass("highcharts-background").add(),k="attr"),l?t=e=r.strokeWidth():(e=(t=i.borderWidth||0)+(i.shadow?8:0),d={fill:d||"none"},(t||r["stroke-width"])&&(d.stroke=i.borderColor,d["stroke-width"]=t),r.attr(d).shadow(i.shadow)),r[k]({x:e/2,y:e/2,width:o-e-t%2,height:n-e-t%2,r:i.borderRadius}),k="animate",a||(k="attr",this.plotBackground=a=s.rect().addClass("highcharts-plot-background").add()),a[k](y),l||(a.attr({fill:p||"none"}).shadow(i.plotShadow),u&&(c?c.animate(y):this.plotBGImage=s.image(u,f,m,x,v).add())),b?b.animate({width:M.width,height:M.height}):this.clipRect=s.clipRect(M),k="animate",h||(k="attr",this.plotBorder=h=s.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),l||h.attr({stroke:i.plotBorderColor,"stroke-width":i.plotBorderWidth||0,fill:"none"}),h[k](h.crisp({x:f,y:m,width:x,height:v},-h.strokeWidth())),this.isDirtyBox=!1,g(this,"afterDrawChartBox")},propFromSeries:function(){var t,e,i,s=this,o=s.options.chart,n=s.options.series;["inverted","angular","polar"].forEach(function(r){for(t=P[o.type||o.defaultSeriesType],i=o[r]||t&&t.prototype[r],e=n&&n.length;!i&&e--;)(t=P[n[e].type])&&t.prototype[r]&&(i=!0);s[r]=i})},linkSeries:function(){var t=this,e=t.series;e.forEach(function(t){t.linkedSeries.length=0}),e.forEach(function(e){var i=e.options.linkedTo;v(i)&&(i=":previous"===i?t.series[e.index-1]:t.get(i))&&i.linkedParent!==e&&(i.linkedSeries.push(e),e.linkedParent=i,e.visible=S(e.options.visible,i.options.visible,e.visible))}),g(this,"afterLinkSeries")},renderSeries:function(){this.series.forEach(function(t){t.translate(),t.render()})},renderLabels:function(){var t=this,e=t.options.labels;e.items&&e.items.forEach(function(i){var s=u(e.style,i.style),o=A(s.left)+t.plotLeft,n=A(s.top)+t.plotTop+12;delete s.left,delete s.top,t.renderer.text(i.html,o,n).attr({zIndex:2}).css(s).add()})},render:function(){var t,e,i,s=this.axes,o=this.renderer,n=this.options,r=0;this.setTitle(),this.legend=new y(this,n.legend),this.getStacks&&this.getStacks(),this.getMargins(!0),this.setChartSize(),n=this.plotWidth,s.some(function(t){if(t.horiz&&t.visible&&t.options.labels.enabled&&t.series.length)return r=21,!0}),t=this.plotHeight=Math.max(this.plotHeight-r,0),s.forEach(function(t){t.setScale()}),this.getAxisMargins(),e=1.1<n/this.plotWidth,i=1.05<t/this.plotHeight,(e||i)&&(s.forEach(function(t){(t.horiz&&e||!t.horiz&&i)&&t.setTickInterval(!0)}),this.getMargins()),this.drawChartBox(),this.hasCartesianSeries&&s.forEach(function(t){t.visible&&t.render()}),this.seriesGroup||(this.seriesGroup=o.g("series-group").attr({zIndex:3}).add()),this.renderSeries(),this.renderLabels(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},addCredits:function(t){var e=this;(t=M(!0,this.options.credits,t)).enabled&&!this.credits&&(this.credits=this.renderer.text(t.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){t.href&&(L.location.href=t.href)}).attr({align:t.position.align,zIndex:8}),e.styledMode||this.credits.css(t.style),this.credits.add().align(t.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},destroy:function(){var e,i=this,s=i.axes,o=i.series,n=i.container,r=n&&n.parentNode;for(g(i,"destroy"),i.renderer.forExport?t.erase(c,i):c[i.index]=void 0,t.chartCount--,i.renderTo.removeAttribute("data-highcharts-chart"),T(i),e=s.length;e--;)s[e]=s[e].destroy();for(this.scroller&&this.scroller.destroy&&this.scroller.destroy(),e=o.length;e--;)o[e]=o[e].destroy();"title subtitle chartBackground plotBackground plotBGImage plotBorder seriesGroup clipRect credits pointer rangeSelector legend resetZoomButton tooltip renderer".split(" ").forEach(function(t){var e=i[t];e&&e.destroy&&(i[t]=e.destroy())}),n&&(n.innerHTML="",T(n),r&&l(n)),k(i,function(t,e){delete i[e]})},firstRender:function(){var e=this,i=e.options;e.isReadyToRender&&!e.isReadyToRender()||(e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.getAxes(),(t.isArray(i.series)?i.series:[]).forEach(function(t){e.initSeries(t)}),e.linkSeries(),g(e,"beforeRender"),w&&(e.pointer=new w(e,i)),e.render(),!e.renderer.imgCount&&e.onload&&e.onload(),e.temporaryDisplay(!0))},onload:function(){[this.callback].concat(this.callbacks).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),g(this,"load"),g(this,"render"),p(this.index)&&this.setReflow(this.options.chart.reflow),this.onload=null}})}),e(i,"parts/ScrollablePlotArea.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.Chart;e(i,"afterSetChartSize",function(e){var i=this.options.chart.scrollablePlotArea;(i=i&&i.minWidth)&&!this.renderer.forExport&&(this.scrollablePixels=i=Math.max(0,i-this.chartWidth))&&(this.plotWidth+=i,this.clipBox.width+=i,e.skipAxes||this.axes.forEach(function(e){1===e.side?e.getPlotLinePath=function(){var i,s=this.right;return this.right=s-e.chart.scrollablePixels,i=t.Axis.prototype.getPlotLinePath.apply(this,arguments),this.right=s,i}:(e.setAxisSize(),e.setAxisTranslation())}))}),e(i,"render",function(){this.scrollablePixels?(this.setUpScrolling&&this.setUpScrolling(),this.applyFixed()):this.fixedDiv&&this.applyFixed()}),i.prototype.setUpScrolling=function(){this.scrollingContainer=t.createElement("div",{className:"highcharts-scrolling"},{overflowX:"auto",WebkitOverflowScrolling:"touch"},this.renderTo),this.innerContainer=t.createElement("div",{className:"highcharts-inner-container"},null,this.scrollingContainer),this.innerContainer.appendChild(this.container),this.setUpScrolling=null},i.prototype.moveFixedElements=function(){var t=this.container,e=this.fixedRenderer;[this.inverted?".highcharts-xaxis":".highcharts-yaxis",this.inverted?".highcharts-xaxis-labels":".highcharts-yaxis-labels",".highcharts-contextbutton",".highcharts-credits",".highcharts-legend",".highcharts-reset-zoom",".highcharts-subtitle",".highcharts-title",".highcharts-legend-checkbox"].forEach(function(i){[].forEach.call(t.querySelectorAll(i),function(t){(t.namespaceURI===e.SVG_NS?e.box:e.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})})},i.prototype.applyFixed=function(){var i,s=!this.fixedDiv,o=this.options.chart.scrollablePlotArea;s&&(this.fixedDiv=t.createElement("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:2},null,!0),this.renderTo.insertBefore(this.fixedDiv,this.renderTo.firstChild),this.renderTo.style.overflow="visible",this.fixedRenderer=i=new t.Renderer(this.fixedDiv,0,0),this.scrollableMask=i.path().attr({fill:t.color(this.options.chart.backgroundColor||"#fff").setOpacity(t.pick(o.opacity,.85)).get(),zIndex:-1}).addClass("highcharts-scrollable-mask").add(),this.moveFixedElements(),e(this,"afterShowResetZoom",this.moveFixedElements)),this.fixedRenderer.setSize(this.chartWidth,this.chartHeight),i=this.chartWidth+this.scrollablePixels,t.stop(this.container),this.container.style.width=i+"px",this.renderer.boxWrapper.attr({width:i,height:this.chartHeight,viewBox:[0,0,i,this.chartHeight].join(" ")}),this.chartBackground.attr({width:i}),s&&o.scrollPositionX&&(this.scrollingContainer.scrollLeft=this.scrollablePixels*o.scrollPositionX),o=this.axisOffset,s=this.plotTop-o[0]-1,o=this.plotTop+this.plotHeight+o[2],i=this.plotLeft+this.plotWidth-this.scrollablePixels,this.scrollableMask.attr({d:this.scrollablePixels?["M",0,s,"L",this.plotLeft-1,s,"L",this.plotLeft-1,o,"L",0,o,"Z","M",i,s,"L",this.chartWidth,s,"L",this.chartWidth,o,"L",i,o,"Z"]:["M",0,0]})}}),e(i,"parts/Point.js",[i["parts/Globals.js"]],function(t){var e,i=t.extend,s=t.erase,o=t.fireEvent,n=t.format,r=t.isArray,a=t.isNumber,h=t.pick,l=t.uniqueKey,c=t.defined,d=t.removeEvent;t.Point=e=function(){},t.Point.prototype={init:function(t,e,i){return this.series=t,this.applyOptions(e,i),this.id=c(this.id)?this.id:l(),this.resolveColor(),t.chart.pointCount++,o(this,"afterInit"),this},resolveColor:function(){var t,e=this.series;t=e.chart.options.chart.colorCount;var i=e.chart.styledMode;i||this.options.color||(this.color=e.color),e.options.colorByPoint?(i||(t=e.options.colors||e.chart.options.colors,this.color=this.color||t[e.colorCounter],t=t.length),i=e.colorCounter,e.colorCounter++,e.colorCounter===t&&(e.colorCounter=0)):i=e.colorIndex,this.colorIndex=h(this.colorIndex,i)},applyOptions:function(t,s){var o=this.series,n=o.options.pointValKey||o.pointValKey;return t=e.prototype.optionsToObject.call(this,t),i(this,t),this.options=this.options?i(this.options,t):t,t.group&&delete this.group,t.dataLabels&&delete this.dataLabels,n&&(this.y=this[n]),(this.isNull=h(this.isValid&&!this.isValid(),null===this.x||!a(this.y,!0)))&&(this.formatPrefix="null"),this.selected&&(this.state="select"),"name"in this&&void 0===s&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o&&(this.x=void 0===s?o.autoIncrement(this):s),this},setNestedProperty:function(e,i,s){return s.split(".").reduce(function(e,s,o,n){return e[s]=n.length-1===o?i:t.isObject(e[s],!0)?e[s]:{},e[s]},e),e},optionsToObject:function(e){var i={},s=this.series,o=s.options.keys,n=o||s.pointArrayMap||["y"],h=n.length,l=0,c=0;if(a(e)||null===e)i[n[0]]=e;else if(r(e))for(!o&&e.length>h&&("string"===(s=typeof e[0])?i.name=e[0]:"number"===s&&(i.x=e[0]),l++);c<h;)o&&void 0===e[l]||(0<n[c].indexOf(".")?t.Point.prototype.setNestedProperty(i,e[l],n[c]):i[n[c]]=e[l]),l++,c++;else"object"==typeof e&&(i=e,e.dataLabels&&(s._hasPointLabels=!0),e.marker&&(s._hasPointMarkers=!0));return i},getClassName:function(){return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+(this.zone&&this.zone.className?" "+this.zone.className.replace("highcharts-negative",""):"")},getZone:function(){var t,e=(i=this.series).zones,i=i.zoneAxis||"y",s=0;for(t=e[s];this[i]>=t.value;)t=e[++s];return this.nonZonedColor||(this.nonZonedColor=this.color),this.color=t&&t.color&&!this.options.color?t.color:this.nonZonedColor,t},destroy:function(){var t,e=this.series.chart,i=e.hoverPoints;for(t in e.pointCount--,i&&(this.setState(),s(i,this),i.length||(e.hoverPoints=null)),this===e.hoverPoint&&this.onMouseOut(),(this.graphic||this.dataLabel||this.dataLabels)&&(d(this),this.destroyElements()),this.legendItem&&e.legend.destroyItem(this),this)this[t]=null},destroyElements:function(t){var e,i,s=this,o=[];for((t=t||{graphic:1,dataLabel:1}).graphic&&o.push("graphic","shadowGroup"),t.dataLabel&&o.push("dataLabel","dataLabelUpper","connector"),i=o.length;i--;)e=o[i],s[e]&&(s[e]=s[e].destroy());["dataLabel","connector"].forEach(function(e){var i=e+"s";t[e]&&s[i]&&(s[i].forEach(function(t){t.element&&t.destroy()}),delete s[i])})},getLabelConfig:function(){return{x:this.category,y:this.y,color:this.color,colorIndex:this.colorIndex,key:this.name||this.category,series:this.series,point:this,percentage:this.percentage,total:this.total||this.stackTotal}},tooltipFormatter:function(t){var e=this.series,i=e.tooltipOptions,s=h(i.valueDecimals,""),o=i.valuePrefix||"",r=i.valueSuffix||"";return e.chart.styledMode&&(t=e.chart.tooltip.styledModeFormat(t)),(e.pointArrayMap||["y"]).forEach(function(e){e="{point."+e,(o||r)&&(t=t.replace(RegExp(e+"}","g"),o+e+"}"+r)),t=t.replace(RegExp(e+"}","g"),e+":,."+s+"f}")}),n(t,{point:this,series:this.series},e.chart.time)},firePointEvent:function(t,e,i){var s=this,n=this.series.options;(n.point.events[t]||s.options&&s.options.events&&s.options.events[t])&&this.importEvents(),"click"===t&&n.allowPointSelect&&(i=function(t){s.select&&s.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),o(this,t,e,i)},visible:!0}}),e(i,"parts/Series.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.animObject,s=t.arrayMax,o=t.arrayMin,n=t.correctFloat,r=t.defaultOptions,a=t.defaultPlotOptions,h=t.defined,l=t.erase,c=t.extend,d=t.fireEvent,p=t.isArray,u=t.isNumber,f=t.isString,g=t.merge,m=t.objectEach,x=t.pick,v=t.removeEvent,y=t.splat,b=t.SVGElement,M=t.syncTimeout,k=t.win;t.Series=t.seriesType("line",null,{lineWidth:2,allowPointSelect:!1,showCheckbox:!1,animation:{duration:1e3},events:{},marker:{lineWidth:0,lineColor:"#ffffff",enabledThreshold:2,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:50},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{align:"center",formatter:function(){return null===this.y?"":t.numberFormat(this.y,-1)},padding:5,style:{fontSize:"11px",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:50},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:50},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},{isCartesian:!0,pointClass:t.Point,sorted:!0,requireSorting:!0,directTouch:!1,axisTypes:["xAxis","yAxis"],colorCounter:0,parallelArrays:["x","y"],coll:"series",cropShoulder:1,init:function(t,i){d(this,"init",{options:i});var s,o,n=this,r=t.series;n.chart=t,n.options=i=n.setOptions(i),n.linkedSeries=[],n.bindAxes(),c(n,{name:i.name,state:"",visible:!1!==i.visible,selected:!0===i.selected}),s=i.events,m(s,function(t,i){n.hcEvents&&n.hcEvents[i]&&-1!==n.hcEvents[i].indexOf(t)||e(n,i,t)}),(s&&s.click||i.point&&i.point.events&&i.point.events.click||i.allowPointSelect)&&(t.runTrackerClick=!0),n.getColor(),n.getSymbol(),n.parallelArrays.forEach(function(t){n[t+"Data"]||(n[t+"Data"]=[])}),n.points||n.setData(i.data,!1),n.isCartesian&&(t.hasCartesianSeries=!0),r.length&&(o=r[r.length-1]),n._i=x(o&&o._i,-1)+1,t.orderSeries(this.insert(r)),d(this,"afterInit")},insert:function(t){var e,i=this.options.index;if(u(i)){for(e=t.length;e--;)if(i>=x(t[e].options.index,t[e]._i)){t.splice(e+1,0,this);break}-1===e&&t.unshift(this),e+=1}else t.push(this);return x(e,t.length-1)},bindAxes:function(){var e,i=this,s=i.options,o=i.chart;d(this,"bindAxes",null,function(){(i.axisTypes||[]).forEach(function(n){o[n].forEach(function(t){e=t.options,(s[n]===e.index||void 0!==s[n]&&s[n]===e.id||void 0===s[n]&&0===e.index)&&(i.insert(t.series),i[n]=t,t.isDirty=!0)}),i[n]||i.optionalAxis===n||t.error(18,!0,o)})})},updateParallelArrays:function(t,e){var i=t.series,s=arguments,o=u(e)?function(s){var o="y"===s&&i.toYData?i.toYData(t):t[s];i[s+"Data"][e]=o}:function(t){Array.prototype[e].apply(i[t+"Data"],Array.prototype.slice.call(s,2))};i.parallelArrays.forEach(o)},hasData:function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.yData&&0<this.yData.length},autoIncrement:function(){var t,e=this.options,i=this.xIncrement,s=e.pointIntervalUnit,o=this.chart.time;i=x(i,e.pointStart,0);return this.pointInterval=t=x(this.pointInterval,e.pointInterval,1),s&&(e=new o.Date(i),"day"===s?o.set("Date",e,o.get("Date",e)+t):"month"===s?o.set("Month",e,o.get("Month",e)+t):"year"===s&&o.set("FullYear",e,o.get("FullYear",e)+t),t=e.getTime()-i),this.xIncrement=i+t,i},setOptions:function(t){var e=this.chart,i=e.options,s=i.plotOptions,o=(e.userOptions||{}).plotOptions||{},n=s[this.type],a=g(t);return t=e.styledMode,d(this,"setOptions",{userOptions:a}),this.userOptions=a,e=g(n,s.series,a),this.tooltipOptions=g(r.tooltip,r.plotOptions.series&&r.plotOptions.series.tooltip,r.plotOptions[this.type].tooltip,i.tooltip.userOptions,s.series&&s.series.tooltip,s[this.type].tooltip,a.tooltip),this.stickyTracking=x(a.stickyTracking,o[this.type]&&o[this.type].stickyTracking,o.series&&o.series.stickyTracking,!(!this.tooltipOptions.shared||this.noSharedTooltip)||e.stickyTracking),null===n.marker&&delete e.marker,this.zoneAxis=e.zoneAxis,i=this.zones=(e.zones||[]).slice(),!e.negativeColor&&!e.negativeFillColor||e.zones||(s={value:e[this.zoneAxis+"Threshold"]||e.threshold||0,className:"highcharts-negative"},t||(s.color=e.negativeColor,s.fillColor=e.negativeFillColor),i.push(s)),i.length&&h(i[i.length-1].value)&&i.push(t?{}:{color:this.color,fillColor:this.fillColor}),d(this,"afterSetOptions",{options:e}),e},getName:function(){return x(this.options.name,"Series "+(this.index+1))},getCyclic:function(t,e,i){var s,o=this.chart,n=this.userOptions,r=t+"Index",a=t+"Counter",l=i?i.length:x(o.options.chart[t+"Count"],o[t+"Count"]);e||(s=x(n[r],n["_"+r]),h(s)||(o.series.length||(o[a]=0),n["_"+r]=s=o[a]%l,o[a]+=1),i&&(e=i[s])),void 0!==s&&(this[r]=s),this[t]=e},getColor:function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.options.color=null:this.getCyclic("color",this.options.color||a[this.type].color,this.chart.options.colors)},getSymbol:function(){this.getCyclic("symbol",this.options.marker.symbol,this.chart.options.symbols)},findPointIndex:function(t,e){var i=t.id;t=t.x;var s,o,n=this.points;return i&&(void 0!==(o=(i=this.chart.get(i))&&i.index)&&(s=!0)),void 0===o&&u(t)&&(o=this.xData.indexOf(t,e)),-1!==o&&void 0!==o&&this.cropped&&(o=o>=this.cropStart?o-this.cropStart:o),!s&&n[o]&&n[o].touched&&(o=void 0),o},drawLegendSymbol:t.LegendSymbolMixin.drawLineMarker,updateData:function(e){var i,s,o,n=this.options,r=this.points,a=[],h=this.requireSorting,l=e.length===r.length,c=!0;if(this.xIncrement=null,e.forEach(function(e,s){var c,d=t.defined(e)&&this.pointClass.prototype.optionsToObject.call({series:this},e)||{};c=d.x,(d.id||u(c))&&(-1===(c=this.findPointIndex(d,o))||void 0===c?a.push(e):r[c]&&e!==n.data[c]?(r[c].update(e,!1,null,!1),r[c].touched=!0,h&&(o=c+1)):r[c]&&(r[c].touched=!0),(!l||s!==c||this.hasDerivedData)&&(i=!0))},this),i)for(e=r.length;e--;)(s=r[e])&&!s.touched&&s.remove(!1);else l?e.forEach(function(t,e){r[e].update&&t!==r[e].y&&r[e].update(t,!1,null,!1)}):c=!1;return r.forEach(function(t){t&&(t.touched=!1)}),!!c&&(a.forEach(function(t){this.addPoint(t,!1,null,null,!1)},this),!0)},setData:function(e,i,s,o){var n,r,a=this,h=a.points,l=h&&h.length||0,c=a.options,d=a.chart,g=null,m=a.xAxis,v=c.turboThreshold,y=this.xData,b=this.yData,M=(n=a.pointArrayMap)&&n.length,k=c.keys,w=0,S=1;if(n=(e=e||[]).length,i=x(i,!0),!1!==o&&n&&l&&!a.cropped&&!a.hasGroupedData&&a.visible&&!a.isSeriesBoosting&&(r=this.updateData(e)),!r){if(a.xIncrement=null,a.colorCounter=0,this.parallelArrays.forEach(function(t){a[t+"Data"].length=0}),v&&n>v){for(s=0;null===g&&s<n;)g=e[s],s++;if(u(g))for(s=0;s<n;s++)y[s]=this.autoIncrement(),b[s]=e[s];else if(p(g))if(M)for(s=0;s<n;s++)g=e[s],y[s]=g[0],b[s]=g.slice(1,M+1);else for(k&&(w=0<=(w=k.indexOf("x"))?w:0,S=0<=(S=k.indexOf("y"))?S:1),s=0;s<n;s++)g=e[s],y[s]=g[w],b[s]=g[S];else t.error(12,!1,d)}else for(s=0;s<n;s++)void 0!==e[s]&&(g={series:a},a.pointClass.prototype.applyOptions.apply(g,[e[s]]),a.updateParallelArrays(g,s));for(b&&f(b[0])&&t.error(14,!0,d),a.data=[],a.options.data=a.userOptions.data=e,s=l;s--;)h[s]&&h[s].destroy&&h[s].destroy();m&&(m.minRange=m.userMinRange),a.isDirty=d.isDirtyBox=!0,a.isDirtyData=!!h,s=!1}"point"===c.legendType&&(this.processData(),this.generatePoints()),i&&d.redraw(s)},processData:function(e){var i,s=this.xData,o=this.yData,n=s.length;i=0;var r,a,h,l=this.xAxis;h=(f=this.options).cropThreshold;var c,d,p=this.getExtremesFromAll||f.getExtremesFromAll,u=this.isCartesian,f=l&&l.val2lin,g=l&&l.isLog,m=this.requireSorting;if(u&&!this.isDirty&&!l.isDirty&&!this.yAxis.isDirty&&!e)return!1;for(l&&(c=(e=l.getExtremes()).min,d=e.max),u&&this.sorted&&!p&&(!h||n>h||this.forceCrop)&&(s[n-1]<c||s[0]>d?(s=[],o=[]):this.yData&&(s[0]<c||s[n-1]>d)&&(s=(i=this.cropData(this.xData,this.yData,c,d)).xData,o=i.yData,i=i.start,r=!0)),h=s.length||1;--h;)0<(n=g?f(s[h])-f(s[h-1]):s[h]-s[h-1])&&(void 0===a||n<a)?a=n:0>n&&m&&(t.error(15,!1,this.chart),m=!1);this.cropped=r,this.cropStart=i,this.processedXData=s,this.processedYData=o,this.closestPointRange=a},cropData:function(t,e,i,s,o){var n,r=t.length,a=0,h=r;for(o=x(o,this.cropShoulder),n=0;n<r;n++)if(t[n]>=i){a=Math.max(0,n-o);break}for(i=n;i<r;i++)if(t[i]>s){h=i+o;break}return{xData:t.slice(a,h),yData:e.slice(a,h),start:a,end:h}},generatePoints:function(){var t,e,i,s,o=(f=this.options).data,n=this.data,r=this.processedXData,a=this.processedYData,h=this.pointClass,l=r.length,p=this.cropStart||0,u=this.hasGroupedData,f=f.keys,g=[];for(n||u||((n=[]).length=o.length,n=this.data=n),f&&u&&(this.options.keys=!1),s=0;s<l;s++)e=p+s,u?((i=(new h).init(this,[r[s]].concat(y(a[s])))).dataGroup=this.groupMap[s],i.dataGroup.options&&(i.options=i.dataGroup.options,c(i,i.dataGroup.options),delete i.dataLabels)):(i=n[e])||void 0===o[e]||(n[e]=i=(new h).init(this,o[e],r[s])),i&&(i.index=e,g[s]=i);if(this.options.keys=f,n&&(l!==(t=n.length)||u))for(s=0;s<t;s++)s!==p||u||(s+=l),n[s]&&(n[s].destroyElements(),n[s].plotX=void 0);this.data=n,this.points=g,d(this,"afterGeneratePoints")},getXExtremes:function(t){return{min:o(t),max:s(t)}},getExtremes:function(t){var e,i,n,r,a,h=this.yAxis,l=this.processedXData,c=[],f=0,g=(e=this.xAxis.getExtremes()).min,m=e.max,x=this.requireSorting?this.cropShoulder:0;for(e=(t=t||this.stackedYData||this.processedYData||[]).length,a=0;a<e;a++)if(n=l[a],r=t[a],i=(u(r,!0)||p(r))&&(!h.positiveValuesOnly||r.length||0<r),n=this.getExtremesFromAll||this.options.getExtremesFromAll||this.cropped||(l[a+x]||n)>=g&&(l[a-x]||n)<=m,i&&n)if(i=r.length)for(;i--;)"number"==typeof r[i]&&(c[f++]=r[i]);else c[f++]=r;this.dataMin=o(c),this.dataMax=s(c),d(this,"afterGetExtremes")},translate:function(){this.processedXData||this.processData(),this.generatePoints();var t,e,i,s,o,r=this.options,a=r.stacking,l=this.xAxis,c=l.categories,f=this.yAxis,g=this.points,m=g.length,v=!!this.modifyValue,y=this.pointPlacementToXValue(),b=u(y),M=r.threshold,k=r.startFromThreshold?M:0,w=this.zoneAxis||"y",S=Number.MAX_VALUE;for(t=0;t<m;t++){var A=g[t],T=A.x;i=A.y;var P,E,C=A.low,L=a&&f.stacks[(this.negStacks&&i<(k?0:M)?"-":"")+this.stackKey];f.positiveValuesOnly&&null!==i&&0>=i&&(A.isNull=!0),A.plotX=e=n(Math.min(Math.max(-1e5,l.translate(T,0,0,0,1,y,"flags"===this.type)),1e5)),
a&&this.visible&&!A.isNull&&L&&L[T]&&(o=this.getStackIndicator(o,T,this.index),E=(P=L[T]).points[o.key]),p(E)&&(C=E[0],i=E[1],C===k&&o.key===L[T].base&&(C=x(u(M)&&M,f.min)),f.positiveValuesOnly&&0>=C&&(C=null),A.total=A.stackTotal=P.total,A.percentage=P.total&&A.y/P.total*100,A.stackY=i,P.setOffset(this.pointXOffset||0,this.barW||0)),A.yBottom=h(C)?Math.min(Math.max(-1e5,f.translate(C,0,1,0,1)),1e5):null,v&&(i=this.modifyValue(i,A)),A.plotY=i="number"==typeof i&&Infinity!==i?Math.min(Math.max(-1e5,f.translate(i,0,1,0,1)),1e5):void 0,A.isInside=void 0!==i&&0<=i&&i<=f.len&&0<=e&&e<=l.len,A.clientX=b?n(l.translate(T,0,0,0,1,y)):e,A.negative=A[w]<(r[w+"Threshold"]||M||0),A.category=c&&void 0!==c[A.x]?c[A.x]:A.x,A.isNull||(void 0!==s&&(S=Math.min(S,Math.abs(e-s))),s=e),A.zone=this.zones.length&&A.getZone()}this.closestPointRangePx=S,d(this,"afterTranslate")},getValidPoints:function(t,e,i){var s=this.chart;return(t||this.points||[]).filter(function(t){return!(e&&!s.isInsidePlot(t.plotX,t.plotY,s.inverted))&&(i||!t.isNull)})},setClip:function(t){var e=this.chart,i=this.options,s=e.renderer,o=e.inverted,n=this.clipBox,r=n||e.clipBox,a=this.sharedClipKey||["_sharedClip",t&&t.duration,t&&t.easing,r.height,i.xAxis,i.yAxis].join(),h=e[a],l=e[a+"m"];h||(t&&(r.width=0,o&&(r.x=e.plotSizeX),e[a+"m"]=l=s.clipRect(o?e.plotSizeX+99:-99,o?-e.plotLeft:-e.plotTop,99,o?e.chartWidth:e.chartHeight)),e[a]=h=s.clipRect(r),h.count={length:0}),t&&!h.count[this.index]&&(h.count[this.index]=!0,h.count.length+=1),!1!==i.clip&&(this.group.clip(t||n?h:e.clipRect),this.markerGroup.clip(l),this.sharedClipKey=a),t||(h.count[this.index]&&(delete h.count[this.index],--h.count.length),0===h.count.length&&a&&e[a]&&(n||(e[a]=e[a].destroy()),e[a+"m"]&&(e[a+"m"]=e[a+"m"].destroy())))},animate:function(t){var e,s=this.chart,o=i(this.options.animation);t?this.setClip(o):((t=s[e=this.sharedClipKey])&&t.animate({width:s.plotSizeX,x:0},o),s[e+"m"]&&s[e+"m"].animate({width:s.plotSizeX+99,x:s.inverted?0:-99},o),this.animate=null)},afterAnimate:function(){this.setClip(),d(this,"afterAnimate"),this.finishedAnimating=!0},drawPoints:function(){var t,e,i,s,o,n,r,a,h=this.points,l=this.chart,c=this.options.marker,d=this[this.specialGroup]||this.markerGroup;t=this.xAxis;var p,u=x(c.enabled,!(t&&!t.isRadial)||null,this.closestPointRangePx>=c.enabledThreshold*c.radius);if(!1!==c.enabled||this._hasPointMarkers)for(t=0;t<h.length;t++)o=(s=(e=h[t]).graphic)?"animate":"attr",n=e.marker||{},r=!!e.marker,i=u&&void 0===n.enabled||n.enabled,a=!1!==e.isInside,i&&!e.isNull?(i=x(n.symbol,this.symbol),p=this.markerAttribs(e,e.selected&&"select"),s?s[a?"show":"hide"](!0).animate(p):a&&(0<p.width||e.hasImage)&&(e.graphic=s=l.renderer.symbol(i,p.x,p.y,p.width,p.height,r?n:c).add(d)),s&&!l.styledMode&&s[o](this.pointAttribs(e,e.selected&&"select")),s&&s.addClass(e.getClassName(),!0)):s&&(e.graphic=s.destroy())},markerAttribs:function(t,e){var i=this.options.marker,s=t.marker||{},o=s.symbol||i.symbol,n=x(s.radius,i.radius);return e&&(i=i.states[e],e=s.states&&s.states[e],n=x(e&&e.radius,i&&i.radius,n+(i&&i.radiusPlus||0))),t.hasImage=o&&0===o.indexOf("url"),t.hasImage&&(n=0),t={x:Math.floor(t.plotX)-n,y:t.plotY-n},n&&(t.width=t.height=2*n),t},pointAttribs:function(t,e){var i=this.options.marker,s=(a=t&&t.options)&&a.marker||{},o=this.color,n=a&&a.color,r=t&&t.color,a=x(s.lineWidth,i.lineWidth),h=t&&t.zone&&t.zone.color;return t=1,o=n||h||r||o,n=s.fillColor||i.fillColor||o,o=s.lineColor||i.lineColor||o,e&&(i=i.states[e],e=s.states&&s.states[e]||{},a=x(e.lineWidth,i.lineWidth,a+x(e.lineWidthPlus,i.lineWidthPlus,0)),n=e.fillColor||i.fillColor||n,o=e.lineColor||i.lineColor||o,t=x(e.opacity,i.opacity,t)),{stroke:o,"stroke-width":a,fill:n,opacity:t}},destroy:function(e){var i,s,o,n=this,r=n.chart,a=/AppleWebKit\/533/.test(k.navigator.userAgent),h=n.data||[];for(d(n,"destroy"),e||v(n),(n.axisTypes||[]).forEach(function(t){(o=n[t])&&o.series&&(l(o.series,n),o.isDirty=o.forceRedraw=!0)}),n.legendItem&&n.chart.legend.destroyItem(n),i=h.length;i--;)(s=h[i])&&s.destroy&&s.destroy();n.points=null,t.clearTimeout(n.animationTimeout),m(n,function(t,e){t instanceof b&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),r.hoverSeries===n&&(r.hoverSeries=null),l(r.series,n),r.orderSeries(),m(n,function(t,i){e&&"hcEvents"===i||delete n[i]})},getGraphPath:function(t,e,i){var s,o,n=this,r=n.options,a=r.step,l=[],c=[];return(s=(t=t||n.points).reversed)&&t.reverse(),(a={right:1,center:2}[a]||a&&3)&&s&&(a=4-a),!r.connectNulls||e||i||(t=this.getValidPoints(t)),t.forEach(function(s,d){var p=s.plotX,u=s.plotY,f=t[d-1];(s.leftCliff||f&&f.rightCliff)&&!i&&(o=!0),s.isNull&&!h(e)&&0<d?o=!r.connectNulls:s.isNull&&!e?o=!0:(0===d||o?d=["M",s.plotX,s.plotY]:n.getPointSpline?d=n.getPointSpline(t,s,d):a?(d=1===a?["L",f.plotX,u]:2===a?["L",(f.plotX+p)/2,f.plotY,"L",(f.plotX+p)/2,u]:["L",p,f.plotY]).push("L",p,u):d=["L",p,u],c.push(s.x),a&&(c.push(s.x),2===a&&c.push(s.x)),l.push.apply(l,d),o=!1)}),l.xMap=c,n.graphPath=l},drawGraph:function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),s=this.chart.styledMode,o=[["graph","highcharts-graph"]];s||o[0].push(e.lineColor||this.color||"#cccccc",e.dashStyle),(o=t.getZonesGraphs(o)).forEach(function(o,n){var r=o[0],a=t[r],h=a?"animate":"attr";a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(t[r]=a=t.chart.renderer.path(i).addClass(o[1]).attr({zIndex:1}).add(t.group)),a&&!s&&(r={stroke:o[2],"stroke-width":e.lineWidth,fill:t.fillGraph&&t.color||"none"},o[3]?r.dashstyle=o[3]:"square"!==e.linecap&&(r["stroke-linecap"]=r["stroke-linejoin"]="round"),a[h](r).shadow(2>n&&e.shadow)),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},getZonesGraphs:function(t){return this.zones.forEach(function(e,i){i=["zone-graph-"+i,"highcharts-graph highcharts-zone-graph-"+i+" "+(e.className||"")],this.chart.styledMode||i.push(e.color||this.color,e.dashStyle||this.options.dashStyle),t.push(i)},this),t},applyZones:function(){var t,e,i,s,o,n,r,a,h,l=this,c=this.chart,d=c.renderer,p=this.zones,u=this.clips||[],f=this.graph,g=this.area,m=Math.max(c.chartWidth,c.chartHeight),v=this[(this.zoneAxis||"y")+"Axis"],y=c.inverted,b=!1;p.length&&(f||g)&&v&&void 0!==v.min&&(o=v.reversed,n=v.horiz,f&&!this.showLine&&f.hide(),g&&g.hide(),s=v.getExtremes(),p.forEach(function(p,M){t=o?n?c.plotWidth:0:n?0:v.toPixels(s.min)||0,t=Math.min(Math.max(x(e,t),0),m),e=Math.min(Math.max(Math.round(v.toPixels(x(p.value,s.max),!0)||0),0),m),b&&(t=e=v.toPixels(s.max)),r=Math.abs(t-e),a=Math.min(t,e),h=Math.max(t,e),v.isXAxis?(i={x:y?h:a,y:0,width:r,height:m},n||(i.x=c.plotHeight-i.x)):(i={x:0,y:y?h:a,width:m,height:r},n&&(i.y=c.plotWidth-i.y)),y&&d.isVML&&(i=v.isXAxis?{x:0,y:o?a:h,height:i.width,width:c.chartWidth}:{x:i.y-c.plotLeft-c.spacingBox.x,y:0,width:i.height,height:c.chartHeight}),u[M]?u[M].animate(i):(u[M]=d.clipRect(i),f&&l["zone-graph-"+M].clip(u[M]),g&&l["zone-area-"+M].clip(u[M])),b=p.value>s.max,l.resetZones&&0===e&&(e=void 0)}),this.clips=u)},invertGroups:function(t){function i(){["group","markerGroup"].forEach(function(e){o[e]&&(n.renderer.isVML&&o[e].attr({width:o.yAxis.len,height:o.xAxis.len}),o[e].width=o.yAxis.len,o[e].height=o.xAxis.len,o[e].invert(t))})}var s,o=this,n=o.chart;o.xAxis&&(s=e(n,"resize",i),e(o,"destroy",s),i(t),o.invertGroups=i)},plotGroup:function(t,e,i,s,o){var n=this[t],r=!n;return r&&(this[t]=n=this.chart.renderer.g().attr({zIndex:s||.1}).add(o)),n.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(h(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(n.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),n.attr({visibility:i})[r?"attr":"animate"](this.getPlotBox()),n},getPlotBox:function(){var t=this.chart,e=this.xAxis,i=this.yAxis;return t.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:t.plotLeft,translateY:i?i.top:t.plotTop,scaleX:1,scaleY:1}},render:function(){var t,e=this,s=e.chart,o=e.options,n=!!e.animate&&s.renderer.isSVG&&i(o.animation).duration,r=e.visible?"inherit":"hidden",a=o.zIndex,h=e.hasRendered,l=s.seriesGroup,c=s.inverted;d(this,"render"),t=e.plotGroup("group","series",r,a,l),e.markerGroup=e.plotGroup("markerGroup","markers",r,a,l),n&&e.animate(!0),t.inverted=!(!e.isCartesian&&!e.invertable)&&c,e.drawGraph&&(e.drawGraph(),e.applyZones()),e.visible&&e.drawPoints(),e.drawDataLabels&&e.drawDataLabels(),e.redrawPoints&&e.redrawPoints(),e.drawTracker&&!1!==e.options.enableMouseTracking&&e.drawTracker(),e.invertGroups(c),!1===o.clip||e.sharedClipKey||h||t.clip(s.clipRect),n&&e.animate(),h||(e.animationTimeout=M(function(){e.afterAnimate()},n)),e.isDirty=!1,e.hasRendered=!0,d(e,"afterRender")},redraw:function(){var t=this.chart,e=this.isDirty||this.isDirtyData,i=this.group,s=this.xAxis,o=this.yAxis;i&&(t.inverted&&i.attr({width:t.plotWidth,height:t.plotHeight}),i.animate({translateX:x(s&&s.left,t.plotLeft),translateY:x(o&&o.top,t.plotTop)})),this.translate(),this.render(),e&&delete this.kdTree},kdAxisArray:["clientX","plotY"],searchPoint:function(t,e){var i=this.xAxis,s=this.yAxis,o=this.chart.inverted;return this.searchKDTree({clientX:o?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:o?s.len-t.chartX+s.pos:t.chartY-s.pos},e,t)},buildKDTree:function(t){function e(t,s,o){var n,r;if(r=t&&t.length)return n=i.kdAxisArray[s%o],t.sort(function(t,e){return t[n]-e[n]}),{point:t[r=Math.floor(r/2)],left:e(t.slice(0,r),s+1,o),right:e(t.slice(r+1),s+1,o)}}this.buildingKdTree=!0;var i=this,s=-1<i.options.findNearestPointBy.indexOf("y")?2:1;delete i.kdTree,M(function(){i.kdTree=e(i.getValidPoints(null,!i.directTouch),s,s),i.buildingKdTree=!1},i.options.kdNow||t&&"touchstart"===t.type?0:1)},searchKDTree:function(t,e,i){function s(t,e,i,l){var c,d,p=e.point,u=o.kdAxisArray[i%l],f=p;return c=((d=h(t[n])&&h(p[n])?Math.pow(t[n]-p[n],2):null)||0)+((c=h(t[r])&&h(p[r])?Math.pow(t[r]-p[r],2):null)||0),p.dist=h(c)?Math.sqrt(c):Number.MAX_VALUE,p.distX=h(d)?Math.sqrt(d):Number.MAX_VALUE,d=0>(u=t[u]-p[u])?"right":"left",e[c=0>u?"left":"right"]&&(f=(c=s(t,e[c],i+1,l))[a]<f[a]?c:p),e[d]&&Math.sqrt(u*u)<f[a]&&(f=(t=s(t,e[d],i+1,l))[a]<f[a]?t:f),f}var o=this,n=this.kdAxisArray[0],r=this.kdAxisArray[1],a=e?"distX":"dist";if(e=-1<o.options.findNearestPointBy.indexOf("y")?2:1,this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return s(t,this.kdTree,e,e)},pointPlacementToXValue:function(){var t=this.options.pointPlacement;return"between"===t&&(t=.5),u(t)&&(t*=x(this.options.pointRange||this.xAxis.pointRange)),t}})}),e(i,"parts/Stacking.js",[i["parts/Globals.js"]],function(t){var e=t.Axis,i=t.Chart,s=t.correctFloat,o=t.defined,n=t.destroyObjectProperties,r=t.format,a=t.objectEach,h=t.pick,l=t.Series;t.StackItem=function(t,e,i,s,o){var n=t.chart.inverted;this.axis=t,this.isNegative=i,this.options=e,this.x=s,this.total=null,this.points={},this.stack=o,this.rightCliff=this.leftCliff=0,this.alignOptions={align:e.align||(n?i?"left":"right":"center"),verticalAlign:e.verticalAlign||(n?"middle":i?"bottom":"top"),y:h(e.y,n?4:i?14:-6),x:h(e.x,n?i?-6:6:0)},this.textAlign=e.textAlign||(n?i?"right":"left":"center")},t.StackItem.prototype={destroy:function(){n(this,this.axis)},render:function(t){var e=this.axis.chart,i=this.options,s=(s=i.format)?r(s,this,e.time):i.formatter.call(this);this.label?this.label.attr({text:s,visibility:"hidden"}):this.label=e.renderer.text(s,null,null,i.useHTML).css(i.style).attr({align:this.textAlign,rotation:i.rotation,visibility:"hidden"}).add(t),this.label.labelrank=e.plotHeight},setOffset:function(t,e){var i=this.axis,s=i.chart,n=i.translate(i.usePercentage?100:this.total,0,0,0,1),r=i.translate(0);r=o(n)&&Math.abs(n-r);t=s.xAxis[0].translate(this.x)+t,i=o(n)&&this.getStackBox(s,this,t,n,e,r,i),(e=this.label)&&i&&(e.align(this.alignOptions,null,i),i=e.alignAttr,e[!1===this.options.crop||s.isInsidePlot(i.x,i.y)?"show":"hide"](!0))},getStackBox:function(t,e,i,s,o,n,r){var a=e.axis.reversed,h=t.inverted;return t=r.height+r.pos-(h?t.plotLeft:t.plotTop),e=e.isNegative&&!a||!e.isNegative&&a,{x:h?e?s:s-n:i,y:h?t-i-o:e?t-s-n:t-s,width:h?n:o,height:h?o:n}}},i.prototype.getStacks=function(){var t=this;t.yAxis.forEach(function(t){t.stacks&&t.hasVisibleSeries&&(t.oldStacks=t.stacks)}),t.series.forEach(function(e){!e.options.stacking||!0!==e.visible&&!1!==t.options.chart.ignoreHiddenSeries||(e.stackKey=e.type+h(e.options.stack,""))})},e.prototype.buildStacks=function(){var t,e=this.series,i=h(this.options.reversedStacks,!0),s=e.length;if(!this.isXAxis){for(this.usePercentage=!1,t=s;t--;)e[i?t:s-t-1].setStackedPoints();for(t=0;t<s;t++)e[t].modifyStacks()}},e.prototype.renderStackTotals=function(){var t=this.chart,e=t.renderer,i=this.stacks,s=this.stackTotalGroup;s||(this.stackTotalGroup=s=e.g("stack-labels").attr({visibility:"visible",zIndex:6}).add()),s.translate(t.plotLeft,t.plotTop),a(i,function(t){a(t,function(t){t.render(s)})})},e.prototype.resetStacks=function(){var t=this,e=t.stacks;t.isXAxis||a(e,function(e){a(e,function(i,s){i.touched<t.stacksTouched?(i.destroy(),delete e[s]):(i.total=null,i.cumulative=null)})})},e.prototype.cleanStacks=function(){var t;this.isXAxis||(this.oldStacks&&(t=this.stacks=this.oldStacks),a(t,function(t){a(t,function(t){t.cumulative=t.total})}))},l.prototype.setStackedPoints=function(){if(this.options.stacking&&(!0===this.visible||!1===this.chart.options.chart.ignoreHiddenSeries)){var e,i,n,r,a,l,c,d=this.processedXData,p=this.processedYData,u=[],f=p.length,g=(v=this.options).threshold,m=h(v.startFromThreshold&&g,0),x=v.stack,v=v.stacking,y=this.stackKey,b="-"+y,M=this.negStacks,k=this.yAxis,w=k.stacks,S=k.oldStacks;for(k.stacksTouched+=1,a=0;a<f;a++)l=d[a],c=p[a],r=(e=this.getStackIndicator(e,l,this.index)).key,w[n=(i=M&&c<(m?0:g))?b:y]||(w[n]={}),w[n][l]||(S[n]&&S[n][l]?(w[n][l]=S[n][l],w[n][l].total=null):w[n][l]=new t.StackItem(k,k.options.stackLabels,i,l,x)),n=w[n][l],null!==c?(n.points[r]=n.points[this.index]=[h(n.cumulative,m)],o(n.cumulative)||(n.base=r),n.touched=k.stacksTouched,0<e.index&&!1===this.singleStacks&&(n.points[r][0]=n.points[this.index+","+l+",0"][0])):n.points[r]=n.points[this.index]=null,"percent"===v?(i=i?y:b,M&&w[i]&&w[i][l]?(i=w[i][l],n.total=i.total=Math.max(i.total,n.total)+Math.abs(c)||0):n.total=s(n.total+(Math.abs(c)||0))):n.total=s(n.total+(c||0)),n.cumulative=h(n.cumulative,m)+(c||0),null!==c&&(n.points[r].push(n.cumulative),u[a]=n.cumulative);"percent"===v&&(k.usePercentage=!0),this.stackedYData=u,k.oldStacks={}}},l.prototype.modifyStacks=function(){var t,e=this,i=e.stackKey,s=e.yAxis.stacks,o=e.processedXData,n=e.options.stacking;e[n+"Stacker"]&&[i,"-"+i].forEach(function(i){for(var r,a,h=o.length;h--;)r=o[h],t=e.getStackIndicator(t,r,e.index,i),(a=(r=s[i]&&s[i][r])&&r.points[t.key])&&e[n+"Stacker"](a,r,h)})},l.prototype.percentStacker=function(t,e,i){e=e.total?100/e.total:0,t[0]=s(t[0]*e),t[1]=s(t[1]*e),this.stackedYData[i]=t[1]},l.prototype.getStackIndicator=function(t,e,i,s){return!o(t)||t.x!==e||s&&t.key!==s?t={x:e,index:0,key:s}:t.index++,t.key=[i,e,t.index].join(),t}}),e(i,"parts/Dynamics.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.animate,s=t.Axis,o=t.Chart,n=t.createElement,r=t.css,a=t.defined,h=t.erase,l=t.extend,c=t.fireEvent,d=t.isNumber,p=t.isObject,u=t.isArray,f=t.merge,g=t.objectEach,m=t.pick,x=t.Point,v=t.Series,y=t.seriesTypes,b=t.setAnimation,M=t.splat;t.cleanRecursively=function(e,i){var s={};return g(e,function(o,n){p(e[n],!0)&&i[n]?(o=t.cleanRecursively(e[n],i[n]),Object.keys(o).length&&(s[n]=o)):(p(e[n])||e[n]!==i[n])&&(s[n]=e[n])}),s},l(o.prototype,{addSeries:function(t,e,i){var s,o=this;return t&&(e=m(e,!0),c(o,"addSeries",{options:t},function(){s=o.initSeries(t),o.isDirtyLegend=!0,o.linkSeries(),c(o,"afterAddSeries",{series:s}),e&&o.redraw(i)})),s},addAxis:function(t,e,i,o){var n=e?"xAxis":"yAxis",r=this.options;return t=f(t,{index:this[n].length,isX:e}),e=new s(this,t),r[n]=M(r[n]||{}),r[n].push(t),m(i,!0)&&this.redraw(o),e},showLoading:function(t){var s=this,o=s.options,a=s.loadingDiv,h=o.loading,c=function(){a&&r(a,{left:s.plotLeft+"px",top:s.plotTop+"px",width:s.plotWidth+"px",height:s.plotHeight+"px"})};a||(s.loadingDiv=a=n("div",{className:"highcharts-loading highcharts-loading-hidden"},null,s.container),s.loadingSpan=n("span",{className:"highcharts-loading-inner"},null,a),e(s,"redraw",c)),a.className="highcharts-loading",s.loadingSpan.innerHTML=t||o.lang.loading,s.styledMode||(r(a,l(h.style,{zIndex:10})),r(s.loadingSpan,h.labelStyle),s.loadingShown||(r(a,{opacity:0,display:""}),i(a,{opacity:h.style.opacity||.5},{duration:h.showDuration||0}))),s.loadingShown=!0,c()},hideLoading:function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||i(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){r(e,{display:"none"})}})),this.loadingShown=!1},propsRequireDirtyBox:"backgroundColor borderColor borderWidth borderRadius plotBackgroundColor plotBackgroundImage plotBorderColor plotBorderWidth plotShadow shadow".split(" "),propsRequireReflow:"margin marginTop marginRight marginBottom marginLeft spacing spacingTop spacingRight spacingBottom spacingLeft".split(" "),propsRequireUpdateSeries:"chart.inverted chart.polar chart.ignoreHiddenSeries chart.type colors plotOptions time tooltip".split(" "),collectionsWithUpdate:"xAxis yAxis zAxis series colorAxis pane".split(" "),update:function(e,i,s,o){var n,r,h,l,p=this,u={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle"},x=[];c(p,"update",{options:e}),e.isResponsiveOptions||p.setResponsive(!1,!0),(n=(e=t.cleanRecursively(e,p.options)).chart)&&(f(!0,p.options.chart,n),"className"in n&&p.setClassName(n.className),"reflow"in n&&p.setReflow(n.reflow),("inverted"in n||"polar"in n||"type"in n)&&(p.propFromSeries(),r=!0),"alignTicks"in n&&(r=!0),g(n,function(t,e){-1!==p.propsRequireUpdateSeries.indexOf("chart."+e)&&(h=!0),-1!==p.propsRequireDirtyBox.indexOf(e)&&(p.isDirtyBox=!0),-1!==p.propsRequireReflow.indexOf(e)&&(l=!0)}),!p.styledMode&&"style"in n&&p.renderer.setStyle(n.style)),!p.styledMode&&e.colors&&(this.options.colors=e.colors),e.plotOptions&&f(!0,this.options.plotOptions,e.plotOptions),g(e,function(t,e){p[e]&&"function"==typeof p[e].update?p[e].update(t,!1):"function"==typeof p[u[e]]&&p[u[e]](t),"chart"!==e&&-1!==p.propsRequireUpdateSeries.indexOf(e)&&(h=!0)}),this.collectionsWithUpdate.forEach(function(t){var i;e[t]&&("series"===t&&(i=[],p[t].forEach(function(t,e){t.options.isInternal||i.push(m(t.options.index,e))})),M(e[t]).forEach(function(e,o){(o=a(e.id)&&p.get(e.id)||p[t][i?i[o]:o])&&o.coll===t&&(o.update(e,!1),s&&(o.touched=!0)),!o&&s&&("series"===t?p.addSeries(e,!1).touched=!0:"xAxis"!==t&&"yAxis"!==t||(p.addAxis(e,"xAxis"===t,!1).touched=!0))}),s&&p[t].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:x.push(t)}))}),x.forEach(function(t){t.remove&&t.remove(!1)}),r&&p.axes.forEach(function(t){t.update({},!1)}),h&&p.series.forEach(function(t){t.update({},!1)}),e.loading&&f(!0,p.options.loading,e.loading),r=n&&n.width,n=n&&n.height,t.isString(n)&&(n=t.relativeLength(n,r||p.chartWidth)),l||d(r)&&r!==p.chartWidth||d(n)&&n!==p.chartHeight?p.setSize(r,n,o):m(i,!0)&&p.redraw(o),c(p,"afterUpdate",{options:e,redraw:i,animation:o})},setSubtitle:function(t){this.setTitle(void 0,t)}}),l(x.prototype,{update:function(t,e,i,s){function o(){r.applyOptions(t),null===r.y&&h&&(r.graphic=h.destroy()),p(t,!0)&&(h&&h.element&&t&&t.marker&&void 0!==t.marker.symbol&&(r.graphic=h.destroy()),t&&t.dataLabels&&r.dataLabel&&(r.dataLabel=r.dataLabel.destroy()),r.connector&&(r.connector=r.connector.destroy())),n=r.index,a.updateParallelArrays(r,n),c.data[n]=p(c.data[n],!0)||p(t,!0)?r.options:m(t,c.data[n]),a.isDirty=a.isDirtyData=!0,!a.fixedBox&&a.hasCartesianSeries&&(l.isDirtyBox=!0),"point"===c.legendType&&(l.isDirtyLegend=!0),e&&l.redraw(i)}var n,r=this,a=r.series,h=r.graphic,l=a.chart,c=a.options;e=m(e,!0),!1===s?o():r.firePointEvent("update",{options:t},o)},remove:function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)}}),l(v.prototype,{addPoint:function(t,e,i,s,o){var n,r,a,h,l=this.options,d=this.data,p=this.chart,u=(u=this.xAxis)&&u.hasNames&&u.names,f=l.data,g=this.xData;if(e=m(e,!0),n={series:this},this.pointClass.prototype.applyOptions.apply(n,[t]),h=n.x,a=g.length,this.requireSorting&&h<g[a-1])for(r=!0;a&&g[a-1]>h;)a--;this.updateParallelArrays(n,"splice",a,0,0),this.updateParallelArrays(n,a),u&&n.name&&(u[h]=n.name),f.splice(a,0,t),r&&(this.data.splice(a,0,null),this.processData()),"point"===l.legendType&&this.generatePoints(),i&&(d[0]&&d[0].remove?d[0].remove(!1):(d.shift(),this.updateParallelArrays(n,"shift"),f.shift())),!1!==o&&c(this,"addPoint",{point:n}),this.isDirtyData=this.isDirty=!0,e&&p.redraw(s)},removePoint:function(t,e,i){var s=this,o=s.data,n=o[t],r=s.points,a=s.chart,h=function(){r&&r.length===o.length&&r.splice(t,1),o.splice(t,1),s.options.data.splice(t,1),s.updateParallelArrays(n||{series:s},"splice",t,1),n&&n.destroy(),s.isDirty=!0,s.isDirtyData=!0,e&&a.redraw()};b(i,a),e=m(e,!0),n?n.firePointEvent("remove",null,h):h()},remove:function(t,e,i,s){function o(){n.destroy(s),n.remove=null,r.isDirtyLegend=r.isDirtyBox=!0,r.linkSeries(),m(t,!0)&&r.redraw(e)}var n=this,r=n.chart;!1!==i?c(n,"remove",null,o):o()},update:function(e,i){e=t.cleanRecursively(e,this.userOptions),c(this,"update",{options:e});var s,o,n=this,r=n.chart,a=n.userOptions,h=n.initialType||n.type,d=e.type||a.type||r.options.chart.type,p=!(this.hasDerivedData||e.dataGrouping||d&&d!==this.type||void 0!==e.pointStart||e.pointInterval||e.pointIntervalUnit||e.keys),u=y[h].prototype,g=["group","markerGroup","dataLabelsGroup"],x=["navigatorSeries","baseSeries"],v=n.finishedAnimating&&{animation:!1},b={};for(o in p&&(x.push("data","isDirtyData","points","processedXData","processedYData","xIncrement"),!1!==e.visible&&x.push("area","graph"),n.parallelArrays.forEach(function(t){x.push(t+"Data")}),e.data&&this.setData(e.data,!1)),e=f(a,v,{index:void 0===a.index?n.index:a.index,pointStart:m(a.pointStart,n.xData[0])},!p&&{data:n.options.data},e),(x=g.concat(x)).forEach(function(t){x[t]=n[t],delete n[t]}),n.remove(!1,null,!1,!0),u)n[o]=void 0;y[d||h]?l(n,y[d||h].prototype):t.error(17,!0,r),x.forEach(function(t){n[t]=x[t]}),n.init(r,e),p&&this.points&&(!1===(s=n.options).visible?(b.graphic=1,b.dataLabel=1):(s.marker&&!1===s.marker.enabled&&(b.graphic=1),s.dataLabels&&!1===s.dataLabels.enabled&&(b.dataLabel=1)),this.points.forEach(function(t){t&&t.series&&(t.resolveColor(),Object.keys(b).length&&t.destroyElements(b),!1===s.showInLegend&&t.legendItem&&r.legend.destroyItem(t))},this)),e.zIndex!==a.zIndex&&g.forEach(function(t){n[t]&&n[t].attr({zIndex:e.zIndex})}),n.initialType=h,r.linkSeries(),c(this,"afterUpdate"),m(i,!0)&&r.redraw(!!p&&void 0)},setName:function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0}}),l(s.prototype,{update:function(t,e){var i=this.chart,s=t&&t.events||{};t=f(this.userOptions,t),i.options[this.coll].indexOf&&(i.options[this.coll][i.options[this.coll].indexOf(this.userOptions)]=t),g(i.options[this.coll].events,function(t,e){"undefined"==typeof s[e]&&(s[e]=void 0)}),this.destroy(!0),this.init(i,l(t,{events:s})),i.isDirtyBox=!0,m(e,!0)&&i.redraw()},remove:function(t){for(var e=this.chart,i=this.coll,s=this.series,o=s.length;o--;)s[o]&&s[o].remove(!1);h(e.axes,this),h(e[i],this),u(e.options[i])?e.options[i].splice(this.options.index,1):delete e.options[i],e[i].forEach(function(t,e){t.options.index=t.userOptions.index=e}),this.destroy(),e.isDirtyBox=!0,m(t,!0)&&e.redraw()},setTitle:function(t,e){this.update({title:t},e)},setCategories:function(t,e){this.update({categories:t},e)}})}),e(i,"parts/AreaSeries.js",[i["parts/Globals.js"]],function(t){var e=t.color,i=t.pick,s=t.Series;(0,t.seriesType)("area","line",{softThreshold:!1,threshold:0},{singleStacks:!1,getStackPoints:function(e){var s,o,n=[],r=[],a=this.xAxis,h=this.yAxis,l=h.stacks[this.stackKey],c={},d=this.index,p=h.series,u=p.length,f=i(h.options.reversedStacks,!0)?1:-1;if(e=e||this.points,this.options.stacking){for(o=0;o<e.length;o++)e[o].leftNull=e[o].rightNull=null,c[e[o].x]=e[o];t.objectEach(l,function(t,e){null!==t.total&&r.push(e)}),r.sort(function(t,e){return t-e}),s=p.map(function(t){return t.visible}),r.forEach(function(t,e){var i,p,g=0;if(c[t]&&!c[t].isNull)n.push(c[t]),[-1,1].forEach(function(n){var a=1===n?"rightNull":"leftNull",h=0,g=l[r[e+n]];if(g)for(o=d;0<=o&&o<u;)(i=g.points[o])||(o===d?c[t][a]=!0:s[o]&&(p=l[t].points[o])&&(h-=p[1]-p[0])),o+=f;c[t][1===n?"rightCliff":"leftCliff"]=h});else{for(o=d;0<=o&&o<u;){if(i=l[t].points[o]){g=i[1];break}o+=f}g=h.translate(g,0,1,0,1),n.push({isNull:!0,plotX:a.translate(t,0,0,0,1),x:t,plotY:g,yBottom:g})}})}return n},getGraphPath:function(t){var e,o,n,r,a=s.prototype.getGraphPath,h=(m=this.options).stacking,l=this.yAxis,c=[],d=[],p=this.index,u=l.stacks[this.stackKey],f=m.threshold,g=l.getThreshold(m.threshold),m=m.connectNulls||"percent"===h,x=function(e,i,s){var o=t[e];e=h&&u[o.x].points[p];var r=o[s+"Null"]||0;s=o[s+"Cliff"]||0;var a,m;o=!0;s||r?(a=(r?e[0]:e[1])+s,m=e[0]+s,o=!!r):!h&&t[i]&&t[i].isNull&&(a=m=f),void 0!==a&&(d.push({plotX:n,plotY:null===a?g:l.getThreshold(a),isNull:o,isCliff:!0}),c.push({plotX:n,plotY:null===m?g:l.getThreshold(m),doCurve:!1}))};for(t=t||this.points,h&&(t=this.getStackPoints(t)),e=0;e<t.length;e++)o=t[e].isNull,n=i(t[e].rectPlotX,t[e].plotX),r=i(t[e].yBottom,g),(!o||m)&&(m||x(e,e-1,"left"),o&&!h&&m||(d.push(t[e]),c.push({x:e,plotX:n,plotY:r})),m||x(e,e+1,"right"));return e=a.call(this,d,!0,!0),c.reversed=!0,(o=a.call(this,c,!0,!0)).length&&(o[0]="L"),o=e.concat(o),a=a.call(this,d,!1,m),o.xMap=e.xMap,this.areaPath=o,a},drawGraph:function(){this.areaPath=[],s.prototype.drawGraph.apply(this);var t=this,o=this.areaPath,n=this.options,r=[["area","highcharts-area",this.color,n.fillColor]];this.zones.forEach(function(e,i){r.push(["zone-area-"+i,"highcharts-area highcharts-zone-area-"+i+" "+e.className,e.color||t.color,e.fillColor||n.fillColor])}),r.forEach(function(s){var r=s[0],a=t[r],h=a?"animate":"attr",l={};a?(a.endX=t.preventGraphAnimation?null:o.xMap,a.animate({d:o})):(l.zIndex=0,(a=t[r]=t.chart.renderer.path(o).addClass(s[1]).add(t.group)).isArea=!0),t.chart.styledMode||(l.fill=i(s[3],e(s[2]).setOpacity(i(n.fillOpacity,.75)).get())),a[h](l),a.startX=o.xMap,a.shiftUnit=n.step?2:1})},drawLegendSymbol:t.LegendSymbolMixin.drawRectangle})}),e(i,"parts/SplineSeries.js",[i["parts/Globals.js"]],function(t){var e=t.pick;(t=t.seriesType)("spline","line",{},{getPointSpline:function(t,i,s){var o,n,r,a,h=i.plotX,l=i.plotY,c=t[s-1];if(s=t[s+1],c&&!c.isNull&&!1!==c.doCurve&&!i.isCliff&&s&&!s.isNull&&!1!==s.doCurve&&!i.isCliff){t=c.plotY,r=s.plotX;var d=0;n=(1.5*l+t)/2.5,a=(1.5*l+(s=s.plotY))/2.5,(r=(1.5*h+r)/2.5)!==(o=(1.5*h+c.plotX)/2.5)&&(d=(a-n)*(r-h)/(r-o)+l-a),a+=d,(n+=d)>t&&n>l?a=2*l-(n=Math.max(t,l)):n<t&&n<l&&(a=2*l-(n=Math.min(t,l))),a>s&&a>l?n=2*l-(a=Math.max(s,l)):a<s&&a<l&&(n=2*l-(a=Math.min(s,l))),i.rightContX=r,i.rightContY=a}return i=["C",e(c.rightContX,c.plotX),e(c.rightContY,c.plotY),e(o,h),e(n,l),h,l],c.rightContX=c.rightContY=null,i}})}),e(i,"parts/AreaSplineSeries.js",[i["parts/Globals.js"]],function(t){var e=t.seriesTypes.area.prototype;(0,t.seriesType)("areaspline","spline",t.defaultPlotOptions.area,{getStackPoints:e.getStackPoints,getGraphPath:e.getGraphPath,drawGraph:e.drawGraph,drawLegendSymbol:t.LegendSymbolMixin.drawRectangle})}),e(i,"parts/ColumnSeries.js",[i["parts/Globals.js"]],function(t){var e=t.animObject,i=t.color,s=t.extend,o=t.defined,n=t.isNumber,r=t.merge,a=t.pick,h=t.Series,l=t.seriesType,c=t.svg;l("column","line",{borderRadius:0,crisp:!0,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:null,verticalAlign:null,y:null},softThreshold:!1,startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},{cropShoulder:0,directTouch:!0,trackerGroups:["group","dataLabelsGroup"],negStacks:!0,init:function(){h.prototype.init.apply(this,arguments);var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)})},getColumnMetrics:function(){var t,e=this,i=e.options,s=e.xAxis,o=e.yAxis,n=s.options.reversedStacks,r=(n=s.reversed&&!n||!s.reversed&&n,{}),h=0;!1===i.grouping?h=1:e.chart.series.forEach(function(i){var s,n=i.options,a=i.yAxis;i.type!==e.type||!i.visible&&e.chart.options.chart.ignoreHiddenSeries||o.len!==a.len||o.pos!==a.pos||(n.stacking?(t=i.stackKey,void 0===r[t]&&(r[t]=h++),s=r[t]):!1!==n.grouping&&(s=h++),i.columnIndex=s)});var l=Math.min(Math.abs(s.transA)*(s.ordinalSlope||i.pointRange||s.closestPointRange||s.tickInterval||1),s.len),c=l*i.groupPadding,d=(l-2*c)/(h||1);i=Math.min(i.maxPointWidth||s.len,a(i.pointWidth,d*(1-2*i.pointPadding)));return e.columnMetrics={width:i,offset:(d-i)/2+(c+((e.columnIndex||0)+(n?1:0))*d-l/2)*(n?-1:1)},e.columnMetrics},crispCol:function(t,e,i,s){var o=this.chart,n=-((r=this.borderWidth)%2?.5:0),r=r%2?.5:1;return o.inverted&&o.renderer.isVML&&(r+=1),this.options.crisp&&(i=Math.round(t+i)+n,i-=t=Math.round(t)+n),s=Math.round(e+s)+r,n=.5>=Math.abs(e)&&.5<s,s-=e=Math.round(e)+r,n&&s&&(--e,s+=1),{x:t,y:e,width:i,height:s}},translate:function(){var t=this,e=t.chart,i=t.options,s=t.dense=2>t.closestPointRange*t.xAxis.transA,n=(s=t.borderWidth=a(i.borderWidth,s?0:1),t.yAxis),r=i.threshold,l=t.translatedThreshold=n.getThreshold(r),c=a(i.minPointLength,5),d=t.getColumnMetrics(),p=d.width,u=t.barW=Math.max(p,1+2*s),f=t.pointXOffset=d.offset;e.inverted&&(l-=.5),i.pointPadding&&(u=Math.ceil(u)),h.prototype.translate.apply(t),t.points.forEach(function(i){var s,h=a(i.yBottom,l),d=999+Math.abs(h),g=p,m=(d=Math.min(Math.max(-d,i.plotY),n.len+d),i.plotX+f),x=u,v=Math.min(d,h),y=Math.max(d,h)-v;c&&Math.abs(y)<c&&(y=c,s=!n.reversed&&!i.negative||n.reversed&&i.negative,i.y===r&&t.dataMax<=r&&n.min<r&&(s=!s),v=Math.abs(v-l)>c?h-c:l-(s?c:0)),o(i.options.pointWidth)&&(g=x=Math.ceil(i.options.pointWidth),m-=Math.round((g-p)/2)),i.barX=m,i.pointWidth=g,i.tooltipPos=e.inverted?[n.len+n.pos-e.plotLeft-d,t.xAxis.len-m-x/2,y]:[m+x/2,d+n.pos-e.plotTop,y],i.shapeType=t.pointClass.prototype.shapeType||"rect",i.shapeArgs=t.crispCol.apply(t,i.isNull?[m,l,x,0]:[m,v,x,y])})},getSymbol:t.noop,drawLegendSymbol:t.LegendSymbolMixin.drawRectangle,drawGraph:function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},pointAttribs:function(t,e){var s,o=this.options;s=(p=this.pointAttrToOptions||{}).stroke||"borderColor";var n,h=p["stroke-width"]||"borderWidth",l=t&&t.color||this.color,c=t&&t[s]||o[s]||this.color||l,d=t&&t[h]||o[h]||this[h]||0,p=t&&t.dashStyle||o.dashStyle,u=a(o.opacity,1);return t&&this.zones.length&&(n=t.getZone(),l=t.options.color||n&&n.color||this.color,n&&(c=n.borderColor||c,p=n.dashStyle||p,d=n.borderWidth||d)),e&&(e=(t=r(o.states[e],t.options.states&&t.options.states[e]||{})).brightness,l=t.color||void 0!==e&&i(l).brighten(t.brightness).get()||l,c=t[s]||c,d=t[h]||d,p=t.dashStyle||p,u=a(t.opacity,u)),s={fill:l,stroke:c,"stroke-width":d,opacity:u},p&&(s.dashstyle=p),s},drawPoints:function(){var t,e=this,i=this.chart,s=e.options,o=i.renderer,a=s.animationLimit||250;e.points.forEach(function(h){var l=h.graphic,c=l&&i.pointCount<a?"animate":"attr";n(h.plotY)&&null!==h.y?(t=h.shapeArgs,l&&l.element.nodeName!==h.shapeType&&(l=l.destroy()),l?l[c](r(t)):h.graphic=l=o[h.shapeType](t).add(h.group||e.group),s.borderRadius&&l[c]({r:s.borderRadius}),i.styledMode||l[c](e.pointAttribs(h,h.selected&&"select")).shadow(!1!==h.allowShadow&&s.shadow,null,s.stacking&&!s.borderRadius),l.addClass(h.getClassName(),!0)):l&&(h.graphic=l.destroy())})},animate:function(t){var i,o=this,n=this.yAxis,r=o.options,a=this.chart.inverted,h={},l=a?"translateX":"translateY";c&&(t?(h.scaleY=.001,t=Math.min(n.pos+n.len,Math.max(n.pos,n.toPixels(r.threshold))),a?h.translateX=t-n.len:h.translateY=t,o.clipBox&&o.setClip(),o.group.attr(h)):(i=o.group.attr(l),o.group.animate({scaleY:1},s(e(o.options.animation),{step:function(t,e){h[l]=i+e.pos*(n.pos-i),o.group.attr(h)}})),o.animate=null))},remove:function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),h.prototype.remove.apply(t,arguments)}})}),e(i,"parts/BarSeries.js",[i["parts/Globals.js"]],function(t){(t=t.seriesType)("bar","column",null,{inverted:!0})}),e(i,"parts/ScatterSeries.js",[i["parts/Globals.js"]],function(t){var e=t.Series;(0,t.seriesType)("scatter","line",{lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{
enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">\u25cf</span> <span style="font-size: 10px"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},{sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"],takeOrdinalPosition:!1,drawGraph:function(){this.options.lineWidth&&e.prototype.drawGraph.call(this)},applyJitter:function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(s,o){["x","y"].forEach(function(n,r){var a,h,l,c="plot"+n.toUpperCase();e[n]&&!s.isNull&&(a=t[n+"Axis"],l=e[n]*a.transA,a&&!a.isLog&&(h=Math.max(0,s[c]-l),a=Math.min(a.len,s[c]+l),r=1e4*Math.sin(o+r*i),s[c]=h+(a-h)*(r-Math.floor(r)),"x"===n&&(s.clientX=s.plotX)))})})}}),t.addEvent(e,"afterTranslate",function(){this.applyJitter&&this.applyJitter()})}),e(i,"mixins/centered-series.js",[i["parts/Globals.js"]],function(t){var e=t.deg2rad,i=t.isNumber,s=t.pick,o=t.relativeLength;t.CenteredSeriesMixin={getCenter:function(){var t,e,i=this.options,n=this.chart,r=2*(i.slicedOffset||0),a=n.plotWidth-2*r,h=(n=n.plotHeight-2*r,i.center),l=(h=[s(h[0],"50%"),s(h[1],"50%"),i.size||"100%",i.innerSize||0],Math.min(a,n));for(t=0;4>t;++t)e=h[t],i=2>t||2===t&&/%$/.test(e),h[t]=o(e,[a,n,l,h[2]][t])+(i?r:0);return h[3]>h[2]&&(h[3]=h[2]),h},getStartAndEndRadians:function(t,s){return t=i(t)?t:0,s=i(s)&&s>t&&360>s-t?s:t+360,{start:e*(t+-90),end:e*(s+-90)}}}}),e(i,"parts/PieSeries.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.CenteredSeriesMixin,s=t.defined,o=i.getStartAndEndRadians,n=t.merge,r=t.noop,a=t.pick,h=t.Point,l=t.Series,c=t.seriesType,d=t.setAnimation;c("pie","line",{center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{allowOverlap:!0,connectorPadding:5,distance:30,enabled:!0,formatter:function(){return this.point.isNull?void 0:this.point.name},softConnector:!0,x:0,connectorShape:"fixedOffset",crookDistance:"70%"},ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,states:{hover:{brightness:.1}}},{isCartesian:!1,requireSorting:!1,directTouch:!0,noSharedTooltip:!0,trackerGroups:["group","dataLabelsGroup"],axisTypes:[],pointAttribs:t.seriesTypes.column.prototype.pointAttribs,animate:function(t){var e=this,i=e.points,s=e.startAngleRad;t||(i.forEach(function(t){var i=t.graphic,o=t.shapeArgs;i&&(i.attr({r:t.startR||e.center[3]/2,start:s,end:s}),i.animate({r:o.r,start:o.start,end:o.end},e.options.animation))}),e.animate=null)},hasData:function(){return!!this.processedXData.length},updateTotals:function(){var t,e,i=0,s=this.points,o=s.length,n=this.options.ignoreHiddenPoint;for(t=0;t<o;t++)e=s[t],i+=n&&!e.visible?0:e.isNull?0:e.y;for(this.total=i,t=0;t<o;t++)(e=s[t]).percentage=0<i&&(e.visible||!n)?e.y/i*100:0,e.total=i},generatePoints:function(){l.prototype.generatePoints.call(this),this.updateTotals()},getX:function(t,e,i){var s=this.center,o=this.radii?this.radii[i.index]:s[2]/2;return s[0]+(e?-1:1)*Math.cos(Math.asin(Math.max(Math.min((t-s[1])/(o+i.labelDistance),1),-1)))*(o+i.labelDistance)+(0<i.labelDistance?(e?-1:1)*this.options.dataLabels.padding:0)},translate:function(t){this.generatePoints();var e,i,s,n,r,h,l=0,c=(m=this.options).slicedOffset,d=c+(m.borderWidth||0),p=o(m.startAngle,m.endAngle),u=this.startAngleRad=p.start,f=(p=(this.endAngleRad=p.end)-u,this.points),g=m.dataLabels.distance,m=m.ignoreHiddenPoint,x=f.length;for(t||(this.center=t=this.getCenter()),r=0;r<x;r++)(h=f[r]).labelDistance=a(h.options.dataLabels&&h.options.dataLabels.distance,g),this.maxLabelDistance=Math.max(this.maxLabelDistance||0,h.labelDistance),e=u+l*p,m&&!h.visible||(l+=h.percentage/100),i=u+l*p,h.shapeType="arc",h.shapeArgs={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3},(i=(i+e)/2)>1.5*Math.PI?i-=2*Math.PI:i<-Math.PI/2&&(i+=2*Math.PI),h.slicedTranslation={translateX:Math.round(Math.cos(i)*c),translateY:Math.round(Math.sin(i)*c)},s=Math.cos(i)*t[2]/2,n=Math.sin(i)*t[2]/2,h.tooltipPos=[t[0]+.7*s,t[1]+.7*n],h.half=i<-Math.PI/2||i>Math.PI/2?1:0,h.angle=i,e=Math.min(d,h.labelDistance/5),h.labelPosition={natural:{x:t[0]+s+Math.cos(i)*h.labelDistance,y:t[1]+n+Math.sin(i)*h.labelDistance},final:{},alignment:0>h.labelDistance?"center":h.half?"right":"left",connectorPosition:{breakAt:{x:t[0]+s+Math.cos(i)*e,y:t[1]+n+Math.sin(i)*e},touchingSliceAt:{x:t[0]+s,y:t[1]+n}}}},drawGraph:null,redrawPoints:function(){var t,e,i,s,o=this,r=o.chart,a=r.renderer,h=o.options.shadow;!h||o.shadowGroup||r.styledMode||(o.shadowGroup=a.g("shadow").attr({zIndex:-1}).add(o.group)),o.points.forEach(function(l){var c={};if(e=l.graphic,!l.isNull&&e){if(s=l.shapeArgs,t=l.getTranslate(),!r.styledMode){var d=l.shadowGroup;h&&!d&&(d=l.shadowGroup=a.g("shadow").add(o.shadowGroup)),d&&d.attr(t),i=o.pointAttribs(l,l.selected&&"select")}l.delayedRendering?(e.setRadialReference(o.center).attr(s).attr(t),r.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}).shadow(h,d),l.delayRendering=!1):(e.setRadialReference(o.center),r.styledMode||n(!0,c,i),n(!0,c,s,t),e.animate(c)),e.attr({visibility:l.visible?"inherit":"hidden"}),e.addClass(l.getClassName())}else e&&(l.graphic=e.destroy())})},drawPoints:function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},searchPoint:r,sortByAngle:function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},drawLegendSymbol:t.LegendSymbolMixin.drawRectangle,getCenter:i.getCenter,getSymbol:r},{init:function(){h.prototype.init.apply(this,arguments);var t,i=this;return i.name=a(i.name,"Slice"),e(i,"select",t=function(t){i.slice("select"===t.type)}),e(i,"unselect",t),i},isValid:function(){return t.isNumber(this.y,!0)&&0<=this.y},setVisible:function(t,e){var i=this,s=i.series,o=s.chart,n=s.options.ignoreHiddenPoint;e=a(e,n),t!==i.visible&&(i.visible=i.options.visible=t=void 0===t?!i.visible:t,s.options.data[s.data.indexOf(i)]=i.options,["graphic","dataLabel","connector","shadowGroup"].forEach(function(e){i[e]&&i[e][t?"show":"hide"](!0)}),i.legendItem&&o.legend.colorizeItem(i,t),t||"hover"!==i.state||i.setState(""),n&&(s.isDirty=!0),e&&o.redraw())},slice:function(t,e,i){var o=this.series;d(i,o.chart),a(e,!0),this.sliced=this.options.sliced=s(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic.animate(this.getTranslate()),this.shadowGroup&&this.shadowGroup.animate(this.getTranslate())},getTranslate:function(){return this.sliced?this.slicedTranslation:{translateX:0,translateY:0}},haloPath:function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:this.shapeArgs.r-1,start:e.start,end:e.end})},connectorShapes:{fixedOffset:function(t,e,i){var s=e.breakAt;return e=e.touchingSliceAt,["M",t.x,t.y].concat(i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*s.x-e.x,2*s.y-e.y,s.x,s.y]:["L",s.x,s.y]).concat(["L",e.x,e.y])},straight:function(t,e){return e=e.touchingSliceAt,["M",t.x,t.y,"L",e.x,e.y]},crookedLine:function(e,i,s){i=i.touchingSliceAt;var o=(a=this.series).center[0],n=a.chart.plotWidth,r=a.chart.plotLeft,a=e.alignment,h=this.shapeArgs.r;return s=t.relativeLength(s.crookDistance,1),o=["L",s="left"===a?o+h+(n+r-o-h)*(1-s):r+(o-h)*s,e.y],("left"===a?s>e.x||s<i.x:s<e.x||s>i.x)&&(o=[]),["M",e.x,e.y].concat(o).concat(["L",i.x,i.y])}},getConnectorPath:function(){var t=this.labelPosition,e=this.series.options.dataLabels,i=e.connectorShape,s=this.connectorShapes;return s[i]&&(i=s[i]),i.call(this,{x:t.final.x,y:t.final.y,alignment:t.alignment},t.connectorPosition,e)}})}),e(i,"parts/DataLabels.js",[i["parts/Globals.js"]],function(t){var e=t.arrayMax,i=t.defined,s=t.extend,o=t.format,n=t.merge,r=t.noop,a=t.pick,h=t.relativeLength,l=t.Series,c=t.seriesTypes,d=t.stableSort,p=t.isArray,u=t.splat;t.distribute=function(e,i,s){function o(t,e){return t.target-e.target}var n,r,h=!0,l=e,c=[];r=0;var p=l.reducedLen||i;for(n=e.length;n--;)r+=e[n].size;if(r>p){for(d(e,function(t,e){return(e.rank||0)-(t.rank||0)}),r=n=0;r<=p;)r+=e[n].size,n++;c=e.splice(n-1,e.length)}for(d(e,o),e=e.map(function(t){return{size:t.size,targets:[t.target],align:a(t.align,.5)}});h;){for(n=e.length;n--;)h=e[n],r=(Math.min.apply(0,h.targets)+Math.max.apply(0,h.targets))/2,h.pos=Math.min(Math.max(0,r-h.size*h.align),i-h.size);for(n=e.length,h=!1;n--;)0<n&&e[n-1].pos+e[n-1].size>e[n].pos&&(e[n-1].size+=e[n].size,e[n-1].targets=e[n-1].targets.concat(e[n].targets),e[n-1].align=.5,e[n-1].pos+e[n-1].size>i&&(e[n-1].pos=i-e[n-1].size),e.splice(n,1),h=!0)}l.push.apply(l,c),n=0,e.some(function(e){var o=0;if(e.targets.some(function(){if(l[n].pos=e.pos+o,Math.abs(l[n].pos-l[n].target)>s)return l.slice(0,n+1).forEach(function(t){delete t.pos}),l.reducedLen=(l.reducedLen||i)-.1*i,l.reducedLen>.1*i&&t.distribute(l,i,s),!0;o+=l[n].size,n++}))return!0}),d(l,o)},l.prototype.drawDataLabels=function(){function e(t,e){var i=e.filter;return!i||(e=i.operator,t=t[i.property],i=i.value,">"===e&&t>i||"<"===e&&t<i||">="===e&&t>=i||"<="===e&&t<=i||"=="===e&&t==i||"==="===e&&t===i)}function s(t,e){var i,s=[];if(p(t)&&!p(e))s=t.map(function(t){return n(t,e)});else if(p(e)&&!p(t))s=e.map(function(e){return n(t,e)});else if(p(t)||p(e))for(i=Math.max(t.length,e.length);i--;)s[i]=n(t[i],e[i]);else s=n(t,e);return s}var r,h=this,l=h.chart,c=h.options,d=c.dataLabels,f=h.points,g=h.hasRendered||0,m=t.animObject(c.animation).duration,x=Math.min(m,200),v=a(d.defer,0<x),y=l.renderer;d=s(s(l.options.plotOptions&&l.options.plotOptions.series&&l.options.plotOptions.series.dataLabels,l.options.plotOptions&&l.options.plotOptions[h.type]&&l.options.plotOptions[h.type].dataLabels),d);t.fireEvent(this,"drawDataLabels"),(p(d)||d.enabled||h._hasPointLabels)&&(r=h.plotGroup("dataLabelsGroup","data-labels",v&&!g?"hidden":"inherit",d.zIndex||6),v&&(r.attr({opacity:+g}),g||setTimeout(function(){var t=h.dataLabelsGroup;t&&(h.visible&&r.show(!0),t[c.animation?"animate":"attr"]({opacity:1},{duration:x}))},m-x)),f.forEach(function(n){u(s(d,n.dlOptions||n.options&&n.options.dataLabels)).forEach(function(s,d){var p,u,f,g,m=s.enabled&&(!n.isNull||n.dataLabelOnNull)&&e(n,s),x=n.dataLabels?n.dataLabels[d]:n.dataLabel,v=n.connectors?n.connectors[d]:n.connector,b=!x;m&&(p=n.getLabelConfig(),u=a(s[n.formatPrefix+"Format"],s.format),p=i(u)?o(u,p,l.time):(s[n.formatPrefix+"Formatter"]||s.formatter).call(p,s),u=s.style,f=s.rotation,l.styledMode||(u.color=a(s.color,u.color,h.color,"#000000"),"contrast"===u.color&&(n.contrastColor=y.getContrast(n.color||h.color),u.color=s.inside||0>a(s.distance,n.labelDistance)||c.stacking?n.contrastColor:"#000000"),c.cursor&&(u.cursor=c.cursor)),g={r:s.borderRadius||0,rotation:f,padding:s.padding,zIndex:1},l.styledMode||(g.fill=s.backgroundColor,g.stroke=s.borderColor,g["stroke-width"]=s.borderWidth),t.objectEach(g,function(t,e){void 0===t&&delete g[e]})),!x||m&&i(p)?m&&i(p)&&(x?g.text=p:(n.dataLabels=n.dataLabels||[],x=n.dataLabels[d]=f?y.text(p,0,-9999).addClass("highcharts-data-label"):y.label(p,0,-9999,s.shape,null,null,s.useHTML,null,"data-label"),d||(n.dataLabel=x),x.addClass(" highcharts-data-label-color-"+n.colorIndex+" "+(s.className||"")+(s.useHTML?" highcharts-tracker":""))),x.options=s,x.attr(g),l.styledMode||x.css(u).shadow(s.shadow),x.added||x.add(r),s.textPath&&x.setTextPath(n.getDataLabelPath&&n.getDataLabelPath(x)||n.graphic,s.textPath),h.alignDataLabel(n,x,s,null,b)):(n.dataLabel=n.dataLabel&&n.dataLabel.destroy(),n.dataLabels&&(1===n.dataLabels.length?delete n.dataLabels:delete n.dataLabels[d]),d||delete n.dataLabel,v&&(n.connector=n.connector.destroy(),n.connectors&&(1===n.connectors.length?delete n.connectors:delete n.connectors[d])))})})),t.fireEvent(this,"afterDrawDataLabels")},l.prototype.alignDataLabel=function(t,e,i,o,n){var r,h=this.chart,l=this.isCartesian&&h.inverted,c=a(t.dlBox&&t.dlBox.centerX,t.plotX,-9999),d=a(t.plotY,-9999),p=e.getBBox(),u=i.rotation,f=i.align,g=this.visible&&(t.series.forceDL||h.isInsidePlot(c,Math.round(d),l)||o&&h.isInsidePlot(c,l?o.x+1:o.y+o.height-1,l)),m="justify"===a(i.overflow,"justify");g&&(r=h.renderer.fontMetrics(h.styledMode?void 0:i.style.fontSize,e).b,o=s({x:l?this.yAxis.len-d:c,y:Math.round(l?this.xAxis.len-c:d),width:0,height:0},o),s(i,{width:p.width,height:p.height}),u?(m=!1,c=h.renderer.rotCorr(r,u),c={x:o.x+i.x+o.width/2+c.x,y:o.y+i.y+{top:0,middle:.5,bottom:1}[i.verticalAlign]*o.height},e[n?"attr":"animate"](c).attr({align:f}),d=180<(d=(u+720)%360)&&360>d,"left"===f?c.y-=d?p.height:0:"center"===f?(c.x-=p.width/2,c.y-=p.height/2):"right"===f&&(c.x-=p.width,c.y-=d?0:p.height),e.placed=!0,e.alignAttr=c):(e.align(i,null,o),c=e.alignAttr),m&&0<=o.height?t.isLabelJustified=this.justifyDataLabel(e,i,c,p,o,n):a(i.crop,!0)&&(g=h.isInsidePlot(c.x,c.y)&&h.isInsidePlot(c.x+p.width,c.y+p.height)),i.shape&&!u)&&e[n?"attr":"animate"]({anchorX:l?h.plotWidth-t.plotY:t.plotX,anchorY:l?h.plotHeight-t.plotX:t.plotY}),g||(e.attr({y:-9999}),e.placed=!1)},l.prototype.justifyDataLabel=function(t,e,i,s,o,n){var r,a,h=this.chart,l=e.align,c=e.verticalAlign,d=t.box?0:t.padding||0;return 0>(r=i.x+d)&&("right"===l?e.align="left":e.x=-r,a=!0),(r=i.x+s.width-d)>h.plotWidth&&("left"===l?e.align="right":e.x=h.plotWidth-r,a=!0),0>(r=i.y+d)&&("bottom"===c?e.verticalAlign="top":e.y=-r,a=!0),(r=i.y+s.height-d)>h.plotHeight&&("top"===c?e.verticalAlign="bottom":e.y=h.plotHeight-r,a=!0),a&&(t.placed=!n,t.align(e,null,o)),a},c.pie&&(c.pie.prototype.dataLabelPositioners={radialDistributionY:function(t){return t.top+t.distributeBox.pos},radialDistributionX:function(t,e,i,s){return t.getX(i<e.top+2||i>e.bottom-2?s:i,e.half,e)},justify:function(t,e,i){return i[0]+(t.half?-1:1)*(e+t.labelDistance)},alignToPlotEdges:function(t,e,i,s){return t=t.getBBox().width,e?t+s:i-t-s},alignToConnectors:function(t,e,i,s){var o,n=0;return t.forEach(function(t){(o=t.dataLabel.getBBox().width)>n&&(n=o)}),e?n+s:i-n-s}},c.pie.prototype.drawDataLabels=function(){var s,o,r,h,c,d,p,u,f,g,m,x,v=this,y=v.data,b=v.chart,M=v.options.dataLabels,k=M.connectorPadding,w=b.plotWidth,S=b.plotHeight,A=b.plotLeft,T=Math.round(b.chartWidth/3),P=v.center,E=P[2]/2,C=P[1],L=[[],[]],O=[0,0,0,0],D=v.dataLabelPositioners;v.visible&&(M.enabled||v._hasPointLabels)&&(y.forEach(function(t){t.dataLabel&&t.visible&&t.dataLabel.shortened&&(t.dataLabel.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.dataLabel.shortened=!1)}),l.prototype.drawDataLabels.apply(v),y.forEach(function(t){t.dataLabel&&(t.visible?(L[t.half].push(t),t.dataLabel._pos=null,!i(M.style.width)&&!i(t.options.dataLabels&&t.options.dataLabels.style&&t.options.dataLabels.style.width)&&t.dataLabel.getBBox().width>T&&(t.dataLabel.css({width:.7*T}),t.dataLabel.shortened=!0)):(t.dataLabel=t.dataLabel.destroy(),t.dataLabels&&1===t.dataLabels.length&&delete t.dataLabels))}),L.forEach(function(e,o){var n,r,l,x=e.length,y=[];if(x)for(v.sortByAngle(e,o-.5),0<v.maxLabelDistance&&(n=Math.max(0,C-E-v.maxLabelDistance),r=Math.min(C+E+v.maxLabelDistance,b.plotHeight),e.forEach(function(t){0<t.labelDistance&&t.dataLabel&&(t.top=Math.max(0,C-E-t.labelDistance),t.bottom=Math.min(C+E+t.labelDistance,b.plotHeight),l=t.dataLabel.getBBox().height||21,t.distributeBox={target:t.labelPosition.natural.y-t.top+l/2,size:l,rank:t.y},y.push(t.distributeBox))}),n=r+l-n,t.distribute(y,n,n/5)),m=0;m<x;m++){if(s=e[m],d=s.labelPosition,h=s.dataLabel,g=!1===s.visible?"hidden":"inherit",f=n=d.natural.y,y&&i(s.distributeBox)&&(void 0===s.distributeBox.pos?g="hidden":(p=s.distributeBox.size,f=D.radialDistributionY(s))),delete s.positionIndex,M.justify)u=D.justify(s,E,P);else switch(M.alignTo){case"connectors":u=D.alignToConnectors(e,o,w,A);break;case"plotEdges":u=D.alignToPlotEdges(h,o,w,A);break;default:u=D.radialDistributionX(v,s,f,n)}h._attr={visibility:g,align:d.alignment},h._pos={x:u+M.x+({left:k,right:-k}[d.alignment]||0),y:f+M.y-10},d.final.x=u,d.final.y=f,a(M.crop,!0)&&(c=h.getBBox().width,n=null,u-c<k&&1===o?(n=Math.round(c-u+k),O[3]=Math.max(n,O[3])):u+c>w-k&&0===o&&(n=Math.round(u+c-w+k),O[1]=Math.max(n,O[1])),0>f-p/2?O[0]=Math.max(Math.round(p/2-f),O[0]):f+p/2>S&&(O[2]=Math.max(Math.round(f+p/2-S),O[2])),h.sideOverflow=n)}}),0===e(O)||this.verifyDataLabelOverflow(O))&&(this.placeDataLabels(),this.points.forEach(function(t){var e;(x=n(M,t.options.dataLabels),o=a(x.connectorWidth,1))&&(r=t.connector,(h=t.dataLabel)&&h._pos&&t.visible&&0<t.labelDistance?(g=h._attr.visibility,(e=!r)&&(t.connector=r=b.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(v.dataLabelsGroup),b.styledMode||r.attr({"stroke-width":o,stroke:x.connectorColor||t.color||"#666666"})),r[e?"attr":"animate"]({d:t.getConnectorPath()}),r.attr("visibility",g)):r&&(t.connector=r.destroy()))}))},c.pie.prototype.placeDataLabels=function(){this.points.forEach(function(t){var e,i=t.dataLabel;i&&t.visible&&((e=i._pos)?(i.sideOverflow&&(i._attr.width=Math.max(i.getBBox().width-i.sideOverflow,0),i.css({width:i._attr.width+"px",textOverflow:(this.options.dataLabels.style||{}).textOverflow||"ellipsis"}),i.shortened=!0),i.attr(i._attr),i[i.moved?"animate":"attr"](e),i.moved=!0):i&&i.attr({y:-9999})),delete t.distributeBox},this)},c.pie.prototype.alignDataLabel=r,c.pie.prototype.verifyDataLabelOverflow=function(t){var e,i=this.center,s=this.options,o=s.center,n=s.minSize||80,r=null!==s.size;return r||(null!==o[0]?e=Math.max(i[2]-Math.max(t[1],t[3]),n):(e=Math.max(i[2]-t[1]-t[3],n),i[0]+=(t[3]-t[1])/2),null!==o[1]?e=Math.max(Math.min(e,i[2]-Math.max(t[0],t[2])),n):(e=Math.max(Math.min(e,i[2]-t[0]-t[2]),n),i[1]+=(t[0]-t[2])/2),e<i[2]?(i[2]=e,i[3]=Math.min(h(s.innerSize||0,e),e),this.translate(i),this.drawDataLabels&&this.drawDataLabels()):r=!0),r}),c.column&&(c.column.prototype.alignDataLabel=function(t,e,i,s,o){var r=this.chart.inverted,h=t.series,c=t.dlBox||t.shapeArgs,d=a(t.below,t.plotY>a(this.translatedThreshold,h.yAxis.len)),p=a(i.inside,!!this.options.stacking);c&&(0>(s=n(c)).y&&(s.height+=s.y,s.y=0),0<(c=s.y+s.height-h.yAxis.len)&&(s.height-=c),r&&(s={x:h.yAxis.len-s.y-s.height,y:h.xAxis.len-s.x-s.width,width:s.height,height:s.width}),p||(r?(s.x+=d?0:s.width,s.width=0):(s.y+=d?s.height:0,s.height=0))),i.align=a(i.align,!r||p?"center":d?"right":"left"),i.verticalAlign=a(i.verticalAlign,r||p?"middle":d?"top":"bottom"),l.prototype.alignDataLabel.call(this,t,e,i,s,o),t.isLabelJustified&&t.contrastColor&&e.css({color:t.contrastColor})})}),e(i,"modules/overlapping-datalabels.src.js",[i["parts/Globals.js"]],function(t){var e=t.Chart,i=t.isArray,s=t.objectEach,o=t.pick,n=t.addEvent,r=t.fireEvent;n(e,"render",function(){var t=[];(this.labelCollectors||[]).forEach(function(e){t=t.concat(e())}),(this.yAxis||[]).forEach(function(e){e.options.stackLabels&&!e.options.stackLabels.allowOverlap&&s(e.stacks,function(e){s(e,function(e){t.push(e.label)})})}),(this.series||[]).forEach(function(e){var s=e.options.dataLabels;e.visible&&(!1!==s.enabled||e._hasPointLabels)&&e.points.forEach(function(e){e.visible&&(i(e.dataLabels)?e.dataLabels:e.dataLabel?[e.dataLabel]:[]).forEach(function(i){var s=i.options;i.labelrank=o(s.labelrank,e.labelrank,e.shapeArgs&&e.shapeArgs.height),s.allowOverlap||t.push(i)})})}),this.hideOverlappingLabels(t)}),e.prototype.hideOverlappingLabels=function(t){var e,i,s,o,n,a,h=this,l=t.length,c=h.renderer,d=function(t,e,i,s,o,n,r,a){return!(o>t+i||o+r<t||n>e+s||n+a<e)};for(s=function(t){var e,i,s,o=t.box?0:t.padding||0;if(s=0,t&&(!t.alignAttr||t.placed))return e=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},i=t.parentGroup,t.width||(s=t.getBBox(),t.width=s.width,t.height=s.height,s=c.fontMetrics(null,t.element).h),{x:e.x+(i.translateX||0)+o,y:e.y+(i.translateY||0)+o-s,width:t.width-2*o,height:t.height-2*o}},i=0;i<l;i++)(e=t[i])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=s(e));for(t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)}),i=0;i<l;i++)for(a=(s=t[i])&&s.absoluteBox,e=i+1;e<l;++e)n=(o=t[e])&&o.absoluteBox,a&&n&&s!==o&&0!==s.newOpacity&&0!==o.newOpacity&&(n=d(a.x,a.y,a.width,a.height,n.x,n.y,n.width,n.height))&&((s.labelrank<o.labelrank?s:o).newOpacity=0);t.forEach(function(t){var e,i;t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.alignAttr&&t.placed?(i?t.show(!0):e=function(){t.hide()},t.alignAttr.opacity=i,t[t.isOld?"animate":"attr"](t.alignAttr,null,e),r(h,"afterHideOverlappingLabels")):t.attr({opacity:i})),t.isOld=!0)})}}),e(i,"parts/Interaction.js",[i["parts/Globals.js"]],function(t){var e,i=t.addEvent,s=t.Chart,o=t.createElement,n=t.css,r=t.defaultOptions,a=t.defaultPlotOptions,h=t.extend,l=t.fireEvent,c=t.hasTouch,d=t.isObject,p=t.Legend,u=t.merge,f=t.pick,g=t.Point,m=t.Series,x=t.seriesTypes,v=t.svg;e=t.TrackerMixin={drawTrackerPoint:function(){var t=this,e=t.chart,i=e.pointer,s=function(t){var e=i.getPointFromEvent(t);void 0!==e&&(i.isDirectTouch=!0,e.onMouseOver(t))};t.points.forEach(function(t){t.graphic&&(t.graphic.element.point=t),t.dataLabel&&(t.dataLabel.div?t.dataLabel.div.point=t:t.dataLabel.element.point=t)}),t._hasTracking||(t.trackerGroups.forEach(function(o){t[o]&&(t[o].addClass("highcharts-tracker").on("mouseover",s).on("mouseout",function(t){i.onTrackerMouseOut(t)}),c&&t[o].on("touchstart",s),!e.styledMode&&t.options.cursor&&t[o].css(n).css({cursor:t.options.cursor}))}),t._hasTracking=!0),l(this,"afterDrawTracker")},drawTrackerGraph:function(){var t,e=this,i=e.options,s=i.trackByArea,o=[].concat(s?e.areaPath:e.graphPath),n=o.length,r=e.chart,a=r.pointer,h=r.renderer,d=r.options.tooltip.snap,p=e.tracker,u=function(){r.hoverSeries!==e&&e.onMouseOver()},f="rgba(192,192,192,"+(v?1e-4:.002)+")";if(n&&!s)for(t=n+1;t--;)"M"===o[t]&&o.splice(t+1,0,o[t+1]-d,o[t+2],"L"),(t&&"M"===o[t]||t===n)&&o.splice(t,0,"L",o[t-2]+d,o[t-1]);p?p.attr({d:o}):e.graph&&(e.tracker=h.path(o).attr({visibility:e.visible?"visible":"hidden",zIndex:2}).addClass(s?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),r.styledMode||e.tracker.attr({"stroke-linejoin":"round",stroke:f,fill:s?f:"none","stroke-width":e.graph.strokeWidth()+(s?0:2*d)}),[e.tracker,e.markerGroup].forEach(function(t){t.addClass("highcharts-tracker").on("mouseover",u).on("mouseout",function(t){a.onTrackerMouseOut(t)}),i.cursor&&!r.styledMode&&t.css({cursor:i.cursor}),c&&t.on("touchstart",u)})),l(this,"afterDrawTracker")}},x.column&&(x.column.prototype.drawTracker=e.drawTrackerPoint),x.pie&&(x.pie.prototype.drawTracker=e.drawTrackerPoint),x.scatter&&(x.scatter.prototype.drawTracker=e.drawTrackerPoint),h(p.prototype,{setItemEvents:function(t,e,i){var s=this,o=s.chart.renderer.boxWrapper,n=t instanceof g,r="highcharts-legend-"+(n?"point":"series")+"-active",a=s.chart.styledMode;(i?e:t.legendGroup).on("mouseover",function(){s.allItems.forEach(function(e){t!==e&&e.setState("inactive",!n)}),t.setState("hover"),t.visible&&o.addClass(r),a||e.css(s.options.itemHoverStyle)}).on("mouseout",function(){s.styledMode||e.css(u(t.visible?s.itemStyle:s.itemHiddenStyle)),s.allItems.forEach(function(e){t!==e&&e.setState("",!n)}),o.removeClass(r),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible()};o.removeClass(r),e={browserEvent:e},t.firePointEvent?t.firePointEvent("legendItemClick",e,i):l(t,"legendItemClick",e,i)})},createCheckboxForItem:function(t){t.checkbox=o("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),i(t.checkbox,"click",function(e){l(t.series||t,"checkboxClick",{checked:e.target.checked,item:t},function(){t.select()})})}}),h(s.prototype,{showResetZoom:function(){function t(){e.zoomOut()}var e=this,i=r.lang,s=e.options.chart.resetZoomButton,o=s.theme,n=o.states,a="chart"===s.relativeTo||"spaceBox"===s.relativeTo?null:"plotBox";l(this,"beforeShowResetZoom",null,function(){e.resetZoomButton=e.renderer.button(i.resetZoom,null,null,t,o,n&&n.hover).attr({align:s.position.align,title:i.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(s.position,!1,a)}),l(this,"afterShowResetZoom")},zoomOut:function(){l(this,"selection",{resetSelection:!0},this.zoom)},zoom:function(e){var i,s,o=this,n=o.pointer,r=!1,a=o.inverted?n.mouseDownX:n.mouseDownY;!e||e.resetSelection?(o.axes.forEach(function(t){i=t.zoom()}),n.initiated=!1):e.xAxis.concat(e.yAxis).forEach(function(e){var s=e.axis,h=o.inverted?s.left:s.top,l=o.inverted?h+s.width:h+s.height,c=s.isXAxis,d=!1;(!c&&a>=h&&a<=l||c||!t.defined(a))&&(d=!0),n[c?"zoomX":"zoomY"]&&d&&(i=s.zoom(e.min,e.max),s.displayBtn&&(r=!0))}),s=o.resetZoomButton,r&&!s?o.showResetZoom():!r&&d(s)&&(o.resetZoomButton=s.destroy()),i&&o.redraw(f(o.options.chart.animation,e&&e.animation,100>o.pointCount))},pan:function(t,e){var i,s=this,o=s.hoverPoints;l(this,"pan",{originalEvent:t},function(){o&&o.forEach(function(t){t.setState()}),("xy"===e?[1,0]:[1]).forEach(function(e){var o,n=(e=s[e?"xAxis":"yAxis"][0]).horiz,r=t[n?"chartX":"chartY"],a=s[n=n?"mouseDownX":"mouseDownY"],h=(e.pointRange||0)/2,l=e.reversed&&!s.inverted||!e.reversed&&s.inverted?-1:1,c=e.getExtremes(),d=e.toValue(a-r,!0)+h*l;a=(o=(l=e.toValue(a+e.len-r,!0)-h*l)<d)?l:d,d=o?d:l;0<(o=(l=Math.min(c.dataMin,h?c.min:e.toValue(e.toPixels(c.min)-e.minPixelPadding)))-a)&&(d+=o,a=l),0<(o=d-(h=Math.max(c.dataMax,h?c.max:e.toValue(e.toPixels(c.max)+e.minPixelPadding))))&&(d=h,a-=o),e.series.length&&a!==c.min&&d!==c.max&&(e.setExtremes(a,d,!1,!1,{trigger:"pan"}),i=!0),s[n]=r}),i&&s.redraw(!1),n(s.container,{cursor:"move"})})}}),h(g.prototype,{select:function(t,e){var i=this,s=i.series,o=s.chart;t=f(t,!i.selected),i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,s.options.data[s.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||o.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(o.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})})},onMouseOver:function(t){var e=this.series.chart,i=e.pointer;t=t?i.normalize(t):i.getChartCoordinatesFromPoint(this,e.inverted),i.runPointActions(t,this)},onMouseOut:function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},importEvents:function(){if(!this.hasImportedEvents){var e=this,s=u(e.series.options.point,e.options).events;e.events=s,t.objectEach(s,function(t,s){i(e,s,t)}),this.hasImportedEvents=!0}},setState:function(t,e){var i,s,o,n=Math.floor(this.plotX),r=this.plotY,c=this.series,d=this.state,p=c.options.states[t||"normal"]||{},u=a[c.type].marker&&c.options.marker,g=u&&!1===u.enabled,m=u&&u.states&&u.states[t||"normal"]||{},x=!1===m.enabled,v=c.stateMarkerGraphic,y=this.marker||{},b=c.chart,M=c.halo,k=u&&c.markerAttribs;(t=t||"")===this.state&&!e||this.selected&&"select"!==t||!1===p.enabled||t&&(x||g&&!1===m.enabled)||t&&y.states&&y.states[t]&&!1===y.states[t].enabled||(this.state=t,k&&(i=c.markerAttribs(this,t)),this.graphic?(d&&this.graphic.removeClass("highcharts-point-"+d),t&&this.graphic.addClass("highcharts-point-"+t),b.styledMode||(s=c.pointAttribs(this,t),o=f(b.options.chart.animation,p.animation),c.options.inactiveOtherPoints&&((this.dataLabels||[]).forEach(function(t){t&&t.animate({opacity:s.opacity},o)}),this.connector&&this.connector.animate({opacity:s.opacity},o)),this.graphic.animate(s,o)),i&&this.graphic.animate(i,f(b.options.chart.animation,m.animation,u.animation)),v&&v.hide()):(t&&m&&(d=y.symbol||c.symbol,v&&v.currentSymbol!==d&&(v=v.destroy()),v?v[e?"animate":"attr"]({x:i.x,y:i.y}):d&&(c.stateMarkerGraphic=v=b.renderer.symbol(d,i.x,i.y,i.width,i.height).add(c.markerGroup),v.currentSymbol=d),!b.styledMode&&v&&v.attr(c.pointAttribs(this,t))),v&&(v[t&&b.isInsidePlot(n,r,b.inverted)?"show":"hide"](),v.element.point=this)),(t=p.halo)&&t.size?(M||(c.halo=M=b.renderer.path().add((this.graphic||v).parentGroup)),M.show()[e?"animate":"attr"]({d:this.haloPath(t.size)}),M.attr({"class":"highcharts-halo highcharts-color-"+f(this.colorIndex,c.colorIndex)+(this.className?" "+this.className:""),zIndex:-1}),M.point=this,b.styledMode||M.attr(h({fill:this.color||c.color,"fill-opacity":t.opacity},t.attributes))):M&&M.point&&M.point.haloPath&&M.animate({d:M.point.haloPath(0)},null,M.hide),l(this,"afterSetState"))},haloPath:function(t){return this.series.chart.renderer.symbols.circle(Math.floor(this.plotX)-t,this.plotY-t,2*t,2*t)}}),h(m.prototype,{onMouseOver:function(){var t=this.chart,e=t.hoverSeries;e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&l(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},onMouseOut:function(){var t=this.options,e=this.chart,i=e.tooltip,s=e.hoverPoint;e.hoverSeries=null,s&&s.onMouseOut(),this&&t.events.mouseOut&&l(this,"mouseOut"),!i||this.stickyTracking||i.shared&&!this.noSharedTooltip||i.hide(),e.series.forEach(function(t){t.setState("",!0)})},setState:function(t,e){var i=this,s=i.options,o=i.graph,n=s.inactiveOtherPoints,r=s.states,a=s.lineWidth,h=s.opacity,l=f(r[t||"normal"]&&r[t||"normal"].animation,i.chart.options.chart.animation);s=0;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(r[t]&&!1===r[t].enabled)return;if(t&&(a=r[t].lineWidth||a+(r[t].lineWidthPlus||0),h=f(r[t].opacity,h)),o&&!o.dashstyle)for(r={"stroke-width":a},o.animate(r,l);i["zone-graph-"+s];)i["zone-graph-"+s].attr(r),s+=1;n||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:h},l)})}e&&n&&i.points&&i.points.forEach(function(e){e.setState&&e.setState(t)})},setVisible:function(t,e){var i,s=this,o=s.chart,n=s.legendItem,r=o.options.chart.ignoreHiddenSeries,a=s.visible;i=(s.visible=t=s.options.visible=s.userOptions.visible=void 0===t?!a:t)?"show":"hide",["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){s[t]&&s[t][i]()}),o.hoverSeries!==s&&(o.hoverPoint&&o.hoverPoint.series)!==s||s.onMouseOut(),n&&o.legend.colorizeItem(s,t),s.isDirty=!0,s.options.stacking&&o.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),s.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),r&&(o.isDirtyBox=!0),l(s,i),!1!==e&&o.redraw()},show:function(){this.setVisible(!0)},hide:function(){this.setVisible(!1)},select:function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),l(this,t?"select":"unselect")},drawTracker:e.drawTrackerGraph})}),e(i,"parts/Responsive.js",[i["parts/Globals.js"]],function(t){var e=t.Chart,i=t.isArray,s=t.isObject,o=t.pick,n=t.splat;e.prototype.setResponsive=function(e,i){var s=this.options.responsive,o=[],n=this.currentResponsive;!i&&s&&s.rules&&s.rules.forEach(function(i){void 0===i._id&&(i._id=t.uniqueKey()),this.matchResponsiveRule(i,o,e)},this),(i=t.merge.apply(0,o.map(function(e){return t.find(s.rules,function(t){return t._id===e}).chartOptions}))).isResponsiveOptions=!0,(o=o.toString()||void 0)!==(n&&n.ruleIds)&&(n&&this.update(n.undoOptions,e),o?((n=this.currentOptions(i)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:o,mergedOptions:i,undoOptions:n},this.update(i,e)):this.currentResponsive=void 0)},e.prototype.matchResponsiveRule=function(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=o(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=o(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=o(i.minWidth,0)&&this.chartHeight>=o(i.minHeight,0)}).call(this)&&e.push(t._id)},e.prototype.currentOptions=function(e){function r(e,a,h,l){var c;t.objectEach(e,function(t,e){if(!l&&-1<["series","xAxis","yAxis"].indexOf(e))for(t=n(t),h[e]=[],c=0;c<t.length;c++)a[e][c]&&(h[e][c]={},r(t[c],a[e][c],h[e][c],l+1));else s(t)?(h[e]=i(t)?[]:{},r(t,a[e]||{},h[e],l+1)):h[e]=o(a[e],null)})}var a={};return r(e,this.options,a,0),a}}),e(i,"masters/highcharts.src.js",[i["parts/Globals.js"]],function(t){return t}),e(i,"parts/Scrollbar.js",[i["parts/Globals.js"]],function(t){function e(t,e,i){this.init(t,e,i)}var i,s=t.addEvent,o=t.Axis,n=t.correctFloat,r=t.defaultOptions,a=t.defined,h=t.destroyObjectProperties,l=t.fireEvent,c=t.hasTouch,d=t.merge,p=t.pick,u=t.removeEvent,f={height:t.isTouchDevice?20:14,barBorderRadius:0,buttonBorderRadius:0,liveRedraw:void 0,margin:10,minWidth:6,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:1,
barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"#333333",trackBackgroundColor:"#f2f2f2",trackBorderColor:"#f2f2f2",trackBorderWidth:1};r.scrollbar=d(!0,f,r.scrollbar),t.swapXY=i=function(t,e){var i,s=t.length;if(e)for(e=0;e<s;e+=3)i=t[e+1],t[e+1]=t[e+2],t[e+2]=i;return t},e.prototype={init:function(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=d(f,e),this.chart=i,this.size=p(this.options.size,this.options.height),e.enabled&&(this.render(),this.initEvents(),this.addEvents())},render:function(){var t,e=this.renderer,s=this.options,o=this.size,n=this.chart.styledMode;this.group=t=e.g("scrollbar").attr({zIndex:s.zIndex,translateY:-99999}).add(),this.track=e.rect().addClass("highcharts-scrollbar-track").attr({x:0,r:s.trackBorderRadius||0,height:o,width:o}).add(t),n||this.track.attr({fill:s.trackBackgroundColor,stroke:s.trackBorderColor,"stroke-width":s.trackBorderWidth}),this.trackBorderWidth=this.track.strokeWidth(),this.track.attr({y:-this.trackBorderWidth%2/2}),this.scrollbarGroup=e.g().add(t),this.scrollbar=e.rect().addClass("highcharts-scrollbar-thumb").attr({height:o,width:o,r:s.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=e.path(i(["M",-3,o/4,"L",-3,2*o/3,"M",0,o/4,"L",0,2*o/3,"M",3,o/4,"L",3,2*o/3],s.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),n||(this.scrollbar.attr({fill:s.barBackgroundColor,stroke:s.barBorderColor,"stroke-width":s.barBorderWidth}),this.scrollbarRifles.attr({stroke:s.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-this.scrollbarStrokeWidth%2/2,-this.scrollbarStrokeWidth%2/2),this.drawScrollbarButton(0),this.drawScrollbarButton(1)},position:function(t,e,i,s){var o=this.options.vertical,n=0,r=this.rendered?"animate":"attr";this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.xOffset=this.height=s,this.yOffset=n,o?(this.width=this.yOffset=i=n=this.size,this.xOffset=e=0,this.barWidth=s-2*i,this.x=t+=this.options.margin):(this.height=this.xOffset=s=e=this.size,this.barWidth=i-2*s,this.y+=this.options.margin),this.group[r]({translateX:t,translateY:this.y}),this.track[r]({width:i,height:s}),this.scrollbarButtons[1][r]({translateX:o?0:i-e,translateY:o?s-n:0})},drawScrollbarButton:function(t){var e,s=this.renderer,o=this.scrollbarButtons,n=this.options,r=this.size;e=s.g().add(this.group),o.push(e),e=s.rect().addClass("highcharts-scrollbar-button").add(e),this.chart.styledMode||e.attr({stroke:n.buttonBorderColor,"stroke-width":n.buttonBorderWidth,fill:n.buttonBackgroundColor}),e.attr(e.crisp({x:-.5,y:-.5,width:r+1,height:r+1,r:n.buttonBorderRadius},e.strokeWidth())),e=s.path(i(["M",r/2+(t?-1:1),r/2-3,"L",r/2+(t?-1:1),r/2+3,"L",r/2+(t?2:-2),r/2],n.vertical)).addClass("highcharts-scrollbar-arrow").add(o[t]),this.chart.styledMode||e.attr({fill:n.buttonArrowColor})},setRange:function(t,e){var i,s,o=this.options,r=o.vertical,h=o.minWidth,l=this.barWidth,c=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";a(l)&&(t=Math.max(t,0),i=Math.ceil(l*t),this.calculatedWidth=s=n(l*Math.min(e,1)-i),s<h&&(i=(l-h+s)*t,s=h),h=Math.floor(i+this.xOffset+this.yOffset),l=s/2-.5,this.from=t,this.to=e,r?(this.scrollbarGroup[c]({translateY:h}),this.scrollbar[c]({height:s}),this.scrollbarRifles[c]({translateY:l}),this.scrollbarTop=h,this.scrollbarLeft=0):(this.scrollbarGroup[c]({translateX:h}),this.scrollbar[c]({width:s}),this.scrollbarRifles[c]({translateX:l}),this.scrollbarLeft=h,this.scrollbarTop=0),12>=s?this.scrollbarRifles.hide():this.scrollbarRifles.show(!0),!1===o.showFull&&(0>=t&&1<=e?this.group.hide():this.group.show()),this.rendered=!0)},initEvents:function(){var t=this;t.mouseMoveHandler=function(e){var i=t.chart.pointer.normalize(e),s=t.options.vertical?"chartY":"chartX",o=t.initPositions;!t.grabbedCenter||e.touches&&0===e.touches[0][s]||(s=(i=t.cursorToScrollbarPosition(i)[s])-(s=t[s]),t.hasDragged=!0,t.updatePosition(o[0]+s,o[1]+s),t.hasDragged&&l(t,"changed",{from:t.from,to:t.to,trigger:"scrollbar",DOMType:e.type,DOMEvent:e}))},t.mouseUpHandler=function(e){t.hasDragged&&l(t,"changed",{from:t.from,to:t.to,trigger:"scrollbar",DOMType:e.type,DOMEvent:e}),t.grabbedCenter=t.hasDragged=t.chartX=t.chartY=null},t.mouseDownHandler=function(e){e=t.chart.pointer.normalize(e),e=t.cursorToScrollbarPosition(e),t.chartX=e.chartX,t.chartY=e.chartY,t.initPositions=[t.from,t.to],t.grabbedCenter=!0},t.buttonToMinClick=function(e){var i=n(t.to-t.from)*t.options.step;t.updatePosition(n(t.from-i),n(t.to-i)),l(t,"changed",{from:t.from,to:t.to,trigger:"scrollbar",DOMEvent:e})},t.buttonToMaxClick=function(e){var i=(t.to-t.from)*t.options.step;t.updatePosition(t.from+i,t.to+i),l(t,"changed",{from:t.from,to:t.to,trigger:"scrollbar",DOMEvent:e})},t.trackClick=function(e){var i=t.chart.pointer.normalize(e),s=t.to-t.from,o=t.y+t.scrollbarTop,n=t.x+t.scrollbarLeft;t.options.vertical&&i.chartY>o||!t.options.vertical&&i.chartX>n?t.updatePosition(t.from+s,t.to+s):t.updatePosition(t.from-s,t.to-s),l(t,"changed",{from:t.from,to:t.to,trigger:"scrollbar",DOMEvent:e})}},cursorToScrollbarPosition:function(t){var e=(e=this.options).minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-e),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-e)}},updatePosition:function(t,e){1<e&&(t=n(1-n(e-t)),e=1),0>t&&(e=n(e-t),t=0),this.from=t,this.to=e},update:function(t){this.destroy(),this.init(this.chart.renderer,d(!0,this.options,t),this.chart)},addEvents:function(){var t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,o=this.mouseDownHandler,n=this.mouseMoveHandler,r=this.mouseUpHandler;t=[[e[t[0]].element,"click",this.buttonToMinClick],[e[t[1]].element,"click",this.buttonToMaxClick],[this.track.element,"click",this.trackClick],[i,"mousedown",o],[i.ownerDocument,"mousemove",n],[i.ownerDocument,"mouseup",r]];c&&t.push([i,"touchstart",o],[i.ownerDocument,"touchmove",n],[i.ownerDocument,"touchend",r]),t.forEach(function(t){s.apply(null,t)}),this._events=t},removeEvents:function(){this._events.forEach(function(t){u.apply(null,t)}),this._events.length=0},destroy:function(){var t=this.chart.scroller;this.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(t){this[t]&&this[t].destroy&&(this[t]=this[t].destroy())},this),t&&this===t.scrollbar&&(t.scrollbar=null,h(t.scrollbarButtons))}},t.Scrollbar||(s(o,"afterInit",function(){var i=this;i.options&&i.options.scrollbar&&i.options.scrollbar.enabled&&(i.options.scrollbar.vertical=!i.horiz,i.options.startOnTick=i.options.endOnTick=!1,i.scrollbar=new e(i.chart.renderer,i.options.scrollbar,i.chart),s(i.scrollbar,"changed",function(e){var s,o=Math.min(p(i.options.min,i.min),i.min,i.dataMin),n=Math.max(p(i.options.max,i.max),i.max,i.dataMax)-o;i.horiz&&!i.reversed||!i.horiz&&i.reversed?(s=o+n*this.to,o+=n*this.from):(s=o+n*(1-this.from),o+=n*(1-this.to)),p(this.options.liveRedraw,t.svg&&!t.isTouchDevice&&!this.chart.isBoosting)||"mouseup"===e.DOMType||!a(e.DOMType)?i.setExtremes(o,s,!0,"mousemove"!==e.DOMType,e):this.setRange(this.from,this.to)}))}),s(o,"afterRender",function(){var t=Math.min(p(this.options.min,this.min),this.min,p(this.dataMin,this.min)),e=Math.max(p(this.options.max,this.max),this.max,p(this.dataMax,this.max)),i=this.scrollbar,s=this.titleOffset||0;i&&(this.horiz?(i.position(this.left,this.top+this.height+2+this.chart.scrollbarsOffsets[1]+(this.opposite?0:s+this.axisTitleMargin+this.offset),this.width,this.height),s=1):(i.position(this.left+this.width+2+this.chart.scrollbarsOffsets[0]+(this.opposite?s+this.axisTitleMargin+this.offset:0),this.top,this.width,this.height),s=0),(!this.opposite&&!this.horiz||this.opposite&&this.horiz)&&(this.chart.scrollbarsOffsets[s]+=this.scrollbar.size+this.scrollbar.options.margin),isNaN(t)||isNaN(e)||!a(this.min)||!a(this.max)?i.setRange(0,0):(s=(this.min-t)/(e-t),t=(this.max-t)/(e-t),this.horiz&&!this.reversed||!this.horiz&&this.reversed?i.setRange(s,t):i.setRange(1-t,1-s)))}),s(o,"afterGetOffset",function(){var t=this.horiz?2:1,e=this.scrollbar;e&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[t]+=e.size+e.options.margin)}),t.Scrollbar=e)}),e(i,"parts/Navigator.js",[i["parts/Globals.js"]],function(t){function e(t){this.init(t)}var i,s=t.addEvent,o=t.Axis,n=t.Chart,r=t.color,a=t.defaultOptions,h=t.defined,l=t.destroyObjectProperties,c=t.erase,d=t.extend,p=t.hasTouch,u=t.isArray,f=t.isNumber,g=t.isTouchDevice,m=t.merge,x=t.pick,v=t.removeEvent,y=t.Scrollbar,b=t.Series,M=function(t){var e=[].filter.call(arguments,f);if(e.length)return Math[t].apply(0,e)};i=void 0===t.seriesTypes.areaspline?"line":"areaspline",d(a,{navigator:{height:40,margin:25,maskInside:!0,handles:{width:7,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:r("#6685c2").setOpacity(.3).get(),outlineColor:"#cccccc",outlineWidth:1,series:{type:i,fillOpacity:.05,lineWidth:1,compare:null,dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,smoothed:!0,units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},pointRange:0,threshold:null},xAxis:{overscroll:0,className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#999999"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:null},tickLength:0,tickWidth:0}}}),t.Renderer.prototype.symbols["navigator-handle"]=function(t,e,i,s,o){return t=o.width/2,e=Math.round(t/3)+.5,["M",-t-1,.5,"L",t,.5,"L",t,(o=o.height)+.5,"L",-t-1,o+.5,"L",-t-1,.5,"M",-e,4,"L",-e,o-3,"M",e-1,4,"L",e-1,o-3]},o.prototype.toFixedRange=function(t,e,i,s){var o=this.chart&&this.chart.fixedRange;return t=x(i,this.translate(t,!0,!this.horiz)),e=x(s,this.translate(e,!0,!this.horiz)),.7<(i=o&&(e-t)/o)&&1.3>i&&(s?t=e-o:e=t+o),f(t)&&f(e)||(t=e=void 0),{min:t,max:e}},e.prototype={drawHandle:function(t,e,i,s){var o=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-o)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-o/2-1)})},drawOutline:function(t,e,i,s){var o=this.navigatorOptions.maskInside,n=(r=this.outline.strokeWidth())/2,r=r%2/2,a=this.outlineHeight,h=this.scrollbarHeight,l=this.size,c=this.left-h,d=this.top;i?t=["M",(c-=n)+a,d-h-r,"L",c+a,i=d+e+r,"L",c,i,"L",c,e=d+t+r,"L",c+a,e,"L",c+a,d+l+h].concat(o?["M",c+a,i-n,"L",c+a,e+n]:[]):t=["M",c,d+=n,"L",t+=c+h-r,d,"L",t,d+a,"L",e+=c+h-r,d+a,"L",e,d,"L",c+l+2*h,d].concat(o?["M",t-n,d,"L",e+n,d]:[]),this.outline[s]({d:t})},drawMasks:function(t,e,i,s){var o,n,r,a,h=this.left,l=this.top,c=this.height;i?(r=[h,h,h],a=[l,l+t,l+e],n=[c,c,c],o=[t,e-t,this.size-e]):(r=[h,h+t,h+e],a=[l,l,l],n=[t,e-t,this.size-e],o=[c,c,c]),this.shades.forEach(function(t,e){t[s]({x:r[e],y:a[e],width:n[e],height:o[e]})})},renderElements:function(){var t,e=this,i=e.navigatorOptions,s=i.maskInside,o=e.chart,n=o.renderer,r={cursor:o.inverted?"ns-resize":"ew-resize"};e.navigatorGroup=t=n.g("navigator").attr({zIndex:8,visibility:"hidden"}).add(),[!s,s,!s].forEach(function(s,a){e.shades[a]=n.rect().addClass("highcharts-navigator-mask"+(1===a?"-inside":"-outside")).add(t),o.styledMode||e.shades[a].attr({fill:s?i.maskFill:"rgba(0,0,0,0)"}).css(1===a&&r)}),e.outline=n.path().addClass("highcharts-navigator-outline").add(t),o.styledMode||e.outline.attr({"stroke-width":i.outlineWidth,stroke:i.outlineColor}),i.handles.enabled&&[0,1].forEach(function(s){if(i.handles.inverted=o.inverted,e.handles[s]=n.symbol(i.handles.symbols[s],-i.handles.width/2-1,0,i.handles.width,i.handles.height,i.handles),e.handles[s].attr({zIndex:7-s}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][s]).add(t),!o.styledMode){var a=i.handles;e.handles[s].attr({fill:a.backgroundColor,stroke:a.borderColor,"stroke-width":a.lineWidth}).css(r)}})},update:function(t){(this.series||[]).forEach(function(t){t.baseSeries&&delete t.baseSeries.navigatorSeries}),this.destroy(),m(!0,this.chart.options.navigator,this.options,t),this.init(this.chart)},render:function(e,i,s,o){var n,r,a,l=this.chart,c=this.scrollbarHeight,d=this.xAxis;n=d.fake?l.xAxis[0]:d;var p,u=this.navigatorEnabled,g=this.rendered;r=l.inverted;var m,v=l.xAxis[0].minRange,y=l.xAxis[0].options.maxRange;if(!this.hasDragged||h(s)){if(!f(e)||!f(i)){if(!g)return;s=0,o=x(d.width,n.width)}this.left=x(d.left,l.plotLeft+c+(r?l.plotWidth:0)),this.size=p=a=x(d.len,(r?l.plotHeight:l.plotWidth)-2*c),l=r?c:a+2*c,s=x(s,d.toPixels(e,!0)),o=x(o,d.toPixels(i,!0)),f(s)&&Infinity!==Math.abs(s)||(s=0,o=l),e=d.toValue(s,!0),i=d.toValue(o,!0),(m=Math.abs(t.correctFloat(i-e)))<v?this.grabbedLeft?s=d.toPixels(i-v,!0):this.grabbedRight&&(o=d.toPixels(e+v,!0)):h(y)&&m>y&&(this.grabbedLeft?s=d.toPixels(i-y,!0):this.grabbedRight&&(o=d.toPixels(e+y,!0))),this.zoomedMax=Math.min(Math.max(s,o,0),p),this.zoomedMin=Math.min(Math.max(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(s,o),0),p),this.range=this.zoomedMax-this.zoomedMin,p=Math.round(this.zoomedMax),s=Math.round(this.zoomedMin),u&&(this.navigatorGroup.attr({visibility:"visible"}),g=g&&!this.hasDragged?"animate":"attr",this.drawMasks(s,p,r,g),this.drawOutline(s,p,r,g),this.navigatorOptions.handles.enabled&&(this.drawHandle(s,0,r,g),this.drawHandle(p,1,r,g))),this.scrollbar&&(r?(r=this.top-c,n=this.left-c+(u||!n.opposite?0:(n.titleOffset||0)+n.axisTitleMargin),c=a+2*c):(r=this.top+(u?this.height:-c),n=this.left-c),this.scrollbar.position(n,r,l,c),this.scrollbar.setRange(this.zoomedMin/(a||1),this.zoomedMax/(a||1))),this.rendered=!0}},addMouseEvents:function(){var t,e,i=this,o=i.chart,n=o.container,r=[];i.mouseMoveHandler=t=function(t){i.onMouseMove(t)},i.mouseUpHandler=e=function(t){i.onMouseUp(t)},(r=i.getPartsEvents("mousedown")).push(s(n,"mousemove",t),s(n.ownerDocument,"mouseup",e)),p&&(r.push(s(n,"touchmove",t),s(n.ownerDocument,"touchend",e)),r.concat(i.getPartsEvents("touchstart"))),i.eventsToUnbind=r,i.series&&i.series[0]&&r.push(s(i.series[0].xAxis,"foundExtremes",function(){o.navigator.modifyNavigatorAxisExtremes()}))},getPartsEvents:function(t){var e=this,i=[];return["shades","handles"].forEach(function(o){e[o].forEach(function(n,r){i.push(s(n.element,t,function(t){e[o+"Mousedown"](t,r)}))})}),i},shadesMousedown:function(t,e){t=this.chart.pointer.normalize(t);var i,s,o=this.chart,n=this.xAxis,r=this.zoomedMin,a=this.left,l=this.size,c=this.range,d=t.chartX;o.inverted&&(d=t.chartY,a=this.top),1===e?(this.grabbedCenter=d,this.fixedWidth=c,this.dragOffset=d-r):(t=d-a-c/2,0===e?t=Math.max(0,t):2===e&&t+c>=l&&(t=l-c,this.reversedExtremes?(t-=c,s=this.getUnionExtremes().dataMin):i=this.getUnionExtremes().dataMax),t!==r&&(this.fixedWidth=c,e=n.toFixedRange(t,t+c,s,i),h(e.min)&&o.xAxis[0].setExtremes(Math.min(e.min,e.max),Math.max(e.min,e.max),!0,null,{trigger:"navigator"})))},handlesMousedown:function(t,e){this.chart.pointer.normalize(t);var i=(t=this.chart).xAxis[0],s=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=s?i.min:i.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=s?i.max:i.min),t.fixedRange=null},onMouseMove:function(e){var i=this,s=i.chart,o=i.left,n=i.navigatorSize,r=i.range,a=i.dragOffset,h=s.inverted;e.touches&&0===e.touches[0].pageX||(s=(e=s.pointer.normalize(e)).chartX,h&&(o=i.top,s=e.chartY),i.grabbedLeft?(i.hasDragged=!0,i.render(0,0,s-o,i.otherHandlePos)):i.grabbedRight?(i.hasDragged=!0,i.render(0,0,i.otherHandlePos,s-o)):i.grabbedCenter&&(i.hasDragged=!0,s<a?s=a:s>n+a-r&&(s=n+a-r),i.render(0,0,s-a,s-a+r)),i.hasDragged&&i.scrollbar&&x(i.scrollbar.options.liveRedraw,t.svg&&!g&&!this.chart.isBoosting)&&(e.DOMType=e.type,setTimeout(function(){i.onMouseUp(e)},0)))},onMouseUp:function(t){var e,i,s=this.chart,o=this.xAxis,n=this.scrollbar,r=t.DOMEvent||t;(!this.hasDragged||n&&n.hasDragged)&&"scrollbar"!==t.trigger||(n=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?e=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(i=this.fixedExtreme),this.zoomedMax===this.size&&(i=this.reversedExtremes?n.dataMin:n.dataMax),0===this.zoomedMin&&(e=this.reversedExtremes?n.dataMax:n.dataMin),o=o.toFixedRange(this.zoomedMin,this.zoomedMax,e,i),h(o.min)&&s.xAxis[0].setExtremes(Math.min(o.min,o.max),Math.max(o.min,o.max),!0,!this.hasDragged&&null,{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:r})),"mousemove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null)},removeEvents:function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()},removeBaseSeriesEvents:function(){var t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){v(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&v(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},init:function(t){var e=(r=t.options).navigator,i=e.enabled,n=(l=r.scrollbar).enabled,r=i?e.height:0,a=n?l.height:0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=r,this.scrollbarHeight=a,this.scrollbarEnabled=n,this.navigatorEnabled=i,this.navigatorOptions=e,this.scrollbarOptions=l,this.outlineHeight=r+a,this.opposite=x(e.opposite,!i&&t.inverted);var h=this,l=(i=h.baseSeries,t.xAxis.length),c=(n=t.yAxis.length,i&&i[0]&&i[0].xAxis||t.xAxis[0]||{options:{}});t.isDirtyBox=!0,h.navigatorEnabled?(h.xAxis=new o(t,m({breaks:c.options.breaks,ordinal:c.options.ordinal},e.xAxis,{id:"navigator-x-axis",yAxis:"navigator-y-axis",isX:!0,type:"datetime",index:l,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:0,maxPadding:0,zoomEnabled:!1},t.inverted?{offsets:[a,0,-a,0],width:r}:{offsets:[0,-a,0,a],height:r})),h.yAxis=new o(t,m(e.yAxis,{id:"navigator-y-axis",alignTicks:!1,offset:0,index:n,isInternal:!0,zoomEnabled:!1},t.inverted?{width:r}:{height:r})),i||e.series.data?h.updateNavigatorSeries(!1):0===t.series.length&&(h.unbindRedraw=s(t,"beforeRedraw",function(){0<t.series.length&&!h.series&&(h.setBaseSeries(),h.unbindRedraw())})),h.reversedExtremes=t.inverted&&!h.xAxis.reversed||!t.inverted&&h.xAxis.reversed,h.renderElements(),h.addMouseEvents()):h.xAxis={translate:function(e,i){var s=(r=t.xAxis[0]).getExtremes(),o=r.len-2*a,n=M("min",r.options.min,s.dataMin),r=M("max",r.options.max,s.dataMax)-n;return i?e*r/o+n:o*(e-n)/r},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)},toFixedRange:o.prototype.toFixedRange,fake:!0},t.options.scrollbar.enabled&&(t.scrollbar=h.scrollbar=new y(t.renderer,m(t.options.scrollbar,{margin:h.navigatorEnabled?0:10,vertical:t.inverted}),t),s(h.scrollbar,"changed",function(e){var i=(s=h.size)*this.to,s=s*this.from;h.hasDragged=h.scrollbar.hasDragged,h.render(0,0,s,i),(t.options.scrollbar.liveRedraw||"mousemove"!==e.DOMType&&"touchmove"!==e.DOMType)&&setTimeout(function(){h.onMouseUp(e)})})),h.addBaseSeriesEvents(),h.addChartEvents()},getUnionExtremes:function(t){var e,i=this.chart.xAxis[0],s=this.xAxis,o=s.options,n=i.options;return t&&null===i.dataMin||(e={dataMin:x(o&&o.min,M("min",n.min,i.dataMin,s.dataMin,s.min)),dataMax:x(o&&o.max,M("max",n.max,i.dataMax,s.dataMax,s.max))}),e},setBaseSeries:function(e,i){var s=this.chart,o=this.baseSeries=[];e=e||s.options&&s.options.navigator.baseSeries||(s.series.length?t.find(s.series,function(t){return!t.options.isInternal}).index:0),(s.series||[]).forEach(function(t,i){t.options.isInternal||!t.options.showInNavigator&&(i!==e&&t.options.id!==e||!1===t.options.showInNavigator)||o.push(t)}),this.xAxis&&!this.xAxis.fake&&this.updateNavigatorSeries(!0,i)},updateNavigatorSeries:function(e,i){var s,o,n,r=this,h=r.chart,l=r.baseSeries,c=r.navigatorOptions.series,p={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:"navigator-x-axis",yAxis:"navigator-y-axis",showInLegend:!1,stacking:!1,isInternal:!0,states:{inactive:{opacity:1}}},f=r.series=(r.series||[]).filter(function(t){var e=t.baseSeries;return!(0>l.indexOf(e))||(e&&(v(e,"updatedData",r.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)});l&&l.length&&l.forEach(function(t){var e=t.navigatorSeries,g=d({color:t.color,visible:t.visible},u(c)?a.navigator.series:c);e&&!1===r.navigatorOptions.adaptToUpdatedData||(p.name="Navigator "+l.length,s=t.options||{},n=s.navigatorOptions||{},o=m(s,p,g,n),g=n.data||g.data,r.hasNavigatorData=r.hasNavigatorData||!!g,o.data=g||s.data&&s.data.slice(0),e&&e.options?e.update(o,i):(t.navigatorSeries=h.initSeries(o),t.navigatorSeries.baseSeries=t,f.push(t.navigatorSeries)))}),(!c.data||l&&l.length)&&!u(c)||(r.hasNavigatorData=!1,(c=t.splat(c)).forEach(function(t,e){p.name="Navigator "+(f.length+1),(o=m(a.navigator.series,{color:h.series[e]&&!h.series[e].options.isInternal&&h.series[e].color||h.options.colors[e]||h.options.colors[0]},p,t)).data=t.data,o.data&&(r.hasNavigatorData=!0,f.push(h.initSeries(o)))})),e&&this.addBaseSeriesEvents()},addBaseSeriesEvents:function(){var t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&s(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes),e.forEach(function(e){s(e,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)}),s(e,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)}),!1!==this.navigatorOptions.adaptToUpdatedData&&e.xAxis&&s(e,"updatedData",this.updatedDataHandler),s(e,"remove",function(){this.navigatorSeries&&(c(t.series,this.navigatorSeries),h(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)})},this)},getBaseSeriesMin:function(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.xData?e.xData[0]:t)},t)},modifyNavigatorAxisExtremes:function(){var t,e=this.xAxis;e.getExtremes&&(!(t=this.getUnionExtremes(!0))||t.dataMin===e.min&&t.dataMax===e.max||(e.min=t.dataMin,e.max=t.dataMax))},modifyBaseAxisExtremes:function(){var t,e,i=this.chart.navigator,s=(n=this.getExtremes()).dataMin,o=n.dataMax,n=n.max-n.min,r=i.stickToMin,a=i.stickToMax,h=x(this.options.overscroll,0),l=i.series&&i.series[0],c=!!this.setExtremes;this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger||(r&&(t=(e=s)+n),a&&(t=o+h,r||(e=Math.max(t-n,i.getBaseSeriesMin(l&&l.xData?l.xData[0]:-Number.MAX_VALUE)))),c&&(r||a)&&f(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null},updatedDataHandler:function(){var t=this.chart.navigator,e=this.navigatorSeries,i=t.getBaseSeriesMin(this.xData[0]);t.stickToMax=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size),t.stickToMin=f(this.xAxis.min)&&this.xAxis.min<=i&&(!this.chart.fixedRange||!t.stickToMax),e&&!t.hasNavigatorData&&(e.options.pointStart=this.xData[0],e.setData(this.options.data,!1,null,!1))},addChartEvents:function(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(s(this.chart,"redraw",function(){var t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||t.scrollbar&&this.xAxis[0]);e&&t.render(e.min,e.max)}),s(this.chart,"getMargins",function(){var t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.outlineHeight:0)+t.navigatorOptions.margin}))},destroy:function(){this.removeEvents(),this.xAxis&&(c(this.chart.xAxis,this.xAxis),c(this.chart.axes,this.xAxis)),this.yAxis&&(c(this.chart.yAxis,this.yAxis),c(this.chart.axes,this.yAxis)),(this.series||[]).forEach(function(t){t.destroy&&t.destroy()}),"series xAxis yAxis shades outline scrollbarTrack scrollbarRifles scrollbarGroup scrollbar navigatorGroup rendered".split(" ").forEach(function(t){this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null},this),[this.handles].forEach(function(t){l(t)},this)}},t.Navigator||(t.Navigator=e,s(o,"zoom",function(t){var e=(o=this.chart.options).chart.zoomType,i=o.chart.pinchType,s=o.navigator,o=o.rangeSelector;this.isXAxis&&(s&&s.enabled||o&&o.enabled)&&("y"===e?t.zoomed=!1:(!g&&"xy"===e||g&&"xy"===i)&&this.options.range&&(e=this.previousZoom,h(t.newMin)?this.previousZoom=[this.min,this.max]:e&&(t.newMin=e[0],t.newMax=e[1],delete this.previousZoom))),void 0!==t.zoomed&&t.preventDefault()}),s(n,"beforeShowResetZoom",function(){var t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!g&&"x"===t.chart.zoomType||g&&"x"===t.chart.pinchType))return!1}),s(n,"beforeRender",function(){var t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new e(this))}),s(n,"afterSetChartSize",function(){var t,e,i,s,o=this.legend,n=this.navigator;n&&(e=o&&o.options,i=n.xAxis,s=n.yAxis,t=n.scrollbarHeight,this.inverted?(n.left=n.opposite?this.chartWidth-t-n.height:this.spacing[3]+t,n.top=this.plotTop+t):(n.left=this.plotLeft+t,n.top=n.navigatorOptions.top||this.chartHeight-n.height-t-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(e&&"bottom"===e.verticalAlign&&e.enabled&&!e.floating?o.legendHeight+x(e.margin,10):0)),i&&s&&(this.inverted?i.options.left=s.options.left=n.left:i.options.top=s.options.top=n.top,i.setAxisSize(),s.setAxisSize()))}),s(n,"update",function(t){var e=t.options.navigator||{},i=t.options.scrollbar||{};this.navigator||this.scroller||!e.enabled&&!i.enabled||(m(!0,this.options.navigator,e),m(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}),s(n,"afterUpdate",function(t){this.navigator||this.scroller||!this.options.navigator.enabled&&!this.options.scrollbar.enabled||(this.scroller=this.navigator=new e(this),x(t.redraw,!0)&&this.redraw(t.animation))}),s(n,"afterAddSeries",function(){this.navigator&&this.navigator.setBaseSeries(null,!1)}),s(b,"afterUpdate",function(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}),n.prototype.callbacks.push(function(t){var e=t.navigator;e&&t.xAxis[0]&&(t=t.xAxis[0].getExtremes(),e.render(t.min,t.max))}))}),e(i,"parts/OrdinalAxis.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.Axis,s=t.Chart,o=t.css,n=t.defined,r=t.extend,a=t.noop,h=t.pick,l=t.timeUnits;e(t.Series,"updatedData",function(){var t=this.xAxis;t&&t.options.ordinal&&delete t.ordinalIndex}),i.prototype.getTimeTicks=function(t,e,i,s,o,r,a){var h,c,d,p,u,f=0,g={},m=[],x=-Number.MAX_VALUE,v=this.options.tickPixelInterval,y=this.chart.time,b=[];if(!this.options.ordinal&&!this.options.breaks||!o||3>o.length||void 0===e)return y.getTimeTicks.apply(y,arguments);for(p=o.length,h=0;h<p;h++){if(u=h&&o[h-1]>i,o[h]<e&&(f=h),h===p-1||o[h+1]-o[h]>5*r||u){if(o[h]>x){for(c=y.getTimeTicks(t,o[f],o[h],s);c.length&&c[0]<=x;)c.shift();c.length&&(x=c[c.length-1]),b.push(m.length),m=m.concat(c)}f=h+1}if(u)break}if(c=c.info,a&&c.unitRange<=l.hour){for(h=m.length-1,f=1;f<h;f++)y.dateFormat("%d",m[f])!==y.dateFormat("%d",m[f-1])&&(g[m[f]]="day",d=!0);d&&(g[m[0]]="day"),c.higherRanks=g}if(c.segmentStarts=b,m.info=c,a&&n(v)){f=b=m.length,d=[];var M;for(y=[];f--;)h=this.translate(m[f]),M&&(y[f]=M-h),d[f]=M=h;for(y.sort(),(y=y[Math.floor(y.length/2)])<.6*v&&(y=null),f=m[b-1]>i?b-1:b,M=void 0;f--;)h=d[f],b=Math.abs(M-h),M&&b<.8*v&&(null===y||b<.8*y)?(g[m[f]]&&!g[m[f+1]]?(b=f+1,M=h):b=f,m.splice(b,1)):M=h}return m},r(i.prototype,{beforeSetTickPositions:function(){var t,e,i,s,o,n=[],r=!1,a=(d=this.getExtremes()).min,l=d.max,c=this.isXAxis&&!!this.options.breaks,d=this.options.ordinal,p=Number.MAX_VALUE,u=this.chart.options.chart.ignoreHiddenSeries;if(d||c){if(this.series.forEach(function(i,s){if(e=[],!(u&&!1===i.visible||!1===i.takeOrdinalPosition&&!c)&&(n=n.concat(i.processedXData),t=n.length,n.sort(function(t,e){return t-e}),p=Math.min(p,h(i.closestPointRange,p)),t)){for(s=0;s<t-1;)n[s]!==n[s+1]&&e.push(n[s+1]),s++;e[0]!==n[0]&&e.unshift(n[0]),n=e}i.isSeriesBoosting&&(o=!0)}),o&&(n.length=0),2<(t=n.length)){for(i=n[1]-n[0],s=t-1;s--&&!r;)n[s+1]-n[s]!==i&&(r=!0);!this.options.keepOrdinalPadding&&(n[0]-a>i||l-n[n.length-1]>i)&&(r=!0)}else this.options.overscroll&&(2===t?p=n[1]-n[0]:1===t?(p=this.options.overscroll,n=[n[0],n[0]+p]):p=this.overscrollPointsRange);r?(this.options.overscroll&&(this.overscrollPointsRange=p,n=n.concat(this.getOverscrollPositions())),this.ordinalPositions=n,i=this.ordinal2lin(Math.max(a,n[0]),!0),s=Math.max(this.ordinal2lin(Math.min(l,n[n.length-1]),!0),1),this.ordinalSlope=l=(l-a)/(s-i),this.ordinalOffset=a-i*l):(this.overscrollPointsRange=h(this.closestPointRange,this.overscrollPointsRange),this.ordinalPositions=this.ordinalSlope=this.ordinalOffset=void 0)}this.isOrdinal=d&&r,this.groupIntervalFactor=null},val2lin:function(t,e){var i=this.ordinalPositions;if(i){var s,o,n=i.length;for(s=n;s--;)if(i[s]===t){o=s;break}for(s=n-1;s--;)if(t>i[s]||0===s){o=s+(t=(t-i[s])/(i[s+1]-i[s]));break}e=e?o:this.ordinalSlope*(o||0)+this.ordinalOffset}else e=t;return e},lin2val:function(t,e){var i=this.ordinalPositions;if(i){var s,o=this.ordinalSlope,n=this.ordinalOffset,r=i.length-1;if(e)0>t?t=i[0]:t>r?t=i[r]:s=t-(r=Math.floor(t));else for(;r--;)if(t>=(e=o*r+n)){s=(t-e)/((o=o*(r+1)+n)-e);break}return void 0!==s&&void 0!==i[r]?i[r]+(s?s*(i[r+1]-i[r]):0):t}return t},getExtendedPositions:function(){var t,e,s=this,o=s.chart,n=s.series[0].currentDataGrouping,r=s.ordinalIndex,h=n?n.count+n.unitName:"raw",l=s.options.overscroll,c=s.getExtremes();return r||(r=s.ordinalIndex={}),r[h]||(t={series:[],chart:o,getExtremes:function(){return{min:c.dataMin,max:c.dataMax+l}},options:{ordinal:!0},val2lin:i.prototype.val2lin,ordinal2lin:i.prototype.ordinal2lin},s.series.forEach(function(i){(e={xAxis:t,xData:i.xData.slice(),chart:o,destroyGroupedData:a}).xData=e.xData.concat(s.getOverscrollPositions()),e.options={dataGrouping:n?{enabled:!0,forced:!0,approximation:"open",units:[[n.unitName,[n.count]]]}:{enabled:!1}},i.processData.apply(e),t.series.push(e)}),s.beforeSetTickPositions.apply(t),r[h]=t.ordinalPositions),r[h]},getOverscrollPositions:function(){var e=this.options.overscroll,i=this.overscrollPointsRange,s=[],o=this.dataMax;if(t.defined(i))for(s.push(o);o<=this.dataMax+e;)o+=i,s.push(o);return s},getGroupIntervalFactor:function(t,e,i){var s,o=(i=i.processedXData).length,n=[];if(!(s=this.groupIntervalFactor)){for(s=0;s<o-1;s++)n[s]=i[s+1]-i[s];n.sort(function(t,e){return t-e}),n=n[Math.floor(o/2)],t=Math.max(t,i[0]),e=Math.min(e,i[o-1]),this.groupIntervalFactor=s=o*n/(e-t)}return s},postProcessTickInterval:function(t){var e=this.ordinalSlope;return e?this.options.breaks?this.closestPointRange||t:t/(e/this.closestPointRange):t}}),i.prototype.ordinal2lin=i.prototype.val2lin,e(s,"pan",function(t){var e=this.xAxis[0],i=e.options.overscroll,s=t.originalEvent.chartX,n=!1;if(e.options.ordinal&&e.series.length){var r,a=this.mouseDownX,h=e.getExtremes(),l=h.dataMax,c=h.min,d=h.max,p=this.hoverPoints,u=e.closestPointRange||e.overscrollPointsRange,f=(a=(a-s)/(e.translationSlope*(e.ordinalSlope||u)),{ordinalPositions:e.getExtendedPositions()}),g=(u=e.lin2val,e.val2lin);f.ordinalPositions?1<Math.abs(a)&&(p&&p.forEach(function(t){t.setState()}),0>a?(p=f,r=e.ordinalPositions?e:f):(p=e.ordinalPositions?e:f,r=f),l>(f=r.ordinalPositions)[f.length-1]&&f.push(l),this.fixedRange=d-c,(a=e.toFixedRange(null,null,u.apply(p,[g.apply(p,[c,!0])+a,!0]),u.apply(r,[g.apply(r,[d,!0])+a,!0]))).min>=Math.min(h.dataMin,c)&&a.max<=Math.max(l,d)+i&&e.setExtremes(a.min,a.max,!0,!1,{trigger:"pan"}),this.mouseDownX=s,o(this.container,{cursor:"move"})):n=!0}else n=!0;n?i&&(e.max=e.dataMax+i):t.preventDefault()}),e(i,"foundExtremes",function(){this.isXAxis&&n(this.options.overscroll)&&this.max===this.dataMax&&(!this.chart.mouseIsDown||this.isInternal)&&(!this.eventArgs||this.eventArgs&&"navigator"!==this.eventArgs.trigger)&&(this.max+=this.options.overscroll,!this.isInternal&&n(this.userMin)&&(this.min+=this.options.overscroll))})}),
e(i,"modules/broken-axis.src.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.pick,s=t.extend,o=t.isArray,n=t.find,r=t.fireEvent,a=t.Axis,h=t.Series,l=function(t,e){return n(e,function(e){return e.from<t&&t<e.to})};s(a.prototype,{isInBreak:function(t,e){var i=t.repeat||Infinity,s=t.from,o=t.to-t.from;return e=e>=s?(e-s)%i:i-(s-e)%i,t.inclusive?e<=o:e<o&&0!==e},isInAnyBreak:function(t,e){var s,o,n,r=this.options.breaks,a=r&&r.length;if(a){for(;a--;)this.isInBreak(r[a],t)&&(s=!0,o||(o=i(r[a].showPoints,!this.isXAxis)));n=s&&e?s&&!o:s}return n}}),e(a,"afterInit",function(){"function"==typeof this.setBreaks&&this.setBreaks(this.options.breaks,!1)}),e(a,"afterSetTickPositions",function(){if(this.isBroken){var t,e=this.tickPositions,i=this.tickPositions.info,s=[];for(t=0;t<e.length;t++)this.isInAnyBreak(e[t])||s.push(e[t]);this.tickPositions=s,this.tickPositions.info=i}}),e(a,"afterSetOptions",function(){this.isBroken&&(this.options.ordinal=!1)}),a.prototype.setBreaks=function(t,e){function s(t){var e,i,s=t;for(i=0;i<h.breakArray.length;i++)if((e=h.breakArray[i]).to<=t)s-=e.len;else{if(e.from>=t)break;if(h.isInBreak(e,t)){s-=t-e.from;break}}return s}function n(t){var e,i;for(i=0;i<h.breakArray.length&&!((e=h.breakArray[i]).from>=t);i++)e.to<t?t+=e.len:h.isInBreak(e,t)&&(t+=e.len);return t}var h=this,c=o(t)&&!!t.length;h.isDirty=h.isBroken!==c,h.isBroken=c,h.options.breaks=h.userOptions.breaks=t,h.forceRedraw=!0,c||h.val2lin!==s||(delete h.val2lin,delete h.lin2val),c&&(h.userOptions.ordinal=!1,h.val2lin=s,h.lin2val=n,h.setExtremes=function(t,e,i,s,o){if(this.isBroken){for(var n,r=this.options.breaks;n=l(t,r);)t=n.to;for(;n=l(e,r);)e=n.from;e<t&&(e=t)}a.prototype.setExtremes.call(this,t,e,i,s,o)},h.setAxisTranslation=function(t){if(a.prototype.setAxisTranslation.call(this,t),this.unitLength=null,this.isBroken){t=h.options.breaks;var e,s,o,n,l=[],c=[],d=0,p=h.userMin||h.min,u=h.userMax||h.max,f=i(h.pointRangePadding,0);t.forEach(function(t){s=t.repeat||Infinity,h.isInBreak(t,p)&&(p+=t.to%s-p%s),h.isInBreak(t,u)&&(u-=u%s-t.from%s)}),t.forEach(function(t){for(o=t.from,s=t.repeat||Infinity;o-s>p;)o-=s;for(;o<p;)o+=s;for(n=o;n<u;n+=s)l.push({value:n,move:"in"}),l.push({value:n+(t.to-t.from),move:"out",size:t.breakSize})}),l.sort(function(t,e){return t.value===e.value?("in"===t.move?0:1)-("in"===e.move?0:1):t.value-e.value}),e=0,o=p,l.forEach(function(t){1===(e+="in"===t.move?1:-1)&&"in"===t.move&&(o=t.value),0===e&&(c.push({from:o,to:t.value,len:t.value-o-(t.size||0)}),d+=t.value-o-(t.size||0))}),h.breakArray=c,h.unitLength=u-p-d+f,r(h,"afterBreaks"),h.staticScale?h.transA=h.staticScale:h.unitLength&&(h.transA*=(u-h.min+f)/h.unitLength),f&&(h.minPixelPadding=h.transA*h.minPointOffset),h.min=p,h.max=u}}),i(e,!0)&&this.chart.redraw()},e(h,"afterGeneratePoints",function(){var t,e=this.xAxis,i=this.yAxis,s=this.points,o=s.length,n=this.options.connectNulls;if(e&&i&&(e.options.breaks||i.options.breaks))for(;o--;)null===(t=s[o]).y&&!1===n||!e.isInAnyBreak(t.x,!0)&&!i.isInAnyBreak(t.y,!0)||(s.splice(o,1),this.data[o]&&this.data[o].destroyElements())}),e(h,"afterRender",function(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,i(this.pointArrayMap,["y"]))}),t.Series.prototype.drawBreaks=function(t,e){var s,o,n,a,h=this,l=h.points;t&&e.forEach(function(e){s=t.breakArray||[],o=t.isXAxis?t.min:i(h.options.threshold,t.min),l.forEach(function(h){a=i(h["stack"+e.toUpperCase()],h[e]),s.forEach(function(e){n=!1,o<e.from&&a>e.to||o>e.from&&a<e.from?n="pointBreak":(o<e.from&&a>e.from&&a<e.to||o>e.from&&a>e.to&&a<e.from)&&(n="pointInBreak"),n&&r(t,n,{point:h,brk:e})})})})},t.Series.prototype.gappedPath=function(){var e=(i=this.currentDataGrouping)&&i.gapSize,i=this.options.gapSize,s=this.points.slice(),o=s.length-1,n=this.yAxis;if(i&&0<o)for("value"!==this.options.gapUnit&&(i*=this.closestPointRange),e&&e>i&&(i=e);o--;)s[o+1].x-s[o].x>i&&(e=(s[o].x+s[o+1].x)/2,s.splice(o+1,0,{isNull:!0,x:e}),this.options.stacking&&((e=n.stacks[this.stackKey][e]=new t.StackItem(n,n.options.stackLabels,!1,e,this.stack)).total=0));return this.getGraphPath(s)}}),e(i,"masters/modules/broken-axis.src.js",[],function(){}),e(i,"parts/DataGrouping.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.arrayMax,s=t.arrayMin,o=t.Axis,n=t.defaultPlotOptions,r=t.defined,a=t.extend,h=t.format,l=t.isNumber,c=t.merge,d=t.pick,p=t.Point,u=t.Series,f=t.Tooltip,g=t.approximations={sum:function(t){var e,i=t.length;if(!i&&t.hasNulls)e=null;else if(i)for(e=0;i--;)e+=t[i];return e},average:function(t){var e=t.length;return t=g.sum(t),l(t)&&e&&(t/=e),t},averages:function(){var t=[];return[].forEach.call(arguments,function(e){t.push(g.average(e))}),void 0===t[0]?void 0:t},open:function(t){return t.length?t[0]:t.hasNulls?null:void 0},high:function(t){return t.length?i(t):t.hasNulls?null:void 0},low:function(t){return t.length?s(t):t.hasNulls?null:void 0},close:function(t){return t.length?t[t.length-1]:t.hasNulls?null:void 0},ohlc:function(t,e,i,s){if(t=g.open(t),e=g.high(e),i=g.low(i),s=g.close(s),l(t)||l(e)||l(i)||l(s))return[t,e,i,s]},range:function(t,e){return t=g.low(t),e=g.high(e),l(t)||l(e)?[t,e]:null===t&&null===e?null:void 0}},m=function(t,e,i,s){var o,n,a,h,d=this,p=d.data,u=d.options&&d.options.data,f=[],m=[],x=[],v=t.length,y=!!e,b=[],M=d.pointArrayMap,k=M&&M.length,w=["x"].concat(M||["y"]),S=0,A=0;for(s="function"==typeof s?s:g[s]?g[s]:g[d.getDGApproximation&&d.getDGApproximation()||"average"],k?M.forEach(function(){b.push([])}):b.push([]),a=k||1,h=0;h<=v&&!(t[h]>=i[0]);h++);for(;h<=v;h++){for(;void 0!==i[S+1]&&t[h]>=i[S+1]||h===v;){for(o=i[S],d.dataGroupInfo={start:d.cropStart+A,length:b[0].length},n=s.apply(d,b),d.pointClass&&!r(d.dataGroupInfo.options)&&(d.dataGroupInfo.options=c(d.pointClass.prototype.optionsToObject.call({series:d},d.options.data[d.cropStart+A])),w.forEach(function(t){delete d.dataGroupInfo.options[t]})),void 0!==n&&(f.push(o),m.push(n),x.push(d.dataGroupInfo)),A=h,o=0;o<a;o++)b[o].length=0,b[o].hasNulls=!1;if(S+=1,h===v)break}if(h===v)break;var T;if(M)for(o=d.cropStart+h,n=p&&p[o]||d.pointClass.prototype.applyOptions.apply({series:d},[u[o]]),o=0;o<k;o++)T=n[M[o]],l(T)?b[o].push(T):null===T&&(b[o].hasNulls=!0);else o=y?e[h]:null,l(o)?b[0].push(o):null===o&&(b[0].hasNulls=!0)}return{groupedXData:f,groupedYData:m,groupMap:x}},x={approximations:g,groupData:m},v=u.prototype,y=v.processData,b=v.generatePoints,M={groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%A, %b %e, %H:%M:%S.%L","%A, %b %e, %H:%M:%S.%L","-%H:%M:%S.%L"],second:["%A, %b %e, %H:%M:%S","%A, %b %e, %H:%M:%S","-%H:%M:%S"],minute:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],hour:["%A, %b %e, %H:%M","%A, %b %e, %H:%M","-%H:%M"],day:["%A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],week:["Week from %A, %b %e, %Y","%A, %b %e","-%A, %b %e, %Y"],month:["%B %Y","%B","-%B %Y"],year:["%Y","%Y","-%Y"]}},k={line:{},spline:{},area:{},areaspline:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5}},w=t.defaultDataGroupingUnits=[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]];return v.getDGApproximation=function(){return t.seriesTypes.arearange&&this instanceof t.seriesTypes.arearange?"range":t.seriesTypes.ohlc&&this instanceof t.seriesTypes.ohlc?"ohlc":t.seriesTypes.column&&this instanceof t.seriesTypes.column?"sum":"average"},v.groupData=m,v.processData=function(){var t,e,i=this.chart,s=this.options.dataGrouping,o=!1!==this.allowDG&&s&&d(s.enabled,i.options.isStock),n=this.visible||!i.options.chart.ignoreHiddenSeries,a=this.currentDataGrouping,h=!1;if(this.forceCrop=o,this.groupPixelWidth=null,this.hasProcessed=!0,o&&!this.requireSorting&&(this.requireSorting=h=!0),o=!1===y.apply(this,arguments)||!o,h&&(this.requireSorting=!1),!o){this.destroyGroupedData();o=s.groupAll?this.xData:this.processedXData;var l,c=s.groupAll?this.yData:this.processedYData,p=i.plotSizeX,u=(i=this.xAxis).options.ordinal;if(f=this.groupPixelWidth=i.getGroupPixelWidth&&i.getGroupPixelWidth()){this.isDirty=t=!0,this.points=null,e=(h=i.getExtremes()).min;var f=f*((h=h.max)-e)/p*(u=u&&i.getGroupIntervalFactor(e,h,this)||1),g=(p=i.getTimeTicks(i.normalizeTimeTickInterval(f,s.units||w),Math.min(e,o[0]),Math.max(h,o[o.length-1]),i.options.startOfWeek,o,this.closestPointRange),o=(c=v.groupData.apply(this,[o,c,p,s.approximation])).groupedXData,u=c.groupedYData,0);if(s.smoothed&&o.length){for(o[l=o.length-1]=Math.min(o[l],h);l--&&0<l;)o[l]+=f/2;o[0]=Math.max(o[0],e)}for(l=1;l<p.length;l++)p.info.segmentStarts&&-1!==p.info.segmentStarts.indexOf(l)||(g=Math.max(p[l]-p[l-1],g));(e=p.info).gapSize=g,this.closestPointRange=p.info.totalRange,this.groupMap=c.groupMap,r(o[0])&&o[0]<i.dataMin&&n&&((!r(i.options.min)&&i.min<=i.dataMin||i.min===i.dataMin)&&(i.min=o[0]),i.dataMin=o[0]),s.groupAll&&(o=(s=this.cropData(o,u,i.min,i.max,1)).xData,u=s.yData),this.processedXData=o,this.processedYData=u}else this.groupMap=null;this.hasGroupedData=t,this.currentDataGrouping=e,this.preventGraphAnimation=(a&&a.totalRange)!==(e&&e.totalRange)}},v.destroyGroupedData=function(){var t=this.groupedData;(t||[]).forEach(function(e,i){e&&(t[i]=e.destroy?e.destroy():null)}),this.groupedData=null},v.generatePoints=function(){b.apply(this),this.destroyGroupedData(),this.groupedData=this.hasGroupedData?this.points:null},e(p,"update",function(){if(this.dataGroup)return t.error(24,!1,this.series.chart),!1}),e(f,"headerFormatter",function(t){var e,i,s=this.chart.time,o=t.labelConfig,n=o.series,r=n.tooltipOptions,c=n.options.dataGrouping,d=r.xDateFormat,p=n.xAxis,u=r[(t.isFooter?"footer":"header")+"Format"];p&&"datetime"===p.options.type&&c&&l(o.key)&&(i=n.currentDataGrouping,c=c.dateTimeLabelFormats||M.dateTimeLabelFormats,i?(r=c[i.unitName],1===i.count?d=r[0]:(d=r[1],e=r[2])):!d&&c&&(d=this.getXDateFormat(o,r,p)),d=s.dateFormat(d,o.key),e&&(d+=s.dateFormat(e,o.key+i.totalRange-1)),n.chart.styledMode&&(u=this.styledModeFormat(u)),t.text=h(u,{point:a(o.point,{key:d}),series:n},s),t.preventDefault())}),e(u,"destroy",v.destroyGroupedData),e(u,"afterSetOptions",function(t){t=t.options;var e=this.type,i=this.chart.options.plotOptions,s=n[e].dataGrouping,o=this.useCommonDataGrouping&&M;(k[e]||o)&&(s||(s=c(M,k[e])),t.dataGrouping=c(o,s,i.series&&i.series.dataGrouping,i[e].dataGrouping,this.userOptions.dataGrouping))}),e(o,"afterSetScale",function(){this.series.forEach(function(t){t.hasProcessed=!1})}),o.prototype.getGroupPixelWidth=function(){var t,e,i=this.series,s=i.length,o=0,n=!1;for(t=s;t--;)(e=i[t].options.dataGrouping)&&(o=Math.max(o,d(e.groupPixelWidth,M.groupPixelWidth)));for(t=s;t--;)(e=i[t].options.dataGrouping)&&i[t].hasProcessed&&(s=(i[t].processedXData||i[t].data).length,i[t].groupPixelWidth||s>this.chart.plotSizeX/o||s&&e.forced)&&(n=!0);return n?o:0},o.prototype.setDataGrouping=function(t,e){var i;if(e=d(e,!0),t||(t={forced:!1,units:null}),this instanceof o)for(i=this.series.length;i--;)this.series[i].update({dataGrouping:t},!1);else this.chart.options.series.forEach(function(e){e.dataGrouping=t},!1);this.ordinalSlope=null,e&&this.chart.redraw()},t.dataGrouping=x}),e(i,"parts/OHLCSeries.js",[i["parts/Globals.js"]],function(t){var e=t.Point,i=t.seriesType,s=t.seriesTypes;i("ohlc","column",{lineWidth:1,tooltip:{pointFormat:'<span style="color:{point.color}">\u25cf</span> <b> {series.name}</b><br/>Open: {point.open}<br/>High: {point.high}<br/>Low: {point.low}<br/>Close: {point.close}<br/>'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0},{directTouch:!1,pointArrayMap:["open","high","low","close"],toYData:function(t){return[t.open,t.high,t.low,t.close]},pointValKey:"close",pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},init:function(){s.column.prototype.init.apply(this,arguments),this.options.stacking=!1},pointAttribs:function(t,e){e=s.column.prototype.pointAttribs.call(this,t,e);var i=this.options;return delete e.fill,!t.options.color&&i.upColor&&t.open<t.close&&(e.stroke=i.upColor),e},translate:function(){var t=this,e=t.yAxis,i=!!t.modifyValue,o=["plotOpen","plotHigh","plotLow","plotClose","yBottom"];s.column.prototype.translate.apply(t),t.points.forEach(function(s){[s.open,s.high,s.low,s.close,s.low].forEach(function(n,r){null!==n&&(i&&(n=t.modifyValue(n)),s[o[r]]=e.toPixels(n,!0))}),s.tooltipPos[1]=s.plotHigh+e.pos-t.chart.plotTop})},drawPoints:function(){var t=this,e=t.chart;t.points.forEach(function(i){var s,o,n,r,a,h=i.graphic,l=!h;void 0!==i.plotY&&(h||(i.graphic=h=e.renderer.path().add(t.group)),e.styledMode||h.attr(t.pointAttribs(i,i.selected&&"select")),o=h.strokeWidth()%2/2,a=Math.round(i.plotX)-o,n=Math.round(i.shapeArgs.width/2),r=["M",a,Math.round(i.yBottom),"L",a,Math.round(i.plotHigh)],null!==i.open&&(s=Math.round(i.plotOpen)+o,r.push("M",a,s,"L",a-n,s)),null!==i.close&&(s=Math.round(i.plotClose)+o,r.push("M",a,s,"L",a+n,s)),h[l?"attr":"animate"]({d:r}).addClass(i.getClassName(),!0))})},animate:null},{getClassName:function(){return e.prototype.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")}})}),e(i,"parts/CandlestickSeries.js",[i["parts/Globals.js"]],function(t){var e=t.defaultPlotOptions,i=t.merge,s=t.seriesType,o=t.seriesTypes;s("candlestick","ohlc",i(e.column,{states:{hover:{lineWidth:2}},tooltip:e.ohlc.tooltip,threshold:null,lineColor:"#000000",lineWidth:1,upColor:"#ffffff",stickyTracking:!0}),{pointAttribs:function(t,e){var i=o.column.prototype.pointAttribs.call(this,t,e),s=this.options,n=t.open<t.close,r=s.lineColor||this.color;return i["stroke-width"]=s.lineWidth,i.fill=t.options.color||n&&s.upColor||this.color,i.stroke=t.lineColor||n&&s.upLineColor||r,e&&(t=s.states[e],i.fill=t.color||i.fill,i.stroke=t.lineColor||i.stroke,i["stroke-width"]=t.lineWidth||i["stroke-width"]),i},drawPoints:function(){var t=this,e=t.chart,i=t.yAxis.reversed;t.points.forEach(function(s){var o,n,r,a,h,l,c,d=s.graphic,p=!d;void 0!==s.plotY&&(d||(s.graphic=d=e.renderer.path().add(t.group)),t.chart.styledMode||d.attr(t.pointAttribs(s,s.selected&&"select")).shadow(t.options.shadow),h=d.strokeWidth()%2/2,l=Math.round(s.plotX)-h,o=s.plotOpen,n=s.plotClose,r=Math.min(o,n),o=Math.max(o,n),c=Math.round(s.shapeArgs.width/2),n=i?o!==s.yBottom:Math.round(r)!==Math.round(s.plotHigh),a=i?Math.round(r)!==Math.round(s.plotHigh):o!==s.yBottom,r=Math.round(r)+h,o=Math.round(o)+h,(h=[]).push("M",l-c,o,"L",l-c,r,"L",l+c,r,"L",l+c,o,"Z","M",l,r,"L",l,n?Math.round(i?s.yBottom:s.plotHigh):r,"M",l,o,"L",l,a?Math.round(i?s.plotHigh:s.yBottom):o),d[p?"attr":"animate"]({d:h}).addClass(s.getClassName(),!0))})}})}),e(i,"mixins/on-series.js",[i["parts/Globals.js"]],function(t){var e=t.defined,i=t.seriesTypes,s=t.stableSort;return{getPlotBox:function(){return t.Series.prototype.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this)},translate:function(){i.column.prototype.translate.apply(this);var t,o,n,r,a,h=this,l=h.options,c=h.chart,d=h.points,p=d.length-1,u=(u=l.onSeries)&&c.get(u),f=(l=l.onKey||"y",u&&u.options.step),g=u&&u.points,m=g&&g.length,x=c.inverted,v=h.xAxis,y=h.yAxis,b=0;if(u&&u.visible&&m)for(b=(u.pointXOffset||0)+(u.barW||0)/2,c=u.currentDataGrouping,n=g[m-1].x+(c?c.totalRange:0),s(d,function(t,e){return t.x-e.x}),l="plot"+l[0].toUpperCase()+l.substr(1);m--&&d[p]&&(o=g[m],(c=d[p]).y=o.y,!(o.x<=c.x&&void 0!==o[l]&&(c.x<=n&&(c.plotY=o[l],o.x<c.x&&!f&&(r=g[m+1])&&void 0!==r[l]&&(a=(c.x-o.x)/(r.x-o.x),c.plotY+=a*(r[l]-o[l]),c.y+=a*(r.y-o.y))),p--,m++,0>p))););d.forEach(function(i,s){var o;i.plotX+=b,(void 0===i.plotY||x)&&(0<=i.plotX&&i.plotX<=v.len?x?(i.plotY=v.translate(i.x,0,1,0,1),i.plotX=e(i.y)?y.translate(i.y,0,0,0,1):0):i.plotY=(v.opposite?0:h.yAxis.len)+v.offset:i.shapeArgs={}),(t=d[s-1])&&t.plotX===i.plotX&&(void 0===t.stackIndex&&(t.stackIndex=0),o=t.stackIndex+1),i.stackIndex=o}),this.onSeries=u}}}),e(i,"parts/FlagsSeries.js",[i["parts/Globals.js"],i["mixins/on-series.js"]],function(t,e){function i(t){p[t+"pin"]=function(e,i,s,o,n){var r,a=n&&n.anchorX;return n=n&&n.anchorY,"circle"===t&&o>s&&(e-=Math.round((o-s)/2),s=o),r=p[t](e,i,s,o),a&&n&&(r.push("M","circle"===t?e+s/2:r[1]+r[4]/2,i>n?i:i+o,"L",a,n),r=r.concat(p.circle(a-1,n-1,2,2))),r}}var s=t.addEvent,o=t.merge,n=t.noop,r=t.defined,a=t.Renderer,h=t.Series,l=t.seriesType,c=t.TrackerMixin,d=t.VMLRenderer,p=t.SVGRenderer.prototype.symbols;l("flags","column",{pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}<br/>"},threshold:null,y:-30,fillColor:"#ffffff",lineWidth:1,states:{hover:{lineColor:"#000000",fillColor:"#ccd6eb"}},style:{fontSize:"11px",fontWeight:"bold"}},{sorted:!1,noSharedTooltip:!0,allowDG:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],forceCrop:!0,init:h.prototype.init,pointAttribs:function(t,e){var i=this.options,s=t&&t.color||this.color,o=i.lineColor,n=t&&t.lineWidth;return t=t&&t.fillColor||i.fillColor,e&&(t=i.states[e].fillColor,o=i.states[e].lineColor,n=i.states[e].lineWidth),{fill:t||s,stroke:o||s,"stroke-width":n||i.lineWidth||0}},translate:e.translate,getPlotBox:e.getPlotBox,drawPoints:function(){var e,i,s,n,a,h,l,c,d=this.points,p=this.chart,u=p.renderer,f=p.inverted,g=this.options,m=g.y,x=this.yAxis,v={},y=[];for(n=d.length;n--;)a=d[n],c=(f?a.plotY:a.plotX)>this.xAxis.len,e=a.plotX,h=a.stackIndex,s=a.options.shape||g.shape,void 0!==(i=a.plotY)&&(i=a.plotY+m-(void 0!==h&&h*g.stackDistance)),a.anchorX=h?void 0:a.plotX,l=h?void 0:a.plotY,h=a.graphic,void 0!==i&&0<=e&&!c?(h||(h=a.graphic=u.label("",null,null,s,null,null,g.useHTML),p.styledMode||h.attr(this.pointAttribs(a)).css(o(g.style,a.style)),h.attr({align:"flag"===s?"left":"center",width:g.width,height:g.height,"text-align":g.textAlign}).addClass("highcharts-point").add(this.markerGroup),a.graphic.div&&(a.graphic.div.point=a),p.styledMode||h.shadow(g.shadow),h.isNew=!0),0<e&&(e-=h.strokeWidth()%2),s={y:i,anchorY:l},g.allowOverlapX&&(s.x=e,s.anchorX=a.anchorX),h.attr({text:a.options.title||g.title||"A"})[h.isNew?"attr":"animate"](s),g.allowOverlapX||(v[a.plotX]?v[a.plotX].size=Math.max(v[a.plotX].size,h.width):v[a.plotX]={align:0,size:h.width,target:e,anchorX:e}),a.tooltipPos=[e,i+x.pos-p.plotTop]):h&&(a.graphic=h.destroy());g.allowOverlapX||(t.objectEach(v,function(t){t.plotX=t.anchorX,y.push(t)}),t.distribute(y,f?x.len:this.xAxis.len,100),d.forEach(function(t){var e=t.graphic&&v[t.plotX];e&&(t.graphic[t.graphic.isNew?"attr":"animate"]({x:e.pos,anchorX:t.anchorX}),r(e.pos)?t.graphic.isNew=!1:(t.graphic.attr({x:-9999,anchorX:-9999}),t.graphic.isNew=!0))})),g.useHTML&&t.wrap(this.markerGroup,"on",function(e){return t.SVGElement.prototype.on.apply(e.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})},drawTracker:function(){var t=this.points;c.drawTrackerPoint.apply(this),t.forEach(function(e){var i=e.graphic;i&&s(i.element,"mouseover",function(){0<e.stackIndex&&!e.raised&&(e._y=i.y,i.attr({y:e._y-8}),e.raised=!0),t.forEach(function(t){t!==e&&t.raised&&t.graphic&&(t.graphic.attr({y:t._y}),t.raised=!1)})})})},animate:function(t){t?this.setClip():this.animate=null},setClip:function(){h.prototype.setClip.apply(this,arguments),!1!==this.options.clip&&this.sharedClipKey&&this.markerGroup.clip(this.chart[this.sharedClipKey])},buildKDTree:n,invertGroups:n},{isValid:function(){return t.isNumber(this.y)||void 0===this.y}}),p.flag=function(t,e,i,s,o){var n=o&&o.anchorX||t;return o=o&&o.anchorY||e,p.circle(n-1,o-1,2,2).concat(["M",n,o,"L",t,e+s,t,e,t+i,e,t+i,e+s,t,e+s,"Z"])},i("circle"),i("square"),a===d&&["flag","circlepin","squarepin"].forEach(function(t){d.prototype.symbols[t]=p[t]})}),e(i,"parts/RangeSelector.js",[i["parts/Globals.js"]],function(t){function e(t){this.init(t)}var i=t.addEvent,s=t.Axis,o=t.Chart,n=t.css,r=t.createElement,a=t.defaultOptions,h=t.defined,l=t.destroyObjectProperties,c=t.discardElement,d=t.extend,p=t.fireEvent,u=t.isNumber,f=t.merge,g=t.pick,m=t.pInt,x=t.splat;d(a,{rangeSelector:{verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputPosition:{align:"right",x:0,y:0},buttonPosition:{align:"left",x:0,y:0},labelStyle:{color:"#666666"}}}),a.lang=f(a.lang,{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"From",rangeSelectorTo:"To"}),e.prototype={clickButton:function(t,e){var o,n,r,a,h,l=this.chart,c=this.buttonOptions[t],d=l.xAxis[0],p=(y=l.scroller&&l.scroller.getUnionExtremes()||d||{}).dataMin,f=y.dataMax,m=d&&Math.round(Math.min(d.max,g(f,d.max))),v=c.type,y=c._range,b=c.dataGrouping;if(null!==p&&null!==f){if(l.fixedRange=y,b&&(this.forcedDataGrouping=!0,s.prototype.setDataGrouping.call(d||{chart:this.chart},b,!1),this.frozenStates=c.preserveDataGrouping),"month"===v||"year"===v)d?(v={range:c,max:m,chart:l,dataMin:p,dataMax:f},o=d.minFromRange.call(v),u(v.newMax)&&(m=v.newMax)):y=c;else if(y)o=Math.max(m-y,p),m=Math.min(o+y,f);else if("ytd"===v){if(!d)return void(this.deferredYTDClick=t);void 0===f&&(p=Number.MAX_VALUE,f=Number.MIN_VALUE,l.series.forEach(function(t){t=t.xData,p=Math.min(t[0],p),f=Math.max(t[t.length-1],f)}),e=!1),o=r=(m=this.getYTDExtremes(f,p,l.time.useUTC)).min,m=m.max}else"all"===v&&d&&(o=p,m=f);o+=c._offsetMin,m+=c._offsetMax,this.setSelected(t),d?d.setExtremes(o,m,g(e,1),null,{trigger:"rangeSelectorButton",rangeSelectorButton:c}):(n=x(l.options.xAxis)[0],h=n.range,n.range=y,a=n.min,n.min=r,i(l,"load",function(){n.range=h,n.min=a}))}},setSelected:function(t){this.selected=this.options.selected=t},defaultButtons:[{type:"month",count:1,text:"1m"},{type:"month",count:3,text:"3m"},{type:"month",count:6,text:"6m"},{type:"ytd",text:"YTD"},{type:"year",count:1,text:"1y"},{type:"all",text:"All"}],init:function(t){var e=this,s=t.options.rangeSelector,o=s.buttons||[].concat(e.defaultButtons),n=s.selected,r=function(){var t=e.minInput,i=e.maxInput;t&&t.blur&&p(t,"blur"),i&&i.blur&&p(i,"blur")};e.chart=t,e.options=s,e.buttons=[],e.buttonOptions=o,this.unMouseDown=i(t.container,"mousedown",r),this.unResize=i(t,"resize",r),o.forEach(e.computeButtonRange),void 0!==n&&o[n]&&this.clickButton(n,!1),i(t,"load",function(){t.xAxis&&t.xAxis[0]&&i(t.xAxis[0],"setExtremes",function(i){this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})},updateButtonStates:function(){var t,e=this,i=(t=this.chart).xAxis[0],s=Math.round(i.max-i.min),o=!i.hasVisibleSeries,n=t.scroller&&t.scroller.getUnionExtremes()||i,r=n.dataMin,a=n.dataMax,h=(t=e.getYTDExtremes(a,r,t.time.useUTC)).min,l=t.max,c=e.selected,d=u(c),p=e.options.allButtonsEnabled,f=e.buttons;e.buttonOptions.forEach(function(t,n){var u=t._range,g=t.type,m=t.count||1,x=f[n],v=0,y=t._offsetMax-t._offsetMin;t=n===c;var b=u>a-r,M=u<i.minRange,k=!1,w=!1;u=u===s;("month"===g||"year"===g)&&s+36e5>=864e5*{month:28,year:365}[g]*m-y&&s-36e5<=864e5*{month:31,year:366}[g]*m+y?u=!0:"ytd"===g?(u=l-h+y===s,k=!t):"all"===g&&(u=i.max-i.min>=a-r,w=!t&&d&&u),g=!p&&(b||M||w||o),m=t&&u||u&&!d&&!k||t&&e.frozenStates,g?v=3:m&&(d=!0,v=2),x.state!==v&&(x.setState(v),0===v&&c===n&&e.setSelected(null))})},computeButtonRange:function(t){var e=t.type,i=t.count||1,s={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};s[e]?t._range=s[e]*i:"month"!==e&&"year"!==e||(t._range=864e5*{month:30,year:365}[e]*i),t._offsetMin=g(t.offsetMin,0),t._offsetMax=g(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin},setInputValue:function(t,e){var i=this.chart.options.rangeSelector,s=this.chart.time,o=this[t+"Input"];h(e)&&(o.previousValue=o.HCTime,o.HCTime=e),o.value=s.dateFormat(i.inputEditDateFormat||"%Y-%m-%d",o.HCTime),this[t+"DateBox"].attr({text:s.dateFormat(i.inputDateFormat||"%b %e, %Y",o.HCTime)})},showInput:function(t){var e=this.inputGroup,i=this[t+"DateBox"];n(this[t+"Input"],{left:e.translateX+i.x+"px",top:e.translateY+"px",width:i.width-2+"px",height:i.height-2+"px",border:"2px solid silver"})},hideInput:function(t){n(this[t+"Input"],{border:0,width:"1px",height:"1px"}),this.setInputValue(t)},drawInput:function(e){function i(){var t=s.value,e=(g.inputDateParser||Date.parse)(t),i=l.xAxis[0],o=(n=l.scroller&&l.scroller.xAxis?l.scroller.xAxis:i).dataMin,n=n.dataMax;e!==s.previousValue&&(s.previousValue=e,u(e)||(e=t.split("-"),e=Date.UTC(m(e[0]),m(e[1])-1,m(e[2]))),u(e)&&(l.time.useUTC||(e+=6e4*(new Date).getTimezoneOffset()),v?e>h.maxInput.HCTime?e=void 0:e<o&&(e=o):e<h.minInput.HCTime?e=void 0:e>n&&(e=n),void 0!==e&&i.setExtremes(v?e:i.min,v?i.max:e,void 0,void 0,{trigger:"rangeSelectorInput"})))}var s,o,h=this,l=h.chart,c=l.renderer.style||{},p=l.renderer,g=l.options.rangeSelector,x=h.div,v="min"===e,y=this.inputGroup;this[e+"Label"]=o=p.label(a.lang[v?"rangeSelectorFrom":"rangeSelectorTo"],this.inputGroup.offset).addClass("highcharts-range-label").attr({padding:2}).add(y),y.offset+=o.width+5,this[e+"DateBox"]=p=p.label("",y.offset).addClass("highcharts-range-input").attr({padding:2,width:g.inputBoxWidth||90,height:g.inputBoxHeight||17,"text-align":"center"}).on("click",function(){h.showInput(e),h[e+"Input"].focus()}),l.styledMode||p.attr({stroke:g.inputBoxBorderColor||"#cccccc","stroke-width":1}),p.add(y),y.offset+=p.width+(v?10:0),this[e+"Input"]=s=r("input",{name:e,className:"highcharts-range-selector",type:"text"},{top:l.plotTop+"px"},x),l.styledMode||(o.css(f(c,g.labelStyle)),p.css(f({color:"#333333"},c,g.inputStyle)),n(s,d({position:"absolute",border:0,width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:c.fontSize,fontFamily:c.fontFamily,top:"-9999em"},g.inputStyle))),s.onfocus=function(){h.showInput(e)},s.onblur=function(){s===t.doc.activeElement&&(i(),h.hideInput(e),s.blur())},s.onchange=i,s.onkeypress=function(t){13===t.keyCode&&i()}},getPosition:function(){var t,e=(t=this.chart).options.rangeSelector;return{buttonTop:(t="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0)+e.buttonPosition.y,inputTop:t+e.inputPosition.y-10}},getYTDExtremes:function(t,e,i){var s=this.chart.time,o=new s.Date(t),n=s.get("FullYear",o);return i=i?s.Date.UTC(n,0,1):+new s.Date(n,0,1),e=Math.max(e||0,i),o=o.getTime(),{max:Math.min(t||o,o),min:e}},render:function(t,e){var i,s,o=this,n=o.chart,h=n.renderer,l=n.container,c=(m=n.options).exporting&&!1!==m.exporting.enabled&&m.navigation&&m.navigation.buttonOptions,d=a.lang,p=o.div,u=m.rangeSelector,f=g(m.chart.style&&m.chart.style.zIndex,0)+1,m=u.floating,x=o.buttons,v=(p=o.inputGroup,u.buttonTheme),y=u.buttonPosition,b=u.inputPosition,M=u.inputEnabled,k=v&&v.states,w=n.plotLeft,S=o.buttonGroup;s=o.rendered;var A,T=o.options.verticalAlign,P=n.legend,E=P&&P.options,C=y.y,L=b.y,O=s||!1,D=O?"animate":"attr",I=0,B=0;!1!==u.enabled&&(s||(o.group=s=h.g("range-selector-group").attr({zIndex:7}).add(),o.buttonGroup=S=h.g("range-selector-buttons").add(s),o.zoomText=h.text(d.rangeSelectorZoom,0,15).add(S),n.styledMode||(o.zoomText.css(u.labelStyle),v["stroke-width"]=g(v["stroke-width"],0)),o.buttonOptions.forEach(function(t,e){x[e]=h.button(t.text,0,0,function(i){var s,n=t.events&&t.events.click;n&&(s=n.call(t,i)),!1!==s&&o.clickButton(e),o.isActive=!0},v,k&&k.hover,k&&k.select,k&&k.disabled).attr({"text-align":"center"}).add(S)}),!1!==M&&(o.div=p=r("div",null,{position:"relative",height:0,zIndex:f}),l.parentNode.insertBefore(p,l),o.inputGroup=p=h.g("input-group").add(s),p.offset=0,o.drawInput("min"),o.drawInput("max"))),o.zoomText[D]({x:g(w+y.x,w)}),i=g(w+y.x,w)+o.zoomText.getBBox().width+5,o.buttonOptions.forEach(function(t,e){x[e][D]({x:i}),i+=x[e].width+g(u.buttonSpacing,5)}),w=n.plotLeft-n.spacing[3],o.updateButtonStates(),c&&this.titleCollision(n)&&"top"===T&&"right"===y.align&&y.y+S.getBBox().height-12<(c.y||0)+c.height&&(I=-40),"left"===y.align?A=y.x-n.spacing[3]:"right"===y.align&&(A=y.x+I-n.spacing[1]),S.align({y:y.y,width:S.getBBox().width,align:y.align,x:A},!0,n.spacingBox),o.group.placed=O,o.buttonGroup.placed=O,!1!==M&&(I=c&&this.titleCollision(n)&&"top"===T&&"right"===b.align&&b.y-p.getBBox().height-12<(c.y||0)+c.height+n.spacing[0]?-40:0,"left"===b.align?A=w:"right"===b.align&&(A=-Math.max(n.axisOffset[1],-I)),p.align({y:b.y,width:p.getBBox().width,align:b.align,x:b.x+A-2},!0,n.spacingBox),l=p.alignAttr.translateX+p.alignOptions.x-I+p.getBBox().x+2,c=p.alignOptions.width,d=S.alignAttr.translateX+S.getBBox().x,A=S.getBBox().width+20,(b.align===y.align||d+A>l&&l+c>d&&C<L+p.getBBox().height)&&p.attr({translateX:p.alignAttr.translateX+(n.axisOffset[1]>=-I?0:-I),translateY:p.alignAttr.translateY+S.getBBox().height+10}),o.setInputValue("min",t),o.setInputValue("max",e),o.inputGroup.placed=O),o.group.align({verticalAlign:T},!0,n.spacingBox),t=o.group.getBBox().height+20,e=o.group.alignAttr.translateY,"bottom"===T&&(B=e-(t=t+(P=E&&"bottom"===E.verticalAlign&&E.enabled&&!E.floating?P.legendHeight+g(E.margin,10):0)-20)-(m?0:u.y)-10),"top"===T?(m&&(B=0),n.titleOffset&&(B=n.titleOffset+n.options.title.margin),B+=n.margin[0]-n.spacing[0]||0):"middle"===T&&(L===C?B=0>L?e+void 0:e:(L||C)&&(B=0>L||0>C?B-Math.min(L,C):e-t+NaN)),o.group.translate(u.x,u.y+Math.floor(B)),!1!==M&&(o.minInput.style.marginTop=o.group.translateY+"px",o.maxInput.style.marginTop=o.group.translateY+"px"),o.rendered=!0)},getHeight:function(){var t=this.options,e=this.group,i=t.y,s=t.buttonPosition.y,o=t.inputPosition.y;return t.height?t.height:(t=e?e.getBBox(!0).height+13+i:0,e=Math.min(o,s),(0>o&&0>s||0<o&&0<s)&&(t+=Math.abs(e)),t)},titleCollision:function(t){return!(t.options.title.text||t.options.subtitle.text)},update:function(t){var e=this.chart;f(!0,e.options.rangeSelector,t),this.destroy(),this.init(e),e.rangeSelector.render()},destroy:function(){var i=this,s=i.minInput,o=i.maxInput;i.unMouseDown(),i.unResize(),l(i.buttons),s&&(s.onfocus=s.onblur=s.onchange=null),o&&(o.onfocus=o.onblur=o.onchange=null),t.objectEach(i,function(t,s){t&&"chart"!==s&&(t.destroy?t.destroy():t.nodeType&&c(this[s])),t!==e.prototype[s]&&(i[s]=null)},this)}},s.prototype.minFromRange=function(){var t,e,i,s=this.range,o={month:"Month",year:"FullYear"}[s.type],n=this.max,r=this.chart.time,a=function(t,e){var i=new r.Date(t),s=r.get(o,i);return r.set(o,i,s+e),s===r.get(o,i)&&i.setDate(0),i.getTime()-t};return u(s)?(t=n-s,i=s):(t=n+a(n,-s.count),this.chart&&(this.chart.fixedRange=n-t)),e=g(this.dataMin,Number.MIN_VALUE),u(t)||(t=e),t<=e&&(t=e,void 0===i&&(i=a(t,s.count)),this.newMax=Math.min(t+i,this.dataMax)),u(n)||(t=void 0),t},t.RangeSelector||(i(o,"afterGetContainer",function(){this.options.rangeSelector.enabled&&(this.rangeSelector=new e(this))}),i(o,"beforeRender",function(){var t=this.axes,e=this.rangeSelector;e&&(u(e.deferredYTDClick)&&(e.clickButton(e.deferredYTDClick),delete e.deferredYTDClick),t.forEach(function(t){t.updateNames(),t.setScale()}),this.getAxisMargins(),e.render(),t=e.options.verticalAlign,e.options.floating||("bottom"===t?this.extraBottomMargin=!0:"middle"!==t&&(this.extraTopMargin=!0)))}),i(o,"update",function(t){var i=t.options.rangeSelector;t=this.rangeSelector;var s=this.extraBottomMargin,o=this.extraTopMargin;i&&i.enabled&&!h(t)&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=new e(this)),this.extraTopMargin=this.extraBottomMargin=!1,t&&(t.render(),i=i&&i.verticalAlign||t.options&&t.options.verticalAlign,t.options.floating||("bottom"===i?this.extraBottomMargin=!0:"middle"!==i&&(this.extraTopMargin=!0)),this.extraBottomMargin!==s||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}),i(o,"render",function(){var t=this.rangeSelector;t&&!t.options.floating&&(t.render(),"bottom"===(t=t.options.verticalAlign)?this.extraBottomMargin=!0:"middle"!==t&&(this.extraTopMargin=!0))}),i(o,"getMargins",function(){var t=this.rangeSelector;t&&(t=t.getHeight(),this.extraTopMargin&&(this.plotTop+=t),this.extraBottomMargin&&(this.marginBottom+=t))}),o.prototype.callbacks.push(function(t){function e(){s=t.xAxis[0].getExtremes(),u(s.min)&&r.render(s.min,s.max)}var s,o,n,r=t.rangeSelector;r&&(n=i(t.xAxis[0],"afterSetExtremes",function(t){r.render(t.min,t.max)}),o=i(t,"redraw",e),e()),i(t,"destroy",function(){r&&(o(),n())})}),t.RangeSelector=e)}),e(i,"parts/StockChart.js",[i["parts/Globals.js"]],function(t){var e=t.addEvent,i=t.arrayMax,s=t.arrayMin,o=t.Axis,n=t.Chart,r=t.defined,a=t.extend,h=t.format,l=t.isNumber,c=t.isString,d=t.merge,p=t.pick,u=t.Point,f=t.Renderer,g=t.Series,m=t.splat,x=t.SVGRenderer,v=t.VMLRenderer,y=g.prototype,b=y.init,M=y.processData,k=u.prototype.tooltipFormatter;t.StockChart=t.stockChart=function(e,i,s){var o,r=c(e)||e.nodeName,a=arguments[r?1:0],h=a,l=a.series,u=t.getOptions(),f=p(a.navigator&&a.navigator.enabled,u.navigator.enabled,!0),g=f?{startOnTick:!1,endOnTick:!1}:null,x={marker:{enabled:!1,radius:2}},v={
shadow:!1,borderWidth:0};return a.xAxis=m(a.xAxis||{}).map(function(t,e){return d({minPadding:0,maxPadding:0,overscroll:0,ordinal:!0,title:{text:null},labels:{overflow:"justify"},showLastLabel:!0},u.xAxis,u.xAxis&&u.xAxis[e],t,{type:"datetime",categories:null},g)}),a.yAxis=m(a.yAxis||{}).map(function(t,e){return o=p(t.opposite,!0),d({labels:{y:-2},opposite:o,showLastLabel:!(!t.categories&&"category"!==t.type),title:{text:null}},u.yAxis,u.yAxis&&u.yAxis[e],t)}),a.series=null,(a=d({chart:{panning:!0,pinchType:"x"},navigator:{enabled:f},scrollbar:{enabled:p(u.scrollbar.enabled,!0)},rangeSelector:{enabled:p(u.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:p(u.tooltip.split,!0),crosshairs:!0},legend:{enabled:!1},plotOptions:{line:x,spline:x,area:x,areaspline:x,arearange:x,areasplinerange:x,column:v,columnrange:v,candlestick:v,ohlc:v}},a,{isStock:!0})).series=h.series=l,r?new n(e,a,s):new n(a,i)},e(o,"autoLabelAlign",function(t){var e=this.chart,i=this.options,s=(e=e._labelPanes=e._labelPanes||{},this.options.labels);this.chart.options.isStock&&"yAxis"===this.coll&&(!e[i=i.top+","+i.height]&&s.enabled&&(15===s.x&&(s.x=0),void 0===s.align&&(s.align="right"),e[i]=this,t.align="right",t.preventDefault()))}),e(o,"destroy",function(){var t=this.chart,e=this.options&&this.options.top+","+this.options.height;e&&t._labelPanes&&t._labelPanes[e]===this&&delete t._labelPanes[e]}),e(o,"getPlotLinePath",function(e){function i(t){var e="xAxis"===t?"yAxis":"xAxis";return t=u.options[e],l(t)?[g[e][t]]:c(t)?[g.get(t)]:f.map(function(t){return t[e]})}var s,o,n,a,h,d,u=this,f=this.isLinked&&!this.series?this.linkedParent.series:this.series,g=u.chart,m=g.renderer,x=u.left,v=u.top,y=[],b=[],M=e.translatedValue,k=e.value,w=e.force;"xAxis"!==u.coll&&"yAxis"!==u.coll||(e.preventDefault(),b=i(u.coll),(u.isXAxis?g.yAxis:g.xAxis).forEach(function(t){if(!r(t.options.id)||-1===t.options.id.indexOf("navigator")){var e=t.isXAxis?"yAxis":"xAxis";e=r(t.options[e])?g[e][t.options[e]]:g[e][0];u===e&&b.push(t)}}),h=b.length?[]:[u.isXAxis?g.yAxis[0]:g.xAxis[0]],b.forEach(function(e){-1!==h.indexOf(e)||t.find(h,function(t){return t.pos===e.pos&&t.len===e.len})||h.push(e)}),d=p(M,u.translate(k,null,null,e.old)),l(d)&&(u.horiz?h.forEach(function(t){var e;o=t.pos,a=o+t.len,s=n=Math.round(d+u.transB),"pass"!==w&&(s<x||s>x+u.width)&&(w?s=n=Math.min(Math.max(x,s),x+u.width):e=!0),e||y.push("M",s,o,"L",n,a)}):h.forEach(function(t){var e;s=t.pos,n=s+t.len,o=a=Math.round(v+u.height-d),"pass"!==w&&(o<v||o>v+u.height)&&(w?o=a=Math.min(Math.max(v,o),u.top+u.height):e=!0),e||y.push("M",s,o,"L",n,a)})),e.path=0<y.length?m.crispPolyLine(y,e.lineWidth||1):null)}),x.prototype.crispPolyLine=function(t,e){var i;for(i=0;i<t.length;i+=6)t[i+1]===t[i+4]&&(t[i+1]=t[i+4]=Math.round(t[i+1])-e%2/2),t[i+2]===t[i+5]&&(t[i+2]=t[i+5]=Math.round(t[i+2])+e%2/2);return t},f===v&&(v.prototype.crispPolyLine=x.prototype.crispPolyLine),e(o,"afterHideCrosshair",function(){this.crossLabel&&(this.crossLabel=this.crossLabel.hide())}),e(o,"afterDrawCrosshair",function(t){var e,i;if(r(this.crosshair.label)&&this.crosshair.label.enabled&&this.cross){var s=this.chart,o=this.options.crosshair.label,n=this.horiz;e=this.opposite,i=this.left;var l,c,d=this.top,u=this.crossLabel,f=o.format,g="",m="inside"===this.options.tickPosition,x=!1!==this.crosshair.snap,v=0,y=t.e||this.cross&&this.cross.e,b=t.point;t=this.lin2log,this.isLog?(l=t(this.min),c=t(this.max)):(l=this.min,c=this.max),t=n?"center":e?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center",u||(u=this.crossLabel=s.renderer.label(null,null,null,o.shape||"callout").addClass("highcharts-crosshair-label"+(this.series[0]&&" highcharts-color-"+this.series[0].colorIndex)).attr({align:o.align||t,padding:p(o.padding,8),r:p(o.borderRadius,3),zIndex:2}).add(this.labelGroup),s.styledMode||u.attr({fill:o.backgroundColor||this.series[0]&&this.series[0].color||"#666666",stroke:o.borderColor||"","stroke-width":o.borderWidth||0}).css(a({color:"#ffffff",fontWeight:"normal",fontSize:"11px",textAlign:"center"},o.style))),n?(t=x?b.plotX+i:y.chartX,d+=e?0:this.height):(t=e?this.width+i:0,d=x?b.plotY+d:y.chartY),f||o.formatter||(this.isDatetimeAxis&&(g="%b %d, %Y"),f="{value"+(g?":"+g:"")+"}"),g=x?b[this.isXAxis?"x":"y"]:this.toValue(n?y.chartX:y.chartY),u.attr({text:f?h(f,{value:g},s.time):o.formatter.call(this,g),x:t,y:d,visibility:g<l||g>c?"hidden":"visible"}),o=u.getBBox(),n?(m&&!e||!m&&e)&&(d=u.y-o.height):d=u.y-o.height/2,n?(e=i-o.x,i=i+this.width-o.x):(e="left"===this.labelAlign?i:0,i="right"===this.labelAlign?i+this.width:s.chartWidth),u.translateX<e&&(v=e-u.translateX),u.translateX+o.width>=i&&(v=-(u.translateX+o.width-i)),u.attr({x:t+v,y:d,anchorX:n?t:this.opposite?0:s.chartWidth,anchorY:n?this.opposite?s.chartHeight:0:d+o.height/2})}}),y.init=function(){b.apply(this,arguments),this.setCompare(this.options.compare)},y.setCompare=function(t){this.modifyValue="value"===t||"percent"===t?function(e,i){var s=this.compareValue;if(void 0!==e&&void 0!==s)return e="value"===t?e-s:e/s*100-(100===this.options.compareBase?0:100),i&&(i.change=e),e}:null,this.userOptions.compare=t,this.chart.hasRendered&&(this.isDirty=!0)},y.processData=function(){var t,e,i,s,o,n=-1,r=!0===this.options.compareStart?0:1;if(M.apply(this,arguments),this.xAxis&&this.processedYData)for(e=this.processedXData,s=(i=this.processedYData).length,this.pointArrayMap&&(n=this.pointArrayMap.indexOf(this.options.pointValKey||this.pointValKey||"y")),t=0;t<s-r;t++)if(o=i[t]&&-1<n?i[t][n]:i[t],l(o)&&e[t+r]>=this.xAxis.min&&0!==o){this.compareValue=o;break}},e(g,"afterGetExtremes",function(){if(this.modifyValue){var t=[this.modifyValue(this.dataMin),this.modifyValue(this.dataMax)];this.dataMin=s(t),this.dataMax=i(t)}}),o.prototype.setCompare=function(t,e){this.isXAxis||(this.series.forEach(function(e){e.setCompare(t)}),p(e,!0)&&this.chart.redraw())},u.prototype.tooltipFormatter=function(e){return e=e.replace("{point.change}",(0<this.change?"+":"")+t.numberFormat(this.change,p(this.series.tooltipOptions.changeDecimals,2))),k.apply(this,[e])},e(g,"render",function(){var t;this.chart.is3d&&this.chart.is3d()||this.chart.polar||!this.xAxis||this.xAxis.isRadial||(t=this.yAxis.len-(this.xAxis.axisLine?Math.floor(this.xAxis.axisLine.strokeWidth()/2):0),!this.clipBox&&this.animate?(this.clipBox=d(this.chart.clipBox),this.clipBox.width=this.xAxis.len,this.clipBox.height=t):this.chart[this.sharedClipKey]&&(this.chart[this.sharedClipKey].animate({width:this.xAxis.len,height:t}),this.chart[this.sharedClipKey+"m"]&&this.chart[this.sharedClipKey+"m"].animate({width:this.xAxis.len})))}),e(n,"update",function(t){"scrollbar"in(t=t.options)&&this.navigator&&(d(!0,this.options.scrollbar,t.scrollbar),this.navigator.update({},!1),delete t.scrollbar)})}),e(i,"masters/modules/stock.src.js",[],function(){}),e(i,"masters/highstock.src.js",[i["masters/highcharts.src.js"]],function(t){return t}),i["masters/highstock.src.js"]._modules=i,i["masters/highstock.src.js"]});