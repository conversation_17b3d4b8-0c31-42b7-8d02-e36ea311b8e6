﻿{"ja":"<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ja\"><head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ja/Product.aspx\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/Product.aspx\" hreflang=\"x-default\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/Product.aspx\" hreflang=\"zh\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Product.aspx\" hreflang=\"zh-tw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Product.aspx\" hreflang=\"zh-yue\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Product.aspx\" hreflang=\"en\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Product.aspx\" hreflang=\"es\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Product.aspx\" hreflang=\"hi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Product.aspx\" hreflang=\"ar\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Product.aspx\" hreflang=\"pt\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Product.aspx\" hreflang=\"bn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Product.aspx\" hreflang=\"ru\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Product.aspx\" hreflang=\"ja\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Product.aspx\" hreflang=\"pa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Product.aspx\" hreflang=\"de\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Product.aspx\" hreflang=\"fr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Product.aspx\" hreflang=\"mr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Product.aspx\" hreflang=\"te\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Product.aspx\" hreflang=\"vi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Product.aspx\" hreflang=\"ko\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Product.aspx\" hreflang=\"ta\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Product.aspx\" hreflang=\"ur\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Product.aspx\" hreflang=\"tr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Product.aspx\" hreflang=\"it\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Product.aspx\" hreflang=\"gu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Product.aspx\" hreflang=\"pl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Product.aspx\" hreflang=\"uk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Product.aspx\" hreflang=\"ms\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Product.aspx\" hreflang=\"id\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Product.aspx\" hreflang=\"ml\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Product.aspx\" hreflang=\"kn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Product.aspx\" hreflang=\"fa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Product.aspx\" hreflang=\"nl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Product.aspx\" hreflang=\"th\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Product.aspx\" hreflang=\"sw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Product.aspx\" hreflang=\"ro\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Product.aspx\" hreflang=\"my\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Product.aspx\" hreflang=\"or\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Product.aspx\" hreflang=\"he\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Product.aspx\" hreflang=\"am\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Product.aspx\" hreflang=\"fil\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Product.aspx\" hreflang=\"sv\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Product.aspx\" hreflang=\"el\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Product.aspx\" hreflang=\"cs\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Product.aspx\" hreflang=\"hu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Product.aspx\" hreflang=\"be\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Product.aspx\" hreflang=\"si\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Product.aspx\" hreflang=\"ne\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Product.aspx\" hreflang=\"km\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Product.aspx\" hreflang=\"sk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Product.aspx\" hreflang=\"bg\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Product.aspx\" hreflang=\"fr-ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Product.aspx\" hreflang=\"ha\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Product.aspx\" hreflang=\"yo\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Product.aspx\" hreflang=\"ig\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Product.aspx\" hreflang=\"ku\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Product.aspx\" hreflang=\"rw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Product.aspx\" hreflang=\"ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Product.aspx\" hreflang=\"da\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Product.aspx\" hreflang=\"fi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Product.aspx\" hreflang=\"nb\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Product.aspx\" hreflang=\"hr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Product.aspx\" hreflang=\"sr-cyrl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Product.aspx\" hreflang=\"sr-latn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Product.aspx\" hreflang=\"sq\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Product.aspx\" hreflang=\"so\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Product.aspx\" hreflang=\"zu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Product.aspx\" hreflang=\"ka\" />\n\n\n\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\">\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    <script src=\"/static/js/autocdn.js\" type=\"text/javascript\"></script>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/log.min.js\"></script>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/common_sdk.min.js\"></script>\n    <link rel=\"stylesheet\" type=\"text/css\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/common_new.css?1=1\">\n    <link rel=\"stylesheet\" type=\"text/css\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/index_new.css\">\n    <title>OCRアシスタント製品紹介 - OCRテキスト認識アシスタント(OCRアシスタント)は、無料のテキスト認識(OCR)ツールで、作業効率の向上に取り組んでいます。</title>\n    <meta name=\"description\" content=\"OCRは、テキスト、表、数式、ドキュメント、および翻訳を統合する無料の高効率生産性ツールです。\">\n    <meta name=\"keywords\" content=\"無料OCR, 無料テキスト認識, 無料画像認識, 無料画像認識, 無料画像からテキスト, OCR認識, テキスト認識, 画像認識, 画像テキスト認識, 画像からテキストへ, 画像からテーブルへ, 画像から数式へ, ドキュメント認識, ドキュメント翻訳\">\n    <link rel=\"stylesheet\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/style.1545014209769.css\" type=\"text/css\" media=\"all\">\n    <link rel=\"stylesheet\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/css/styles.css\" type=\"text/css\" media=\"all\">\n    <script src=\"//lf3-cdn-tos.bytecdntp.com/cdn/jquery/1.4.2/jquery.min.js\" type=\"text/javascript\" onerror=\"autoRetry(this)\"></script>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/body.1509421810036.js\"></script>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/jquery.SuperSlide.2.1.1.js\"></script>\n    <script type=\"application/ld+json\">{\"@context\":\"http://schema.org\",\"@type\":\"SoftwareApplication\",\"name\":\"OCR Assistant\",\"description\":\"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.\",\"category\":\"Productivity\",\"applicationCategory\":\"Business\",\"image\":\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif\",\"screenshot\":[\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png\"],\"aggregateRating\":{\"@type\":\"AggregateRating\",\"worstRating\":\"1\",\"bestRating\":\"5\",\"ratingValue\":\"4.8\",\"ratingCount\":\"524055\"},\"offers\":{\"@type\":\"Offer\",\"price\":0,\"priceCurrency\":\"USD\",\"category\":\"free\"},\"operatingSystem\": \"Windows\"}</script>\n    <style type=\"text/css\">\n        .mod-btn {\n            display: inline-block;\n            box-sizing: border-box;\n            width: 140px;\n            height: 40px;\n            line-height: 40px;\n            padding: 0;\n            border: 1px solid transparent;\n            border-radius: 3px;\n            font-size: 15px;\n            text-align: center;\n            cursor: pointer;\n            -webkit-transition: border-color .2s, color .2s, background-color .2s;\n            transition: border-color .2s, color .2s, background-color .2s;\n        }\n\n        .mod-btn-blue {\n            color: #fff;\n            background-color: #007cfa;\n        }\n\n            .mod-btn-blue:hover {\n                background-color: #3396fb;\n            }\n\n            .mod-btn-blue:focus {\n                background-color: #0064ed;\n            }\n\n        .mod-btn-black {\n            color: #666;\n            background-color: #fff;\n            border-color: #ededed;\n        }\n\n            .mod-btn-black:hover {\n                color: #007cfa;\n                border-color: #007cfa;\n            }\n\n        .mod-btn-white {\n            color: #007cfa;\n            border-color: #007cfa;\n            background-color: #fff;\n        }\n\n            .mod-btn-white:hover {\n                color: #fff;\n                border-color: #3396fb;\n                background-color: #3396fb;\n            }\n\n            .mod-btn-white:focus {\n                color: #fff;\n                background-color: #0064ed;\n            }\n\n        .mod-btn-disabled-blue {\n            color: #fff;\n            background-color: #cce5fe;\n            cursor: default;\n        }\n\n        .mod-btn-disabled-orange {\n            color: #fff;\n            background-color: #ffca69;\n            cursor: default;\n        }\n\n        .mod-btn-disabled-gray {\n            color: #666;\n            background-color: #e6e6e6;\n            cursor: default;\n        }\n\n        .mod-btn-transparent {\n            color: #666;\n            border-color: #ededed;\n            background-color: transparent;\n        }\n\n            .mod-btn-transparent:hover {\n                color: #007cfa;\n                border-color: #007cfa;\n                background-color: transparent;\n            }\n\n        .mod-btn-qq {\n            color: #666;\n            background-color: #fff;\n            border-color: #ededed;\n        }\n\n            .mod-btn-qq:hover {\n                color: #ff8a00;\n                border-color: #ff8a00;\n            }\n\n            .mod-btn-qq .i-qq {\n                width: 22px;\n                height: 22px;\n                margin-bottom: 4px;\n                margin-right: 7px;\n                vertical-align: middle;\n                background-image: url(../images/icon.png);\n                background-position: 0 -535px;\n            }\n    </style>\n    <style fr-css-3fb305a8=\"false\" id=\"HMxJVhcBpBem\" media=\"screen\" type=\"text/css\">\n        @charset \"UTF-8\";\n\n        :root {\n            --fr-font-basefont: system-ui,-apple-system,BlinkMacSystemFont,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji','Android Emoji','EmojiSymbols','emojione mozilla','twemoji mozilla','office365icons','iconfont','icomoon','FontAwesome','Font Awesome 5 Pro','Font Awesome 6 Pro','IcoFont','fontello','themify','Material Icons','Material Icons Extended','bootstrap-icons','Segoe Fluent Icons','Material-Design-Iconic-Font';\n            --fr-font-fontscale: 1;\n            --fr-font-family: 'Microsoft YaHei UI';\n            --fr-font-shadow: 0 0 0.75px #7c7c7cdd;\n            --fr-font-stroke: 0.015px currentcolor;\n            --fr-no-stroke: 0px transparent;\n        }\n\n        @font-face {\n            font-family: \"Arial\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"FangSong\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Georgia\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"HanHei SC\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Helvetica\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Helvetica Neue\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"KaiTi\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Microsoft YaHei\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"MingLiU\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"NSimSun\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Noto Sans\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Open Sans\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PMingLiU\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PingFangHK-Medium\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PingFangHK-Regular\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PingFangSC-Medium\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PingFangSC-Regular\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"PingFangSC-Semibold\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Roboto\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"RobotoDraft\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"SF Pro SC\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Segoe UI\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"SimHei\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"SimSun\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Tahoma\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Ubuntu\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"Verdana\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\4EFF\\5B8B\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\5B8B\\4F53\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\5FAE\\8EDF\\6B63\\9ED1\\9AD4\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\5FAE\\8F6F\\96C5\\9ED1\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\6977\\4F53\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        @font-face {\n            font-family: \"\\9ED1\\4F53\";\n            src: local(\"MicrosoftYaHeiUI\");\n        }\n\n        :root#AOrggp :is(:not(i,head *):not([class*='glyph'],[class*='icon'],[class*='fa-'],[class*='vjs-'],[class*='mu-'])) {\n            font-family: var(--fr-font-family),var(--fr-font-basefont);\n            text-shadow: var(--fr-font-shadow);\n            -webkit-text-stroke: var(--fr-font-stroke);\n            font-feature-settings: unset;\n            font-variant: unset;\n            font-optical-sizing: auto;\n            font-kerning: auto;\n            -webkit-font-smoothing: antialiased !important;\n            text-rendering: optimizeLegibility;\n        }\n\n        :root#AOrggp ::selection {\n            color: #ffffff !important;\n            background: #0084ff !important;\n        }\n\n        :root#AOrggp :is(progress,meter,datalist,samp,kbd,pre,pre *,code,code *) {\n            -webkit-text-stroke: var(--fr-no-stroke) !important;\n            text-shadow: none !important\n        }\n\n        .fr-fix-cc18d11d {\n            -webkit-text-stroke: var(--fr-no-stroke) !important;\n        }\n    </style>\n    <style data-id=\"immersive-translate-input-injected-css\">\n        .immersive-translate-input {\n            position: absolute;\n            top: 0;\n            right: 0;\n            left: 0;\n            bottom: 0;\n            z-index: 2147483647;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n        }\n\n        .immersive-translate-loading-spinner {\n            vertical-align: middle !important;\n            width: 10px !important;\n            height: 10px !important;\n            display: inline-block !important;\n            margin: 0 4px !important;\n            border: 2px rgba(221, 244, 255, 0.6) solid !important;\n            border-top: 2px rgba(0, 0, 0, 0.375) solid !important;\n            border-left: 2px rgba(0, 0, 0, 0.375) solid !important;\n            border-radius: 50% !important;\n            padding: 0 !important;\n            -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;\n            animation: immersive-translate-loading-animation 0.6s infinite linear !important;\n        }\n\n        @-webkit-keyframes immersive-translate-loading-animation {\n            from {\n                -webkit-transform: rotate(0deg);\n            }\n\n            to {\n                -webkit-transform: rotate(359deg);\n            }\n        }\n\n        @keyframes immersive-translate-loading-animation {\n            from {\n                transform: rotate(0deg);\n            }\n\n            to {\n                transform: rotate(359deg);\n            }\n        }\n\n\n        .immersive-translate-input-loading {\n            --loading-color: #f78fb6;\n            width: 6px;\n            height: 6px;\n            border-radius: 50%;\n            display: block;\n            margin: 12px auto;\n            position: relative;\n            color: white;\n            left: -100px;\n            box-sizing: border-box;\n            animation: immersiveTranslateShadowRolling 1.5s linear infinite;\n        }\n\n        @keyframes immersiveTranslateShadowRolling {\n            0% {\n                box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);\n            }\n\n            12% {\n                box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);\n            }\n\n            25% {\n                box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);\n            }\n\n            36% {\n                box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);\n            }\n\n            50% {\n                box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);\n            }\n\n            62% {\n                box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);\n            }\n\n            75% {\n                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color);\n            }\n\n            87% {\n                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);\n            }\n\n            100% {\n                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);\n            }\n        }\n\n\n        .immersive-translate-search-recomend {\n            border: 1px solid #dadce0;\n            border-radius: 8px;\n            padding: 16px;\n            margin-bottom: 16px;\n            position: relative;\n            font-size: 16px;\n        }\n\n        .immersive-translate-search-enhancement-en-title {\n            color: #4d5156;\n        }\n        /* dark */\n        @media (prefers-color-scheme: dark) {\n            .immersive-translate-search-recomend {\n                border: 1px solid #3c4043;\n            }\n\n            .immersive-translate-close-action svg {\n                fill: #bdc1c6;\n            }\n\n            .immersive-translate-search-enhancement-en-title {\n                color: #bdc1c6;\n            }\n        }\n\n\n        .immersive-translate-search-settings {\n            position: absolute;\n            top: 16px;\n            right: 16px;\n            cursor: pointer;\n        }\n\n        .immersive-translate-search-title {\n        }\n\n        .immersive-translate-search-title-wrapper {\n        }\n\n        .immersive-translate-search-time {\n            font-size: 12px;\n            margin: 4px 0 24px;\n            color: #70757a;\n        }\n\n        .immersive-translate-expand-items {\n            display: none;\n        }\n\n        .immersive-translate-search-more {\n            margin-top: 16px;\n            font-size: 14px;\n        }\n\n        .immersive-translate-modal {\n            display: none;\n            position: fixed;\n            z-index: 1000000;\n            left: 0;\n            top: 0;\n            width: 100%;\n            height: 100%;\n            overflow: auto;\n            background-color: rgb(0, 0, 0);\n            background-color: rgba(0, 0, 0, 0.4);\n            font-size: 15px;\n        }\n\n        .immersive-translate-modal-content {\n            background-color: #fefefe;\n            margin: 15% auto;\n            padding: 20px;\n            border: 1px solid #888;\n            border-radius: 10px;\n            width: 80%;\n            max-width: 500px;\n            font-family: system-ui, -apple-system, \"Segoe UI\", \"Roboto\", \"Ubuntu\", \"Cantarell\", \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n        }\n\n        .immersive-translate-modal-title {\n            font-size: 1.3rem;\n            font-weight: 500;\n            margin-bottom: 20px;\n            color: hsl(205, 20%, 32%);\n        }\n\n        .immersive-translate-modal-body {\n            color: hsl(205, 20%, 32%)\n        }\n\n        .immersive-translate-close {\n            color: #aaa;\n            float: right;\n            font-size: 28px;\n            font-weight: bold;\n        }\n\n            .immersive-translate-close:hover,\n            .immersive-translate-close:focus {\n                color: black;\n                text-decoration: none;\n                cursor: pointer;\n            }\n\n        .immersive-translate-modal-footer {\n            display: flex;\n            justify-content: flex-end;\n            flex-wrap: wrap;\n            margin-top: 20px;\n        }\n\n        .immersive-translate-btn {\n            width: fit-content;\n            color: #fff;\n            background-color: #ea4c89;\n            border: none;\n            font-size: 14px;\n            margin: 5px;\n            padding: 10px 20px;\n            font-size: 1rem;\n            border-radius: 5px;\n            display: flex;\n            align-items: center;\n            cursor: pointer;\n            transition: background-color 0.3s ease;\n        }\n\n            .immersive-translate-btn:hover {\n                background-color: #f082ac;\n            }\n\n        .immersive-translate-cancel-btn {\n            /* gray color */\n            background-color: rgb(89, 107, 120);\n        }\n\n\n            .immersive-translate-cancel-btn:hover {\n                background-color: hsl(205, 20%, 32%);\n            }\n\n\n        .immersive-translate-btn svg {\n            margin-right: 5px;\n        }\n\n        .immersive-translate-link {\n            cursor: pointer;\n            user-select: none;\n            -webkit-user-drag: none;\n            text-decoration: none;\n            color: #007bff;\n            -webkit-tap-highlight-color: rgba(0, 0, 0, .1);\n        }\n\n        .immersive-translate-primary-link {\n            cursor: pointer;\n            user-select: none;\n            -webkit-user-drag: none;\n            text-decoration: none;\n            color: #ea4c89;\n            -webkit-tap-highlight-color: rgba(0, 0, 0, .1);\n        }\n\n        .immersive-translate-modal input[type=\"radio\"] {\n            margin: 0 6px;\n            cursor: pointer;\n        }\n\n        .immersive-translate-modal label {\n            cursor: pointer;\n        }\n\n        .immersive-translate-close-action {\n            position: absolute;\n            top: 2px;\n            right: 0px;\n            cursor: pointer;\n        }\n    </style>\n    <style id=\"swal-pub-style\">\n        .swal2-popup.swal2-toast {\n            flex-direction: column;\n            align-items: stretch;\n            width: auto;\n            padding: 1.25em;\n            overflow-y: hidden;\n            background: #fff;\n            box-shadow: 0 0 .625em #d9d9d9\n        }\n\n            .swal2-popup.swal2-toast .swal2-header {\n                flex-direction: row;\n                padding: 0\n            }\n\n            .swal2-popup.swal2-toast .swal2-title {\n                flex-grow: 1;\n                justify-content: flex-start;\n                margin: 0 .625em;\n                font-size: 1em\n            }\n\n            .swal2-popup.swal2-toast .swal2-loading {\n                justify-content: center\n            }\n\n            .swal2-popup.swal2-toast .swal2-input {\n                height: 2em;\n                margin: .3125em auto;\n                font-size: 1em\n            }\n\n            .swal2-popup.swal2-toast .swal2-validation-message {\n                font-size: 1em\n            }\n\n            .swal2-popup.swal2-toast .swal2-footer {\n                margin: .5em 0 0;\n                padding: .5em 0 0;\n                font-size: .8em\n            }\n\n            .swal2-popup.swal2-toast .swal2-close {\n                position: static;\n                width: .8em;\n                height: .8em;\n                line-height: .8\n            }\n\n            .swal2-popup.swal2-toast .swal2-content {\n                justify-content: flex-start;\n                margin: 0 .625em;\n                padding: 0;\n                font-size: 1em;\n                text-align: initial\n            }\n\n            .swal2-popup.swal2-toast .swal2-html-container {\n                padding: .625em 0 0\n            }\n\n                .swal2-popup.swal2-toast .swal2-html-container:empty {\n                    padding: 0\n                }\n\n            .swal2-popup.swal2-toast .swal2-icon {\n                width: 2em;\n                min-width: 2em;\n                height: 2em;\n                margin: 0 .5em 0 0\n            }\n\n                .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {\n                    display: flex;\n                    align-items: center;\n                    font-size: 1.8em;\n                    font-weight: 700\n                }\n\n        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {\n            .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {\n                font-size: .25em\n            }\n        }\n\n        .swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {\n            width: 2em;\n            height: 2em\n        }\n\n        .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line] {\n            top: .875em;\n            width: 1.375em\n        }\n\n            .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {\n                left: .3125em\n            }\n\n            .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {\n                right: .3125em\n            }\n\n        .swal2-popup.swal2-toast .swal2-actions {\n            flex: 1;\n            flex-basis: auto !important;\n            align-self: stretch;\n            width: auto;\n            height: 2.2em;\n            height: auto;\n            margin: 0 .3125em;\n            margin-top: .3125em;\n            padding: 0\n        }\n\n        .swal2-popup.swal2-toast .swal2-styled {\n            margin: .125em .3125em;\n            padding: .3125em .625em;\n            font-size: 1em\n        }\n\n            .swal2-popup.swal2-toast .swal2-styled:focus {\n                box-shadow: 0 0 0 1px #fff,0 0 0 3px rgba(100,150,200,.5)\n            }\n\n        .swal2-popup.swal2-toast .swal2-success {\n            border-color: #a5dc86\n        }\n\n            .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line] {\n                position: absolute;\n                width: 1.6em;\n                height: 3em;\n                transform: rotate(45deg);\n                border-radius: 50%\n            }\n\n                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left] {\n                    top: -.8em;\n                    left: -.5em;\n                    transform: rotate(-45deg);\n                    transform-origin: 2em 2em;\n                    border-radius: 4em 0 0 4em\n                }\n\n                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right] {\n                    top: -.25em;\n                    left: .9375em;\n                    transform-origin: 0 1.5em;\n                    border-radius: 0 4em 4em 0\n                }\n\n            .swal2-popup.swal2-toast .swal2-success .swal2-success-ring {\n                width: 2em;\n                height: 2em\n            }\n\n            .swal2-popup.swal2-toast .swal2-success .swal2-success-fix {\n                top: 0;\n                left: .4375em;\n                width: .4375em;\n                height: 2.6875em\n            }\n\n            .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line] {\n                height: .3125em\n            }\n\n                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip] {\n                    top: 1.125em;\n                    left: .1875em;\n                    width: .75em\n                }\n\n                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long] {\n                    top: .9375em;\n                    right: .1875em;\n                    width: 1.375em\n                }\n\n            .swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip {\n                -webkit-animation: swal2-toast-animate-success-line-tip .75s;\n                animation: swal2-toast-animate-success-line-tip .75s\n            }\n\n            .swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long {\n                -webkit-animation: swal2-toast-animate-success-line-long .75s;\n                animation: swal2-toast-animate-success-line-long .75s\n            }\n\n        .swal2-popup.swal2-toast.swal2-show {\n            -webkit-animation: swal2-toast-show .5s;\n            animation: swal2-toast-show .5s\n        }\n\n        .swal2-popup.swal2-toast.swal2-hide {\n            -webkit-animation: swal2-toast-hide .1s forwards;\n            animation: swal2-toast-hide .1s forwards\n        }\n\n        .swal2-container {\n            display: flex;\n            position: fixed;\n            z-index: 1060;\n            top: 0;\n            right: 0;\n            bottom: 0;\n            left: 0;\n            flex-direction: row;\n            align-items: center;\n            justify-content: center;\n            padding: .625em;\n            overflow-x: hidden;\n            transition: background-color .1s;\n            -webkit-overflow-scrolling: touch\n        }\n\n            .swal2-container.swal2-backdrop-show, .swal2-container.swal2-noanimation {\n                background: rgba(0,0,0,.4)\n            }\n\n            .swal2-container.swal2-backdrop-hide {\n                background: 0 0 !important\n            }\n\n            .swal2-container.swal2-top {\n                align-items: flex-start\n            }\n\n            .swal2-container.swal2-top-left, .swal2-container.swal2-top-start {\n                align-items: flex-start;\n                justify-content: flex-start\n            }\n\n            .swal2-container.swal2-top-end, .swal2-container.swal2-top-right {\n                align-items: flex-start;\n                justify-content: flex-end\n            }\n\n            .swal2-container.swal2-center {\n                align-items: center\n            }\n\n            .swal2-container.swal2-center-left, .swal2-container.swal2-center-start {\n                align-items: center;\n                justify-content: flex-start\n            }\n\n            .swal2-container.swal2-center-end, .swal2-container.swal2-center-right {\n                align-items: center;\n                justify-content: flex-end\n            }\n\n            .swal2-container.swal2-bottom {\n                align-items: flex-end\n            }\n\n            .swal2-container.swal2-bottom-left, .swal2-container.swal2-bottom-start {\n                align-items: flex-end;\n                justify-content: flex-start\n            }\n\n            .swal2-container.swal2-bottom-end, .swal2-container.swal2-bottom-right {\n                align-items: flex-end;\n                justify-content: flex-end\n            }\n\n                .swal2-container.swal2-bottom-end > :first-child, .swal2-container.swal2-bottom-left > :first-child, .swal2-container.swal2-bottom-right > :first-child, .swal2-container.swal2-bottom-start > :first-child, .swal2-container.swal2-bottom > :first-child {\n                    margin-top: auto\n                }\n\n            .swal2-container.swal2-grow-fullscreen > .swal2-modal {\n                display: flex !important;\n                flex: 1;\n                align-self: stretch;\n                justify-content: center\n            }\n\n            .swal2-container.swal2-grow-row > .swal2-modal {\n                display: flex !important;\n                flex: 1;\n                align-content: center;\n                justify-content: center\n            }\n\n            .swal2-container.swal2-grow-column {\n                flex: 1;\n                flex-direction: column\n            }\n\n                .swal2-container.swal2-grow-column.swal2-bottom, .swal2-container.swal2-grow-column.swal2-center, .swal2-container.swal2-grow-column.swal2-top {\n                    align-items: center\n                }\n\n                .swal2-container.swal2-grow-column.swal2-bottom-left, .swal2-container.swal2-grow-column.swal2-bottom-start, .swal2-container.swal2-grow-column.swal2-center-left, .swal2-container.swal2-grow-column.swal2-center-start, .swal2-container.swal2-grow-column.swal2-top-left, .swal2-container.swal2-grow-column.swal2-top-start {\n                    align-items: flex-start\n                }\n\n                .swal2-container.swal2-grow-column.swal2-bottom-end, .swal2-container.swal2-grow-column.swal2-bottom-right, .swal2-container.swal2-grow-column.swal2-center-end, .swal2-container.swal2-grow-column.swal2-center-right, .swal2-container.swal2-grow-column.swal2-top-end, .swal2-container.swal2-grow-column.swal2-top-right {\n                    align-items: flex-end\n                }\n\n                .swal2-container.swal2-grow-column > .swal2-modal {\n                    display: flex !important;\n                    flex: 1;\n                    align-content: center;\n                    justify-content: center\n                }\n\n            .swal2-container.swal2-no-transition {\n                transition: none !important\n            }\n\n            .swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen) > .swal2-modal {\n                margin: auto\n            }\n\n        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {\n            .swal2-container .swal2-modal {\n                margin: 0 !important\n            }\n        }\n\n        .swal2-popup {\n            display: none;\n            position: relative;\n            box-sizing: border-box;\n            flex-direction: column;\n            justify-content: center;\n            width: 32em;\n            max-width: 100%;\n            padding: 1.25em;\n            border: none;\n            border-radius: 5px;\n            background: #fff;\n            font-family: inherit;\n            font-size: 1rem\n        }\n\n            .swal2-popup:focus {\n                outline: 0\n            }\n\n            .swal2-popup.swal2-loading {\n                overflow-y: hidden\n            }\n\n        .swal2-header {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: 0 1.8em\n        }\n\n        .swal2-title {\n            position: relative;\n            max-width: 100%;\n            margin: 0 0 .4em;\n            padding: 0;\n            color: #595959;\n            font-size: 1.875em;\n            font-weight: 600;\n            text-align: center;\n            text-transform: none;\n            word-wrap: break-word\n        }\n\n        .swal2-actions {\n            display: flex;\n            z-index: 1;\n            box-sizing: border-box;\n            flex-wrap: wrap;\n            align-items: center;\n            justify-content: center;\n            width: 100%;\n            margin: 1.25em auto 0;\n            padding: 0\n        }\n\n            .swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {\n                opacity: .4\n            }\n\n            .swal2-actions:not(.swal2-loading) .swal2-styled:hover {\n                background-image: linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))\n            }\n\n            .swal2-actions:not(.swal2-loading) .swal2-styled:active {\n                background-image: linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))\n            }\n\n        .swal2-loader {\n            display: none;\n            align-items: center;\n            justify-content: center;\n            width: 2.2em;\n            height: 2.2em;\n            margin: 0 1.875em;\n            -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n            animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n            border-width: .25em;\n            border-style: solid;\n            border-radius: 100%;\n            border-color: #2778c4 transparent #2778c4 transparent\n        }\n\n        .swal2-styled {\n            margin: .3125em;\n            padding: .625em 1.1em;\n            box-shadow: none;\n            font-weight: 500\n        }\n\n            .swal2-styled:not([disabled]) {\n                cursor: pointer\n            }\n\n            .swal2-styled.swal2-confirm {\n                border: 0;\n                border-radius: .25em;\n                background: initial;\n                background-color: #2778c4;\n                color: #fff;\n                font-size: 1em\n            }\n\n            .swal2-styled.swal2-deny {\n                border: 0;\n                border-radius: .25em;\n                background: initial;\n                background-color: #d14529;\n                color: #fff;\n                font-size: 1em\n            }\n\n            .swal2-styled.swal2-cancel {\n                border: 0;\n                border-radius: .25em;\n                background: initial;\n                background-color: #757575;\n                color: #fff;\n                font-size: 1em\n            }\n\n            .swal2-styled:focus {\n                outline: 0;\n                box-shadow: 0 0 0 3px rgba(100,150,200,.5)\n            }\n\n            .swal2-styled::-moz-focus-inner {\n                border: 0\n            }\n\n        .swal2-footer {\n            justify-content: center;\n            margin: 1.25em 0 0;\n            padding: 1em 0 0;\n            border-top: 1px solid #eee;\n            color: #545454;\n            font-size: 1em\n        }\n\n        .swal2-timer-progress-bar-container {\n            position: absolute;\n            right: 0;\n            bottom: 0;\n            left: 0;\n            height: .25em;\n            overflow: hidden;\n            border-bottom-right-radius: 5px;\n            border-bottom-left-radius: 5px\n        }\n\n        .swal2-timer-progress-bar {\n            width: 100%;\n            height: .25em;\n            background: rgba(0,0,0,.2)\n        }\n\n        .swal2-image {\n            max-width: 100%;\n            margin: 1.25em auto\n        }\n\n        .swal2-close {\n            position: absolute;\n            z-index: 2;\n            top: 0;\n            right: 0;\n            align-items: center;\n            justify-content: center;\n            width: 1.2em;\n            height: 1.2em;\n            padding: 0;\n            overflow: hidden;\n            transition: color .1s ease-out;\n            border: none;\n            border-radius: 5px;\n            background: 0 0;\n            color: #ccc;\n            font-family: serif;\n            font-size: 2.5em;\n            line-height: 1.2;\n            cursor: pointer\n        }\n\n            .swal2-close:hover {\n                transform: none;\n                background: 0 0;\n                color: #f27474\n            }\n\n            .swal2-close:focus {\n                outline: 0;\n                box-shadow: inset 0 0 0 3px rgba(100,150,200,.5)\n            }\n\n            .swal2-close::-moz-focus-inner {\n                border: 0\n            }\n\n        .swal2-content {\n            z-index: 1;\n            justify-content: center;\n            margin: 0;\n            padding: 0 1.6em;\n            color: #545454;\n            font-size: 1.125em;\n            font-weight: 400;\n            line-height: normal;\n            text-align: center;\n            word-wrap: break-word\n        }\n\n        .swal2-checkbox, .swal2-file, .swal2-input, .swal2-radio, .swal2-select, .swal2-textarea {\n            margin: 1em auto\n        }\n\n        .swal2-file, .swal2-input, .swal2-textarea {\n            box-sizing: border-box;\n            width: 100%;\n            transition: border-color .3s,box-shadow .3s;\n            border: 1px solid #d9d9d9;\n            border-radius: .1875em;\n            background: inherit;\n            box-shadow: inset 0 1px 1px rgba(0,0,0,.06);\n            color: inherit;\n            font-size: 1.125em\n        }\n\n            .swal2-file.swal2-inputerror, .swal2-input.swal2-inputerror, .swal2-textarea.swal2-inputerror {\n                border-color: #f27474 !important;\n                box-shadow: 0 0 2px #f27474 !important\n            }\n\n            .swal2-file:focus, .swal2-input:focus, .swal2-textarea:focus {\n                border: 1px solid #b4dbed;\n                outline: 0;\n                box-shadow: 0 0 0 3px rgba(100,150,200,.5)\n            }\n\n            .swal2-file::-moz-placeholder, .swal2-input::-moz-placeholder, .swal2-textarea::-moz-placeholder {\n                color: #ccc\n            }\n\n            .swal2-file:-ms-input-placeholder, .swal2-input:-ms-input-placeholder, .swal2-textarea:-ms-input-placeholder {\n                color: #ccc\n            }\n\n            .swal2-file::placeholder, .swal2-input::placeholder, .swal2-textarea::placeholder {\n                color: #ccc\n            }\n\n        .swal2-range {\n            margin: 1em auto;\n            background: #fff\n        }\n\n            .swal2-range input {\n                width: 80%\n            }\n\n            .swal2-range output {\n                width: 20%;\n                color: inherit;\n                font-weight: 600;\n                text-align: center\n            }\n\n            .swal2-range input, .swal2-range output {\n                height: 2.625em;\n                padding: 0;\n                font-size: 1.125em;\n                line-height: 2.625em\n            }\n\n        .swal2-input {\n            height: 2.625em;\n            padding: 0 .75em\n        }\n\n            .swal2-input[type=number] {\n                max-width: 10em\n            }\n\n        .swal2-file {\n            background: inherit;\n            font-size: 1.125em\n        }\n\n        .swal2-textarea {\n            height: 6.75em;\n            padding: .75em\n        }\n\n        .swal2-select {\n            min-width: 50%;\n            max-width: 100%;\n            padding: .375em .625em;\n            background: inherit;\n            color: inherit;\n            font-size: 1.125em\n        }\n\n        .swal2-checkbox, .swal2-radio {\n            align-items: center;\n            justify-content: center;\n            background: #fff;\n            color: inherit\n        }\n\n            .swal2-checkbox label, .swal2-radio label {\n                margin: 0 .6em;\n                font-size: 1.125em\n            }\n\n            .swal2-checkbox input, .swal2-radio input {\n                flex-shrink: 0;\n                margin: 0 .4em\n            }\n\n        .swal2-input-label {\n            display: flex;\n            justify-content: center;\n            margin: 1em auto\n        }\n\n        .swal2-validation-message {\n            align-items: center;\n            justify-content: center;\n            margin: 0 -2.7em;\n            padding: .625em;\n            overflow: hidden;\n            background: #f0f0f0;\n            color: #666;\n            font-size: 1em;\n            font-weight: 300\n        }\n\n            .swal2-validation-message::before {\n                content: \"!\";\n                display: inline-block;\n                width: 1.5em;\n                min-width: 1.5em;\n                height: 1.5em;\n                margin: 0 .625em;\n                border-radius: 50%;\n                background-color: #f27474;\n                color: #fff;\n                font-weight: 600;\n                line-height: 1.5em;\n                text-align: center\n            }\n\n        .swal2-icon {\n            position: relative;\n            box-sizing: content-box;\n            justify-content: center;\n            width: 5em;\n            height: 5em;\n            margin: 1.25em auto 1.875em;\n            border: .25em solid transparent;\n            border-radius: 50%;\n            border-color: #000;\n            font-family: inherit;\n            line-height: 5em;\n            cursor: default;\n            -webkit-user-select: none;\n            -moz-user-select: none;\n            -ms-user-select: none;\n            user-select: none\n        }\n\n            .swal2-icon .swal2-icon-content {\n                display: flex;\n                align-items: center;\n                font-size: 3.75em\n            }\n\n            .swal2-icon.swal2-error {\n                border-color: #f27474;\n                color: #f27474\n            }\n\n                .swal2-icon.swal2-error .swal2-x-mark {\n                    position: relative;\n                    flex-grow: 1\n                }\n\n                .swal2-icon.swal2-error [class^=swal2-x-mark-line] {\n                    display: block;\n                    position: absolute;\n                    top: 2.3125em;\n                    width: 2.9375em;\n                    height: .3125em;\n                    border-radius: .125em;\n                    background-color: #f27474\n                }\n\n                    .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {\n                        left: 1.0625em;\n                        transform: rotate(45deg)\n                    }\n\n                    .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {\n                        right: 1em;\n                        transform: rotate(-45deg)\n                    }\n\n                .swal2-icon.swal2-error.swal2-icon-show {\n                    -webkit-animation: swal2-animate-error-icon .5s;\n                    animation: swal2-animate-error-icon .5s\n                }\n\n                    .swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark {\n                        -webkit-animation: swal2-animate-error-x-mark .5s;\n                        animation: swal2-animate-error-x-mark .5s\n                    }\n\n            .swal2-icon.swal2-warning {\n                border-color: #facea8;\n                color: #f8bb86\n            }\n\n            .swal2-icon.swal2-info {\n                border-color: #9de0f6;\n                color: #3fc3ee\n            }\n\n            .swal2-icon.swal2-question {\n                border-color: #c9dae1;\n                color: #87adbd\n            }\n\n            .swal2-icon.swal2-success {\n                border-color: #a5dc86;\n                color: #a5dc86\n            }\n\n                .swal2-icon.swal2-success [class^=swal2-success-circular-line] {\n                    position: absolute;\n                    width: 3.75em;\n                    height: 7.5em;\n                    transform: rotate(45deg);\n                    border-radius: 50%\n                }\n\n                    .swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left] {\n                        top: -.4375em;\n                        left: -2.0635em;\n                        transform: rotate(-45deg);\n                        transform-origin: 3.75em 3.75em;\n                        border-radius: 7.5em 0 0 7.5em\n                    }\n\n                    .swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right] {\n                        top: -.6875em;\n                        left: 1.875em;\n                        transform: rotate(-45deg);\n                        transform-origin: 0 3.75em;\n                        border-radius: 0 7.5em 7.5em 0\n                    }\n\n                .swal2-icon.swal2-success .swal2-success-ring {\n                    position: absolute;\n                    z-index: 2;\n                    top: -.25em;\n                    left: -.25em;\n                    box-sizing: content-box;\n                    width: 100%;\n                    height: 100%;\n                    border: .25em solid rgba(165,220,134,.3);\n                    border-radius: 50%\n                }\n\n                .swal2-icon.swal2-success .swal2-success-fix {\n                    position: absolute;\n                    z-index: 1;\n                    top: .5em;\n                    left: 1.625em;\n                    width: .4375em;\n                    height: 5.625em;\n                    transform: rotate(-45deg)\n                }\n\n                .swal2-icon.swal2-success [class^=swal2-success-line] {\n                    display: block;\n                    position: absolute;\n                    z-index: 2;\n                    height: .3125em;\n                    border-radius: .125em;\n                    background-color: #a5dc86\n                }\n\n                    .swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {\n                        top: 2.875em;\n                        left: .8125em;\n                        width: 1.5625em;\n                        transform: rotate(45deg)\n                    }\n\n                    .swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {\n                        top: 2.375em;\n                        right: .5em;\n                        width: 2.9375em;\n                        transform: rotate(-45deg)\n                    }\n\n                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip {\n                    -webkit-animation: swal2-animate-success-line-tip .75s;\n                    animation: swal2-animate-success-line-tip .75s\n                }\n\n                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long {\n                    -webkit-animation: swal2-animate-success-line-long .75s;\n                    animation: swal2-animate-success-line-long .75s\n                }\n\n                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right {\n                    -webkit-animation: swal2-rotate-success-circular-line 4.25s ease-in;\n                    animation: swal2-rotate-success-circular-line 4.25s ease-in\n                }\n\n        .swal2-progress-steps {\n            flex-wrap: wrap;\n            align-items: center;\n            max-width: 100%;\n            margin: 0 0 1.25em;\n            padding: 0;\n            background: inherit;\n            font-weight: 600\n        }\n\n            .swal2-progress-steps li {\n                display: inline-block;\n                position: relative\n            }\n\n            .swal2-progress-steps .swal2-progress-step {\n                z-index: 20;\n                flex-shrink: 0;\n                width: 2em;\n                height: 2em;\n                border-radius: 2em;\n                background: #2778c4;\n                color: #fff;\n                line-height: 2em;\n                text-align: center\n            }\n\n                .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n                    background: #2778c4\n                }\n\n                    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step {\n                        background: #add8e6;\n                        color: #fff\n                    }\n\n                    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n                        background: #add8e6\n                    }\n\n            .swal2-progress-steps .swal2-progress-step-line {\n                z-index: 10;\n                flex-shrink: 0;\n                width: 2.5em;\n                height: .4em;\n                margin: 0 -1px;\n                background: #2778c4\n            }\n\n        [class^=swal2] {\n            -webkit-tap-highlight-color: transparent\n        }\n\n        .swal2-show {\n            -webkit-animation: swal2-show .3s;\n            animation: swal2-show .3s\n        }\n\n        .swal2-hide {\n            -webkit-animation: swal2-hide .15s forwards;\n            animation: swal2-hide .15s forwards\n        }\n\n        .swal2-noanimation {\n            transition: none\n        }\n\n        .swal2-scrollbar-measure {\n            position: absolute;\n            top: -9999px;\n            width: 50px;\n            height: 50px;\n            overflow: scroll\n        }\n\n        .swal2-rtl .swal2-close {\n            right: auto;\n            left: 0\n        }\n\n        .swal2-rtl .swal2-timer-progress-bar {\n            right: 0;\n            left: auto\n        }\n\n        @supports (-ms-accelerator:true) {\n            .swal2-range input {\n                width: 100% !important\n            }\n\n            .swal2-range output {\n                display: none\n            }\n        }\n\n        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {\n            .swal2-range input {\n                width: 100% !important\n            }\n\n            .swal2-range output {\n                display: none\n            }\n        }\n\n        @-webkit-keyframes swal2-toast-show {\n            0% {\n                transform: translateY(-.625em) rotateZ(2deg)\n            }\n\n            33% {\n                transform: translateY(0) rotateZ(-2deg)\n            }\n\n            66% {\n                transform: translateY(.3125em) rotateZ(2deg)\n            }\n\n            100% {\n                transform: translateY(0) rotateZ(0)\n            }\n        }\n\n        @keyframes swal2-toast-show {\n            0% {\n                transform: translateY(-.625em) rotateZ(2deg)\n            }\n\n            33% {\n                transform: translateY(0) rotateZ(-2deg)\n            }\n\n            66% {\n                transform: translateY(.3125em) rotateZ(2deg)\n            }\n\n            100% {\n                transform: translateY(0) rotateZ(0)\n            }\n        }\n\n        @-webkit-keyframes swal2-toast-hide {\n            100% {\n                transform: rotateZ(1deg);\n                opacity: 0\n            }\n        }\n\n        @keyframes swal2-toast-hide {\n            100% {\n                transform: rotateZ(1deg);\n                opacity: 0\n            }\n        }\n\n        @-webkit-keyframes swal2-toast-animate-success-line-tip {\n            0% {\n                top: .5625em;\n                left: .0625em;\n                width: 0\n            }\n\n            54% {\n                top: .125em;\n                left: .125em;\n                width: 0\n            }\n\n            70% {\n                top: .625em;\n                left: -.25em;\n                width: 1.625em\n            }\n\n            84% {\n                top: 1.0625em;\n                left: .75em;\n                width: .5em\n            }\n\n            100% {\n                top: 1.125em;\n                left: .1875em;\n                width: .75em\n            }\n        }\n\n        @keyframes swal2-toast-animate-success-line-tip {\n            0% {\n                top: .5625em;\n                left: .0625em;\n                width: 0\n            }\n\n            54% {\n                top: .125em;\n                left: .125em;\n                width: 0\n            }\n\n            70% {\n                top: .625em;\n                left: -.25em;\n                width: 1.625em\n            }\n\n            84% {\n                top: 1.0625em;\n                left: .75em;\n                width: .5em\n            }\n\n            100% {\n                top: 1.125em;\n                left: .1875em;\n                width: .75em\n            }\n        }\n\n        @-webkit-keyframes swal2-toast-animate-success-line-long {\n            0% {\n                top: 1.625em;\n                right: 1.375em;\n                width: 0\n            }\n\n            65% {\n                top: 1.25em;\n                right: .9375em;\n                width: 0\n            }\n\n            84% {\n                top: .9375em;\n                right: 0;\n                width: 1.125em\n            }\n\n            100% {\n                top: .9375em;\n                right: .1875em;\n                width: 1.375em\n            }\n        }\n\n        @keyframes swal2-toast-animate-success-line-long {\n            0% {\n                top: 1.625em;\n                right: 1.375em;\n                width: 0\n            }\n\n            65% {\n                top: 1.25em;\n                right: .9375em;\n                width: 0\n            }\n\n            84% {\n                top: .9375em;\n                right: 0;\n                width: 1.125em\n            }\n\n            100% {\n                top: .9375em;\n                right: .1875em;\n                width: 1.375em\n            }\n        }\n\n        @-webkit-keyframes swal2-show {\n            0% {\n                transform: scale(.7)\n            }\n\n            45% {\n                transform: scale(1.05)\n            }\n\n            80% {\n                transform: scale(.95)\n            }\n\n            100% {\n                transform: scale(1)\n            }\n        }\n\n        @keyframes swal2-show {\n            0% {\n                transform: scale(.7)\n            }\n\n            45% {\n                transform: scale(1.05)\n            }\n\n            80% {\n                transform: scale(.95)\n            }\n\n            100% {\n                transform: scale(1)\n            }\n        }\n\n        @-webkit-keyframes swal2-hide {\n            0% {\n                transform: scale(1);\n                opacity: 1\n            }\n\n            100% {\n                transform: scale(.5);\n                opacity: 0\n            }\n        }\n\n        @keyframes swal2-hide {\n            0% {\n                transform: scale(1);\n                opacity: 1\n            }\n\n            100% {\n                transform: scale(.5);\n                opacity: 0\n            }\n        }\n\n        @-webkit-keyframes swal2-animate-success-line-tip {\n            0% {\n                top: 1.1875em;\n                left: .0625em;\n                width: 0\n            }\n\n            54% {\n                top: 1.0625em;\n                left: .125em;\n                width: 0\n            }\n\n            70% {\n                top: 2.1875em;\n                left: -.375em;\n                width: 3.125em\n            }\n\n            84% {\n                top: 3em;\n                left: 1.3125em;\n                width: 1.0625em\n            }\n\n            100% {\n                top: 2.8125em;\n                left: .8125em;\n                width: 1.5625em\n            }\n        }\n\n        @keyframes swal2-animate-success-line-tip {\n            0% {\n                top: 1.1875em;\n                left: .0625em;\n                width: 0\n            }\n\n            54% {\n                top: 1.0625em;\n                left: .125em;\n                width: 0\n            }\n\n            70% {\n                top: 2.1875em;\n                left: -.375em;\n                width: 3.125em\n            }\n\n            84% {\n                top: 3em;\n                left: 1.3125em;\n                width: 1.0625em\n            }\n\n            100% {\n                top: 2.8125em;\n                left: .8125em;\n                width: 1.5625em\n            }\n        }\n\n        @-webkit-keyframes swal2-animate-success-line-long {\n            0% {\n                top: 3.375em;\n                right: 2.875em;\n                width: 0\n            }\n\n            65% {\n                top: 3.375em;\n                right: 2.875em;\n                width: 0\n            }\n\n            84% {\n                top: 2.1875em;\n                right: 0;\n                width: 3.4375em\n            }\n\n            100% {\n                top: 2.375em;\n                right: .5em;\n                width: 2.9375em\n            }\n        }\n\n        @keyframes swal2-animate-success-line-long {\n            0% {\n                top: 3.375em;\n                right: 2.875em;\n                width: 0\n            }\n\n            65% {\n                top: 3.375em;\n                right: 2.875em;\n                width: 0\n            }\n\n            84% {\n                top: 2.1875em;\n                right: 0;\n                width: 3.4375em\n            }\n\n            100% {\n                top: 2.375em;\n                right: .5em;\n                width: 2.9375em\n            }\n        }\n\n        @-webkit-keyframes swal2-rotate-success-circular-line {\n            0% {\n                transform: rotate(-45deg)\n            }\n\n            5% {\n                transform: rotate(-45deg)\n            }\n\n            12% {\n                transform: rotate(-405deg)\n            }\n\n            100% {\n                transform: rotate(-405deg)\n            }\n        }\n\n        @keyframes swal2-rotate-success-circular-line {\n            0% {\n                transform: rotate(-45deg)\n            }\n\n            5% {\n                transform: rotate(-45deg)\n            }\n\n            12% {\n                transform: rotate(-405deg)\n            }\n\n            100% {\n                transform: rotate(-405deg)\n            }\n        }\n\n        @-webkit-keyframes swal2-animate-error-x-mark {\n            0% {\n                margin-top: 1.625em;\n                transform: scale(.4);\n                opacity: 0\n            }\n\n            50% {\n                margin-top: 1.625em;\n                transform: scale(.4);\n                opacity: 0\n            }\n\n            80% {\n                margin-top: -.375em;\n                transform: scale(1.15)\n            }\n\n            100% {\n                margin-top: 0;\n                transform: scale(1);\n                opacity: 1\n            }\n        }\n\n        @keyframes swal2-animate-error-x-mark {\n            0% {\n                margin-top: 1.625em;\n                transform: scale(.4);\n                opacity: 0\n            }\n\n            50% {\n                margin-top: 1.625em;\n                transform: scale(.4);\n                opacity: 0\n            }\n\n            80% {\n                margin-top: -.375em;\n                transform: scale(1.15)\n            }\n\n            100% {\n                margin-top: 0;\n                transform: scale(1);\n                opacity: 1\n            }\n        }\n\n        @-webkit-keyframes swal2-animate-error-icon {\n            0% {\n                transform: rotateX(100deg);\n                opacity: 0\n            }\n\n            100% {\n                transform: rotateX(0);\n                opacity: 1\n            }\n        }\n\n        @keyframes swal2-animate-error-icon {\n            0% {\n                transform: rotateX(100deg);\n                opacity: 0\n            }\n\n            100% {\n                transform: rotateX(0);\n                opacity: 1\n            }\n        }\n\n        @-webkit-keyframes swal2-rotate-loading {\n            0% {\n                transform: rotate(0)\n            }\n\n            100% {\n                transform: rotate(360deg)\n            }\n        }\n\n        @keyframes swal2-rotate-loading {\n            0% {\n                transform: rotate(0)\n            }\n\n            100% {\n                transform: rotate(360deg)\n            }\n        }\n\n        body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {\n            overflow: hidden\n        }\n\n        body.swal2-height-auto {\n            height: auto !important\n        }\n\n        body.swal2-no-backdrop .swal2-container {\n            top: auto;\n            right: auto;\n            bottom: auto;\n            left: auto;\n            max-width: calc(100% - .625em * 2);\n            background-color: transparent !important\n        }\n\n            body.swal2-no-backdrop .swal2-container > .swal2-modal {\n                box-shadow: 0 0 10px rgba(0,0,0,.4)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-top {\n                top: 0;\n                left: 50%;\n                transform: translateX(-50%)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-top-left, body.swal2-no-backdrop .swal2-container.swal2-top-start {\n                top: 0;\n                left: 0\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-top-end, body.swal2-no-backdrop .swal2-container.swal2-top-right {\n                top: 0;\n                right: 0\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-center {\n                top: 50%;\n                left: 50%;\n                transform: translate(-50%,-50%)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-center-left, body.swal2-no-backdrop .swal2-container.swal2-center-start {\n                top: 50%;\n                left: 0;\n                transform: translateY(-50%)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-center-end, body.swal2-no-backdrop .swal2-container.swal2-center-right {\n                top: 50%;\n                right: 0;\n                transform: translateY(-50%)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-bottom {\n                bottom: 0;\n                left: 50%;\n                transform: translateX(-50%)\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-bottom-left, body.swal2-no-backdrop .swal2-container.swal2-bottom-start {\n                bottom: 0;\n                left: 0\n            }\n\n            body.swal2-no-backdrop .swal2-container.swal2-bottom-end, body.swal2-no-backdrop .swal2-container.swal2-bottom-right {\n                right: 0;\n                bottom: 0\n            }\n\n        @media print {\n            body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {\n                overflow-y: scroll !important\n            }\n\n                body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) > [aria-hidden=true] {\n                    display: none\n                }\n\n                body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {\n                    position: static !important\n                }\n        }\n\n        body.swal2-toast-shown .swal2-container {\n            background-color: transparent\n        }\n\n            body.swal2-toast-shown .swal2-container.swal2-top {\n                top: 0;\n                right: auto;\n                bottom: auto;\n                left: 50%;\n                transform: translateX(-50%)\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-top-end, body.swal2-toast-shown .swal2-container.swal2-top-right {\n                top: 0;\n                right: 0;\n                bottom: auto;\n                left: auto\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-top-left, body.swal2-toast-shown .swal2-container.swal2-top-start {\n                top: 0;\n                right: auto;\n                bottom: auto;\n                left: 0\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-center-left, body.swal2-toast-shown .swal2-container.swal2-center-start {\n                top: 50%;\n                right: auto;\n                bottom: auto;\n                left: 0;\n                transform: translateY(-50%)\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-center {\n                top: 50%;\n                right: auto;\n                bottom: auto;\n                left: 50%;\n                transform: translate(-50%,-50%)\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-center-end, body.swal2-toast-shown .swal2-container.swal2-center-right {\n                top: 50%;\n                right: 0;\n                bottom: auto;\n                left: auto;\n                transform: translateY(-50%)\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-bottom-left, body.swal2-toast-shown .swal2-container.swal2-bottom-start {\n                top: auto;\n                right: auto;\n                bottom: 0;\n                left: 0\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-bottom {\n                top: auto;\n                right: auto;\n                bottom: 0;\n                left: 50%;\n                transform: translateX(-50%)\n            }\n\n            body.swal2-toast-shown .swal2-container.swal2-bottom-end, body.swal2-toast-shown .swal2-container.swal2-bottom-right {\n                top: auto;\n                right: 0;\n                bottom: 0;\n                left: auto\n            }\n    </style>\n    <style id=\"panai-style\">\n        .panai-container {\n            z-index: 99999 !important\n        }\n\n        .panai-popup {\n            font-size: 14px !important\n        }\n\n        .panai-setting-label {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            padding-top: 20px;\n        }\n\n        .panai-setting-checkbox {\n            width: 16px;\n            height: 16px;\n        }\n    </style>\n</head>\n<body marginwidth=\"0\" marginheight=\"0\" style=\"zoom: 1;\">\n    <div>\n\n        <div class=\"camscanner_banner index_card\">\n            <div class=\"bd\">\n                <ul style=\"position: relative; width: 1886px; height: 400px;\">\n                    <li style=\"background-image: url(&quot;https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/bg3.jpg&quot;); position: absolute; width: 1886px; left: 0px; top: 0px; opacity: 0.9975;\">\n                        <div class=\"warp\">\n                        </div>\n                    </li>\n                    <li style=\"background-image: url(&quot;https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/banner1.png&quot;); position: absolute; width: 1886px; left: 0px; top: 0px; display: none;\">\n                        <div class=\"warp\">\n                        </div>\n                    </li>\n                    <li style=\"background-image: url(&quot;https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/image/camscanner_banner.png&quot;); position: absolute; width: 1886px; left: 0px; top: 0px; display: none;\">\n                        <div class=\"warp\">\n                        </div>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"hd\">\n                <ul><li class=\"on\">1</li><li>2</li><li>3</li></ul>\n            </div>\n        </div>\n        <script>jQuery(\".camscanner_banner\").slide({ titCell: \".hd ul\", mainCell: \".bd ul\", effect: \"fold\", autoPlay: true, autoPage: true, trigger: \"click\", delayTime: 1000, interTime: 5000 });</script>\n        <!-- the banner end -->\n\n        <!-- 应用场景 -->\n        <div class=\"camscanner_menu\" id=\"tip\">\n            <div class=\"warp\">\n                <ul class=\"fl\" style=\"width: 650px;\">\n                    <li style=\"width: 25%; margin-right: 0;\"><a class=\"a_anli\">アプリケーション・シナリオ</a></li>\n                    <li style=\"width: 25%; margin-right: 0;\"><a class=\"a_gongneng\">機能紹介</a></li>\n                    <li style=\"width: 25%; margin-right: 0;\"><a class=\"a_fangan\">ユーザーレビュー:</a></li>\n                    <li style=\"width: 25%; margin-right: 0;\"><a class=\"a_ccc\">報道</a></li>\n                </ul>\n                <ul class=\"fr\" style=\"height: 46px; margin-top: 8px;\">\n                    <li class=\"style\" style=\"line-height: 40px; width: 100%;\"><a href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/cdn/update/Setup.exe\" style=\"height: 40px; width: 100%;\"><b>今すぐダウンロード</b></a></li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"main warp a_anli_content\">\n            <div class=\"sales_x\">\n                <div class=\"biaoti\">\n                    <p class=\"tit mtit\">CTスキャナー、いつでも記録、簡単に共有</p>\n                    <p class=\"info minfo\">「コンピューターに最適な100のソフトウェアの1つ」</p>\n                </div>\n                <dl class=\"d1\">\n                    <dd class=\"pic\">\n                        <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.001.png\" alt=\"写真\"></dd>\n                    <dt>さまざまなシナリオを簡単に処理</dt>\n                    <span class=\"info\">サラリーマンはそれを非常に必要としており、グラフィックやテキストの変換を処理するのに非常に便利です</span>\n                </dl>\n                <dl class=\"d1\">\n                    <dd class=\"pic\">\n                        <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.002.png\" alt=\"写真\"></dd>\n                    <dt>自動画像認識前処理</dt>\n                    <span class=\"info\">画像スキャンにより、シャープニング、ブライトニング、チルト補正が自動的に生成されます</span>\n                </dl>\n                <dl class=\"d1 kkel\">\n                    <dd class=\"pic\">\n                        <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.003.png\" alt=\"写真\"></dd>\n                    <dt>酸っぱい手にタイプするクラスメートのためのアーティファクト</dt>\n                    <span class=\"info\">OCR認識、画像は即座にテキストになり、痛い手へのタイピングに別れを告げます</span>\n                </dl>\n            </div>\n        </div>\n        <!-- 产品优势 end -->\n        <div class=\"clear\"></div>\n        <div class=\"advantage a_gongneng_content\">\n            <div class=\"tu_a\">\n                <div class=\"warp\">\n                    <div class=\"ccb_tr\">\n                        <div class=\"ccb_rt_a\">AI-インテリジェントOCR認識</div>\n                        <div class=\"ccb_rt_b\">\n                            <span>スクリーンショット/写真/ファイルの撮影、ワンクリック操作、ディープアクセラレーション<br>\n100+言語の認識をサポートし、グローバルなビジネス開発を支援します<br>\nアカウント特権、クラウドストレージ、無制限のデバイス、無制限の時間。<br>\n旅行/オフサイト、いつでもログイン、いつでも使用できます!<br>\n                            </span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"tu_e\">\n                <div class=\"warp\">\n                    <div class=\"ccb_tr linx\">\n                        <div class=\"ccb_rt_a\">\nあらゆる種類のドキュメントを簡単に操作できます\n                         </div>\n                        <div class=\"ccb_rt_b\">\n                            <span>サポートOfficeファミリーバケット(Word、PPT、Excelなど)<br>\nPDFドキュメントのスキャンと変換をサポート<br>\n全文スキャンと全文翻訳がサポートされています<br>\nさまざまなオフィスドキュメントのインテリジェントな分析、および処理結果のワンクリックダウンロード\n                             </span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"tu_b\">\n                <div class=\"warp\">\n                    <div class=\"ccb_lr\">\n                        <div class=\"ccb_rt_a\">クラウド上のOCR、ライトサービス</div>\n                        <div class=\"ccb_rt_b\">\n                            <span>アシスタントの高性能サーバーに依存しているため、ユーザーは1つのアカウントしか必要としません<br>\n様々な大手メーカーのサービスを簡単にお楽しみいただけます<br>\n星は月を保持し、仕事と生活の効率を向上させるためだけに!<br>\n                            </span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"advantage a_fangan_content\">\n            <div class=\"ying camying\">\n                <!-- 用户评价 -->\n                <div class=\"user\">\n                    <p class=\"titx\">ユーザーレビュー:</p>\n                    <div class=\"user_pj\">\n                        <div class=\"user_pj_a\">\n                            <div class=\"bd bd_x\">\n                                <div class=\"tempWrap\" style=\"overflow: hidden; position: relative; width: 1021px\">\n                                    <div class=\"tempWrap\" style=\"overflow:hidden; position:relative; width:1021px\"><ul style=\"width: 4084px; left: -2042px; position: relative; overflow: hidden; padding: 0px; margin: 0px;\">\n                                        <li style=\"float: left; width: 1021px;\"><span>仕事の理由は、よくモノをスキャンしてお客様にメールを送ることであり、それらを利用する機会はたくさんあります。 OCRアシスタント自動認識、カットが簡単、キーは互いに変換するためのさまざまな形式を持つことができます、それはちょうど空に対してです、ああああ、本当にお勧めします!!</span></li>\n                                        <li style=\"float: left; width: 1021px;\"><span>特に監査人が使用するのに適しており、画像処理後の効果は良好で明確であることが多く、これは推奨する価値があります。 アカウントを確認してバウチャーをめくると、知る限り、OCRアシスタントがいるととても便利です~</span></li>\n                                        <li style=\"float: left; width: 1021px;\"><span>外来診療所の日々の手書きの医療記録や処方箋は、簡単にデジタル画像に変換してOCRアシスタントで保存でき、デジタルシステムのない小規模な外来診療所でも症例や処方箋の話し合いが簡単に実現できます。 欠点もありますが、優れたソフトウェアです。</span></li>\n                                        <li style=\"float: left; width: 1021px;\"><span>学生パーティーにとても使いやすく、本の要点をOCRで特定して植字して印刷したり、図書館で論文を書いたり資料の写真を撮ったりするのに使うことができるので、とても気に入っています。 多くの学生におすすめ~</span></li>\n                                    </ul></div>\n                                </div>\n                            </div>\n\n                            <div class=\"hd bd_xe\">\n                                <ul>\n                                    <li class=\"\">\n                                        <span class=\"hd_ax\">\n                                            <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.005.png\" alt=\"サラリーマン\"></span>\n                                        <span class=\"hd_bx\">\n                                            <span class=\"hd_bx_a\">John</span>\n                                            <span class=\"hd_bx_b\">サラリーマン</span>\n                                        </span>\n                                    </li>\n                                    <li class=\"\">\n                                        <span class=\"hd_ax\">\n                                            <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.006.png\" alt=\"監査\"></span>\n                                        <span class=\"hd_bx\">\n                                            <span class=\"hd_bx_a\">アビー</span>\n                                            <span class=\"hd_bx_b\">監査</span>\n                                        </span>\n                                    </li>\n                                    <li class=\"on\">\n                                        <span class=\"hd_ax\">\n                                            <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.007.png\" alt=\"医者\"></span>\n                                        <span class=\"hd_bx\">\n                                            <span class=\"hd_bx_a\">漢</span>\n                                            <span class=\"hd_bx_b\">医者</span>\n                                        </span>\n                                    </li>\n                                    <li class=\"\">\n                                        <span class=\"hd_ax\">\n                                            <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/cam.008.png\" alt=\"学生\"></span>\n                                        <span class=\"hd_bx\">\n                                            <span class=\"hd_bx_a\">ミア</span>\n                                            <span class=\"hd_bx_b\">学生</span>\n                                        </span>\n                                    </li>\n                                </ul>\n                            </div>\n                            <script type=\"text/javascript\">jQuery(\".user_pj_a\").slide({ mainCell: \".bd ul\", effect: \"left\", autoPlay: true });</script>\n\n                        </div>\n                    </div>\n                </div>\n                <!-- 用户评价 end -->\n            </div>\n        </div>\n\n        <div class=\"media warp camsmedia a_ccc_content\" style=\"margin-top: 80px;\">\n            <p class=\"tit\">報道</p>\n            <div class=\"news_bd\">\n                <div class=\"slideBoxx\">\n                    <div class=\"bd\">\n                        <ul>\n                            <li style=\"display: none;\">\n                                <span class=\"news_bd_a\">\n                                    <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c103.png\"></span>\n                                <span class=\"news_bd_b\"><a href=\"javascript:;?t=1703579806708\">コンピューター用の50の最高のソフトウェアの1つ</a></span>\n                            </li>\n                            <li style=\"display: none;\">\n                                <span class=\"news_bd_a\">\n                                    <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c102.png\"></span>\n                                <span class=\"news_bd_b\"><a href=\"javascript:;?t=1703579806708\">OCRアシスタント、スーパースキャナー</a></span>\n                            </li>\n                            <li style=\"display: list-item;\">\n                                <span class=\"news_bd_a\">\n                                    <img src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/picture/c100.png\"></span>\n                                <span class=\"news_bd_b\"><a href=\"javascript:;?t=1703579806708\">サラリーマンに必須の10のアプリ、OCRアシスタントがリストに載っています</a></span>\n                            </li>\n                        </ul>\n                    </div>\n                    <div class=\"h36\"></div>\n                    <div class=\"hd\">\n                        <ul>\n                            <li class=\"\"></li>\n                            <li class=\"\"></li>\n                            <li class=\"on\"></li>\n                        </ul>\n                    </div>\n                    <!-- 下面是前/后按钮代码，如果不需要删除即可 -->\n                </div>\n                <script type=\"text/javascript\">\n                    jQuery(\".slideBoxx\").slide({\n                        mainCell: \".bd ul\",\n                        autoPlay: true\n                    });\n                </script>\n            </div>\n        </div>\n    </div>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/wp-embed.min.js\"></script>\n    <script type=\"text/javascript\" src=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/static/v1/js/common_new.js\"></script>\n\n\n<div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script type=\"text/javascript\">isNeedTrans=false;</script><script src=\"/static/js/translate.js\"></script></body></html>"}