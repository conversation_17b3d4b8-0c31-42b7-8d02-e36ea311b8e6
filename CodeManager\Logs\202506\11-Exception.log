﻿2025-06-11 17:07:55,630 [10] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:07:55
2025-06-11 17:10:00,776 [8] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:10:00
2025-06-11 17:11:24,796 [8] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:11:24
2025-06-11 17:18:32,063 [8] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:18:32
2025-06-11 17:30:04,117 [133] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:30:04
2025-06-11 17:30:44,499 [125] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:30:44
2025-06-11 17:31:30,039 [139] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:31:30
2025-06-11 17:32:06,225 [139] ERROR Application_End 
 - 【POD挂了】 时间:2025-06-11 17:32:06
2025-06-11 17:33:12,776 [10] ERROR CommonLog 
 - 发起支付入参：price:, orderNo:, remark:3000次请求API, account:***********
2025-06-11 17:35:47,563 [10] ERROR Application_Error 
 -  URL:http://localhost:19225/code.aspx?op=pay&remark=3000次请求API&account=***********
System.Web.HttpUnhandledException (0x80004005): 引发类型为“System.Web.HttpUnhandledException”的异常。 ---> System.Exception: SQL logic error
table pay_order has no column named source ---> System.Data.SQLite.SQLiteException: SQL logic error
table pay_order has no column named source
   在 System.Data.SQLite.SQLite3.Prepare(SQLiteConnection cnn, String strSql, SQLiteStatement previous, UInt32 timeoutMS, String& strRemain)
   在 System.Data.SQLite.SQLiteCommand.BuildNextCommand()
   在 System.Data.SQLite.SQLiteDataReader.NextResult()
   在 System.Data.SQLite.SQLiteDataReader..ctor(SQLiteCommand cmd, CommandBehavior behave)
   在 System.Data.SQLite.SQLiteCommand.ExecuteReader(CommandBehavior behavior)
   在 System.Data.SQLite.SQLiteCommand.ExecuteNonQuery(CommandBehavior behavior)
   在 FreeSql.Internal.CommonProvider.AdoProvider.ExecuteNonQuery(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- 内部异常堆栈跟踪的结尾 ---
   在 FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   在 FreeSql.Internal.CommonProvider.AdoProvider.ExecuteNonQuery(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   在 FreeSql.Internal.CommonProvider.AdoProvider.ExecuteNonQuery(String cmdText, Object parms)
   在 Account.Web.PayHelper.CreateOrder(PayOrderEntity code) 位置 D:\Code\Code\CodeManager\CodeManager\pay\PayHelper.cs:行号 29
   在 Account.Web.pay.NewPayUtil.createOrder(String payId, String param, String remark, String price, OrderFrom from) 位置 D:\Code\Code\CodeManager\CodeManager\pay\NewPayUtil.cs:行号 101
   在 Account.Web.PayUtil.GetPayUrl(String price, String orderNo, String remark, String account, OrderFrom from, Int32 count) 位置 D:\Code\Code\CodeManager\CodeManager\pay\PayUtil.cs:行号 266
   在 Account.Web.Code.DoOperate(String strOP) 位置 D:\Code\Code\CodeManager\CodeManager\Code.aspx.cs:行号 76
   在 Account.Web.Code.Page_Load(Object sender, EventArgs e) 位置 D:\Code\Code\CodeManager\CodeManager\Code.aspx.cs:行号 22
   在 System.Web.UI.Control.OnLoad(EventArgs e)
   在 System.Web.UI.Control.LoadRecursive()
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.HandleError(Exception e)
   在 System.Web.UI.Page.ProcessRequestMain(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest(Boolean includeStagesBeforeAsyncPoint, Boolean includeStagesAfterAsyncPoint)
   在 System.Web.UI.Page.ProcessRequest()
   在 System.Web.UI.Page.ProcessRequest(HttpContext context)
   在 ASP.code_aspx.ProcessRequest(HttpContext context) 位置 c:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\57c60760\d8703dae\App_Web_qgy0ohnr.10.cs:行号 0
   在 System.Web.HttpApplication.CallHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-06-11 17:35:57,773 [64] ERROR CommonLog 
 - 发起支付入参：price:, orderNo:, remark:3000次请求API, account:***********
2025-06-11 17:35:57,858 [64] ERROR CommonLog 
 - 发起支付结果 ,OrderId:2025061117355742521337,Url:http://ocr.oldfish.cn/ToPay.aspx?orderId=2025061117355742521337,重试次数:1
2025-06-11 17:38:00,433 [10] ERROR CommonLog 
 - 发起支付入参：price:, orderNo:, remark:1000次请求API, account:***********
2025-06-11 17:41:49,056 [12] ERROR CommonLog 
 - 发起支付入参：price:129, orderNo:, remark:3000次请求API, account:***********
2025-06-11 17:41:55,211 [12] ERROR CommonLog 
 - 发起支付结果 ,OrderId:2025061117415477483110,Url:http://ocr.oldfish.cn/ToPay.aspx?orderId=2025061117415477483110,重试次数:1
