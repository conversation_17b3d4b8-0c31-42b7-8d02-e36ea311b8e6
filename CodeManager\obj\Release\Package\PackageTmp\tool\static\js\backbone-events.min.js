!function(){var t,e=Array.prototype.forEach,n=Object.prototype.hasOwnProperty,i=Array.prototype.slice,r=0,s={keys:Object.keys||function(t){if("object"!=typeof t&&"function"!=typeof t||null===t)throw new TypeError("keys() called on a non-object");var e,n=[];for(e in t)t.hasOwnProperty(e)&&(n[n.length]=e);return n},uniqueId:function(t){var e=++r+"";return t?t+e:e},has:function(t,e){return n.call(t,e)},each:function(t,n,i){if(null!=t)if(e&&t.forEach===e)t.forEach(n,i);else if(t.length===+t.length)for(var r=0,s=t.length;s>r;r++)n.call(i,t[r],r,t);else for(var o in t)this.has(t,o)&&n.call(i,t[o],o,t)},once:function(t){var e,n=!1;return function(){return n?e:(n=!0,e=t.apply(this,arguments),t=null,e)}}};t={on:function(t,e,n){return c(this,"on",t,[e,n])&&e?(this._events||(this._events={}),(this._events[t]||(this._events[t]=[])).push({callback:e,context:n,ctx:n||this}),this):this},once:function(t,e,n){if(!c(this,"once",t,[e,n])||!e)return this;var i=this,r=s.once(function(){i.off(t,r),e.apply(this,arguments)});return r._callback=e,this.on(t,r,n)},off:function(t,e,n){var i,r,o,l,f,a,h,u;if(!this._events||!c(this,"off",t,[e,n]))return this;if(!t&&!e&&!n)return this._events={},this;for(f=0,a=(l=t?[t]:s.keys(this._events)).length;a>f;f++)if(t=l[f],o=this._events[t]){if(this._events[t]=i=[],e||n)for(h=0,u=o.length;u>h;h++)r=o[h],(e&&e!==r.callback&&e!==r.callback._callback||n&&n!==r.context)&&i.push(r);i.length||delete this._events[t]}return this},trigger:function(t){if(!this._events)return this;var e=i.call(arguments,1);if(!c(this,"trigger",t,e))return this;var n=this._events[t],r=this._events.all;return n&&l(n,e),r&&l(r,arguments),this},stopListening:function(t,e,n){var i=this._listeners;if(!i)return this;var r=!e&&!n;for(var s in"object"==typeof e&&(n=this),t&&((i={})[t._listenerId]=t),i)i[s].off(e,n,this),r&&delete this._listeners[s];return this}};var o=/\s+/,c=function(t,e,n,i){if(!n)return!0;if("object"==typeof n){for(var r in n)t[e].apply(t,[r,n[r]].concat(i));return!1}if(o.test(n)){for(var s=n.split(o),c=0,l=s.length;l>c;c++)t[e].apply(t,[s[c]].concat(i));return!1}return!0},l=function(t,e){var n,i=-1,r=t.length,s=e[0],o=e[1],c=e[2];switch(e.length){case 0:for(;++i<r;)(n=t[i]).callback.call(n.ctx);return;case 1:for(;++i<r;)(n=t[i]).callback.call(n.ctx,s);return;case 2:for(;++i<r;)(n=t[i]).callback.call(n.ctx,s,o);return;case 3:for(;++i<r;)(n=t[i]).callback.call(n.ctx,s,o,c);return;default:for(;++i<r;)(n=t[i]).callback.apply(n.ctx,e)}};s.each({listenTo:"on",listenToOnce:"once"},function(e,n){t[n]=function(t,n,i){return(this._listeners||(this._listeners={}))[t._listenerId||(t._listenerId=s.uniqueId("l"))]=t,"object"==typeof n&&(i=this),t[e](n,i,this),this}}),t.bind=t.on,t.unbind=t.off,t.mixin=function(t){return s.each(["on","once","off","trigger","stopListening","listenTo","listenToOnce","bind","unbind"],function(e){t[e]=this[e]},this),t},"undefined"!=typeof exports?("undefined"!=typeof module&&module.exports&&(exports=module.exports=t),exports.BackboneEvents=t):"function"==typeof define&&"object"==typeof define.amd?define(function(){return t}):this.BackboneEvents=t}();