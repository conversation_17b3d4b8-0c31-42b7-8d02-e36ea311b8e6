using Account.Web.Common;
using CommonLib;

namespace Account.Web.Common
{
    /// <summary>
    /// 统一的密码服务
    /// </summary>
    public static class PasswordService
    {
        /// <summary>
        /// 验证密码格式
        /// </summary>
        public static bool ValidatePassword(string password)
        {
            return AuthHelper.IsValidPassword(password);
        }
        
        /// <summary>
        /// 加密密码
        /// </summary>
        public static string EncryptPassword(string password)
        {
            return CryptoService.EncryptUserPassword(password);
        }
        
        /// <summary>
        /// 验证密码是否匹配
        /// </summary>
        public static bool VerifyPassword(string inputPassword, string storedPassword)
        {
            var encryptedInput = EncryptPassword(inputPassword);
            return Equals(encryptedInput, storedPassword);
        }
        
        /// <summary>
        /// 更新用户密码
        /// </summary>
        public static bool UpdateUserPassword(string account, string newPassword)
        {
            var encryptedPassword = EncryptPassword(newPassword);
            return CodeHelper.UpdateCodePwd(account, encryptedPassword);
        }
    }
}
