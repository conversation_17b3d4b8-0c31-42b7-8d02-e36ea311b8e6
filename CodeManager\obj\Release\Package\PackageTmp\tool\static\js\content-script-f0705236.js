window.qrcodeContentScript=function(){return{decode:function(t){var e;(e=t,new Promise(t=>{let n=new Image;n.setAttribute("crossOrigin","Anonymous"),n.src=e,n.onload=function(){let e=this.naturalWidth,o=this.naturalHeight,i=document.createElement("canvas");i.style.cssText="position:absolute;top:-10000px;left:-10000px",document.body.appendChild(i),i.setAttribute("id","qr-canvas"),i.height=o+100,i.width=e+100;let r=i.getContext("2d");r.fillStyle="rgb(255,255,255)",r.fillRect(0,0,i.width,i.height),r.drawImage(n,0,0,e,o,50,50,e,o),t(i.toDataURL())},n.onerror=function(){t(e)}})).then(e=>{chrome.runtime.sendMessage({type:"fh-dynamic-any-thing",params:{uri:e||t},func:((t,e)=>(chrome.DynamicToolRunner({withContent:t.uri,query:"tool=qr-code&mode=decode"}),e&&e(),!0)).toString()})})}}};