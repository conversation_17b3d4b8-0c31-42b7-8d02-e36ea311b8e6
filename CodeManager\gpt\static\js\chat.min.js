import {
    micromark
} from "./micromark.bundle.js";
marked.setOptions({
    gfm: true,
    tables: true,
    breaks: true,
    pedantic: false,
    sanitize: false,
    smartLists: true,
    smartypants: false,
    highlight: function (e) {
        return hljs.highlightAuto(e).value
    }
});

window.dialogReload = e => {
    $(".send-view").hide();
    $(".send-loading").css("display", "flex");
    window.chat_status = "wating";
    let t = $("#" + e).html();
    let i = "<div class='loading'><span></span><span></span><span></span><span></span><span></span></div>";
    $("#" + e).html(i);
    let n = getUuid();
    $("#" + e).attr("id", n);
    CHAT_API.chatRetry(e, n, e);
};
var strOpenAiStartStr = '","text":"';
window.dialogOutput = (i, a, status) => {
    let s = "未响应，请重新输入";
    if (!i) {
        s = "Request Failed"
    } else if (i && (i.result == 1 || i.result == 2)) {
        if (i.text.indexOf(strOpenAiStartStr) > 0) {
            var startIndex = i.text.lastIndexOf(`
`, i.text.length - 2);
            if (startIndex > 0) {
                i.text = i.text.substring(startIndex).trim();
                console.log(i.text);
                i.text = JSON.parse(i.text).text;
            }
        }
        s = i.text
    }
    let n = $("#" + a);
    if (i && i.type == 1 && i.result == 1) {
        n.html("<div id='dialog-img-" + a + "'><img class='dialog-img' onerror='imgError(" + a + ")' src='" + s +
            "'></div>")
    } else {
        if (status == 4) {
            if (i && i.result != 1 && i.dialogId) {
                s += "<i class='fa fa-refresh' onclick='dialogReload(\"" + a + "\")'></i>"
            }
            if (i && i.result == 1) {
                s += "<div data-dialogId='" + a + "' class='copy-view btn-copy' data-clipboard-text=''><i class='fa fa-copy'></i><span>Copy</span></div>";
                s += "<div id='copied_" + a + "' class='copy-view copied-view'><i class='fa fa-check'></i><span>Copied</span></div>"
            }
        } else {
            s = s + "|";
        }
        try {
            s = marked.marked(s)
        }
        catch(oe) { }
        n.html(s);
        if (status == 4) {
            $('div[data-dialogid*="' + a + '"]').attr('data-clipboard-text', i.text.replaceAll("```", ""));
        }
    }
    $(document).scrollTop(document.body.scrollHeight)
};

function initTip() {
    var e = $("#tip");
    e.show();
    var t = "你好，我是OCR助手的好朋友ChatGPT，是一款聊天式人工智能（AI）助手。我基于GPT-3.5强大的自然语言处理技术，可以随时随地为您提供准确、有价值的答案和建议，帮助您提高工作效率和生活质量，快来体验吧！";
    var i = 0;
    var a = setInterval(function () {
        e.html(t.slice(0, i) + "|");
        i++;
        if (i > t.length) {
            e.html(t.slice(0, i));
            clearInterval(a)
        }
    }, 100)
    $(".send-view").css("display", "flex");
    $(".send-loading").hide()
}

function getPlatform() {
    var e = navigator.userAgent,
        t = /(?:Windows Phone)/.test(e),
        i = /(?:SymbianOS)/.test(e) || t,
        a = /(?:Android)/.test(e),
        s = /(?:Firefox)/.test(e),
        n = /(?:iPad|PlayBook)/.test(e) || a && !/(?:Mobile)/.test(e) || s && /(?:Tablet)/.test(e),
        o = !!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
        l = !o && !a && !i,
        d = /(?:Safari)/.test(e),
        r = /(?:micromessenger)/.test(e.toLowerCase());
    return {
        isTablet: n,
        isIos: o,
        isAndroid: a,
        isPc: l,
        isSafari: d,
        isWeixin: r
    }
}

function random(e, t) {
    return Math.floor(Math.random() * (t - e)) + e
}
window.getUuid = () => {
    return Number((new Date).getTime() + "" + random(100, 1e3))
};

function imgError(e) {
    $("#dialog-img-" + e).html("<p class='img-error-text'>图片已过期或已被清理</p>")
}
$.preloadImages = function () {
    for (var e = 0; e < arguments.length; e++) {
        var t = $("<img />").attr("src", arguments[e])
    }
};
$.preloadImages("img/my_default.png", "img/sys_default.png");

$(function () {
    if (!getPlatform().isPc) {
        $(".voice-start").css("display", "flex")
    }
    $("input").keydown(function () {
        if (event.keyCode == 13) {
            send()
        }
    });
    $("input").bind("input", function () {
        if ($.trim($(this).val())) {
            $(".fa-send-o").hide();
            $(".fa-send").show()
        } else {
            $(".fa-send").hide();
            $(".fa-send-o").show()
        }
    });
    $(".btn-send").click(function () {
        send()
    });
    if (document.URL.indexOf("?debug") > 0) {
        new VConsole
    }
    var e = new ClipboardJS(".btn-copy");
    e.on("success", function (e) {
        console.info("Text:", e.text);
        e.clearSelection();
        let t = e.trigger.dataset.dialogid;
        e.trigger.style.display = "none";
        $("#copied_" + t).show();
        setTimeout(() => {
            e.trigger.style.display = "block";
            $("#copied_" + t).hide()
        }, 1500)
    });
    e.on("error", function (e) {
        console.error("Action:", e.action);
        console.error("Trigger:", e.trigger)
    });
});