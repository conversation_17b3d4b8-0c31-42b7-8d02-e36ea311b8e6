﻿<%@ Page Title="" Language="C#" CodeBehind="ToPay.aspx.cs" Inherits="Account.Web.ToPay" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Language" content="zh-cn">
    <meta name="apple-mobile-web-app-capable" content="no" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,email=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white">
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>扫码支付<%=PageTitleConst.Default_Ext %></title>
    <link href="./static/css/pay.css?t=5" media="screen" rel="stylesheet">
    <style type="text/css">
        .pay_clickli {
            border: 2px solid #049ff1 !important;
        }

        .pay_down_ico {
            display: none;
            width: 25px;
            height: 25px;
            position: absolute;
            bottom: 0px;
            right: 0px;
            padding: 0;
            background-image: url(./static/image/pay/pay_down_ico.png);
            background-repeat: no-repeat;
        }

        .pay_clickli .pay_down_ico {
            display: block;
        }

        .pay_ul2 {
            float: left;
            margin-top: 10px;
        }

            .pay_ul2 li {
                border-radius: 3px;
                width: 140px;
                height: 40px;
                float: left;
                margin-right: 20px;
                border: 1px solid #dbdbdb;
                position: relative;
                text-align: center;
                cursor: pointer;
                cursor: hand;
            }

                .pay_ul2 li div {
                    text-align: center;
                    line-height: 40px;
                    font-weight: normal;
                }
    </style>
</head>
<body>
    <div class="body" id="body">
        <h1 class="mod-title" style="display: none; flex-wrap: nowrap;">
            <ul class="pay_ul2" style="margin: 0 auto; margin-top: 8px;">
                <li id="alipayLi" class="pay_clickli">
                    <img class="pay_fs" src="./static/image/pay/alipay.jpg" title="支付宝" onclick="javascript:changePay(0)" />
                    <div class="pay_down_ico"></div>
                </li>
                <li id="wxpayLi">
                    <img class="pay_fs" src="./static/image/pay/weixin.jpg" title="微信" onclick="javascript:changePay(1)" />
                    <div class="pay_down_ico"></div>
                </li>
            </ul>
        </h1>
        <div class="mod-ct" id="loadingDiv">
            <img id="loading" class="image " src="./static/image/pay/loading.gif"
                style="width: 200px; height: 200px;" />
        </div>
        <div class="mod-ct" id="orderDiv" style="display: none;">
            <div class="order">
            </div>
            <div class="amount" id="timeOut" style="font-size: 25px;">
                <p style="display: flex; align-items: center; justify-content: center;">
                    <img id="qqHead"
                        style="width: 40px; height: 40px; background-size: 100% 100%; margin: 5px; border-radius: 30px; display: none;" />
                    <span id="strRemark"></span>
                </p>
                <h1 fr-fix-stroke="true" id="timeOutTip" style="font-size: 22px; display: none;">
                    <span style="color: red;" fr-fix-stroke="true">支付超时,订单已关闭！
                    <br>
                        请重新下单或【<a id="keFuQQ1" href="javascript:void(0);" target="_blank">联系客服</a>】
                    </span>
                </h1>
            </div>
            <div id="orderbody">
                <div class="amount" id="money"></div>
                <div class="qrcode-img-wrapper" data-role="qrPayImgWrapper">
                    <div data-role="qrPayImg" class="qrcode-img-area">
                        <div class="ui-loading qrcode-loading" data-role="qrPayImgLoading" style="display: none;">加载中</div>
                        <div style="position: relative; display: inline-block;">
                            <div class="qr-image" id="qrcode"></div>
                        </div>
                    </div>

                </div>
                <div class="time-item">
                    <strong id="hour_show">0时</strong>
                    <strong id="minute_show">0分</strong>
                    <strong id="second_show">0秒</strong>
                    <div class="time-item" id="msg">
                        <h1>
                            <span style="color: blue; display: none" id="lblYouHui">请务必与订单金额一致，以免订单失败！
                            <br>
                            </span>
                            <h1 fr-fix-stroke="true">
                                <span style="color: red;" fr-fix-stroke="true">支付完成后，请耐心等待页面跳转
                                <br>
                                    【有问题点此<a id="keFuQQ" href="javascript:void(0);" target="_blank">联系客服</a>】
                                </span>
                            </h1>
                        </h1>
                    </div>
                </div>

                <div class="tip">
                    <div class="ico-scan"></div>
                    <div class="tip-text">
                        <p>使用 <b id="payTypeMsg">支付宝</b> 扫一扫</p>
                        <p>扫描二维码完成支付</p>
                    </div>
                    <div class="detail" id="orderDetail">
                        <dl class="detail-ct" id="desc" style="display: none;">
                            <dt>金额</dt>
                            <dd>￥<b id="strPrice"></b> 元</dd>
                            <dt>订单单号：</dt>
                            <dd><b id="strOrderId"></b></dd>
                            <dt>商户单号：</dt>
                            <dd><b id="strPayId"></b></dd>
                            <dt>创建时间：</dt>
                            <dd><b id="strDate"></b></dd>
                            <dt>状态</dt>
                            <dd>等待支付</dd>
                        </dl>
                        <br>
                        <a href="javascript:void(0)" class="arrow" onclick="showDetail()"><i class="ico-arrow"></i></a>
                    </div>
                </div>
                <div class="tip-text">
                </div>
            </div>
        </div>
        <%--<div class="foot">
            <div class="inner">
                <p>先截屏保存二维码图片到手机中</p>
                <p>支付宝/微信扫一扫 选择“相册”即可</p>
            </div>
        </div>--%>
    </div>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/jquery/3.6.0/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/jquery.qrcode/1.0/jquery.qrcode.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/layui/2.6.8/layui.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <!--<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>-->
    <script>
        function showDetail() {
            if ($('#orderDetail').hasClass('detail-open')) {
                $('#orderDetail .detail-ct').slideUp(500, function () {
                    $('#orderDetail').removeClass('detail-open');
                });
            } else {
                $('#orderDetail .detail-ct').slideDown(500, function () {
                    $('#orderDetail').addClass('detail-open');
                });
            }
        }

        function formatDate(now) {
            now = new Date(now)
            return now.getFullYear()
                + "-" + (now.getMonth() > 8 ? (now.getMonth() + 1) : "0" + (now.getMonth() + 1))
                + "-" + (now.getDate() > 9 ? now.getDate() : "0" + now.getDate())
                + " " + (now.getHours() > 9 ? now.getHours() : "0" + now.getHours())
                + ":" + (now.getMinutes() > 9 ? now.getMinutes() : "0" + now.getMinutes())
                + ":" + (now.getSeconds() > 9 ? now.getSeconds() : "0" + now.getSeconds());
        }

        var tickTimer;
        var checkTimer;
        function timer() {
            if (intDiff > 0) {
                try {
                    clearInterval(tickTimer);
                } catch (err) { }
                var i = 0;
                tickTimer = window.setInterval(function () {
                    i++;
                    payTick();
                    intDiff--;
                }, 1000);
                checkTimer = window.setInterval(function () {
                    check();
                }, 1800);
            } else {
                qrcode_timeout('')
            }
        }

        function payTick() {
            var day = 0, hour = 0, minute = 0, second = 0;
            if (intDiff > 0) {
                day = Math.floor(intDiff / (60 * 60 * 24));
                hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
            }
            if (minute <= 9) minute = '0' + minute;
            if (second <= 9) second = '0' + second;
            if (hour > 0) {
                $('#hour_show').html('<s id="h"></s>' + hour + '时');
                $('#hour_show').show();
            } else {
                $('#hour_show').hide();
            }
            $('#minute_show').html('<s></s>' + minute + '分');
            $('#second_show').html('<s></s>' + second + '秒');
            if (hour <= 0 && minute <= 0 && second <= 0) {
                qrcode_timeout('')
                clearInterval(tickTimer);
                clearInterval(checkTimer);
            }
        }

        function qrcode_timeout(msg) {
            if (msg == null || msg == '') {
                $("#timeOutTip").show();
            }
            $(".mod-title").hide();
            $("#orderbody").hide();
            $("#timeOut p span").html(msg);
            $("#timeOut").css("color", "red");
        }

        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null)
                return decodeURI(r[2]);
            return null;
        }

        var nowPayType = -1;
        var wxUrl = "";
        var zfbUrl = "";
        var otherUrl = "";
        var orderFrom = 0;
        function changePay(payType) {
            var payUrl = window.location.href;
            if (payType == 0 || payType == 2) {
                payUrl = zfbUrl;
                $("#alipayLi").removeClass("pay_clickli").addClass("pay_clickli");
                $("#wxpayLi").removeClass("pay_clickli");
                $("#payTypeMsg").text('支付宝');
            } else {
                payUrl = wxUrl;
                $("#payTypeMsg").text('微信');
                $("#alipayLi").removeClass("pay_clickli");
                $("#wxpayLi").removeClass("pay_clickli").addClass("pay_clickli");
            }
            if (orderFrom != 0) {
                payUrl = otherUrl;
            }
            if (payUrl == null || payUrl == "") {
                payUrl = window.location.href;
            }
            qrcodeImage(payUrl);
            changePayType(payType);
        }

        $.post("/code.aspx?op=getOrder", "orderId=" + getQueryString("orderId"), function (data) {
            if (data.code == 1) {
                var time = 0;
                if (data.data.state >= 1) {
                    qrcode_timeout("恭喜，订单支付成功!");
                    if (data.data.returnUrl != null && data.data.returnUrl != '') {
                        window.location.href = data.data.returnUrl;
                        return;
                    }
                } else {
                    if (data.data.state != -1) {
                        time = new Date().getTime() - data.data.date;
                        time = data.data.timeOut * 60 - time / 1000;
                    }
                    zfbUrl = data.data.zfbPayUrl;
                    wxUrl = data.data.wxPayUrl;
                    otherUrl = data.data.payUrl;
                    orderFrom = data.data.from;
                    if (navigator.userAgent.match(/Alipay/i) && zfbUrl != null && zfbUrl != '') {
                        window.location.href = zfbUrl;
                    } else if (navigator.userAgent.match(/MicroMessenger\//i) && wxUrl != null && wxUrl != '') {
                        if (data.data.payType != 1) {
                            data.data.payType = 1;
                            changePayType(data.data.payType);
                            return;
                        }
                    } else {
                        //$('#msg').append('<button type="button" onclick="openZfb();" class="tzzfb-btn">打开支付宝</button><button type="button" onclick="openWx();" class="tzwx-btn" style="margin-left:5px;">打开微信</button><br>');
                    }
                }

                nowPayType = data.data.payType;

                document.getElementById('strRemark').innerText = data.data.remark;
                document.getElementById('money').innerText = "￥" + data.data.reallyPrice.toFixed(2);
                if (data.data.needUserPay) {
                    $("#lblYouHui").show();
                }
                $("#strPrice").text(data.data.price);
                $("#strPayId").text(data.data.payId);
                $("#strOrderId").text(data.data.orderId);
                $("#strDate").text(formatDate(data.data.date));

                $("#orderDiv").show();
                $("#loadingDiv").hide();

                if (time > 0) {
                    $(".mod-title").css("display", "flex");
                    changePay(data.data.payType);
                }

                intDiff = time;
                timer();

                check();

                if (data.data.qq != null) {
                    $("#qqHead").attr("src", "https://q1.qlogo.cn/g?b=qq&nk=" + data.data.qq + "&s=100");
                    $("#qqHead").show();
                    $("#keFuQQ").attr("href", "http://wpa.qq.com/msgrd?v=3&uin=" + data.data.qq + "&site=qq&menu=yes");
                    $("#keFuQQ1").attr("href", "http://wpa.qq.com/msgrd?v=3&uin=" + data.data.qq + "&site=qq&menu=yes");
                }
            } else {
                $("#orderDiv").show();
                $("#loadingDiv").hide();
                qrcode_timeout(data.msg);
            }
        });

        function qrcodeImage(code_url) {
            $('#qrcode').html("");
            if (code_url.indexOf("/static") == 0) {
                $("#qrcode").html("<img src='" + code_url + "'>");
            }
            else {
                $('#qrcode').qrcode({
                    text: code_url,
                    width: 230,
                    height: 230,
                    foreground: "#000000",
                    background: "#ffffff",
                    typeNumber: -1
                });
            }
        }

        function showMsg(content, time) {
            layer.msg(content, {
                time: time, icon: 6,
                offset: ['75px', $("#alipayLi").position().left + 25]
            });
        }

        function changePayType(payType) {
            if (nowPayType == payType) {
                showMsg("请使用【" + (payType == 0 ? "支付宝" : "微信") + "】扫码支付！<br>也可以在顶部切换其他付款方式！<br>", 2000);
                return;
            }
            nowPayType = payType;
            $.post("/code.aspx?op=changePayType", "orderId=" + getQueryString("orderId") + "&payType=" + payType);
            if (navigator.userAgent.match(/MicroMessenger\//i) && payType == 1) {
                showMsg("支付方式变更为【微信】，请重新扫码！<br>", 3000);
            } else {
                showMsg("请使用【" + (payType == 0 ? "支付宝" : "微信") + "】扫码支付！<br>也可以在顶部切换其他付款方式！<br>", 2000);
            }
        }

        function check() {
            $.post("/code.aspx?op=checkOrder", "orderId=" + getQueryString("orderId"), function (data) {
                if (data.code == 1) {
                    window.location.href = data.data;
                } else {
                    if (data.date == null || data.date < 0) {
                        data.date = 0;
                    }
                    intDiff = data.date;
                    if (intDiff <= 0) {
                        intDiff = 0;
                    }
                    if (intDiff != 0 && data.payType != null && nowPayType != data.payType) {
                        changePay(data.payType);
                    }
                }
            })
        }

        function openZfb() {
            var scheme = 'alipays://platformapi/startapp?saId=10000007&qrcode=';
            if (navigator.userAgent.indexOf("Safari") > -1) {
                window.location.href = scheme;
            } else {
                var iframe = document.createElement("iframe");
                iframe.style.display = "none";
                iframe.src = scheme;
                document.body.appendChild(iframe);
            }
        }

        function openWx() {
            var scheme = 'weixin://';
            if (navigator.userAgent.indexOf("Safari") > -1) {
                window.location.href = scheme;
            } else {
                var iframe = document.createElement("iframe");
                iframe.style.display = "none";
                iframe.src = scheme;
                document.body.appendChild(iframe);
            }
        }
    </script>
</body>
</html>
