var p=/^1[3456789]\d{9}$/,e=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;function showMessage(m,t){var d=document.createElement('div');d.className='message-toast message-'+t;d.innerHTML='<i class="fas fa-'+(t==='success'?'check-circle':'exclamation-circle')+'"></i> '+m;d.style.cssText='position:fixed;top:20px;right:20px;background:'+(t==='success'?'#4CAF50':'#f44336')+';color:white;padding:15px 20px;border-radius:5px;box-shadow:0 2px 10px rgba(0,0,0,0.2);z-index:10000;transform:translateX(400px);transition:transform 0.3s ease;';document.body.appendChild(d);setTimeout(function(){d.style.transform='translateX(0)';},100);setTimeout(function(){d.style.transform='translateX(400px)';setTimeout(function(){if(d.parentNode)document.body.removeChild(d);},300);},3000);}
function setButtonLoading(b,l,o){if(l){b.disabled=true;b.innerHTML='<i class="fas fa-spinner fa-spin"></i> '+(o||'处理中...');}else{b.disabled=false;b.innerHTML=o||b.getAttribute('data-original-text')||'提交';}}
function validateAccount(a){return p.test(a)||e.test(a);}
function validatePassword(pw){return pw&&/^[0-9A-Za-z].{5,15}$/.test(pw);}
function validateVerifyCode(v){return v&&v.length>=4&&v.length<=8;}
function validateNickname(n){return n&&n.length>=2&&n.length<=20&&/^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])/.test(n);}
function ajaxSubmit(url,data,callback){var xhr=new XMLHttpRequest();xhr.open('POST',url,true);xhr.onreadystatechange=function(){if(xhr.readyState===4){if(xhr.status===200){try{var resp=JSON.parse(xhr.responseText);callback(null,resp);}catch(ex){callback('解析响应失败',null);}}else{callback('网络错误',null);}}};xhr.send(data);}
function sendVerifyCode(account,op,btnId){var btn=document.getElementById(btnId);if(!btn||btn.classList.contains('mod-btn-disabled-gray'))return;if(!account){showMessage('请先输入登录账号','error');return;}if(!validateAccount(account)){showMessage('请输入正确的邮箱或手机号','error');return;}var isEmail=e.test(account);var xhr=new XMLHttpRequest();xhr.open('GET','/mail.aspx?en=1&op='+op+'&'+(isEmail?'email':'mobile')+'='+account,true);xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){if(xhr.responseText!=='True')showMessage(xhr.responseText,'error');else showMessage('验证码发送成功','success');}};xhr.send();btn.classList.add('mod-btn-disabled-gray');var count=60,timer=function(){count--;if(count===0){btn.classList.remove('mod-btn-disabled-gray');btn.innerHTML=btn.getAttribute('data-original-text')||'发送验证码';}else{setTimeout(timer,1000);btn.innerHTML=(btn.getAttribute('data-original-text')||'发送验证码')+'('+count+')';}};timer();}
function bindEnterKey(btnId){document.addEventListener('keydown',function(ev){if(ev.key==='Enter'){var btn=document.getElementById(btnId);if(btn&&!btn.disabled)btn.click();}});}
function initAuthGeetest(captchaId,onSuccess){initGeetest4({captchaId:captchaId,product:'bind',riskType:'nine'},function(captchaObj){captchaObj.onSuccess(function(){if(captchaObj.getValidate())onSuccess();});window.authCaptcha=captchaObj;});}
function showAuthCaptcha(){if(window.authCaptcha)window.authCaptcha.showCaptcha();}
function initGeetestForSend(captchaId,onSuccess){initGeetest4({captchaId:captchaId,product:'bind',riskType:'nine'},function(captchaObj){captchaObj.onSuccess(function(){if(captchaObj.getValidate())onSuccess();});window.sendCaptcha=captchaObj;});}
function showSendCaptcha(){if(window.sendCaptcha)window.sendCaptcha.showCaptcha();}
