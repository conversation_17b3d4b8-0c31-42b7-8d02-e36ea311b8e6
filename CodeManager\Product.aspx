﻿<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0">
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/log.min.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_sdk.min.js"></script>
    <link rel="stylesheet" type="text/css" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/common_new.css?1=1">
    <link rel="stylesheet" type="text/css" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/index_new.css">
    <title>OCR助手产品介绍 | 智能文字识别工具<%=PageTitleConst.Default_Ext %></title>
    <meta name="description" content="OCR文字识别助手是一款集文字、表格、公式、文档及翻译于一体的智能生产力工具。支持100+语言识别，多平台兼容，全球云端存储，业内顶级识别速度。" />
    <meta name="keywords" content="免费OCR,免费文字识别,免费图片识别,免费图文识别,免费图片转文字,OCR识别,文字识别,图片识别,图片文字识别,图片转文字,图片转表格,图片转公式,文档识别,文档翻译" />
    <link rel="stylesheet" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/style.*************.css" type="text/css" media="all">
    <link rel="stylesheet" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/styles.css" type="text/css" media="all">
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/body.*************.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/jquery.SuperSlide.2.1.1.js"></script>
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-********-*************.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-********-*************.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-********-*************.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-********-*************.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-********-*************.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000*3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>
    <style type="text/css">
        .mod-btn {
            display: inline-block;
            box-sizing: border-box;
            width: 140px;
            height: 40px;
            line-height: 40px;
            padding: 0;
            border: 1px solid transparent;
            border-radius: 3px;
            font-size: 15px;
            text-align: center;
            cursor: pointer;
            -webkit-transition: border-color .2s, color .2s, background-color .2s;
            transition: border-color .2s, color .2s, background-color .2s;
        }

        .mod-btn-blue {
            color: #fff;
            background-color: #007cfa;
        }

            .mod-btn-blue:hover {
                background-color: #3396fb;
            }

            .mod-btn-blue:focus {
                background-color: #0064ed;
            }

        .mod-btn-black {
            color: #666;
            background-color: #fff;
            border-color: #ededed;
        }

            .mod-btn-black:hover {
                color: #007cfa;
                border-color: #007cfa;
            }

        .mod-btn-white {
            color: #007cfa;
            border-color: #007cfa;
            background-color: #fff;
        }

            .mod-btn-white:hover {
                color: #fff;
                border-color: #3396fb;
                background-color: #3396fb;
            }

            .mod-btn-white:focus {
                color: #fff;
                background-color: #0064ed;
            }

        .mod-btn-disabled-blue {
            color: #fff;
            background-color: #cce5fe;
            cursor: default;
        }

        .mod-btn-disabled-orange {
            color: #fff;
            background-color: #ffca69;
            cursor: default;
        }

        .mod-btn-disabled-gray {
            color: #666;
            background-color: #e6e6e6;
            cursor: default;
        }

        .mod-btn-transparent {
            color: #666;
            border-color: #ededed;
            background-color: transparent;
        }

            .mod-btn-transparent:hover {
                color: #007cfa;
                border-color: #007cfa;
                background-color: transparent;
            }

        .mod-btn-qq {
            color: #666;
            background-color: #fff;
            border-color: #ededed;
        }

            .mod-btn-qq:hover {
                color: #ff8a00;
                border-color: #ff8a00;
            }

            .mod-btn-qq .i-qq {
                width: 22px;
                height: 22px;
                margin-bottom: 4px;
                margin-right: 7px;
                vertical-align: middle;
                background-image: url(../images/icon.png);
                background-position: 0 -535px;
            }
    </style>
    <style fr-css-3fb305a8="false" id="HMxJVhcBpBem" media="screen" type="text/css">
        @charset "UTF-8";

        :root {
            --fr-font-basefont: system-ui,-apple-system,BlinkMacSystemFont,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji','Android Emoji','EmojiSymbols','emojione mozilla','twemoji mozilla','office365icons','iconfont','icomoon','FontAwesome','Font Awesome 5 Pro','Font Awesome 6 Pro','IcoFont','fontello','themify','Material Icons','Material Icons Extended','bootstrap-icons','Segoe Fluent Icons','Material-Design-Iconic-Font';
            --fr-font-fontscale: 1;
            --fr-font-family: 'Microsoft YaHei UI';
            --fr-font-shadow: 0 0 0.75px #7c7c7cdd;
            --fr-font-stroke: 0.015px currentcolor;
            --fr-no-stroke: 0px transparent;
        }

        @font-face {
            font-family: "Arial";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "FangSong";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Georgia";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "HanHei SC";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Helvetica";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Helvetica Neue";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "KaiTi";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Microsoft YaHei";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "MingLiU";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "NSimSun";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Noto Sans";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Open Sans";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PMingLiU";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangHK-Medium";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangHK-Regular";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Medium";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Regular";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Semibold";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Roboto";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "RobotoDraft";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SF Pro SC";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Segoe UI";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SimHei";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SimSun";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Tahoma";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Ubuntu";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Verdana";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\4EFF\5B8B";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5B8B\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5FAE\8EDF\6B63\9ED1\9AD4";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5FAE\8F6F\96C5\9ED1";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\6977\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\9ED1\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        :root#AOrggp :is(:not(i,head *):not([class*='glyph'],[class*='icon'],[class*='fa-'],[class*='vjs-'],[class*='mu-'])) {
            font-family: var(--fr-font-family),var(--fr-font-basefont);
            text-shadow: var(--fr-font-shadow);
            -webkit-text-stroke: var(--fr-font-stroke);
            font-feature-settings: unset;
            font-variant: unset;
            font-optical-sizing: auto;
            font-kerning: auto;
            -webkit-font-smoothing: antialiased !important;
            text-rendering: optimizeLegibility;
        }

        :root#AOrggp ::selection {
            color: #ffffff !important;
            background: #0084ff !important;
        }

        :root#AOrggp :is(progress,meter,datalist,samp,kbd,pre,pre *,code,code *) {
            -webkit-text-stroke: var(--fr-no-stroke) !important;
            text-shadow: none !important
        }

        .fr-fix-cc18d11d {
            -webkit-text-stroke: var(--fr-no-stroke) !important;
        }
    </style>
    <style data-id="immersive-translate-input-injected-css">
        .immersive-translate-input {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            z-index: 2147483647;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .immersive-translate-loading-spinner {
            vertical-align: middle !important;
            width: 10px !important;
            height: 10px !important;
            display: inline-block !important;
            margin: 0 4px !important;
            border: 2px rgba(221, 244, 255, 0.6) solid !important;
            border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
            border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
            border-radius: 50% !important;
            padding: 0 !important;
            -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
            animation: immersive-translate-loading-animation 0.6s infinite linear !important;
        }

        @-webkit-keyframes immersive-translate-loading-animation {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(359deg);
            }
        }

        @keyframes immersive-translate-loading-animation {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(359deg);
            }
        }


        .immersive-translate-input-loading {
            --loading-color: #f78fb6;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            display: block;
            margin: 12px auto;
            position: relative;
            color: white;
            left: -100px;
            box-sizing: border-box;
            animation: immersiveTranslateShadowRolling 1.5s linear infinite;
        }

        @keyframes immersiveTranslateShadowRolling {
            0% {
                box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            12% {
                box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            25% {
                box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            36% {
                box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
            }

            50% {
                box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
            }

            62% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);
            }

            75% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color);
            }

            87% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
            }

            100% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
            }
        }


        .immersive-translate-search-recomend {
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            position: relative;
            font-size: 16px;
        }

        .immersive-translate-search-enhancement-en-title {
            color: #4d5156;
        }
        /* dark */
        @media (prefers-color-scheme: dark) {
            .immersive-translate-search-recomend {
                border: 1px solid #3c4043;
            }

            .immersive-translate-close-action svg {
                fill: #bdc1c6;
            }

            .immersive-translate-search-enhancement-en-title {
                color: #bdc1c6;
            }
        }


        .immersive-translate-search-settings {
            position: absolute;
            top: 16px;
            right: 16px;
            cursor: pointer;
        }

        .immersive-translate-search-title {
        }

        .immersive-translate-search-title-wrapper {
        }

        .immersive-translate-search-time {
            font-size: 12px;
            margin: 4px 0 24px;
            color: #70757a;
        }

        .immersive-translate-expand-items {
            display: none;
        }

        .immersive-translate-search-more {
            margin-top: 16px;
            font-size: 14px;
        }

        .immersive-translate-modal {
            display: none;
            position: fixed;
            z-index: 1000000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgb(0, 0, 0);
            background-color: rgba(0, 0, 0, 0.4);
            font-size: 15px;
        }

        .immersive-translate-modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            border-radius: 10px;
            width: 80%;
            max-width: 500px;
            font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu", "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        .immersive-translate-modal-title {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 20px;
            color: hsl(205, 20%, 32%);
        }

        .immersive-translate-modal-body {
            color: hsl(205, 20%, 32%)
        }

        .immersive-translate-close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

            .immersive-translate-close:hover,
            .immersive-translate-close:focus {
                color: black;
                text-decoration: none;
                cursor: pointer;
            }

        .immersive-translate-modal-footer {
            display: flex;
            justify-content: flex-end;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .immersive-translate-btn {
            width: fit-content;
            color: #fff;
            background-color: #ea4c89;
            border: none;
            font-size: 14px;
            margin: 5px;
            padding: 10px 20px;
            font-size: 1rem;
            border-radius: 5px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

            .immersive-translate-btn:hover {
                background-color: #f082ac;
            }

        .immersive-translate-cancel-btn {
            /* gray color */
            background-color: rgb(89, 107, 120);
        }


            .immersive-translate-cancel-btn:hover {
                background-color: hsl(205, 20%, 32%);
            }


        .immersive-translate-btn svg {
            margin-right: 5px;
        }

        .immersive-translate-link {
            cursor: pointer;
            user-select: none;
            -webkit-user-drag: none;
            text-decoration: none;
            color: #007bff;
            -webkit-tap-highlight-color: rgba(0, 0, 0, .1);
        }

        .immersive-translate-primary-link {
            cursor: pointer;
            user-select: none;
            -webkit-user-drag: none;
            text-decoration: none;
            color: #ea4c89;
            -webkit-tap-highlight-color: rgba(0, 0, 0, .1);
        }

        .immersive-translate-modal input[type="radio"] {
            margin: 0 6px;
            cursor: pointer;
        }

        .immersive-translate-modal label {
            cursor: pointer;
        }

        .immersive-translate-close-action {
            position: absolute;
            top: 2px;
            right: 0px;
            cursor: pointer;
        }
    </style>
    <style id="swal-pub-style">
        .swal2-popup.swal2-toast {
            flex-direction: column;
            align-items: stretch;
            width: auto;
            padding: 1.25em;
            overflow-y: hidden;
            background: #fff;
            box-shadow: 0 0 .625em #d9d9d9
        }

            .swal2-popup.swal2-toast .swal2-header {
                flex-direction: row;
                padding: 0
            }

            .swal2-popup.swal2-toast .swal2-title {
                flex-grow: 1;
                justify-content: flex-start;
                margin: 0 .625em;
                font-size: 1em
            }

            .swal2-popup.swal2-toast .swal2-loading {
                justify-content: center
            }

            .swal2-popup.swal2-toast .swal2-input {
                height: 2em;
                margin: .3125em auto;
                font-size: 1em
            }

            .swal2-popup.swal2-toast .swal2-validation-message {
                font-size: 1em
            }

            .swal2-popup.swal2-toast .swal2-footer {
                margin: .5em 0 0;
                padding: .5em 0 0;
                font-size: .8em
            }

            .swal2-popup.swal2-toast .swal2-close {
                position: static;
                width: .8em;
                height: .8em;
                line-height: .8
            }

            .swal2-popup.swal2-toast .swal2-content {
                justify-content: flex-start;
                margin: 0 .625em;
                padding: 0;
                font-size: 1em;
                text-align: initial
            }

            .swal2-popup.swal2-toast .swal2-html-container {
                padding: .625em 0 0
            }

                .swal2-popup.swal2-toast .swal2-html-container:empty {
                    padding: 0
                }

            .swal2-popup.swal2-toast .swal2-icon {
                width: 2em;
                min-width: 2em;
                height: 2em;
                margin: 0 .5em 0 0
            }

                .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
                    display: flex;
                    align-items: center;
                    font-size: 1.8em;
                    font-weight: 700
                }

        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {
            .swal2-popup.swal2-toast .swal2-icon .swal2-icon-content {
                font-size: .25em
            }
        }

        .swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
            width: 2em;
            height: 2em
        }

        .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
            top: .875em;
            width: 1.375em
        }

            .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
                left: .3125em
            }

            .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
                right: .3125em
            }

        .swal2-popup.swal2-toast .swal2-actions {
            flex: 1;
            flex-basis: auto !important;
            align-self: stretch;
            width: auto;
            height: 2.2em;
            height: auto;
            margin: 0 .3125em;
            margin-top: .3125em;
            padding: 0
        }

        .swal2-popup.swal2-toast .swal2-styled {
            margin: .125em .3125em;
            padding: .3125em .625em;
            font-size: 1em
        }

            .swal2-popup.swal2-toast .swal2-styled:focus {
                box-shadow: 0 0 0 1px #fff,0 0 0 3px rgba(100,150,200,.5)
            }

        .swal2-popup.swal2-toast .swal2-success {
            border-color: #a5dc86
        }

            .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line] {
                position: absolute;
                width: 1.6em;
                height: 3em;
                transform: rotate(45deg);
                border-radius: 50%
            }

                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left] {
                    top: -.8em;
                    left: -.5em;
                    transform: rotate(-45deg);
                    transform-origin: 2em 2em;
                    border-radius: 4em 0 0 4em
                }

                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right] {
                    top: -.25em;
                    left: .9375em;
                    transform-origin: 0 1.5em;
                    border-radius: 0 4em 4em 0
                }

            .swal2-popup.swal2-toast .swal2-success .swal2-success-ring {
                width: 2em;
                height: 2em
            }

            .swal2-popup.swal2-toast .swal2-success .swal2-success-fix {
                top: 0;
                left: .4375em;
                width: .4375em;
                height: 2.6875em
            }

            .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line] {
                height: .3125em
            }

                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip] {
                    top: 1.125em;
                    left: .1875em;
                    width: .75em
                }

                .swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long] {
                    top: .9375em;
                    right: .1875em;
                    width: 1.375em
                }

            .swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip {
                -webkit-animation: swal2-toast-animate-success-line-tip .75s;
                animation: swal2-toast-animate-success-line-tip .75s
            }

            .swal2-popup.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long {
                -webkit-animation: swal2-toast-animate-success-line-long .75s;
                animation: swal2-toast-animate-success-line-long .75s
            }

        .swal2-popup.swal2-toast.swal2-show {
            -webkit-animation: swal2-toast-show .5s;
            animation: swal2-toast-show .5s
        }

        .swal2-popup.swal2-toast.swal2-hide {
            -webkit-animation: swal2-toast-hide .1s forwards;
            animation: swal2-toast-hide .1s forwards
        }

        .swal2-container {
            display: flex;
            position: fixed;
            z-index: 1060;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            padding: .625em;
            overflow-x: hidden;
            transition: background-color .1s;
            -webkit-overflow-scrolling: touch
        }

            .swal2-container.swal2-backdrop-show, .swal2-container.swal2-noanimation {
                background: rgba(0,0,0,.4)
            }

            .swal2-container.swal2-backdrop-hide {
                background: 0 0 !important
            }

            .swal2-container.swal2-top {
                align-items: flex-start
            }

            .swal2-container.swal2-top-left, .swal2-container.swal2-top-start {
                align-items: flex-start;
                justify-content: flex-start
            }

            .swal2-container.swal2-top-end, .swal2-container.swal2-top-right {
                align-items: flex-start;
                justify-content: flex-end
            }

            .swal2-container.swal2-center {
                align-items: center
            }

            .swal2-container.swal2-center-left, .swal2-container.swal2-center-start {
                align-items: center;
                justify-content: flex-start
            }

            .swal2-container.swal2-center-end, .swal2-container.swal2-center-right {
                align-items: center;
                justify-content: flex-end
            }

            .swal2-container.swal2-bottom {
                align-items: flex-end
            }

            .swal2-container.swal2-bottom-left, .swal2-container.swal2-bottom-start {
                align-items: flex-end;
                justify-content: flex-start
            }

            .swal2-container.swal2-bottom-end, .swal2-container.swal2-bottom-right {
                align-items: flex-end;
                justify-content: flex-end
            }

                .swal2-container.swal2-bottom-end > :first-child, .swal2-container.swal2-bottom-left > :first-child, .swal2-container.swal2-bottom-right > :first-child, .swal2-container.swal2-bottom-start > :first-child, .swal2-container.swal2-bottom > :first-child {
                    margin-top: auto
                }

            .swal2-container.swal2-grow-fullscreen > .swal2-modal {
                display: flex !important;
                flex: 1;
                align-self: stretch;
                justify-content: center
            }

            .swal2-container.swal2-grow-row > .swal2-modal {
                display: flex !important;
                flex: 1;
                align-content: center;
                justify-content: center
            }

            .swal2-container.swal2-grow-column {
                flex: 1;
                flex-direction: column
            }

                .swal2-container.swal2-grow-column.swal2-bottom, .swal2-container.swal2-grow-column.swal2-center, .swal2-container.swal2-grow-column.swal2-top {
                    align-items: center
                }

                .swal2-container.swal2-grow-column.swal2-bottom-left, .swal2-container.swal2-grow-column.swal2-bottom-start, .swal2-container.swal2-grow-column.swal2-center-left, .swal2-container.swal2-grow-column.swal2-center-start, .swal2-container.swal2-grow-column.swal2-top-left, .swal2-container.swal2-grow-column.swal2-top-start {
                    align-items: flex-start
                }

                .swal2-container.swal2-grow-column.swal2-bottom-end, .swal2-container.swal2-grow-column.swal2-bottom-right, .swal2-container.swal2-grow-column.swal2-center-end, .swal2-container.swal2-grow-column.swal2-center-right, .swal2-container.swal2-grow-column.swal2-top-end, .swal2-container.swal2-grow-column.swal2-top-right {
                    align-items: flex-end
                }

                .swal2-container.swal2-grow-column > .swal2-modal {
                    display: flex !important;
                    flex: 1;
                    align-content: center;
                    justify-content: center
                }

            .swal2-container.swal2-no-transition {
                transition: none !important
            }

            .swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen) > .swal2-modal {
                margin: auto
            }

        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {
            .swal2-container .swal2-modal {
                margin: 0 !important
            }
        }

        .swal2-popup {
            display: none;
            position: relative;
            box-sizing: border-box;
            flex-direction: column;
            justify-content: center;
            width: 32em;
            max-width: 100%;
            padding: 1.25em;
            border: none;
            border-radius: 5px;
            background: #fff;
            font-family: inherit;
            font-size: 1rem
        }

            .swal2-popup:focus {
                outline: 0
            }

            .swal2-popup.swal2-loading {
                overflow-y: hidden
            }

        .swal2-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 1.8em
        }

        .swal2-title {
            position: relative;
            max-width: 100%;
            margin: 0 0 .4em;
            padding: 0;
            color: #595959;
            font-size: 1.875em;
            font-weight: 600;
            text-align: center;
            text-transform: none;
            word-wrap: break-word
        }

        .swal2-actions {
            display: flex;
            z-index: 1;
            box-sizing: border-box;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            width: 100%;
            margin: 1.25em auto 0;
            padding: 0
        }

            .swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
                opacity: .4
            }

            .swal2-actions:not(.swal2-loading) .swal2-styled:hover {
                background-image: linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))
            }

            .swal2-actions:not(.swal2-loading) .swal2-styled:active {
                background-image: linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))
            }

        .swal2-loader {
            display: none;
            align-items: center;
            justify-content: center;
            width: 2.2em;
            height: 2.2em;
            margin: 0 1.875em;
            -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
            animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
            border-width: .25em;
            border-style: solid;
            border-radius: 100%;
            border-color: #2778c4 transparent #2778c4 transparent
        }

        .swal2-styled {
            margin: .3125em;
            padding: .625em 1.1em;
            box-shadow: none;
            font-weight: 500
        }

            .swal2-styled:not([disabled]) {
                cursor: pointer
            }

            .swal2-styled.swal2-confirm {
                border: 0;
                border-radius: .25em;
                background: initial;
                background-color: #2778c4;
                color: #fff;
                font-size: 1em
            }

            .swal2-styled.swal2-deny {
                border: 0;
                border-radius: .25em;
                background: initial;
                background-color: #d14529;
                color: #fff;
                font-size: 1em
            }

            .swal2-styled.swal2-cancel {
                border: 0;
                border-radius: .25em;
                background: initial;
                background-color: #757575;
                color: #fff;
                font-size: 1em
            }

            .swal2-styled:focus {
                outline: 0;
                box-shadow: 0 0 0 3px rgba(100,150,200,.5)
            }

            .swal2-styled::-moz-focus-inner {
                border: 0
            }

        .swal2-footer {
            justify-content: center;
            margin: 1.25em 0 0;
            padding: 1em 0 0;
            border-top: 1px solid #eee;
            color: #545454;
            font-size: 1em
        }

        .swal2-timer-progress-bar-container {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            height: .25em;
            overflow: hidden;
            border-bottom-right-radius: 5px;
            border-bottom-left-radius: 5px
        }

        .swal2-timer-progress-bar {
            width: 100%;
            height: .25em;
            background: rgba(0,0,0,.2)
        }

        .swal2-image {
            max-width: 100%;
            margin: 1.25em auto
        }

        .swal2-close {
            position: absolute;
            z-index: 2;
            top: 0;
            right: 0;
            align-items: center;
            justify-content: center;
            width: 1.2em;
            height: 1.2em;
            padding: 0;
            overflow: hidden;
            transition: color .1s ease-out;
            border: none;
            border-radius: 5px;
            background: 0 0;
            color: #ccc;
            font-family: serif;
            font-size: 2.5em;
            line-height: 1.2;
            cursor: pointer
        }

            .swal2-close:hover {
                transform: none;
                background: 0 0;
                color: #f27474
            }

            .swal2-close:focus {
                outline: 0;
                box-shadow: inset 0 0 0 3px rgba(100,150,200,.5)
            }

            .swal2-close::-moz-focus-inner {
                border: 0
            }

        .swal2-content {
            z-index: 1;
            justify-content: center;
            margin: 0;
            padding: 0 1.6em;
            color: #545454;
            font-size: 1.125em;
            font-weight: 400;
            line-height: normal;
            text-align: center;
            word-wrap: break-word
        }

        .swal2-checkbox, .swal2-file, .swal2-input, .swal2-radio, .swal2-select, .swal2-textarea {
            margin: 1em auto
        }

        .swal2-file, .swal2-input, .swal2-textarea {
            box-sizing: border-box;
            width: 100%;
            transition: border-color .3s,box-shadow .3s;
            border: 1px solid #d9d9d9;
            border-radius: .1875em;
            background: inherit;
            box-shadow: inset 0 1px 1px rgba(0,0,0,.06);
            color: inherit;
            font-size: 1.125em
        }

            .swal2-file.swal2-inputerror, .swal2-input.swal2-inputerror, .swal2-textarea.swal2-inputerror {
                border-color: #f27474 !important;
                box-shadow: 0 0 2px #f27474 !important
            }

            .swal2-file:focus, .swal2-input:focus, .swal2-textarea:focus {
                border: 1px solid #b4dbed;
                outline: 0;
                box-shadow: 0 0 0 3px rgba(100,150,200,.5)
            }

            .swal2-file::-moz-placeholder, .swal2-input::-moz-placeholder, .swal2-textarea::-moz-placeholder {
                color: #ccc
            }

            .swal2-file:-ms-input-placeholder, .swal2-input:-ms-input-placeholder, .swal2-textarea:-ms-input-placeholder {
                color: #ccc
            }

            .swal2-file::placeholder, .swal2-input::placeholder, .swal2-textarea::placeholder {
                color: #ccc
            }

        .swal2-range {
            margin: 1em auto;
            background: #fff
        }

            .swal2-range input {
                width: 80%
            }

            .swal2-range output {
                width: 20%;
                color: inherit;
                font-weight: 600;
                text-align: center
            }

            .swal2-range input, .swal2-range output {
                height: 2.625em;
                padding: 0;
                font-size: 1.125em;
                line-height: 2.625em
            }

        .swal2-input {
            height: 2.625em;
            padding: 0 .75em
        }

            .swal2-input[type=number] {
                max-width: 10em
            }

        .swal2-file {
            background: inherit;
            font-size: 1.125em
        }

        .swal2-textarea {
            height: 6.75em;
            padding: .75em
        }

        .swal2-select {
            min-width: 50%;
            max-width: 100%;
            padding: .375em .625em;
            background: inherit;
            color: inherit;
            font-size: 1.125em
        }

        .swal2-checkbox, .swal2-radio {
            align-items: center;
            justify-content: center;
            background: #fff;
            color: inherit
        }

            .swal2-checkbox label, .swal2-radio label {
                margin: 0 .6em;
                font-size: 1.125em
            }

            .swal2-checkbox input, .swal2-radio input {
                flex-shrink: 0;
                margin: 0 .4em
            }

        .swal2-input-label {
            display: flex;
            justify-content: center;
            margin: 1em auto
        }

        .swal2-validation-message {
            align-items: center;
            justify-content: center;
            margin: 0 -2.7em;
            padding: .625em;
            overflow: hidden;
            background: #f0f0f0;
            color: #666;
            font-size: 1em;
            font-weight: 300
        }

            .swal2-validation-message::before {
                content: "!";
                display: inline-block;
                width: 1.5em;
                min-width: 1.5em;
                height: 1.5em;
                margin: 0 .625em;
                border-radius: 50%;
                background-color: #f27474;
                color: #fff;
                font-weight: 600;
                line-height: 1.5em;
                text-align: center
            }

        .swal2-icon {
            position: relative;
            box-sizing: content-box;
            justify-content: center;
            width: 5em;
            height: 5em;
            margin: 1.25em auto 1.875em;
            border: .25em solid transparent;
            border-radius: 50%;
            border-color: #000;
            font-family: inherit;
            line-height: 5em;
            cursor: default;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none
        }

            .swal2-icon .swal2-icon-content {
                display: flex;
                align-items: center;
                font-size: 3.75em
            }

            .swal2-icon.swal2-error {
                border-color: #f27474;
                color: #f27474
            }

                .swal2-icon.swal2-error .swal2-x-mark {
                    position: relative;
                    flex-grow: 1
                }

                .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
                    display: block;
                    position: absolute;
                    top: 2.3125em;
                    width: 2.9375em;
                    height: .3125em;
                    border-radius: .125em;
                    background-color: #f27474
                }

                    .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
                        left: 1.0625em;
                        transform: rotate(45deg)
                    }

                    .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
                        right: 1em;
                        transform: rotate(-45deg)
                    }

                .swal2-icon.swal2-error.swal2-icon-show {
                    -webkit-animation: swal2-animate-error-icon .5s;
                    animation: swal2-animate-error-icon .5s
                }

                    .swal2-icon.swal2-error.swal2-icon-show .swal2-x-mark {
                        -webkit-animation: swal2-animate-error-x-mark .5s;
                        animation: swal2-animate-error-x-mark .5s
                    }

            .swal2-icon.swal2-warning {
                border-color: #facea8;
                color: #f8bb86
            }

            .swal2-icon.swal2-info {
                border-color: #9de0f6;
                color: #3fc3ee
            }

            .swal2-icon.swal2-question {
                border-color: #c9dae1;
                color: #87adbd
            }

            .swal2-icon.swal2-success {
                border-color: #a5dc86;
                color: #a5dc86
            }

                .swal2-icon.swal2-success [class^=swal2-success-circular-line] {
                    position: absolute;
                    width: 3.75em;
                    height: 7.5em;
                    transform: rotate(45deg);
                    border-radius: 50%
                }

                    .swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left] {
                        top: -.4375em;
                        left: -2.0635em;
                        transform: rotate(-45deg);
                        transform-origin: 3.75em 3.75em;
                        border-radius: 7.5em 0 0 7.5em
                    }

                    .swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right] {
                        top: -.6875em;
                        left: 1.875em;
                        transform: rotate(-45deg);
                        transform-origin: 0 3.75em;
                        border-radius: 0 7.5em 7.5em 0
                    }

                .swal2-icon.swal2-success .swal2-success-ring {
                    position: absolute;
                    z-index: 2;
                    top: -.25em;
                    left: -.25em;
                    box-sizing: content-box;
                    width: 100%;
                    height: 100%;
                    border: .25em solid rgba(165,220,134,.3);
                    border-radius: 50%
                }

                .swal2-icon.swal2-success .swal2-success-fix {
                    position: absolute;
                    z-index: 1;
                    top: .5em;
                    left: 1.625em;
                    width: .4375em;
                    height: 5.625em;
                    transform: rotate(-45deg)
                }

                .swal2-icon.swal2-success [class^=swal2-success-line] {
                    display: block;
                    position: absolute;
                    z-index: 2;
                    height: .3125em;
                    border-radius: .125em;
                    background-color: #a5dc86
                }

                    .swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
                        top: 2.875em;
                        left: .8125em;
                        width: 1.5625em;
                        transform: rotate(45deg)
                    }

                    .swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {
                        top: 2.375em;
                        right: .5em;
                        width: 2.9375em;
                        transform: rotate(-45deg)
                    }

                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-tip {
                    -webkit-animation: swal2-animate-success-line-tip .75s;
                    animation: swal2-animate-success-line-tip .75s
                }

                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-line-long {
                    -webkit-animation: swal2-animate-success-line-long .75s;
                    animation: swal2-animate-success-line-long .75s
                }

                .swal2-icon.swal2-success.swal2-icon-show .swal2-success-circular-line-right {
                    -webkit-animation: swal2-rotate-success-circular-line 4.25s ease-in;
                    animation: swal2-rotate-success-circular-line 4.25s ease-in
                }

        .swal2-progress-steps {
            flex-wrap: wrap;
            align-items: center;
            max-width: 100%;
            margin: 0 0 1.25em;
            padding: 0;
            background: inherit;
            font-weight: 600
        }

            .swal2-progress-steps li {
                display: inline-block;
                position: relative
            }

            .swal2-progress-steps .swal2-progress-step {
                z-index: 20;
                flex-shrink: 0;
                width: 2em;
                height: 2em;
                border-radius: 2em;
                background: #2778c4;
                color: #fff;
                line-height: 2em;
                text-align: center
            }

                .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
                    background: #2778c4
                }

                    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step {
                        background: #add8e6;
                        color: #fff
                    }

                    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
                        background: #add8e6
                    }

            .swal2-progress-steps .swal2-progress-step-line {
                z-index: 10;
                flex-shrink: 0;
                width: 2.5em;
                height: .4em;
                margin: 0 -1px;
                background: #2778c4
            }

        [class^=swal2] {
            -webkit-tap-highlight-color: transparent
        }

        .swal2-show {
            -webkit-animation: swal2-show .3s;
            animation: swal2-show .3s
        }

        .swal2-hide {
            -webkit-animation: swal2-hide .15s forwards;
            animation: swal2-hide .15s forwards
        }

        .swal2-noanimation {
            transition: none
        }

        .swal2-scrollbar-measure {
            position: absolute;
            top: -9999px;
            width: 50px;
            height: 50px;
            overflow: scroll
        }

        .swal2-rtl .swal2-close {
            right: auto;
            left: 0
        }

        .swal2-rtl .swal2-timer-progress-bar {
            right: 0;
            left: auto
        }

        @supports (-ms-accelerator:true) {
            .swal2-range input {
                width: 100% !important
            }

            .swal2-range output {
                display: none
            }
        }

        @media all and (-ms-high-contrast:none),(-ms-high-contrast:active) {
            .swal2-range input {
                width: 100% !important
            }

            .swal2-range output {
                display: none
            }
        }

        @-webkit-keyframes swal2-toast-show {
            0% {
                transform: translateY(-.625em) rotateZ(2deg)
            }

            33% {
                transform: translateY(0) rotateZ(-2deg)
            }

            66% {
                transform: translateY(.3125em) rotateZ(2deg)
            }

            100% {
                transform: translateY(0) rotateZ(0)
            }
        }

        @keyframes swal2-toast-show {
            0% {
                transform: translateY(-.625em) rotateZ(2deg)
            }

            33% {
                transform: translateY(0) rotateZ(-2deg)
            }

            66% {
                transform: translateY(.3125em) rotateZ(2deg)
            }

            100% {
                transform: translateY(0) rotateZ(0)
            }
        }

        @-webkit-keyframes swal2-toast-hide {
            100% {
                transform: rotateZ(1deg);
                opacity: 0
            }
        }

        @keyframes swal2-toast-hide {
            100% {
                transform: rotateZ(1deg);
                opacity: 0
            }
        }

        @-webkit-keyframes swal2-toast-animate-success-line-tip {
            0% {
                top: .5625em;
                left: .0625em;
                width: 0
            }

            54% {
                top: .125em;
                left: .125em;
                width: 0
            }

            70% {
                top: .625em;
                left: -.25em;
                width: 1.625em
            }

            84% {
                top: 1.0625em;
                left: .75em;
                width: .5em
            }

            100% {
                top: 1.125em;
                left: .1875em;
                width: .75em
            }
        }

        @keyframes swal2-toast-animate-success-line-tip {
            0% {
                top: .5625em;
                left: .0625em;
                width: 0
            }

            54% {
                top: .125em;
                left: .125em;
                width: 0
            }

            70% {
                top: .625em;
                left: -.25em;
                width: 1.625em
            }

            84% {
                top: 1.0625em;
                left: .75em;
                width: .5em
            }

            100% {
                top: 1.125em;
                left: .1875em;
                width: .75em
            }
        }

        @-webkit-keyframes swal2-toast-animate-success-line-long {
            0% {
                top: 1.625em;
                right: 1.375em;
                width: 0
            }

            65% {
                top: 1.25em;
                right: .9375em;
                width: 0
            }

            84% {
                top: .9375em;
                right: 0;
                width: 1.125em
            }

            100% {
                top: .9375em;
                right: .1875em;
                width: 1.375em
            }
        }

        @keyframes swal2-toast-animate-success-line-long {
            0% {
                top: 1.625em;
                right: 1.375em;
                width: 0
            }

            65% {
                top: 1.25em;
                right: .9375em;
                width: 0
            }

            84% {
                top: .9375em;
                right: 0;
                width: 1.125em
            }

            100% {
                top: .9375em;
                right: .1875em;
                width: 1.375em
            }
        }

        @-webkit-keyframes swal2-show {
            0% {
                transform: scale(.7)
            }

            45% {
                transform: scale(1.05)
            }

            80% {
                transform: scale(.95)
            }

            100% {
                transform: scale(1)
            }
        }

        @keyframes swal2-show {
            0% {
                transform: scale(.7)
            }

            45% {
                transform: scale(1.05)
            }

            80% {
                transform: scale(.95)
            }

            100% {
                transform: scale(1)
            }
        }

        @-webkit-keyframes swal2-hide {
            0% {
                transform: scale(1);
                opacity: 1
            }

            100% {
                transform: scale(.5);
                opacity: 0
            }
        }

        @keyframes swal2-hide {
            0% {
                transform: scale(1);
                opacity: 1
            }

            100% {
                transform: scale(.5);
                opacity: 0
            }
        }

        @-webkit-keyframes swal2-animate-success-line-tip {
            0% {
                top: 1.1875em;
                left: .0625em;
                width: 0
            }

            54% {
                top: 1.0625em;
                left: .125em;
                width: 0
            }

            70% {
                top: 2.1875em;
                left: -.375em;
                width: 3.125em
            }

            84% {
                top: 3em;
                left: 1.3125em;
                width: 1.0625em
            }

            100% {
                top: 2.8125em;
                left: .8125em;
                width: 1.5625em
            }
        }

        @keyframes swal2-animate-success-line-tip {
            0% {
                top: 1.1875em;
                left: .0625em;
                width: 0
            }

            54% {
                top: 1.0625em;
                left: .125em;
                width: 0
            }

            70% {
                top: 2.1875em;
                left: -.375em;
                width: 3.125em
            }

            84% {
                top: 3em;
                left: 1.3125em;
                width: 1.0625em
            }

            100% {
                top: 2.8125em;
                left: .8125em;
                width: 1.5625em
            }
        }

        @-webkit-keyframes swal2-animate-success-line-long {
            0% {
                top: 3.375em;
                right: 2.875em;
                width: 0
            }

            65% {
                top: 3.375em;
                right: 2.875em;
                width: 0
            }

            84% {
                top: 2.1875em;
                right: 0;
                width: 3.4375em
            }

            100% {
                top: 2.375em;
                right: .5em;
                width: 2.9375em
            }
        }

        @keyframes swal2-animate-success-line-long {
            0% {
                top: 3.375em;
                right: 2.875em;
                width: 0
            }

            65% {
                top: 3.375em;
                right: 2.875em;
                width: 0
            }

            84% {
                top: 2.1875em;
                right: 0;
                width: 3.4375em
            }

            100% {
                top: 2.375em;
                right: .5em;
                width: 2.9375em
            }
        }

        @-webkit-keyframes swal2-rotate-success-circular-line {
            0% {
                transform: rotate(-45deg)
            }

            5% {
                transform: rotate(-45deg)
            }

            12% {
                transform: rotate(-405deg)
            }

            100% {
                transform: rotate(-405deg)
            }
        }

        @keyframes swal2-rotate-success-circular-line {
            0% {
                transform: rotate(-45deg)
            }

            5% {
                transform: rotate(-45deg)
            }

            12% {
                transform: rotate(-405deg)
            }

            100% {
                transform: rotate(-405deg)
            }
        }

        @-webkit-keyframes swal2-animate-error-x-mark {
            0% {
                margin-top: 1.625em;
                transform: scale(.4);
                opacity: 0
            }

            50% {
                margin-top: 1.625em;
                transform: scale(.4);
                opacity: 0
            }

            80% {
                margin-top: -.375em;
                transform: scale(1.15)
            }

            100% {
                margin-top: 0;
                transform: scale(1);
                opacity: 1
            }
        }

        @keyframes swal2-animate-error-x-mark {
            0% {
                margin-top: 1.625em;
                transform: scale(.4);
                opacity: 0
            }

            50% {
                margin-top: 1.625em;
                transform: scale(.4);
                opacity: 0
            }

            80% {
                margin-top: -.375em;
                transform: scale(1.15)
            }

            100% {
                margin-top: 0;
                transform: scale(1);
                opacity: 1
            }
        }

        @-webkit-keyframes swal2-animate-error-icon {
            0% {
                transform: rotateX(100deg);
                opacity: 0
            }

            100% {
                transform: rotateX(0);
                opacity: 1
            }
        }

        @keyframes swal2-animate-error-icon {
            0% {
                transform: rotateX(100deg);
                opacity: 0
            }

            100% {
                transform: rotateX(0);
                opacity: 1
            }
        }

        @-webkit-keyframes swal2-rotate-loading {
            0% {
                transform: rotate(0)
            }

            100% {
                transform: rotate(360deg)
            }
        }

        @keyframes swal2-rotate-loading {
            0% {
                transform: rotate(0)
            }

            100% {
                transform: rotate(360deg)
            }
        }

        body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
            overflow: hidden
        }

        body.swal2-height-auto {
            height: auto !important
        }

        body.swal2-no-backdrop .swal2-container {
            top: auto;
            right: auto;
            bottom: auto;
            left: auto;
            max-width: calc(100% - .625em * 2);
            background-color: transparent !important
        }

            body.swal2-no-backdrop .swal2-container > .swal2-modal {
                box-shadow: 0 0 10px rgba(0,0,0,.4)
            }

            body.swal2-no-backdrop .swal2-container.swal2-top {
                top: 0;
                left: 50%;
                transform: translateX(-50%)
            }

            body.swal2-no-backdrop .swal2-container.swal2-top-left, body.swal2-no-backdrop .swal2-container.swal2-top-start {
                top: 0;
                left: 0
            }

            body.swal2-no-backdrop .swal2-container.swal2-top-end, body.swal2-no-backdrop .swal2-container.swal2-top-right {
                top: 0;
                right: 0
            }

            body.swal2-no-backdrop .swal2-container.swal2-center {
                top: 50%;
                left: 50%;
                transform: translate(-50%,-50%)
            }

            body.swal2-no-backdrop .swal2-container.swal2-center-left, body.swal2-no-backdrop .swal2-container.swal2-center-start {
                top: 50%;
                left: 0;
                transform: translateY(-50%)
            }

            body.swal2-no-backdrop .swal2-container.swal2-center-end, body.swal2-no-backdrop .swal2-container.swal2-center-right {
                top: 50%;
                right: 0;
                transform: translateY(-50%)
            }

            body.swal2-no-backdrop .swal2-container.swal2-bottom {
                bottom: 0;
                left: 50%;
                transform: translateX(-50%)
            }

            body.swal2-no-backdrop .swal2-container.swal2-bottom-left, body.swal2-no-backdrop .swal2-container.swal2-bottom-start {
                bottom: 0;
                left: 0
            }

            body.swal2-no-backdrop .swal2-container.swal2-bottom-end, body.swal2-no-backdrop .swal2-container.swal2-bottom-right {
                right: 0;
                bottom: 0
            }

        @media print {
            body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
                overflow-y: scroll !important
            }

                body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) > [aria-hidden=true] {
                    display: none
                }

                body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {
                    position: static !important
                }
        }

        body.swal2-toast-shown .swal2-container {
            background-color: transparent
        }

            body.swal2-toast-shown .swal2-container.swal2-top {
                top: 0;
                right: auto;
                bottom: auto;
                left: 50%;
                transform: translateX(-50%)
            }

            body.swal2-toast-shown .swal2-container.swal2-top-end, body.swal2-toast-shown .swal2-container.swal2-top-right {
                top: 0;
                right: 0;
                bottom: auto;
                left: auto
            }

            body.swal2-toast-shown .swal2-container.swal2-top-left, body.swal2-toast-shown .swal2-container.swal2-top-start {
                top: 0;
                right: auto;
                bottom: auto;
                left: 0
            }

            body.swal2-toast-shown .swal2-container.swal2-center-left, body.swal2-toast-shown .swal2-container.swal2-center-start {
                top: 50%;
                right: auto;
                bottom: auto;
                left: 0;
                transform: translateY(-50%)
            }

            body.swal2-toast-shown .swal2-container.swal2-center {
                top: 50%;
                right: auto;
                bottom: auto;
                left: 50%;
                transform: translate(-50%,-50%)
            }

            body.swal2-toast-shown .swal2-container.swal2-center-end, body.swal2-toast-shown .swal2-container.swal2-center-right {
                top: 50%;
                right: 0;
                bottom: auto;
                left: auto;
                transform: translateY(-50%)
            }

            body.swal2-toast-shown .swal2-container.swal2-bottom-left, body.swal2-toast-shown .swal2-container.swal2-bottom-start {
                top: auto;
                right: auto;
                bottom: 0;
                left: 0
            }

            body.swal2-toast-shown .swal2-container.swal2-bottom {
                top: auto;
                right: auto;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%)
            }

            body.swal2-toast-shown .swal2-container.swal2-bottom-end, body.swal2-toast-shown .swal2-container.swal2-bottom-right {
                top: auto;
                right: 0;
                bottom: 0;
                left: auto
            }
    </style>
    <style id="panai-style">
        .panai-container {
            z-index: 99999 !important
        }

        .panai-popup {
            font-size: 14px !important
        }

        .panai-setting-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 20px;
        }

        .panai-setting-checkbox {
            width: 16px;
            height: 16px;
        }
    </style>
</head>
<body>
    <div class="product-page-wrapper">

        <div class="camscanner_banner index_card">
            <div class="bd">
                <ul style="position: relative; width: 1903px; height: 400px;">
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/bg3.jpg&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: list-item;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner1.png&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/camscanner_banner.png&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hd">
                <ul>
                    <li class="on">1</li>
                    <li class="">2</li>
                    <li class="">3</li>
                    <li class="">4</li>
                </ul>
            </div>
        </div>
        <script>jQuery(".camscanner_banner").slide({ titCell: ".hd ul", mainCell: ".bd ul", effect: "fold", autoPlay: true, autoPage: true, trigger: "click", delayTime: 1000, interTime: 5000 });</script>
        <!-- the banner end -->

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>
        
        <!-- 应用场景 -->
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl" style="width: 650px;">
                    <li style="width: 25%; margin-right: 0;"><a class="a_anli" href="#scene-section">应用场景</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_gongneng" href="#feature-section">功能介绍</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_fangan" href="#review-section">用户评价</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_ccc" href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr" style="height: 46px; margin-top: 8px;">
                    <li class="style" style="line-height: 40px; width: 100%;"><a href="<%=Account.Web.CommonRequest.GetDownLoadUrl(Request) %>" style="height: 40px; width: 100%;"><b>立即下载</b></a></li>
                </ul>
            </div>
        </div>

        <div class="main warp a_anli_content">
            <div class="sales_x">
                <div class="biaoti">
                    <p class="tit mtit">电脑扫描仪，随时记录，轻松分享</p>
                    <p class="info minfo">“电脑上最好的100个软件之一”</p>
                </div>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png" alt="pic"></dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png" alt="pic"></dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1 kkel">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png" alt="pic"></dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </div>
        <!-- 产品优势 end -->
        <div class="clear"></div>
        <main>
        <section id="feature-section" class="advantage a_gongneng_content">
            <div class="tu_a">
                <div class="warp">
                    <div class="ccb_tr">
                        <h2 class="ccb_rt_a">AI-智能OCR识别</h2>
                        <div class="ccb_rt_b">
                            <p>截图/拍照/文件，一键操作，深度加速<br>
                                支持识别100+语言，助力全球业务展开<br>
                                账号特权，云端存储，不限设备，不限次数。<br>
                                出差/异地，随时登录，随时使用！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_e">
                <div class="warp">
                    <div class="ccb_tr linx">
                        <h2 class="ccb_rt_a">
                            轻松处理各种文档
                        </h2>
                        <div class="ccb_rt_b">
                            <p>支持Office全家桶(Word,PPT,Excel等)<br>
                                支持PDF文档扫描及转换<br>
                                支持全文扫描，支持全文翻译<br>
                                各种办公文档智能解析，处理结果支持一键下载
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_b">
                <div class="warp">
                    <div class="ccb_lr">
                        <div class="ccb_rt_a">云上OCR，轻享服务</div>
                        <div class="ccb_rt_b">
                            <span>依托于助手高性能服务器，用户只需要一个账号<br>
                                即可轻松享用各种大厂提供的服务<br>
                                众星捧月，只为让您提升工作生活效率！<br>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <section id="review-section" class="advantage a_fangan_content">
            <div class="ying camying">
                <!-- 用户评价 -->
                <div class="user">
                    <h2 class="titx">用户评价</h2>
                    <div class="user_pj">
                        <div class="user_pj_a">
                            <div class="bd bd_x">
                                <div class="tempWrap" style="overflow: hidden; position: relative; width: 1021px">
                                    <ul style="width: 4084px; left: -1992.58px; position: relative; overflow: hidden; padding: 0px; margin: 0px;">
                                        <li style="float: left; width: 1021px;"><span>工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</span></li>
                                        <li style="float: left; width: 1021px;"><span>审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～</span></li>
                                        <li style="float: left; width: 1021px;"><span>门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！有不足但是很棒的软件！</span></li>
                                        <li style="float: left; width: 1021px;"><span>不得不说，对于学生党太好用了，书上的重点都可以OCR识别下来再排版打印，写论文到图书馆拍资料都是用的它，很喜欢。推荐给好多同学用~</span></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="hd bd_xe">
                                <ul>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.005.png" alt="办公族"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">John</span>
                                            <span class="hd_bx_b">办公族</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.006.png" alt="审计"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Abby</span>
                                            <span class="hd_bx_b">审计</span>
                                        </span>
                                    </li>
                                    <li class="on">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.007.png" alt="医生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Han</span>
                                            <span class="hd_bx_b">医生</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.008.png" alt="学生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Mia</span>
                                            <span class="hd_bx_b">学生</span>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <script type="text/javascript">jQuery(".user_pj_a").slide({ mainCell: ".bd ul", effect: "left", autoPlay: true });</script>

                        </div>
                    </div>
                </div>
                <!-- 用户评价 end -->
            </div>
        </div>

        <section id="media-section" class="media warp camsmedia a_ccc_content" style="margin-top: 80px;">
            <h2 class="tit">媒体报道</h2>
            <div class="news_bd">
                <div class="slideBoxx">
                    <div class="bd">
                        <ul>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c103.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">电脑上最好的50个软件之一</a></span>
                            </li>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c102.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">OCR助手，超级扫描仪</a></span>
                            </li>
                            <li style="display: list-item;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c100.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">上班族必备10款应用，OCR助手榜上有名</a></span>
                            </li>
                        </ul>
                    </div>
                    <div class="h36"></div>
                    <div class="hd">
                        <ul>
                            <li class=""></li>
                            <li class=""></li>
                            <li class="on"></li>
                        </ul>
                    </div>
                    <!-- 下面是前/后按钮代码，如果不需要删除即可 -->
                </div>
                <script type="text/javascript">
                    jQuery(".slideBoxx").slide({
                        mainCell: ".bd ul",
                        autoPlay: true
                    });
                </script>
            </div>
        </div>
        </main>
        <br />
        <br />
        <br />
    </div>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/wp-embed.min.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_new.js"></script>
</body>
</html>
