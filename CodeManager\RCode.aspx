﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="RCode.aspx.cs" Inherits="Account.Web.RCode" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link rel="stylesheet" href="/css/style.css" media="screen" type="text/css" />
</head>
<body>
    <section class='container'>
    <form id="form1" runat="server">
		<fieldset class='alpha'>
			<legend><b>Code System</b></legend>
			<div class='frow'><asp:DropDownList ID="cmbType" runat="server" CssClass="item" placeholder='类别' /></div>
			<div class='frow'><asp:TextBox ID="txtDays" runat="server" CssClass="item" placeholder='天数' /></div>
			<div class='frow'><asp:TextBox ID="txtMoney" runat="server" CssClass="item" placeholder='金额' /></div>
			<div class='frow'><asp:TextBox ID="txtApp" runat="server" CssClass="item" placeholder='账号' /></div>
			<div class='frow'><asp:TextBox ID="txtPwd" runat="server" CssClass="item" placeholder='密码' /></div>
			<div class='frow'>
            <asp:Button ID="btnQuery" runat="server" Text="Query" OnClick="btnQuery_Click" Width="33%" />
            <asp:Button ID="btnOK" runat="server" Text="Create/Change" OnClick="btnOK_Click" Width="33%" style="margin-left:5px;" />
            <asp:Button ID="btnForbid" runat="server" Text="Forbid" OnClick="btnForbid_Click" Width="30%" style="margin-left:5px;" />
			</div>
			<div class='frow' style="height:10px;"></div>
			<legend><b>Opreate Info</b></legend>
			<div class='frow'><asp:Label ID="lblMsg" runat="server" Text=""></asp:Label></div>
		</fieldset>

<%--		<fieldset class='charlie'>
			<legend><b>3.</b>完成</legend>
			<div class='frow'><p>您的信息已经填写完整，谢谢！</p></div>
		</fieldset>--%>
	</form>
</section>
</body>
</html>
