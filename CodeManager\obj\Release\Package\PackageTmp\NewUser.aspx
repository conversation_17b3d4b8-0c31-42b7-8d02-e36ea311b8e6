﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="NewUser.aspx.cs" Inherits="Account.Web.NewUser" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head runat="server">
    <title></title>
    <script type="text/javascript" src="https://code.hcharts.cn/highcharts/highcharts.js"></script>
    <style type="text/css">
        .div-container {
            display: flex;
            justify-content: space-between;
        }

        .div-inline {
            width: 48%; /* 或者其他百分比，确保两个div可以并排 */
            margin: 10px; /* 外边距 */
            padding: 20px; /* 内边距 */
            border: 1px solid #ccc; /* 边框 */
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <select id="cmbType" name="type">
                <option value="1">近一周</option>
                <option value="2">近一月</option>
                <option value="3">近三月</option>
                <option value="4">近半年</option>
                <option value="5">近一年</option>
            </select>
            <asp:Button ID="btnQueryUser" runat="server" Text="查询" OnClick="btnQueryUser_Click" />
        </div>
        <div class="div-container">
            <div class="div-inline">
                <div id="containerNew" style="min-width: 400px; height: 400px"></div>
            </div>
            <div class="div-inline">
                <div id="containerOcr"></div>
            </div>
        </div>
        <div class="div-container">
            <%--<div class="div-inline" style="width:35%">
                <div id="containerUser" style="height: 350px"></div>
            </div>--%>
            <div class="div-inline">
                <div id="containerVersion" style="height: 350px;"></div>
            </div>
            <div class="div-inline">
                <div id="containerLang" style="height: 350px;"></div>
            </div>
        </div>
        <asp:HiddenField ID="hidType" runat="server" Value="1" />
        <%
            var strDateType = Equals(hidType.Value, "2") ? "一月"
                : (Equals(hidType.Value, "3") ? "三月"
                : (Equals(hidType.Value, "4") ? "半年"
                : (Equals(hidType.Value, "5") ? "一年" : "一周")));
            var strGroupType = Equals(hidType.Value, "2") ? "周"
                : (Equals(hidType.Value, "3") ? "月"
                : (Equals(hidType.Value, "4") ? "月"
                : (Equals(hidType.Value, "5") ? "月" : "日")));

            //版本数据
            var strVersionData = "";
            //版本数据
            var strLangData = "";

            //新用户汇总信息
            var strTotalUserData = "";
            //新用户X轴
            var strRegDate = "";

            var strNewUserData = "";
            var strPayUserData = "";

            var strLoginDate = "";
            var strLoginUserData = "";
            var strNoLoginUserData = "";
            var strTotalLoginData = "";
            var startDate = "";
            var strGroup = "DATE(";
            var days = 6;
            switch (hidType.Value)
            {
                case "1":
                    days = 6;
                    strGroup = "DATE(";
                    break;
                case "2":
                    days = 30;
                    strGroup = "strftime('%Y-%W',";
                    break;
                case "3":
                    days = 90;
                    strGroup = "strftime('%Y-%m',";
                    break;
                case "4":
                    days = 180;
                    strGroup = "strftime('%Y-%m',";
                    break;
                case "5":
                    days = 365;
                    strGroup = "strftime('%Y-%m',";
                    break;
            }
            startDate = CommonLib.ServerTime.DateTime.AddDays(-days).ToString("yyyy-MM-dd") + " 00:00:00";
            var dtTmp = Account.Web.CodeHelper.GetPayUserData(startDate, strGroup);
            var newCount = 0;
            var payCount = 0;
            foreach (System.Data.DataRow item in dtTmp.Rows)
            {
                strRegDate += "'" + item["Date"] + "',";
                strNewUserData += item["NewCount"] + ",";
                strPayUserData += item["PayCount"] + ",";
                newCount += int.Parse(item["NewCount"].ToString());
                payCount += int.Parse(item["PayCount"].ToString());
            }
            if (dtTmp != null && dtTmp.Rows.Count > 0)
                strTotalUserData = string.Format("总在线:{5},新注册:{0},{4}均:{2},付费:{1},{4}均:{3}"
                    , newCount
                    , payCount
                    , Math.Ceiling(1d * newCount / days)
                    , (1d * payCount / days).ToString("F1")
                    , strGroupType
                    , CommonLib.RdsCacheHelper.LstAccountCache.GetLoginUserCount(CommonLib.ServerTime.DateTime.AddHours(-1)));

            dtTmp = Account.Web.CodeHelper.GetVersionData(startDate, strGroup);
            foreach (System.Data.DataRow item in dtTmp.Rows)
            {
                strVersionData += "{" + "name:'" + item["Date"].ToString() + "(" + item["Count"].ToString() + ")" + "',y:" + item["Count"].ToString() + "},";
            }

            dtTmp = Account.Web.CodeHelper.GetLangData(startDate, strGroup);
            foreach (System.Data.DataRow item in dtTmp.Rows)
            {
                strLangData += "{" + "name:'" + item["Lang"].ToString() + "(" + item["Count"].ToString() + ")" + "',y:" + item["Count"].ToString() + "},";
            }

            //dtTmp = Account.Web.CodeHelper.GetUserData(startDate, strGroup);
            //var loginCount = 0;
            //var noLoginCount = 0;
            //foreach (System.Data.DataRow item in dtTmp.Rows)
            //{
            //    strLoginDate += "'" + item["Date"] + "',";
            //    strLoginUserData += item["RegCount"] + ",";
            //    strNoLoginUserData += (CommonLib.BoxUtil.GetInt32FromObject(item["UnRegCount"]) - CommonLib.BoxUtil.GetInt32FromObject(item["RegCount"])) + ",";
            //    loginCount += int.Parse(item["RegCount"].ToString());
            //    noLoginCount += int.Parse(item["UnRegCount"].ToString());
            //}
            //if (dtTmp != null && dtTmp.Rows.Count > 0)
            //    strTotalLoginData = string.Format("总:{5},{4}均:{6},注册:{0},{4}均:{2},未注册:{1},{4}均:{3}", loginCount, noLoginCount
            //        , Math.Ceiling(1d * loginCount / days), Math.Ceiling(1d * noLoginCount / days)
            //        , strGroupType
            //        , loginCount + noLoginCount, Math.Ceiling(1d * (loginCount + noLoginCount) / days));

            var strOcrSubTitle = "";
            var strOcrData = "";
            var strOcrDate = "";
            long nTotalOcrCount = 0;
            for (DateTime i = DateTime.Parse(startDate); i <= CommonLib.ServerTime.DateTime.Date; i = i.AddDays(1))
            {
                strOcrDate += "'" + i.ToString("MM-dd") + "',";
                var nTodayCount = CommonLib.ApiCountCacheHelper.GetCount("OcrCounts", i);
                strOcrData += nTodayCount + ",";
                nTotalOcrCount += nTodayCount;
            }
            strOcrSubTitle = string.Format("总:{0},{1}均:{2}"
                , nTotalOcrCount
                , strGroupType
                , Math.Ceiling(1d * nTotalOcrCount / days));
        %>
        <script type="text/javascript">
            document.getElementById("cmbType").value = '<%=string.IsNullOrEmpty(hidType.Value)?"1":hidType.Value%>';
            var chartOcr = Highcharts.chart('containerOcr', {
                title: {
                    text: '近<%=strDateType%>识别量统计'
                },
                subtitle: {
                    text: '<%=strOcrSubTitle%>'
                },
                xAxis: {
                    categories: [
    <%=strOcrDate.TrimEnd(',')%>
                    ],
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '识别次数'
                    }
                },
                tooltip: {
                    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y:.0f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {
                    spline: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true
                    },
                    column: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true,
                        borderWidth: 0
                    }
                },
                series: [{
                    name: '日期',
                    type: 'column',
                    data: [<%=strOcrData.TrimEnd(',')%>]
                }]
            });
            var chartNewUser = Highcharts.chart('containerNew', {
                title: {
                    text: '近<%=strDateType%>新用户统计'
                },
                subtitle: {
                    text: '<%=strTotalUserData%>'
                },
                xAxis: {
                    categories: [
                    <%=strRegDate.TrimEnd(',')%>
                    ],
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '用户数'
                    }
                },
                tooltip: {
                    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y:.0f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {
                    spline: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true
                    },
                    column: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true
                    }
                },
                series: [{
                    name: '新注册',
                    type: 'spline',
                    data: [<%=strNewUserData.TrimEnd(',')%>]
                }, {
                        name: '升级',
                        type: 'column',
                        data: [<%=strPayUserData.TrimEnd(',')%>]
                    }]
            });
            var chartVersion = Highcharts.chart('containerVersion', {
                title: {
                    text: '近<%=strDateType%>设备版本统计'
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
                            style: {
                                color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
                            }
                        }
                    }
                },
                series: [{
                    name: '版本',
                    type: 'pie',
                    data: [<%=strVersionData.TrimEnd(',')%>]
                }]
            });
            var chartLang = Highcharts.chart('containerLang', {
                title: {
                    text: '近<%=strDateType%>语言统计'
                },
                tooltip: {
                    pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
                },
                plotOptions: {
                    pie: {
                        allowPointSelect: true,
                        cursor: 'pointer',
                        dataLabels: {
                            enabled: true,
                            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
                            style: {
                                color: (Highcharts.theme && Highcharts.theme.contrastTextColor) || 'black'
                            }
                        }
                    }
                },
                series: [{
                    name: '语言',
                    type: 'pie',
                    data: [<%=strLangData.TrimEnd(',')%>]
                }]
            });
            var chartUser = Highcharts.chart('containerUser', {
                title: {
                    text: '近<%=strDateType%>活跃用户统计'
                },
                subtitle: {
                    text: '<%=strTotalLoginData%>'
                },
                xAxis: {
                    categories: [
    <%=strLoginDate.TrimEnd(',')%>
                    ],
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '用户数'
                    }
                },
                tooltip: {
                    headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y:.0f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: true,
                    useHTML: true
                },
                plotOptions: {
                    spline: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true
                    },
                    column: {
                        dataLabels: {
                            enabled: true
                        },
                        enableMouseTracking: true,
                        borderWidth: 0
                    }
                },
                series: [{
                    name: '未注册',
                    type: 'column',
                    data: [<%=strNoLoginUserData.TrimEnd(',')%>]
                }, {
                        name: '注册用户',
                        type: 'column',
                        data: [<%=strLoginUserData.TrimEnd(',')%>]
                    }]
            });
        </script>
    </form>
</body>
</html>
