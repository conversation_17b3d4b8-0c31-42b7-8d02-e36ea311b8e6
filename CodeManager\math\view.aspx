﻿<%@ Page Language="C#" AutoEventWireup="true" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>OCR助手-公式识别预览</title>
    <script src="contextMenu.js" type="text/javascript"></script>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//cdn.bootcdn.net/ajax/libs/KaTeX/0.15.2/katex.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <link rel="stylesheet" href="//cdn.bootcdn.net/ajax/libs/KaTeX/0.15.2/katex.min.css" onerror="autoRetry(this)" />
    <script src="//cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?features=default,Array.prototype.includes" type="text/javascript"></script>
    <style>
        body, div, span, h2 {
            margin: 0;
            padding: 0
        }

        body {
            background-position: center;
            background-size: cover
        }

        .kinerMenuBox {
            width: 200px;
            background: #FEFEFE;
            border: solid 1px #444;
            cursor: pointer;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 1px 1px 5px #000,-1px -1px 5px #fff;
            -webkit-box-shadow: 1px 1px 5px #000,-1px -1px 5px #fff;
            -moz-box-shadow: 1px 1px 5px #000,-1px -1px 5px #fff;
            -o-box-shadow: 1px 1px 5px #000,-1px -1px 5px #fff;
            font-size: 16px;
            position: absolute
        }

            .kinerMenuBox .kinerMenuTitle {
                background: #DDD;
                padding: 10px 5px;
                border-bottom: inset #444 1px;
                cursor: default
            }

                .kinerMenuBox .kinerMenuTitle .kinerMenuTitleIcon {
                    width: 30px;
                    height: 30px
                }

                .kinerMenuBox .kinerMenuTitle .kinerMenuTitleCon {
                    height: 30px;
                    line-height: 30px;
                    padding-left: 10px;
                    color: #444;
                    font-size: 18px;
                    display: inline-block;
                    vertical-align: top
                }

            .kinerMenuBox .kinerMenuItem {
                background: #FFF;
                padding: 5px 5px
            }

                .kinerMenuBox .kinerMenuItem:hover {
                    background: #CCC
                }

                .kinerMenuBox .kinerMenuItem .kinerMenuItemIcon {
                    display: inline-block;
                    width: 30px;
                    height: 30px
                }

                .kinerMenuBox .kinerMenuItem .kinerMenuItemCon {
                    height: 30px;
                    line-height: 30px;
                    padding-left: 10px;
                    position: relative;
                    color: #444;
                    font-size: 16px;
                    display: inline-block;
                    vertical-align: top
                }

            .kinerMenuBox .kinerSeparator {
                width: 100%;
                height: 1px;
                border-bottom: dashed 1px #444;
                border-left: none;
                border-right: none
            }

            .kinerMenuBox .kinerKeyMap {
                margin-right: 5px;
                right: 10px;
                position: absolute;
                color: #444;
                height: 30px;
                line-height: 30px;
                display: inline-block
            }
    </style>
</head>
<body style="cursor: default">
    <div id="content"></div>
    <script>var contentTxt = ""; document.addEventListener("DOMContentLoaded", function () { contentTxt = new Base64().decode('<%=new System.IO.StreamReader(Request.InputStream, Encoding.UTF8).ReadToEnd()%>'), null != contentTxt && "" != contentTxt && katex.render(contentTxt, document.getElementById("content"), { throwOnError: !1 }), document.getElementsByTagName("body")[0].style.height = window.innerHeight + "px" }), window.onload = function () { new contextMenu({ target: document.body, hasIcon: !0, hasTitle: !1, autoHide: !0, linkClass: !0, menu: { title: { icon: "em_01.png", content: "公式预览" }, items: [{ icon: "copy.png", content: "复制公式", action: function () { const e = document.createElement("input"); document.body.appendChild(e), e.setAttribute("value", contentTxt), e.setAttribute("style", "width:1px;height:1px;"), e.select(), document.execCommand("copy") && (document.execCommand("copy"), alert("复制成功！")), document.body.removeChild(e) }, keymap: "ctrl+C" }, "-", { icon: "refresh.png", content: "刷新预览", action: function () { document.location = document.location }, keymap: "ctrl+R" }] }, classes: { menuBox: { width: "200px", background: "#FEFEFE", border: "solid 1px #333", cursor: "pointer", "border-radius": "10px", overflow: "hidden", "box-shadow": "-1px -1px 5px #000,1px 1px 5px #ccc", "-webkit-box-shadow": "-1px -1px 5px #000,1px 1px 5px #ccc", "-moz-box-shadow": "-1px -1px 5px #000,1px 1px 5px #ccc", "-o-box-shadow": "-1px -1px 5px #000,1px 1px 5px #ccc", "font-size": "16px" }, menuTitle: { self: { background: "#DDDDDD", padding: "10px 5px", "border-bottom": "inset #000 1px", cursor: "default" }, icon: { width: "30px", height: "30px" }, content: { height: "30px", "line-height": "30px", "padding-left": "10px", color: "#333333", "font-size": "18px", display: "inline-block", "vertical-align": "top" } }, item: { self: { background: "#FFF", padding: "5px 5px" }, icon: { width: "30px", height: "30px" }, content: { height: "30px", "line-height": "30px", "padding-left": "10px", color: "#333333", "font-size": "16px", display: "inline-block", "vertical-align": "top" }, hover: { background: "#ccc" }, keymap: { "margin-right": "5px", color: "blue" } }, separator: { width: "100%", height: "1px", border: "solid 1px #000", "border-left": "none", "border-right": "none" } } }) };</script>
    <script type="text/javascript">
        function Base64() {
            // private property 
            _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
            // public method for decoding 
            this.decode = function (input) {
                var output = "";
                var chr1, chr2, chr3;
                var enc1, enc2, enc3, enc4;
                var i = 0;
                input = input.replace(/[^A-Za-z0-9+/=]/g, "");
                while (i < input.length) {
                    enc1 = _keyStr.indexOf(input.charAt(i++));
                    enc2 = _keyStr.indexOf(input.charAt(i++));
                    enc3 = _keyStr.indexOf(input.charAt(i++));
                    enc4 = _keyStr.indexOf(input.charAt(i++));
                    chr1 = (enc1 << 2) | (enc2 >> 4);
                    chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                    chr3 = ((enc3 & 3) << 6) | enc4;
                    output = output + String.fromCharCode(chr1);
                    if (enc3 != 64) {
                        output = output + String.fromCharCode(chr2);
                    }
                    if (enc4 != 64) {
                        output = output + String.fromCharCode(chr3);
                    }
                }
                output = _utf8_decode(output);
                return output;
            }
            // private method for UTF-8 decoding 
            _utf8_decode = function (utftext) {
                var string = "";
                var i = 0;
                var c = c1 = c2 = 0;
                while (i < utftext.length) {
                    c = utftext.charCodeAt(i);
                    if (c < 128) {
                        string += String.fromCharCode(c);
                        i++;
                    } else if ((c > 191) && (c < 224)) {
                        c2 = utftext.charCodeAt(i + 1);
                        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                        i += 2;
                    } else {
                        c2 = utftext.charCodeAt(i + 1);
                        c3 = utftext.charCodeAt(i + 2);
                        string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                        i += 3;
                    }
                }
                return string;
            }
        }
    </script>
</body>
</html>
