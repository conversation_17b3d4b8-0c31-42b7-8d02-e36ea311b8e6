﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="PayList.aspx.cs" Inherits="Account.Web.PayList" Async="true" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <style type="text/css">
        .action-links a {
            margin-right: 5px;
            text-decoration: none;
            color: blue;
        }

        a {
            color: inherit;
            text-decoration: inherit;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            line-height: 1;
            border-radius: .375rem;
            height: 2.5rem;
            padding-left: 1rem;
            padding-right: 1rem;
            gap: .375rem;
            border: 1px solid transparent;
            font-weight: 500;
            font-size: .8125rem;
            outline: none;
        }

        .btn-sm {
            height: 2rem;
            padding-left: .75rem;
            padding-right: .75rem;
            font-weight: 500;
            font-size: .75rem;
            gap: .275rem;
            margin-left: 5px;
        }

        .btn-light {
            color: var(--tw-gray-700);
            border-color: var(--tw-gray-300);
            background-color: var(--tw-light);
        }

            .btn-light:active, .btn-light:hover {
                border-color: var(--tw-gray-300);
                background-color: var(--tw-light-active);
                box-shadow: var(--tw-default-box-shadow);
                color: var(--tw-gray-800);
            }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            账号：<asp:TextBox ID="txtApp" runat="server"></asp:TextBox>
            类别：
            <select id="cmbType" name="type">
                <option value="1">近一周</option>
                <option value="2">近一月</option>
                <option value="3">近三月</option>
                <option value="4">近半年</option>
                <option value="5">近一年</option>
                <option value="6">近三年</option>
            </select>
            <asp:Button ID="btnOnLineUser" runat="server" Text="查询" OnClick="btnAllUser_Click" />
            <%--<asp:Button ID="btnClear" runat="server" Text="清理过期订单" OnClick="btnClear_Click" />--%>
            <a href="NewUser.aspx?pwd=<%=Request["pwd"] %>">用户报表</a>
        </div>
        <%
            var strDateType = Equals(hidType.Value, "2") ? "一月"
                : (Equals(hidType.Value, "3") ? "三月"
                : (Equals(hidType.Value, "4") ? "半年"
                : (Equals(hidType.Value, "6") ? "三年"
                : (Equals(hidType.Value, "5") ? "一年" : "一周"))));
            var strGroupType = Equals(hidType.Value, "2") ? "周"
                : (Equals(hidType.Value, "3") ? "月"
                : (Equals(hidType.Value, "4") ? "月"
                : (Equals(hidType.Value, "6") ? "月"
                : (Equals(hidType.Value, "5") ? "月" : "日"))));

            var startDate = "";
            var days = 6;
            var perAmountPercent = 1;
            switch (hidType.Value)
            {
                case "2":
                    perAmountPercent = 7;
                    days = 30;
                    break;
                case "3":
                    perAmountPercent = 30;
                    days = 90;
                    break;
                case "4":
                    perAmountPercent = 30;
                    days = 180;
                    break;
                case "5":
                    perAmountPercent = 30;
                    days = 365;
                    break;
                case "6":
                    perAmountPercent = 30;
                    days = 365 * 3;
                    break;
            }
            startDate = CommonLib.ServerTime.DateTime.AddDays(-days).ToString("yyyy-MM-dd") + " 00:00:00";
            var dtTmp = Account.Web.PayHelper.GetPayList(startDate, txtApp.Text.Trim());
            dtTmp.Columns.Remove("payId");
            dtTmp.Columns.Remove("closeDate");

            double totalMoney = 0;
            var payCount = 0;
            foreach (System.Data.DataRow item in dtTmp.Rows)
            {
                item["remark"] = CommonLib.BoxUtil.GetStringFromObject(item["remark"]).Replace("OCR助手-", "");
                if (Equals(item["state"].ToString(), "支付成功"))
                {
                    totalMoney += double.Parse(item["reallyPrice"].ToString());
                    payCount += 1;
                }
            }
            dtTmp.Columns["reallyPrice"].ColumnName = "pay";
            gvDataSource.DataSource = dtTmp;
            gvDataSource.DataBind();
            var strInfo = string.Format("总计: {1}/{0} 笔({2} 元),{3}均 {4} 笔,日均 {5} 元"
                , dtTmp == null || dtTmp.Rows.Count <= 0 ? 0 : dtTmp.Rows.Count
                , payCount
                , totalMoney.ToString("F2")
                , strGroupType
                , (1d * payCount * perAmountPercent / days).ToString("F1")
                , Math.Ceiling(1d * totalMoney / days).ToString("F2")
                );
        %>
        <%=strInfo %>
        <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
            BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
            <FooterStyle BackColor="White" ForeColor="#000066" />
            <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
            <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
            <RowStyle ForeColor="#000066" />
            <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
        </asp:GridView>
        <asp:HiddenField ID="hidType" runat="server" Value="2" />
    </form>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//lf3-cdn-tos.bytecdntp.com/cdn/jquery/3.6.0/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript">
        document.getElementById("gvDataSource").style = "width:100%";
        document.getElementById("cmbType").value = '<%=string.IsNullOrEmpty(hidType.Value)?"1":hidType.Value%>';
        // 获取表格的tbody
        const tableBody = document.querySelector('#gvDataSource tbody');

        // 遍历每一行，并添加操作栏
        Array.from(tableBody.rows).forEach(row => {
            if (row.rowIndex == 0) {
                return;
            }
            //row.cells[row.cells.length - 1].style = 'width: 130px';
            if (row.cells[3].innerText == '微信') {
                row.cells[3].style = "color: rgb(23, 198, 83)";
            }
            else if (row.cells[3].innerText == '支付宝') {
                row.cells[3].style = "color: rgb(27, 132, 255)";
            }
            //else {
            //    row.cells[3].style = "box-sizing: border-box; border: 1px solid transparent;width: 70px;align-items: center; justify-content: center; line-height: 11px; border-radius: 4px; padding: 8px;color: red; background-color: rgb(249, 249, 249); font-family: &quot;Microsoft YaHei UI&quot;, system-ui, -apple-system, BlinkMacSystemFont, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Noto Color Emoji&quot;, &quot;Android Emoji&quot;, EmojiSymbols, &quot;EmojiOne Mozilla&quot;, &quot;Twemoji Mozilla&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji Compat&quot;, emoji, &quot;Font Awesome 6 Pro&quot;, &quot;Font Awesome 5 Pro&quot;, FontAwesome, iconfont, icomoon, IcoFont, fontello, themify, &quot;Segoe Fluent Icons&quot;, &quot;Material Design Icons&quot;, bootstrap-icons; text-shadow: var(--fr-font-shadow); -webkit-text-stroke: var(--fr-font-stroke); text-rendering: optimizelegibility; margin: 0px; text-align: start;";
            //}
            // 创建一个新的单元格
            const actionCell = row.cells[row.cells.length - 1];
            //actionCell.className = 'action-links';

            //// 创建查看链接
            //const viewLink = document.createElement('a');
            //viewLink.href = 'javascript:void(0)';
            //viewLink.innerText = '详情';
            //viewLink.onclick = function () {
            //    alert('查看 ' + row.cells[0].innerText);
            //};

            // 创建支付链接
            if (row.cells[4].innerText == '待支付') {
                row.cells[4].style = "color:blue; background-color: rgb(130 255 36);";
                const payLink = document.createElement('a');
                payLink.href = 'javascript:void(0)';
                payLink.innerText = '去支付';
                payLink.className = "btn btn-sm btn-light btn-outline";
                payLink.onclick = function () {
                    window.open('ToPay.aspx?orderId=' + row.cells[0].innerText);
                };
                actionCell.appendChild(payLink);
            } else if (row.cells[4].innerText == '已过期') {
                row.cells[4].style = "color:red; background-color: yellow;";
            } else if (row.cells[4].innerText == '支付成功') {
                row.cells[4].style = "color:green;background-color: rgb(234, 255, 241);";
            }

            // 创建补单链接
            const buDanLink = document.createElement('a');
            buDanLink.href = 'javascript:void(0)';
            buDanLink.innerText = '补单';
            buDanLink.className = "btn btn-sm btn-light btn-outline";
            buDanLink.onclick = function () {
                if (confirm('确定要补单 ' + row.cells[0].innerText + ' 吗？该操作将会将该订单标记为已支付，并向您的服务器发送订单完成通知！')) {
                    $.post("/code.aspx?op=pay_BuDan", "orderId=" + row.cells[0].innerText, function (data) {
                        alert(data.msg);
                    })
                }
            };
            actionCell.appendChild(buDanLink);

            // 创建删除链接
            const deleteLink = document.createElement('a');
            deleteLink.href = 'javascript:void(0)';
            deleteLink.innerText = '删除';
            deleteLink.className = "btn btn-sm btn-light btn-outline";
            deleteLink.style = "color: red;";
            deleteLink.onclick = function () {
                if (confirm('确定要删除 ' + row.cells[0].innerText + ' 吗？')) {
                    $.post("/code.aspx?op=pay_Del", "orderId=" + row.cells[0].innerText, function (data) {
                        alert(data.msg);
                        tableBody.removeChild(row);
                    })
                }
            };
            actionCell.appendChild(deleteLink);

            // 将链接添加到单元格
            //actionCell.appendChild(viewLink);

            // 将操作单元格添加到当前行
            row.appendChild(actionCell);
        });
    </script>
</body>
</html>
