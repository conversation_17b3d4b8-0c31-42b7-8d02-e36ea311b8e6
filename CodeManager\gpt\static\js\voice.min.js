let recorderWorker=new Worker("static/js/transformpcm.worker.js");let buffer=[];let AudioContext=window.AudioContext||window.webkitAudioContext;let notSupportTip="环境暂不支持语音";navigator.getUserMedia=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;recorderWorker.onmessage=function(e){buffer.push(...e.data.buffer)};let audio_state="";class IatRecorder{constructor(e){this.config=e;audio_state="ing";this.appId="a0cd430c";this.apiKey="1af37340963ae9b07bf9d1739eedb3e4"}start(){this.stop();if(AudioContext){audio_state="ing";if(!this.recorder){var e=new AudioContext;this.context=e;this.recorder=e.createScriptProcessor(0,1,1);var t=e=>{var t=this.context.createMediaStreamSource(e);this.mediaStream=t;this.recorder.onaudioprocess=e=>{this.sendData(e.inputBuffer.getChannelData(0))};this.connectWebsocket()};var o=e=>{this.recorder=null;this.mediaStream=null;this.context=null;console.log("请求麦克风失败")};if(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia){navigator.mediaDevices.getUserMedia({audio:true,video:false}).then(e=>{t(e)}).catch(e=>{o(e)})}else{navigator.getUserMedia({audio:true,video:false},e=>{t(e)},function(e){o(e)})}}else{this.connectWebsocket()}}else{var s=navigator.userAgent.toLowerCase().match(/chrome/);console.log(notSupportTip)}}stop(){audio_state="end";try{this.mediaStream.disconnect(this.recorder);this.recorder.disconnect()}catch(e){}}sendData(e){recorderWorker.postMessage({command:"transform",buffer:e})}getHandShakeParams(){var e=this.appId;var t=this.apiKey;var o=Math.floor((new Date).getTime()/1e3);var s=hex_md5(e+o);var r=CryptoJSNew.HmacSHA1(s,t);var i=CryptoJS.enc.Base64.stringify(r);i=encodeURIComponent(i);return"?appid="+e+"&ts="+o+"&signa="+i}connectWebsocket(){var e="wss://rtasr.xfyun.cn/v1/ws";var t=this.getHandShakeParams();console.log(t);e=`${e}${t}`;if("WebSocket"in window){this.ws=new WebSocket(e)}else if("MozWebSocket"in window){this.ws=new MozWebSocket(e)}else{alert(notSupportTip);return null}this.ws.onopen=e=>{this.mediaStream.connect(this.recorder);this.recorder.connect(this.context.destination);setTimeout(()=>{this.wsOpened(e)},500);this.config.onStart&&this.config.onStart(e)};this.ws.onmessage=e=>{this.wsOnMessage(e)};this.ws.onerror=e=>{this.stop();console.log("关闭连接ws.onerror");this.config.onError&&this.config.onError(e)};this.ws.onclose=e=>{this.stop();console.log("关闭连接ws.onclose");this.config.onClose&&this.config.onClose(e)}}wsOpened(){if(this.ws.readyState!==1){return}var e=buffer.splice(0,1280);this.ws.send(new Int8Array(e));this.handlerInterval=setInterval(()=>{if(this.ws.readyState!==1){clearInterval(this.handlerInterval);return}if(buffer.length===0){if(audio_state==="end"){this.ws.send('{"end": true}');console.log("发送结束标识");clearInterval(this.handlerInterval)}return false}var e=buffer.splice(0,1280);if(e.length>0){this.ws.send(new Int8Array(e))}},40)}wsOnMessage(e){let t=JSON.parse(e.data);console.log(t);if(t.action=="started"){console.log("握手成功")}else if(t.action=="result"){if(this.config.onMessage&&typeof this.config.onMessage=="function"){this.config.onMessage(t.data)}}else if(t.action=="error"){console.log("出错了:",t)}}ArrayBufferToBase64(e){var t="";var o=new Uint8Array(e);var s=o.byteLength;for(var r=0;r<s;r++){t+=String.fromCharCode(o[r])}return window.btoa(t)}}class IatTaste{constructor(){var e=new IatRecorder({onClose:()=>{this.stop();this.reset()},onError:e=>{this.stop();this.reset();console.log("WebSocket连接失败")},onMessage:e=>{this.setResult(JSON.parse(e))},onStart:()=>{console.log("onStart");$(".voice-loading").css("display","flex");$(".voice-start").hide();$(".voice-end").css("display","flex")}});this.iatRecorder=e}start(){this.iatRecorder.start()}stop(){this.iatRecorder.stop()}reset(){buffer=[];$(".voice-loading").hide();$(".voice-start").css("display","flex");$(".voice-end").hide()}init(){let e=this;$(".voice-start").click(function(){if(AudioContext&&recorderWorker){e.start()}else{console.log(notSupportTip)}});$(".voice-end").click(function(){e.stop();buffer=[]})}setResult(e){if(audio_state==="end"){return}let t=[];var o=$("input").val();t[e.seg_id]=e;let s="";t.forEach(e=>{if(e.cn.st.type==0){e.cn.st.rt.forEach(e=>{e.ws.forEach(e=>{e.cw.forEach(e=>{console.log(e.w);s+=e.w})})})}if(o.length==0){$("input").val(s)}else{$("input").val($("input").val()+s)}});if($("input").val()){$(".fa-send-o").hide();$(".fa-send").show()}}}var iatTaste=new IatTaste;iatTaste.init();