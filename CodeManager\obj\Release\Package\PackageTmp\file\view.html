﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" charset="utf-8">
    <title>OCR助手-文件预览</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
        }
        html, body {
            height: 100%;
        }
    </style>
</head>
<body>
    <iframe id="iframe" width="100%" height="100%" scrolling="no" frameborder="0">
    </iframe>
    <script type="text/javascript">
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(decodeURI(r[2]));
            return '';
        }
        document.getElementById("iframe").src = "https://view.officeapps.live.com/op/view.aspx?src=" + encodeURIComponent(GetQueryString("url"));
    </script>
</body>
</html>