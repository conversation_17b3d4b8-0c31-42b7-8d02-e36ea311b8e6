﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" charset="utf-8">
    <title>OCR助手-语言播报</title>
</head>
<body>
    <audio id="voice" controls autoplay>
        <embed id="voiceIE" autostart="true" />
    </audio>
    <p id="msg"></p>
    <script type="text/javascript">
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(decodeURI(r[2]));
            return '';
        }
        var url = "https://fanyi.sogou.com/reventondc/synthesis?from=translateweb&text=" + encodeURI(GetQueryString("text")) + "&speed=" + GetQueryString("speed") + "&speaker=" + GetQueryString("speaker");
        voice.src = url;
        voiceIE.src = url;
    </script>
</body>
</html>