﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0">

    <title>OCR助手 | 智能免费OCR文字识别、表格识别及PDF转换工具</title>
    <meta name="description" content="OCR助手提供多功能在线免费OCR解决方案，包括高精度文字识别、智能表格识别、PDF格式转换（PDF转Word/Excel/PPT/JPG）、Word转PDF、图片转PDF等实用功能。支持100+语言，准确度高达98%。">
    <meta name="keywords" content="OCR,文字识别,表格识别,PDF转WORD,提取图片文字,在线文字识别,文档转换,图像转文字,扫描文件识别,Excel转换,PDF转Markdown,PDF转Word,PDF转Excel,PDF转PPT,PDF转JPG,Word转PDF,Excel转PDF,图片转PDF,Word转JPG">
    <link rel="stylesheet" href="static/css/bf406b4dfe4a88f7.css" data-n-g="">
    <link rel="stylesheet" href="static/css/97e00c3eababac2c.css" data-n-p="">
    <link rel="stylesheet" href="static/css/2d170f7bb9587ee4.css" data-n-p="">
    <meta name="robots" content="index, follow" />
</head>
<body>
    <div id="__next" data-reactroot="">
        <main class="y1fU5arT" role="main">
            <div>
                <div class="wUHvzwDX">
                    <main class="xByDAAj_">
                        <div class="IVx7Sant"><h1 class="bltIOZdJ">OCR助手免费OCR工具</h1><h2 class="Ryc5j9WR" style="font-size: 16px; font-weight: normal; margin: inherit;"> 利用OCR助手免费OCR工具，识别文字和表格，转化PDF为WORD，为您的工作生活提升效率</h2></div><div class="olUjsfHS">
                            <div class="ciFXvpTE">
                                <div class="rL4DO8gy" role="heading" aria-level="3">文字识别</div>
                                <div class="ant-row nLFkHDNm" style="margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px">
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="text_recognize.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div>
                                                    <div class="jGh32FHy">
                                                        <div class="mrSAoIfZ">通用文字识别</div><div class="WjiCI_fR">将图片转化成可编辑的word、txt格式</div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="text_accurate.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div>
                                                    <div class="jGh32FHy">
                                                        <div class="mrSAoIfZ">印刷体文字识别</div><div class="WjiCI_fR">将图片转化成可编辑的word、txt格式</div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="handwritten_ocr.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">手写识别</div><div class="WjiCI_fR">将图片转化成可编辑的word、txt格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="ancient_ocr.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">古籍识别</div><div class="WjiCI_fR">将图片转化成可编辑的word、txt格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ciFXvpTE">
                                <div class="rL4DO8gy" role="heading" aria-level="3">表格识别</div>
                                <div class="ant-row nLFkHDNm" style="margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px">
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="table.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div>
                                                    <div class="jGh32FHy"><div class="mrSAoIfZ">通用表格识别</div><div class="WjiCI_fR">将图片中表格转化成可编辑的Excel文件</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="table_frame.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div>
                                                    <div class="jGh32FHy"><div class="mrSAoIfZ">带边框表格识别</div><div class="WjiCI_fR">将图片中表格转化成可编辑的Excel文件</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="table_noframe.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div>
                                                    <div class="jGh32FHy"><div class="mrSAoIfZ">无边框表格识别</div><div class="WjiCI_fR">将图片中表格转化成可编辑的Excel文件</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ciFXvpTE">
                                <div class="rL4DO8gy" role="heading" aria-level="3">PDF转文件</div><div class="ant-row nLFkHDNm" style="margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px">
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="pdf2word.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-pdf-word402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">PDF转Word</div><div class="WjiCI_fR">将PDF文件转化为Word格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="pdf2markdown.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-pdf-md402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">PDF转Markdown</div><div class="WjiCI_fR">将PDF文件转化为Markdown格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="pdf2excel.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-pdf-excel402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">PDF转Excel</div><div class="WjiCI_fR">将PDF文件转化为Excel格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="pdf2jpg.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-pdf-jpg402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">PDF转JPG</div><div class="WjiCI_fR">将PDF文件转化为JPG格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="pdf2ppt.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-pdf-ppt402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">PDF转PPT</div><div class="WjiCI_fR">将PDF文件转化为PowerPoint文档</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div><div class="ciFXvpTE">
                                <div class="rL4DO8gy" role="heading" aria-level="3">其他转换</div><div class="ant-row nLFkHDNm" style="margin-left:-6px;margin-right:-6px;margin-top:-6px;margin-bottom:-6px">
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="word2pdf.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-word-pdf402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">Word转PDF</div><div class="WjiCI_fR">将Word文件转化为PDF格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div><div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="excel2pdf.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-excel-pdf402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">Excel转PDF</div><div class="WjiCI_fR">将Excel文件转化为PDF格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="image2pdf.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-jpg-pdf402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">图片转PDF</div><div class="WjiCI_fR">将png/jpg/bmp图片转化为PDF</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                    <div style="padding-left:6px;padding-right:6px;padding-top:6px;padding-bottom:6px" class="ant-col ant-col-6">
                                        <div class="__4TTnAZxA">
                                            <a href="word2jpg.html" target="_self" class="custom-link">
                                                <div class="la4za2iU">
                                                    <div class="nyfQamHt">
                                                        <span style="box-sizing:border-box;display:inline-block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative;max-width:100%">
                                                            <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;max-width:100%"><img style="display:block;max-width:100%;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0" alt="" aria-hidden="true" src="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2742%27%20height=%2742%27/%3e"></span>
                                                            <img alt="icon" src="static/picture/image-2Ficons2Fhome2Ficon-word-jpg402x.png_96_75.png" decoding="async" data-nimg="intrinsic" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                                        </span>
                                                    </div><div class="jGh32FHy"><div class="mrSAoIfZ">Word转JPG</div><div class="WjiCI_fR">将Word文件转化为JPG格式</div></div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </main>
    </div>
    <script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/","query":{},"buildId":"fmSVWroe3Il4vJrWJlNDc","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script>
</body>
</html>