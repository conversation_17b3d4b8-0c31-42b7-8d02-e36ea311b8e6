new Vue({el:"#pageContainer",data:{allTools:window.FhConfig.toolMap,screenshots:window.FhConfig.screenshots,installIng:!1},mounted(){let e=new URL(location.href).searchParams.get("action");e&&["install-chrome","install-firefox","install-msedge"].includes(e)&&this.install()},methods:{install(e){if(/Edg/.test(navigator.userAgent))window.open("https://microsoftedge.microsoft.com/addons/detail/feolnkbgcbjmamimpfcnklggdcbgakhe?hl=zh-CN");else if(/Firefox/.test(navigator.userAgent))fetch("/fe/web-files/firefox.updates.json").then(e=>e.text()).then(e=>{try{let t=(e=new Function(`return ${e}`)()).addons["<EMAIL>"].updates[0],n=(t.version,t.update_link);window.InstallTrigger?InstallTrigger.install({FeHelper:{URL:n,IconURL:"../static/img/fe-48.png",toString:function(){return this.URL}}}):location.href=n}catch(e){console.log(e)}});else if(chrome.app.isInstalled)alert("你已经安装过这个chrome扩展了");else{this.installIng=!0;let e="https://chrome.google.com/webstore/detail/pkgccpejnmalmdinmhkkfafefagiiiad";chrome.webstore&&chrome.webstore.install&&chrome.webstore.install(e,()=>{alert("恭喜你，Chrome Extension安装成功"),this.installIng=!1},e=>{alert("抱歉，Chrome Extension安装失败"),this.installIng=!1})}e&&(e.preventDefault(),e.stopPropagation())},preClick(e,t){this.allTools[e].extensionOnly&&(alert(`你好，${this.allTools[e].name} 工具只能在浏览器插件中使用，如果你还没安装FeHelper插件，就快去安装吧！`),t.preventDefault(),t.stopPropagation())}}});