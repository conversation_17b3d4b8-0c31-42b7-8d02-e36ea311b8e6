﻿using Account.Web.pay;
using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;

namespace Account.Web
{
    public class PayUtil
    {
        static PayUtil()
        {
            PayProcessThread();
        }

        public static void Init()
        {
            Task.Factory.StartNew(() =>
            {
                while (true)
                {
                    Thread.Sleep(new Random().Next(30, 60) * 60);
                    PayHelper.setTimeout();
                }
            });
        }

        private const string strProductName = "OCR助手";
        public static BlockingCollection<PayOrderEntity> PayPool = new BlockingCollection<PayOrderEntity>();
        private static JavaScriptSerializer JavaScriptSerializer = new JavaScriptSerializer();

        public static bool ValidateMd5(string payId, string param, string price, string reallyPrice, int type, string remark, string fromSign)
        {
            var sign = CommonEncryptHelper.MD5(payId + param + type + price + reallyPrice + ConfigHelper.PayToken);
            var result = Equals(fromSign, sign);
            if (result)
            {
                PushPayMessage(payId, param, Math.Round(BoxUtil.GetDoubleFromObject(price), 2), Math.Round(BoxUtil.GetDoubleFromObject(reallyPrice), 2), type, remark);
            }
            return result;
        }

        public static void PushPayMessage(string payId, string param, double price, double reallyPrice, int type, string remark)
        {
            PayPool.Add(new PayOrderEntity()
            {
                payId = payId,
                param = param,
                price = price,
                reallyPrice = reallyPrice,
                remark = remark,
                type = type,
            });
        }

        private static BlockingCollection<string> PayedPool = new BlockingCollection<string>();

        private static void PayProcessThread()
        {
            new Thread(thread =>
                {
                    try
                    {
                        foreach (var pay in PayPool.GetConsumingEnumerable())
                        {
                            if (PayedPool.Any(p => Equals(p, pay.payId)))
                            {
                                continue;
                            }

                            var payStr = JavaScriptSerializer.Serialize(pay);
                            if (string.IsNullOrEmpty(pay.param) || string.IsNullOrEmpty(pay.remark))
                            {
                                LogHelper.Log.Info("支付参数为空，不处理订单！" + payStr);
                                continue;
                            }

                            PayedPool.Add(pay.payId);

                            var isSuccess = false;
                            try
                            {
                                var regType = "";
                                var regDays = 0;
                                var remark = "";

                                var user = CodeHelper.GetCodeByAccountId(pay.param);
                                if (user == null || string.IsNullOrEmpty(user.StrAppCode))
                                {
                                    // - 用户信息不存在，不处理订单：{"payId":"2024031215043195585","price":"36.0","type":"1","realPrice":"36.0","remark":"OCR助手-终身个人版","param":"***********","sign":"884e400d438e5bb4b27bf83a71b0b1ef"}
                                    user = CodeEntity.GetNewEntity(pay.param, pay.param);
                                    LogHelper.Log.Info("用户信息不存在，注册新用户：" + payStr);
                                }

                                if (string.IsNullOrEmpty(user.StrRemark) || !user.StrRemark.Contains(pay.payId))
                                {
                                    var userType = Code.GetCanRegUserTypes().FirstOrDefault(p => pay.remark.EndsWith(p.Type.ToString()));
                                    if (userType != null && userType.UserChargeType.Count > 0)
                                    {
                                        regType = userType.Type.ToString();
                                        var priceType = userType.UserChargeType.FirstOrDefault(p => pay.remark.Contains(p.Name)) ??
                                                     userType.UserChargeType.FirstOrDefault();
                                        if (priceType != null)
                                        {
                                            var oldPrice = Math.Round(BoxUtil.GetDoubleFromObject(pay.price), 2);
                                            var newPrice = priceType.Price;
                                            isSuccess = Equals(oldPrice, newPrice);
                                            remark = string.Format("{2}:{0},{1}元", priceType.Name + userType.Name, pay.reallyPrice, pay.payId);
                                            if (isSuccess)
                                            {
                                                switch (priceType.Name.TrimEnd('版'))
                                                {
                                                    case "1月":
                                                    case "一月":
                                                        regDays = 30;
                                                        break;
                                                    case "1季":
                                                    case "一季":
                                                    case "1季度":
                                                    case "一季度":
                                                        regDays = 30 * 3;
                                                        break;
                                                    case "半年":
                                                        regDays = 30 * 6;
                                                        break;
                                                    case "1年":
                                                    case "一年":
                                                        regDays = 365;
                                                        break;
                                                    case "3年":
                                                    case "三年":
                                                        regDays = 365 * 3;
                                                        break;
                                                    case "5年":
                                                    case "五年":
                                                        regDays = 365 * 5;
                                                        break;
                                                    case "终身":
                                                    case "终生":
                                                        regDays = 365 * 100;
                                                        break;
                                                    default:
                                                        LogHelper.Log.Info("未找到匹配的时间类型：" + priceType.Name);
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                LogHelper.Log.Info("价格与实际不匹配，不处理订单：" + payStr);
                                            }
                                        }
                                        else
                                        {
                                            LogHelper.Log.Info("价格类型未找到，不处理订单：" + payStr);
                                        }
                                    }
                                    else
                                    {
                                        LogHelper.Log.Info("用户类型未找到，不处理订单：" + payStr);
                                    }
                                }
                                else
                                {
                                    LogHelper.Log.Info("已经处理过了，不处理订单：" + payStr);
                                }

                                if (isSuccess)
                                {
                                    //类型相同，直接叠加
                                    if (Equals(regType, user.StrType))
                                    {
                                        user.DtExpire = (ServerTime.LocalTime > user.DtExpire ? ServerTime.LocalTime : user.DtExpire).AddDays(regDays);
                                    }
                                    else
                                    {
                                        //类型不同，重新计算
                                        user.DtExpire = ServerTime.LocalTime.AddDays(regDays);
                                    }
                                    user.StrType = regType;
                                    user.StrRemark = remark;
                                    isSuccess = CodeHelper.AddOrUpdateCode(user);
                                    RdsCacheHelper.LstAccountCache.Remove(user.StrAppCode);
                                }
                            }
                            catch (Exception oe)
                            {
                                Console.WriteLine(oe.Message);
                            }
                            LogHelper.Log.Info("PayInfo 处理" + (isSuccess ? "成功" : "失败") + ":" + payStr);
                        }
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("PayProcess Error", oe);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        public static string appHeart(string t, string sign)
        {
            string result;
            var request = NewPayUtil.appHeart(t, sign);
            result = JsonConvert.SerializeObject(request);
            LogHelper.Log.Error("AppHeart请求,"
                + Environment.NewLine + "参数：" + ("t=" + t + "&sign=" + sign)
                + Environment.NewLine + "结果：" + result);
            return result;
        }

        public static string appPush(int type, string price, string remark, string t, string sign)
        {
            string result;
            var request = NewPayUtil.appPush(type, price, remark, t, sign);
            result = JsonConvert.SerializeObject(request);
            LogHelper.Log.Error("AppPush请求,"
                + Environment.NewLine + "参数：" + ("type=" + type + "&price=" + price + "&remark=" + remark + "&t=" + t + "&sign=" + sign)
                + Environment.NewLine + "结果：" + result);
            return result;
        }

        public static string BuDan(string orderId)
        {
            var request = NewPayUtil.BuDan(orderId);
            return JsonConvert.SerializeObject(request);
        }

        public static string DelOrder(string orderId)
        {
            var request = NewPayUtil.DelByOrder(orderId);
            return JsonConvert.SerializeObject(request);
        }

        public static string GetPayOrderInfo(string orderNo)
        {
            var order = NewPayUtil.GetPayOrderInfo(orderNo);
            return JsonConvert.SerializeObject(order);
        }

        public static string CheckPayOrderState(string orderNo)
        {
            var order = NewPayUtil.CheckPayOrderState(orderNo);
            return JsonConvert.SerializeObject(order);
        }

        public static string ChangePayType(string orderNo, int payType)
        {
            var order = NewPayUtil.ChangePayType(orderNo, payType);
            return JsonConvert.SerializeObject(order);
        }

        public static string GetPayUrl(string price, string orderNo, string remark, string account, OrderFrom from, int count = 0)
        {
            var result = "";
            count++;
            if (!string.IsNullOrEmpty(remark) && !string.IsNullOrEmpty(account))
            {
                result = "";
                var orderId = string.Empty;
                var order = NewPayUtil.createOrder(orderNo, account, strProductName + "-" + remark, price, from);
                orderId = order?.orderId;
                if (!string.IsNullOrEmpty(orderId))
                {
                    result = "http://ocr.oldfish.cn/ToPay.aspx?orderId=" + orderId;
                }
                LogHelper.Log.Error("发起支付结果 " + ",OrderId:" + orderId + ",Url:" + result + ",重试次数:" + count);
                if (string.IsNullOrEmpty(result) && count < 10)
                {
                    Thread.Sleep(100);
                    return GetPayUrl(price, orderNo, remark, account, from, count);
                }
                if (string.IsNullOrEmpty(result))
                {
                    result = "支付系统繁忙，请稍后重试！";
                }
            }
            return result;
        }
    }

    public class PayOrderEntity
    {
        /// <summary>
        /// 支付云端唯一订单号
        /// </summary>
        public string orderId { get; set; }

        public string payId { get; set; }

        /// <summary>
        /// 订单价格
        /// </summary>
        public double price { get; set; }

        /// <summary>
        /// 实际支付价格
        /// </summary>
        public double reallyPrice { get; set; }

        /// <summary>
        /// 支付类型 0：默认，1：微信 2：支付宝
        /// </summary>
        public int type { get; set; }

        /// <summary>
        /// 收款备注
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// 订单自定义参数，会原封返回给异步接口和同步接口
        /// </summary>
        public string param { get; set; }

        /// <summary>
        /// 支付完成后跳转地址
        /// </summary>
        public string returnUrl { get; set; }

        //创建时间
        public string createDate { get; set; }

        //支付时间
        public string payDate { get; set; }

        //支付过期/订单关闭时间
        public string closeDate { get; set; }

        /// <summary>
        /// 订单状态  -1：订单过期 0：等待支付 1：支付成功 2：通知失败，待处理
        /// </summary>
        public int state { get; set; }

        /// <summary>
        /// 是否为通用二维码，1为通用二维码 0为固定转账二维码
        /// </summary>
        public int isAuto { get; set; }

        /// <summary>
        /// 二维码内容
        /// </summary>
        public string payUrl { get; set; }
        public string wxPayUrl { get; set; }
        public string zfbPayUrl { get; set; }

        /// <summary>
        /// 需要用户手动输入付款金额
        /// </summary>
        public bool needUserPay { get; set; }

        public string qq { get; set; }
        public int timeOut { get; set; }
        public long date { get; set; }

        public int payType { get { return type; } }

        public OrderFrom from { get; set; }

        public string getNotifyContent()
        {
            string p = "payId=" + payId
                        + "&param=" + (string.IsNullOrEmpty(param) ? "" : HttpUtility.UrlEncode(param, Encoding.UTF8))
                        + "&type=" + type
                        + "&price=" + price
                        + "&reallyPrice=" + reallyPrice;
            var sign = CommonEncryptHelper.MD5(payId + param + type + price + reallyPrice + ConfigHelper.PayToken);
            p = p + "&sign=" + sign
                    + "&remark=" + (string.IsNullOrEmpty(remark) ? "" : HttpUtility.UrlEncode(remark, Encoding.UTF8));
            return p;
        }
    }

    public enum PayType
    {
        微信 = 1,
        支付宝 = 2,
        _360 = 3
    }

    public enum OrderFrom
    {
        Self,
        _360,
        Other
    }
}