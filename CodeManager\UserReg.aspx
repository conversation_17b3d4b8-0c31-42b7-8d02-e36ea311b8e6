﻿<%@ Page Title="新用户注册" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <style>
        .auth-wrapper { min-height: calc(100vh - 120px); display: flex; align-items: center; justify-content: center; padding: 40px 20px; background: url('<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner_2.jpg.webp') center/cover no-repeat fixed; }
        .auth-container { width: 100%; max-width: 450px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); padding: 40px; border: 1px solid rgba(255, 255, 255, 0.2); }
        .auth-header { text-align: center; margin-bottom: 30px; }
        .auth-title { font-size: 24px; font-weight: 600; color: #333; margin-bottom: 8px; }
        .auth-subtitle { font-size: 14px; color: #666; margin: 0; }
        .auth-form input[type="text"], .auth-form input[type="password"] { width: 100%; height: 48px; padding: 0 16px; border: 1px solid #e0e0e0; border-radius: 4px; font-size: 14px; margin-bottom: 16px; transition: border-color 0.3s; background: rgba(255, 255, 255, 0.9); }
        .auth-form input:focus { outline: none; border-color: #1764ff; box-shadow: 0 0 0 2px rgba(23, 100, 255, 0.1); }
        .verify-wrapper { display: flex; gap: 12px; margin-bottom: 16px; }
        .verify-wrapper input { flex: 1; margin-bottom: 0; }
        .verify-btn { height: 48px; padding: 0 16px; background: #f5f5f5; border: 1px solid #e0e0e0; border-radius: 4px; color: #666; font-size: 14px; cursor: pointer; transition: all 0.3s; white-space: nowrap; }
        .verify-btn:hover { background: #e8e8e8; border-color: #d0d0d0; }
        .verify-btn.mod-btn-disabled-gray { color: #999 !important; background-color: #f0f0f0 !important; cursor: not-allowed !important; }
        .agreement-wrapper { margin-bottom: 20px; display: flex; align-items: flex-start; font-size: 14px; line-height: 1.5; }
        .agreement-wrapper input[type="checkbox"] { width: auto; height: auto; margin: 2px 8px 0 0; flex-shrink: 0; }
        .agreement-link { color: #1764ff; text-decoration: none; margin-left: 4px; }
        .agreement-link:hover { text-decoration: underline; }
        .auth-button { width: 100%; height: 48px; background: linear-gradient(135deg, #1764ff 0%, #0d47a1 100%); border: none; border-radius: 4px; color: white; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s; margin-bottom: 20px; }
        .auth-button:hover { background: linear-gradient(135deg, #0d47a1 0%, #1764ff 100%); transform: translateY(-1px); box-shadow: 0 6px 20px rgba(23, 100, 255, 0.3); }
        .auth-links { text-align: center; margin-top: 20px; }
        .auth-links a { color: #1764ff; text-decoration: none; font-size: 14px; }
        .auth-links a:hover { text-decoration: underline; }
        @media (max-width: 480px) { .auth-wrapper { padding: 20px 15px; background-attachment: scroll; } .auth-container { padding: 30px 20px; margin: 0 10px; background: rgba(255, 255, 255, 0.98); } .auth-title { font-size: 20px; } .verify-wrapper { flex-direction: column; gap: 8px; } .verify-btn { width: 100%; } }
    </style>

    <div class="auth-wrapper">
        <div class="auth-container">
            <div class="auth-header">
                <h1 class="auth-title">账号注册</h1>
                <p class="auth-subtitle">AI智能一站式平台，专注提升生产力！</p>
            </div>
            <div class="auth-form">
                <input name="account" type="text" placeholder="请输入登录账号(邮箱/手机号)" autocomplete="username" required />
                <div class="verify-wrapper">
                    <input name="verify" type="text" placeholder="请输入收到的验证码！" autocomplete="off" required />
                    <button type="button" id="btn_vcode" class="verify-btn" data-original-text="发送验证码">发送验证码</button>
                </div>
                <input name="nickname" type="text" placeholder="请输入您的昵称" autocomplete="off" required />
                <input name="password" type="password" placeholder="请输入6-15位密码，大小写字母、数字" autocomplete="new-password" required />
                <div class="agreement-wrapper">
                    <input name="agreement" type="checkbox" checked="checked" id="agreement" required />
                    <label for="agreement">我已阅读并同意</label>
                    <a class="agreement-link" href="UserAgreement.aspx" target="_blank">用户协议</a>
                </div>
                <button type="button" id="btnRegister" class="auth-button" data-original-text="创建账号">创建账号</button>
                <div class="auth-links">
                    已有账号？<a href="Login.aspx">点击登录</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://static.geetest.com/v4/gt4.js"></script>
    <script src="static/js/auth-common.js"></script>
    <script>
        function validateRegForm(){var a=document.querySelector('input[name="account"]').value.trim(),pw=document.querySelector('input[name="password"]').value.trim(),v=document.querySelector('input[name="verify"]').value.trim(),n=document.querySelector('input[name="nickname"]').value.trim(),ag=document.querySelector('input[name="agreement"]').checked;if(!a){showMessage('请输入登录账号','error');return false;}if(!validateAccount(a)){showMessage('请输入正确的邮箱或手机号','error');return false;}if(!validateVerifyCode(v)){showMessage('请输入验证码','error');return false;}if(!validatePassword(pw)){showMessage('密码长度必须为6-16位','error');return false;}if(!validateNickname(n)){showMessage('昵称长度必须为2-20位','error');return false;}if(!ag){showMessage('请先同意用户协议','error');return false;}return true;}
        function submitRegister(){if(!validateRegForm())return;var btn=document.getElementById('btnRegister');setButtonLoading(btn,true,'注册中...');var fd=new FormData();fd.append('account',document.querySelector('input[name="account"]').value.trim());fd.append('password',document.querySelector('input[name="password"]').value.trim());fd.append('verify',document.querySelector('input[name="verify"]').value.trim());fd.append('nickname',document.querySelector('input[name="nickname"]').value.trim());ajaxSubmit('User.ashx?op=register',fd,function(err,resp){setButtonLoading(btn,false);if(err){showMessage(err,'error');}else if(resp.success){showMessage(resp.message,'success');setTimeout(function(){window.location=resp.data.redirectUrl;},2000);}else{showMessage(resp.message,'error');}});}
        initGeetestForSend('1946e559ec0dd0b1102aeb4d8b58fe19',function(){sendVerifyCode(document.querySelector('input[name="account"]').value.trim(),'regaccount','btn_vcode');});document.getElementById('btn_vcode').addEventListener('click',function(){var a=document.querySelector('input[name="account"]').value.trim();if(!a){showMessage('请先输入登录账号','error');return;}showSendCaptcha();});
        document.getElementById('btnRegister').addEventListener('click',submitRegister);bindEnterKey('btnRegister');
    </script>
</asp:Content>
