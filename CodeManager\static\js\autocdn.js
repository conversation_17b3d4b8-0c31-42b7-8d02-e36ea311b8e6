
const FALLBACK_PREFIXES = ['//cdn.staticfile.net', '//s0.pstatp.com/cdn', 'https://cdnjs.cloudflare.com/ajax/libs', '//s1.pstatp.com/cdn', '//s2.pstatp.com/cdn', '//s3.pstatp.com/cdn', '//s4.pstatp.com/cdn', '//s6.pstatp.com/cdn'];
function autoRetry(element, attempt = 0) {
    const originalURL = element.src || element.href;
    if (!originalURL) return;

    if (attempt >= FALLBACK_PREFIXES.length) {
        console.error(`次数超限：${FALLBACK_PREFIXES.length}，Url: ${originalURL}`);
        return;
    }

    let sourceIndex = -1;

    const cleanOriginal = originalURL.replace(/^https?:/i, '');
    FALLBACK_PREFIXES.forEach((prefix, index) => {
        const cleanPrefix = prefix.replace(/^https?:/i, '');
        if (cleanOriginal.startsWith(cleanPrefix.replace(/\/+$/, '') + '/')) {
            sourceIndex = index;
            console.log(`匹配到[${index}]: ${prefix}`);
        }
    });

    const currentIndex = (sourceIndex + 1) % FALLBACK_PREFIXES.length;

    const newPath = sourceIndex === -1 ? new URL(originalURL, location.href).pathname : originalURL.substring(originalURL.indexOf(FALLBACK_PREFIXES[sourceIndex]) + FALLBACK_PREFIXES[sourceIndex].length)

    const newURL = FALLBACK_PREFIXES[currentIndex] + newPath;

    console.log(`重试：${attempt + 1}/${FALLBACK_PREFIXES.length}`, {
        originalURL,
        constructedURL: newURL
    });

    const newElement = document.createElement(element.tagName.toLowerCase());
    try {
        [...element.attributes].forEach(attr => {
            if (attr.name !== 'src' && attr.name !== 'href') {
                newElement.setAttribute(attr.name, attr.value);
            }
        });
        if (element.tagName === 'LINK') {
            newElement.href = newURL;
        } else {
            newElement.src = newURL;
        }

        newElement.onerror = () => {
            autoRetry(newElement, attempt + 1);
        };

        element.insertAdjacentElement('afterend', newElement);
        element.remove();
    } catch (e) { }
}