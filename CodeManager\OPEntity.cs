﻿using System;

namespace Account.Web
{
    public class OPEntity
    {
        private string strApp = "";
        private string strMachine = "";
        private DateTime dtReg = DateTime.MinValue;
        private DateTime dtExpired = DateTime.MinValue;
        private string strVersion = "";
        private string strUser = "";
        private string strOp = "";

        public DateTime DtReg
        {
            get { return dtReg; }
            set { dtReg = value; }
        }

        public DateTime DtExpired
        {
            get { return dtExpired; }
            set { dtExpired = value; }
        }

        public string StrApp
        {
            get { return strApp; }
            set { strApp = value; }
        }

        public string StrMachine
        {
            get { return strMachine; }
            set { strMachine = value; }
        }

        public string StrVersion
        {
            get { return strVersion; }
            set { strVersion = value; }
        }

        public string StrUser
        {
            get { return strUser; }
            set { strUser = value; }
        }

        public string StrOp
        {
            get { return strOp; }
            set { strOp = value; }
        }

        public bool IsValidate
        {
            get
            {
                return !string.IsNullOrEmpty(StrApp) && StrApp.Length == 32
                    && !StrApp.Contains(" ") && !StrApp.Contains("(") && !StrApp.Contains("'") && !StrApp.Contains(";") && !StrApp.Contains("=");
            }
        }
    }
}