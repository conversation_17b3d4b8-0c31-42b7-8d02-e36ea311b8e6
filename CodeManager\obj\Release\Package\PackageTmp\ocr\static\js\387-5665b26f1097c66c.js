(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
    [387], {
        93619: function(e, t, r) {
            "use strict";
            var n = r(67294),
                a = r(30381),
                s = r.n(a),
                c = r(69519),
                i = r(90328),
                o = r.n(i),
                u = r(85893);
            t.Z = function(e) {
                var t = e.data,
                    r = c.ZP.useContainer(),
                    a = r.pageInfo,
                    i = r.responseDate,
                    l = (0,
                        n.useState)(),
                    p = l[0],
                    d = l[1],
                    f = (0,
                        n.useRef)();
                (0,
                    n.useEffect)((function() {
                    return i && "number" === typeof i ? f.current = setInterval((function() {
                            d((function(e) {
                                return (e || i) + 1e3
                            }))
                        }), 1e3) : (clearInterval(f.current),
                            d(0)),
                        function() {
                            clearInterval(f.current)
                        }
                }), [i]);
                var v = (0,
                    n.useMemo)((function() {
                    if (!p)
                        return null;
                    var e = s()(new Date(p)).format("YYYY年MM月DD日 HH:mm").split(":");
                    return (0,
                        u.jsxs)("span", {
                        className: o()["header-time"],
                        children: [(0,
                            u.jsx)("span", {
                            children: e[0]
                        }), (0,
                            u.jsx)("span", {
                            className: o()["header-time-animate"],
                            children: ":"
                        }), (0,
                            u.jsx)("span", {
                            children: e[1]
                        })]
                    })
                    }), [p]);
                var txtTitle = a.title || t.title;
                var locRef = location.href.toString().toLocaleLowerCase();
                if (locRef.indexOf('accurate') > 0) {
                    txtTitle = '免费印刷体文字识别(OCR)工具';
                } else if (locRef.indexOf('table_frame') > 0) {
                    txtTitle = '免费带边框的表格识别(OCR)工具';
                } else if (locRef.indexOf('table_noframe') > 0) {
                    txtTitle = '免费无边框的表格识别(OCR)工具';
                }
                return (0,
                    u.jsxs)("div", {
                    className: o()["page-header-wrap"],
                    children: [(0,
                        u.jsx)("div", {
                        className: o()["api-price-wrap"],
                        children: v
                    }), (0,
                        u.jsxs)("div", {
                        className: o()["page-header-title"],
                            children: ["OCR助手 ", " "+txtTitle]
                    }), !!a.desc && (0,
                        u.jsx)("div", {
                        className: o()["page-header-desc"],
                        children: Array.isArray(a.desc) ? a.desc.map((function(e) {
                            return (0,
                                u.jsx)("div", {
                                children: e
                            }, e)
                        })) : a.desc
                    })]
                })
            }
        },
        10130: function(e, t, r) {
            "use strict";
            r.d(t, {
                Z: function() {
                    return p
                }
            });
            var n = r(59499),
                a = (r(67294),
                    r(45771)),
                s = r(30687),
                c = r.n(s),
                i = r(85893);

            function o(e, t) {
                var r = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(e);
                    t && (n = n.filter((function(t) {
                            return Object.getOwnPropertyDescriptor(e, t).enumerable
                        }))),
                        r.push.apply(r, n)
                }
                return r
            }

            function u(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var r = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? o(Object(r), !0).forEach((function(t) {
                        (0,
                            n.Z)(e, t, r[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : o(Object(r)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t))
                    }))
                }
                return e
            }
            var l = function(e) {
                return (0,
                    i.jsx)("svg", u(u({}, e), {}, {
                    children: (0,
                        i.jsx)("path", {
                        d: "M10 2a8 8 0 1 1 0 16 8 8 0 0 1 0-16Zm0 1.2a6.8 6.8 0 1 0 0 13.6 6.8 6.8 0 0 0 0-13.6Zm0 5.49a.6.6 0 0 1 .6.6v4.8a.6.6 0 1 1-1.2 0v-4.8a.6.6 0 0 1 .6-.6ZM10 6a.8.8 0 1 1 0 ******* 0 0 1 0-1.6Z",
                        fill: "currentColor",
                        fillRule: "evenodd"
                    })
                }))
            };
            l.defaultProps = {
                width: "20",
                height: "20",
                xmlns: "http://www.w3.org/2000/svg"
            };
            var p = function() {
                return (0,
                    i.jsxs)("div", {
                    className: c()["privacy-tips"],
                    children: [(0,
                        i.jsx)("span", {
                        className: c()["privacy-tips-icon"],
                        children: (0,
                            i.jsx)(l, {})
                    }), (0,
                        i.jsx)("span", {
                        className: c()["privacy-tips-text"],
                        children: "我们非常尊重您的隐私。文件转换/识别完成30分钟内，将永久从服务器删除。"
                    })]
                })
            };
            t.C = function() {
                return (0,
                    i.jsxs)("div", {
                    className: c()["service-desc"],
                    children: [(0,
                        i.jsxs)("div", {
                        className: c()["desc-box"],
                        children: [(0,
                            i.jsx)(a.E, {})]
                    }), (0,
                        i.jsx)(p, {})]
                })
            }
        },
        45771: function(e, t, r) {
            "use strict";
            r.d(t, {
                E: function() {
                    return f
                }
            });
            r(53294);
            var n = r(56697),
                a = (r(1025),
                    r(65400)),
                s = r(25675),
                c = r.n(s),
                i = r(52216),
                o = r(79968),
                u = r(69519),
                l = r(46161),
                p = r.n(l),
                d = r(85893),
                f = function() {
                    var e = u.ZP.useContainer().pageInfo.marketService;
                    return (0,
                        d.jsxs)(a.default, {
                        type: "primary",
                        onClick: function() {
                            window.open("/UserUpgrade.aspx")
                        },
                        className: p()["button-professional"],
                        children: [(0,
                            d.jsxs)("span", {
                            children: ["立即解锁", o.Rl]
                        }), (0,
                            d.jsxs)("span", {
                            className: p()["button-discount"],
                            children: ["限时1折起"]
                        })]
                    })
                };
            t.Z = function(e) {
                var t = e.visible,
                    r = e.onOk,
                    s = e.onCancel;
                return (0,
                    d.jsxs)(n.Z, {
                    visible: t,
                    wrapClassName: p()["upload-multiple-tips"],
                    footer: null,
                    maskClosable: !1,
                    onCancel: s,
                    closable: !1,
                    bodyStyle: {
                        padding: 30
                    },
                    width: 420,
                    centered: !0,
                    children: [(0,
                        d.jsx)("div", {
                        className: p()["tips-image"],
                        children: (0,
                            d.jsx)(c(), {
                            src: "static/image/<EMAIL>",
                            width: 256,
                            height: 127,
                            alt: "image"
                        })
                    }), (0,
                        d.jsxs)("div", {
                        className: p()["tips-desc"],
                        children: [(0,
                            d.jsx)("div", {
                            children: "免费版不支持批处理，将只处理第一个文件！"
                        }), (0,
                            d.jsxs)("div", {
                                children: ["需要批量转换？请解锁专业版，注册即送", (0,
                                d.jsx)("span", {
                                className: p()["tips-number"],
                                children: "7"
                            }), "天会员！"]
                        })]
                    }), (0,
                        d.jsxs)("div", {
                        className: p()["tips-button"],
                        children: [(0,
                            d.jsx)(a.default, {
                            onClick: function() {
                                s(),
                                    r()
                            },
                            children: "知道了"
                        }), (0,
                            d.jsx)(f, {})]
                    })]
                })
            }
        },
        69519: function(e, t, r) {
            "use strict";
            r.d(t, {
                UW: function() {
                    return g
                },
                lZ: function() {
                    return j
                }
            });
            r(53294);
            var n = r(56697),
                a = r(50029),
                s = r(59499),
                c = r(87794),
                i = r.n(c),
                o = r(67294),
                u = r(16165),
                l = r(2093),
                p = r(88305),
                d = r(93162),
                f = r(79968),
                v = r(76263),
                m = r(93952),
                h = r(52216),
                b = r(85893);

            function x(e, t) {
                var r = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(e);
                    t && (n = n.filter((function(t) {
                            return Object.getOwnPropertyDescriptor(e, t).enumerable
                        }))),
                        r.push.apply(r, n)
                }
                return r
            }

            function w(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var r = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? x(Object(r), !0).forEach((function(t) {
                        (0,
                            s.Z)(e, t, r[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : x(Object(r)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t))
                    }))
                }
                return e
            }
            var j, g, y = function(e) {
                return (0,
                    b.jsx)("svg", w(w({}, e), {}, {
                    children: (0,
                        b.jsx)("path", {
                        d: "M12 2.25c5.385 0 9.75 4.365 9.75 9.75s-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12 6.615 2.25 12 2.25Zm3.182 6.568c-.293-.293-.815-.245-1.167.106L12 10.94 9.985 8.924l-.123-.106c-.342-.252-.784-.26-1.044 0-.293.293-.245.815.106 1.167L10.94 12l-2.016 2.015-.106.123c-.252.342-.26.784 0 1.044.293.293.815.245 1.167-.106L12 13.06l2.015 2.016.123.106c.342.252.784.26 1.044 0 .293-.293.245-.815-.106-1.167L13.06 12l2.016-2.015.106-.123c.252-.342.26-.784 0-1.044Z",
                        fill: "currentColor",
                        fillRule: "evenodd"
                    })
                }))
            };
            y.defaultProps = {
                    width: "24",
                    height: "24",
                    xmlns: "http://www.w3.org/2000/svg"
                },
                function(e) {
                    e.default = "default",
                        e.success = "success",
                        e.error = "error",
                        e.retry = "retry"
                }(j || (j = {})),
                function(e) {
                    e.transform = "transform",
                        e.text = "text"
                }(g || (g = {}));
            var O = (0,
                p.f)((function () {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                    t = e.pageInfo,
                    r = (0,
                        o.useState)((function() {
                        return t ? w(w({}, t), {}, {
                            marketService: t.marketService || t.service
                        }) : {}
                    })),
                    s = r[0],
                    c = (0,
                        o.useState)(g.transform),
                    p = c[0],
                    x = c[1],
                    O = (0,
                        o.useState)(v.Dm.default),
                    k = O[0],
                    S = O[1],
                    Z = (0,
                        o.useState)(),
                    P = Z[0],
                    D = Z[1],
                    N = (0,
                        o.useState)(),
                    T = N[0],
                    C = N[1],
                    E = (0,
                        o.useState)(),
                    _ = E[0],
                    I = E[1],
                    L = (0,
                        o.useState)(),
                    R = L[0],
                    A = L[1],
                    B = (0,
                        o.useState)(),
                    Y = B[0],
                    M = B[1],
                    z = (0,
                        o.useState)(),
                    F = z[0],
                    U = z[1],
                    W = (0,
                        o.useState)(j.default),
                    q = W[0],
                    G = W[1],
                    H = (0,
                        o.useState)(),
                    J = H[0],
                    Q = H[1],
                    $ = (0,
                        o.useState)(),
                    K = $[0],
                    X = $[1];

                function V() {
                    if (R) {
                        var e = _.replace(/\.[a-zA-Z]+$/, s.target);
                        (0,
                            d.saveAs)(R, e)
                    }
                }

                function ee() {
                    "function" === typeof window.npsOpen && window.npsOpen()
                }

                function te() {
                    U((new Date).getTime()),
                        S(v.Dm.default),
                        D(""),
                        I(""),
                        A(null),
                        M(null),
                        G(j.default),
                        X(void 0)
                }

                function re() {
                    G(j.default),
                        Q((new Date).getTime())
                }

                function ne() {
                    return ae.apply(this, arguments)
                }

                function ae() {
                    return (ae = (0,
                        a.Z)(i().mark((function e() {
                        var t, r;
                        return i().wrap((function(e) {
                            for (;;)
                                switch (e.prev = e.next) {
                                    case 0:
                                        return e.next = 2, (0,
                                            m.aP)();
                                    case 2:
                                        t = e.sent;
                                    case 5:
                                    case "end":
                                        return e.stop()
                                }
                        }), e)
                    })))).apply(this, arguments)
                }
                return (0,
                    l.Z)((0,
                    a.Z)(i().mark((function e() {
                    var t, r, a, c, o, l;
                    return i().wrap((function(e) {
                        for (;;)
                            switch (e.prev = e.next) {
                                case 0:
                                    if (!P) {
                                        e.next = 38;
                                        break
                                    }
                                    if (e.prev = 1,
                                        p !== g.transform) {
                                        e.next = 8;
                                        break
                                    }
                                    return e.next = 5, (0,
                                        m.xw)(P, s.service);
                                case 5:
                                    t = e.sent,
                                        e.next = 12;
                                    break;
                                case 8:
                                    return e.next = 10, (0,
                                        m.JZ)(P, s.service, s.queryParams);
                                case 10:
                                    t = e.sent,
                                        ne();
                                case 12:
                                    if (200 !== t.code) {
                                        e.next = 31;
                                        break
                                    }
                                    if (p !== g.transform) {
                                        e.next = 26;
                                        break
                                    }
                                    if (r = f.cS[s.target.replace(".", "")],
                                        s.service != f.Bf.pdf2md) {
                                        e.next = 20;
                                        break
                                    }
                                    c = new Blob([(null === (a = t.data.result) || void 0 === a ? void 0 : a.markdown) || ""], {
                                            type: r
                                        }),
                                        A(c),
                                        e.next = 24;
                                    break;
                                case 20:
                                    return e.next = 22, (0,
                                        f.Jr)(t.data.result, r);
                                case 22:
                                    o = e.sent,
                                        A(o);
                                case 24:
                                    e.next = 27;
                                    break;
                                case 26:
                                    A(t.data);
                                case 27:
                                    G(j.success),
                                        ee(),
                                        e.next = 32;
                                    break;
                                case 31:
                                    451 == t.code ? n.Z.confirm({
                                        title: "提示",
                                        content: "注册OCR文字识别助手，立即解锁更多功能～",
                                        cancelText: "关闭",
                                        onCancel: function() {
                                            re(), (0,
                                                f.j)({
                                                name: "按钮点击",
                                                keyword: "注册提示【关闭】",
                                                path: window.location.pathname
                                            })
                                        },
                                        cancelButtonProps: {
                                            style: {
                                                width: 80
                                            }
                                        },
                                        okButtonProps: {
                                            style: {
                                                width: 80
                                            }
                                        },
                                        okText: "去注册",
                                        onOk: function() {
                                            window.open("User.aspx", "_self"), (0,
                                                f.j)({
                                                name: "按钮点击",
                                                keyword: "注册提示【去注册】",
                                                path: window.location.pathname
                                            })
                                        },
                                        centered: !0
                                    }) : (l = t.message || t.msg || "服务器繁忙，请稍后再试",
                                        n.Z.confirm({
                                            title: p === g.transform ? "转换失败" : "识别失败",
                                            content: l,
                                            cancelText: "重新上传",
                                            onCancel: te,
                                            cancelButtonProps: {
                                                style: {
                                                    width: 80
                                                }
                                            },
                                            okButtonProps: {
                                                style: {
                                                    width: 80
                                                }
                                            },
                                            okText: "重试",
                                            onOk: re,
                                            centered: !0,
                                            icon: (0,
                                                b.jsx)(u.Z, {
                                                component: y,
                                                style: {
                                                    color: "#E55245"
                                                }
                                            })
                                        }),
                                        G(j.error));
                                case 32:
                                    e.next = 38;
                                    break;
                                case 34:
                                    e.prev = 34,
                                        e.t0 = e.catch(1),
                                        console.log(e.t0),
                                        G(j.retry);
                                case 38:
                                case "end":
                                    return e.stop()
                            }
                    }), e, null, [
                        [1, 34]
                    ])
                }))), [P, J]), {
                    pageInfo: s,
                    status: k,
                    setStatus: S,
                    url: P,
                    setUrl: D,
                    base64: T,
                    setBase64: C,
                    result: Y,
                    setResult: M,
                    filename: _,
                    setFilename: I,
                    downloadResult: V,
                    resetDeps: F,
                    resetHandle: te,
                    transformRes: R,
                    serviceStatus: q,
                    retryHandle: re,
                    setType: x,
                    responseDate: K
                }
            }));
            t.ZP = O
        },
        37617: function(e, t, r) {
            "use strict";
            var n = r(59499),
                a = r(27812),
                s = (r(1131),
                    r(28465)),
                c = r(50029),
                i = (r(75314),
                    r(11187)),
                o = r(87794),
                u = r.n(o),
                l = r(67294),
                p = r(77598),
                d = r(79968);

            function f(e, t) {
                var r = Object.keys(e);
                if (Object.getOwnPropertySymbols) {
                    var n = Object.getOwnPropertySymbols(e);
                    t && (n = n.filter((function(t) {
                            return Object.getOwnPropertyDescriptor(e, t).enumerable
                        }))),
                        r.push.apply(r, n)
                }
                return r
            }

            function v(e) {
                for (var t = 1; t < arguments.length; t++) {
                    var r = null != arguments[t] ? arguments[t] : {};
                    t % 2 ? f(Object(r), !0).forEach((function(t) {
                        (0,
                            n.Z)(e, t, r[t])
                    })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : f(Object(r)).forEach((function(t) {
                        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t))
                    }))
                }
                return e
            }
            t.Z = function(e, t) {
                var r = e || {},
                    n = r.sizeLimit,
                    o = r.base64,
                    f = r.paste,
                    m = r.pasteTarget,
                    h = r.accept,
                    b = (0,
                        l.useState)(),
                    x = b[0],
                    w = b[1],
                    j = (0,
                        l.useState)(),
                    g = j[0],
                    y = j[1],
                    O = (0,
                        l.useState)(""),
                    k = O[0],
                    S = O[1],
                    Z = (0,
                        l.useState)(!1),
                    P = Z[0],
                    D = Z[1];
                (0,
                    p.Z)((function() {
                    w(""),
                        S(""),
                        D(!1)
                }), [t]);
                var N = (0,
                    l.useCallback)((function(e) {
                    var t;
                    if (f && null !== (t = e.clipboardData) && void 0 !== t && t.items) {
                        for (var r = [], n = 0, a = e.clipboardData.items.length; n < a; n++) {
                            var s = e.clipboardData.items[n];
                            if ("file" === s.kind) {
                                var c = s.getAsFile();
                                r.push(c)
                            }
                        }
                        var o = r.filter((function(e) {
                            return (0,
                                d.$1)(e, h)
                        }));
                        null !== o && void 0 !== o && o.length ? o[0] && T(o[0], o) : i.default.error("文件类型不支持，请选择正确的文件")
                    }
                }), [t]);
                (0,
                    l.useEffect)((function() {
                    var e = m || window;
                    return f ? e.addEventListener("paste", N) : e.removeEventListener("paste", N),
                        function() {
                            e.removeEventListener("paste", N)
                        }
                }), [t, f]);
                var T = function() {
                    var e = (0,
                        c.Z)(u().mark((function e(t, r) {
                        var a, c, l;
                        return u().wrap((function(e) {
                            for (;;)
                                switch (e.prev = e.next) {
                                    case 0:
                                        if ((null === t || void 0 === t ? void 0 : t.uid) !== (null === (a = r[0]) || void 0 === a ? void 0 : a.uid)) {
                                            e.next = 23;
                                            break
                                        }
                                        if (!(n && (null === t || void 0 === t ? void 0 : t.size) > 1024 * n * 1024)) {
                                            e.next = 4;
                                            break
                                        }
                                        return i.default.error("单个文件大小不能超过".concat(n, "M")),
                                            e.abrupt("return", s.Z.LIST_IGNORE);
                                    case 4:
                                        if ((0,
                                            d.$1)(t, h)) {
                                            e.next = 7;
                                            break
                                        }
                                        return i.default.error("文件类型不支持，请选择正确的文件"),
                                            e.abrupt("return", s.Z.LIST_IGNORE);
                                    case 7:
                                        if (c = t, !o) {
                                            e.next = 19;
                                            break
                                        }
                                        return e.prev = 9,
                                            e.next = 12, (0,
                                                d.n5)(t);
                                    case 12:
                                        l = e.sent,
                                            y(l),
                                            e.next = 19;
                                        break;
                                    case 16:
                                        return e.prev = 16,
                                            e.t0 = e.catch(9),
                                            e.abrupt("return", s.Z.LIST_IGNORE);
                                    case 19:
                                        return D((null === r || void 0 === r ? void 0 : r.length) > 1),
                                            S(t.name),
                                            w(c),
                                            e.abrupt("return", !0);
                                    case 23:
                                        return e.abrupt("return", s.Z.LIST_IGNORE);
                                    case 24:
                                    case "end":
                                        return e.stop()
                                }
                        }), e, null, [
                            [9, 16]
                        ])
                    })));
                    return function(t, r) {
                        return e.apply(this, arguments)
                    }
                }();
                return v(v({}, e), {}, {
                    fileList: [],
                    multiple: !0,
                    maxCount: 1,
                    showUploadList: !1,
                    beforeUpload: T,
                    onDrop: function(e) {
                        e.dataTransfer.files && ((0,
                            a.Z)(e.dataTransfer.files).some((function(e) {
                            return (0,
                                d.$1)(e, h)
                        })) || i.default.error("文件类型不支持，请选择正确的文件"))
                    },
                    customRequest: function() {},
                    url: x,
                    base64: g,
                    showTips: P,
                    filename: k
                })
            }
        },
        90328: function(e) {
            e.exports = {
                "page-header-wrap": "_BEBSCQe",
                "api-price-wrap": "_2iFAvTh",
                "header-time": "__7f_wTG6b",
                "header-time-animate": "ABxEKesz",
                "time-animate": "TAB_Vkwp",
                "header-link": "MyguYwZd",
                "header-link-icon": "YQ0qH2t5",
                "header-link-text": "u9Sx3Clm",
                "page-header-title": "C_f2FiQb",
                "page-header-desc": "YNa21k4i"
            }
        },
        30687: function(e) {
            e.exports = {
                "service-desc": "ghx_WPzb",
                "desc-box": "aSk_CKpN",
                "desc-text": "bzQSCJIy",
                "privacy-tips": "X5n4SzgF",
                "privacy-tips-icon": "bdekne5Z"
            }
        },
        46161: function(e) {
            e.exports = {
                "upload-multiple-tips": "YcfU5XRF",
                "tips-image": "BAhwvOkA",
                "tips-desc": "__5qWarO4e",
                "tips-number": "x_JFITcE",
                "tips-button": "ZavR4Pq0",
                "button-professional": "kTsrWYdI",
                "button-discount": "swtlTWW4"
            }
        }
    }
]);