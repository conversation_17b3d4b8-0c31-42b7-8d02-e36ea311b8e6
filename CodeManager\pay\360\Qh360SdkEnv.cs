﻿using System.Runtime.InteropServices;

namespace Account.Web.pay._360
{
    /// <summary>
    /// 360配置i参数
    /// </summary>
    public class Qh360SdkEnv
    {
        /// <summary>
        /// * 联运产品的 ID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// * 登录联运平台的 360 账号 ID
        /// </summary>
        public ulong Qid { get; set; }

        internal void CopyFromEnvInfo(EnvInfo envInfo)
        {
            AppId = envInfo.wszAppId;
            Qid = envInfo.u64Qid;
        }

        internal EnvInfo ToEnvInfo()
        {
            EnvInfo result = default(EnvInfo);
            result.dwSize = (uint)Marshal.SizeOf(typeof(EnvInfo));
            result.wszAppId = AppId;
            result.u64Qid = Qid;
            return result;
        }
    }

    /// <summary>
    /// SDK 初始化所需环境信息
    /// </summary>
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode, Pack = 1)]
    public struct EnvInfo
    {
        /// <summary>
        /// *
        /// </summary>
        public uint dwSize;

        /// <summary>
        /// * 联运产品的 ID
        /// </summary>
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
        public string wszAppId;

        /// <summary>
        /// * 登录联运平台的 360 账号 ID
        /// </summary>
        public ulong u64Qid;
    }
}
