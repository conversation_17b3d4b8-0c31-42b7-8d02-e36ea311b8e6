using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace Account.Web
{
    public static class CommonTranslate
    {
        public const string UserTranslageKey = "TransCache";
        public const string WebTranslageKey = "WebTrans";

        public static string GetTrans(this string source, HttpRequest Request)
        {
            var lang = Request.GetValue("lang");
            return GetTrans(source, lang);
        }

        public static string GetTrans(this string source, string lang = null, string key = WebTranslageKey)
        {
            if (!string.IsNullOrEmpty(source) && !string.IsNullOrEmpty(lang))
            {
                lang = GetCurrentLang(lang);
                if (!string.IsNullOrEmpty(lang) && !Equals(lang, StrDefaultLang))
                {
                    try
                    {
                        var dicTrans = GetCache(key);
                        if (dicTrans.ContainsKey(source) && dicTrans[source].ContainsKey(lang))
                        {
                            source = dicTrans[source][lang];
                        }
                    }
                    catch { }
                }
            }
            return source;
        }

        private static Dictionary<string, Dictionary<string, string>> GetCache(string key)
        {
            return JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(CodeProcessHelper.ServerConfigCache.Get(key) ?? "") ?? new Dictionary<string, Dictionary<string, string>>();
        }

        public static void SetTranslate(string strLanguage, string strSource, string strTrans, string strKey)
        {
            var dicAll = GetCache(strKey);
            if (!dicAll.ContainsKey(strSource))
                dicAll[strSource] = new Dictionary<string, string>();
            dicAll[strSource][strLanguage] = strTrans;
            CodeProcessHelper.ServerConfigCache.Set(strKey, JsonConvert.SerializeObject(dicAll));
        }

        public static string StrHost = "https://ocr.oldfish.cn/";

        public static string StrDefaultLang = "zh-Hans";

        public static Dictionary<string, List<string>> DicJsLanguage = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
        {
            // 中文简体
            { "zh-Hans", new List<string> { "simplifiedchinese", "chinese_simplified", "zh-cn", "zh-sg", "zh" } },
    
            // 中文繁体
            { "zh-Hant", new List<string> { "traditionalchinese", "chinese_traditional", "zh-tw", "zh-hk", "zh-mo" } },
    
            // 粤语
            { "yue", new List<string> { "cantonese", "zh-yue" } },
    
            // 英语
            { "en", new List<string> { "english", "en-us", "en-gb", "en-ca", "en-au", "en-nz", "en-ie", "en-za", "en-in", "en-ph", "en-sg" } },
    
            // 西班牙语
            { "es", new List<string> { "spanish", "es-es", "es-mx", "es-co", "es-ar", "es-cl", "es-pe", "es-ve", "es-ec", "es-gt", "es-cu", "es-bo", "es-do", "es-hn", "es-sv", "es-ni", "es-py", "es-pr", "es-uy", "es-pa", "es-cr" } },
    
            // 印地语
            { "hi", new List<string> { "hindi", "hi-in" } },
    
            // 阿拉伯语
            { "ar", new List<string> { "arabic", "ar-sa", "ar-eg", "ar-dz", "ar-ma", "ar-tn", "ar-om", "ar-ye", "ar-sy", "ar-jo", "ar-lb", "ar-kw", "ar-ae", "ar-bh", "ar-qa", "ar-iq", "ar-ly" } },
    
            // 葡萄牙语(巴西)
            { "pt", new List<string> { "portuguese", "pt-br" } },
    
            // 孟加拉语
            { "bn", new List<string> { "bengali", "bn-in", "bn-bd" } },
    
            // 俄语
            { "ru", new List<string> { "russian", "ru-ru", "ru-by", "ru-kz" } },
    
            // 日语
            { "ja", new List<string> { "japanese", "ja-jp" } },
    
            // 旁遮普语
            { "pa", new List<string> { "punjabi", "pa-in", "pa-pk" } },
    
            // 德语
            { "de", new List<string> { "german", "de-de", "de-ch", "de-at", "de-lu", "de-li" } },
    
            // 法语
            { "fr", new List<string> { "french", "fr-fr", "fr-be", "fr-ch", "fr-lu", "fr-mc" } },
    
            // 马拉地语
            { "mr", new List<string> { "marathi", "mr-in" } },
    
            // 泰卢固语
            { "te", new List<string> { "telugu", "te-in" } },
    
            // 越南语
            { "vi", new List<string> { "vietnamese", "vi-vn" } },
    
            // 韩语
            { "ko", new List<string> { "korean", "ko-kr" } },
    
            // 泰米尔语
            { "ta", new List<string> { "tamil", "ta-in", "ta-lk", "ta-sg", "ta-my" } },
    
            // 乌尔都语
            { "ur", new List<string> { "urdu", "ur-pk", "ur-in" } },
    
            // 土耳其语
            { "tr", new List<string> { "turkish", "tr-tr", "tr-cy" } },
    
            // 意大利语
            { "it", new List<string> { "italian", "it-it", "it-ch", "it-sm", "it-va" } },
    
            // 古吉拉特语
            { "gu", new List<string> { "gujarati", "gu-in" } },
    
            // 波兰语
            { "pl", new List<string> { "polish", "pl-pl" } },
    
            // 乌克兰语
            { "uk", new List<string> { "ukrainian", "uk-ua" } },
    
            // 马来语
            { "ms", new List<string> { "malay", "ms-my", "ms-bn", "ms-sg" } },
    
            // 印尼语
            { "id", new List<string> { "indonesian", "id-id" } },
    
            // 马拉雅拉姆语
            { "ml", new List<string> { "malayalam", "ml-in" } },
    
            // 卡纳达语
            { "kn", new List<string> { "kannada", "kn-in" } },
    
            // 波斯语
            { "fa", new List<string> { "persian", "fa-ir" } },
    
            // 荷兰语
            { "nl", new List<string> { "dutch", "nl-nl", "nl-be" } },
    
            // 泰语
            { "th", new List<string> { "thai", "th-th" } },
    
            // 斯瓦希里语
            { "sw", new List<string> { "swahili", "sw-ke", "sw-tz", "sw-ug" } },
    
            // 罗马尼亚语
            { "ro", new List<string> { "romanian", "ro-ro", "ro-md" } },
    
            // 缅甸语
            { "my", new List<string> { "burmese", "my-mm" } },
    
            // 奥里亚语
            { "or", new List<string> { "odia", "or-in" } },
    
            // 希伯来语
            { "he", new List<string> { "hebrew", "he-il" } },
    
            // 阿姆哈拉语
            { "am", new List<string> { "amharic", "am-et" } },
    
            // 菲律宾语
            { "fil", new List<string> { "filipino", "fil-ph" } },
    
            // 瑞典语
            { "sv", new List<string> { "swedish", "sv-se", "sv-fi" } },
    
            // 希腊语
            { "el", new List<string> { "greek", "el-gr", "el-cy" } },
    
            // 捷克语
            { "cs", new List<string> { "czech", "cs-cz" } },
    
            // 匈牙利语
            { "hu", new List<string> { "hungarian", "hu-hu" } },
    
            // 白俄罗斯语
            { "be", new List<string> { "belarusian", "be-by" } },
    
            // 僧伽罗语
            { "si", new List<string> { "sinhala", "si-lk" } },
    
            // 尼泊尔语
            { "ne", new List<string> { "nepali", "ne-np", "ne-in" } },
    
            // 柬埔寨语(高棉语)
            { "km", new List<string> { "khmer", "km-kh" } },
    
            // 斯洛伐克语
            { "sk", new List<string> { "slovak", "sk-sk" } },
    
            // 保加利亚语
            { "bg", new List<string> { "bulgarian", "bg-bg" } },
    
            // 法语(加拿大)
            { "fr-ca", new List<string> { "french_canada" } },
    
            // 豪萨语
            { "ha", new List<string> { "hausa", "ha-ng", "ha-ne", "ha-gh" } },

            // 约鲁巴语
            { "yo", new List<string> { "yoruba", "yo-ng", "yo-bj" } },

            // 伊博语
            { "ig", new List<string> { "igbo", "ig-ng" } },

            // 库尔德语(中库尔德语)
            { "ku", new List<string> { "kurdish_central", "kurdish", "ku-tr", "ku-iq", "ku-ir", "ku-sy" } },

            // 卢旺达语
            { "rw", new List<string> { "kinyarwanda", "rw-rw" } },

            // 加泰罗尼亚语
            { "ca", new List<string> { "catalan", "ca-es", "ca-ad" } },

            // 丹麦语
            { "da", new List<string> { "danish", "da-dk" } },

            // 芬兰语
            { "fi", new List<string> { "finnish", "fi-fi" } },

            // 挪威语
            { "nb", new List<string> { "norwegian", "no-no", "nb-no", "nn-no" } },

            // 克罗地亚语
            { "hr", new List<string> { "croatian", "hr-hr", "hr-ba" } },

            // 塞尔维亚语(西里尔)
            { "sr-Cyrl", new List<string> { "serbian_cyrillic", "sr-cyrl-rs", "sr-cyrl" } },

            // 塞尔维亚语(拉丁)
            { "sr-Latn", new List<string> { "serbian", "serbian_latin", "sr-rs", "sr-latn-rs", "sr-latn", "sr-me" } },

            // 阿尔巴尼亚语
            { "sq", new List<string> { "albanian", "sq-al" } },

            // 索马里语
            { "so", new List<string> { "somali", "so-so", "so-dj", "so-et", "so-ke" } },

            // 祖鲁语
            { "zu", new List<string> { "zulu", "zu-za" } },

            // 格鲁吉亚语
            { "ka", new List<string> { "georgian", "ka-ge" } }
        };

        public const string DefaultPage = "default.aspx";
        public const string DefaultIndex = "index.html";

        public static Uri GetCahcePath(Uri url)
        {
            var localPath = url.LocalPath.TrimStart('/').Trim();
            if (string.IsNullOrEmpty(localPath))
            {
                localPath = DefaultPage;
            }
            else if (localPath.EndsWith("/"))
            {
                localPath += DefaultIndex;
            }
            if (!url.LocalPath.Contains(localPath))
            {
                url = new Uri(url, localPath);
            }
            return url;
        }

        public static string GetCurrentLang(HttpRequest request, bool decodeBroswer = false)
        {
            // 首先检查是否已经从URL路径中提取了语言
            var lang = HttpContext.Current.Items["lang"]?.ToString();
            if (string.IsNullOrEmpty(lang))
            {
                lang = LanguageService.GetLanguageFromQueryString(request);

                // 如果仍然没有获取到语言且需要从浏览器头获取
                if (string.IsNullOrEmpty(lang) && decodeBroswer)
                {
                    lang = LanguageService.GetLanguageFromBrowserHeader(request);
                    //LogHelper.Log.Error("Lang:" + lang + "Accept-Language:" + request.Headers["Accept-Language"]);
                }

                // 如果仍未获取到语言，使用默认语言
                if (string.IsNullOrEmpty(lang))
                {
                    lang = StrDefaultLang;
                }

                // 保存到Items中以便后续使用
                try
                {
                    HttpContext.Current.Items["lang"] = lang;
                }
                catch { }
            }
            return lang;
        }

        public static string GetCurrentLang(string lang)
        {
            return DicJsLanguage.FirstOrDefault(p => Equals(lang.ToLower(), p.Key.ToLower()) || p.Value.Contains(lang.ToLower())).Key ??
                (string.IsNullOrEmpty(lang) ? StrDefaultLang : lang);
        }
    }
}