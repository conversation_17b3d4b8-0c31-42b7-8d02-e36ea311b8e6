﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="IPList.aspx.cs"
    Inherits="Account.Web.IPList" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
    <div>
        <asp:Label ID="Label1" runat="server" Text="列表："></asp:Label>
        <asp:Label ID="lblCount" runat="server" Text="0"></asp:Label>
    </div>
    <div>
        <asp:TextBox ID="txtDate" runat="server"></asp:TextBox>
        <asp:TextBox ID="txtType" runat="server" Text="3"></asp:TextBox>
        <asp:Button ID="btnOK" runat="server" Text="查询所有" OnClick="btnOK_Click" />
        <asp:Button ID="btnSpeed" runat="server" Text="查询最快" OnClick="btnSpeed_Click" />
        <br />
    </div>
    <asp:GridView ID="gvDataSource" runat="server" BackColor="White" BorderColor="#CCCCCC"
        BorderStyle="None" BorderWidth="1px" CellPadding="3" EnableModelValidation="True">
        <FooterStyle BackColor="White" ForeColor="#000066" />
        <HeaderStyle BackColor="#006699" Font-Bold="True" ForeColor="White" />
        <PagerStyle BackColor="White" ForeColor="#000066" HorizontalAlign="Left" />
        <RowStyle ForeColor="#000066" />
        <SelectedRowStyle BackColor="#669999" Font-Bold="True" ForeColor="White" />
    </asp:GridView>
    </form>
</body>
</html>
