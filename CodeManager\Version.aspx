﻿<%@ Page Title="VIP会员权益" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>
<%@ Import Namespace="CommonLib" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手VIP会员权益详细介绍，包含个人版、专业版、旗舰版等VIP等级，详细的功能对比及价格方案">
    <link rel="stylesheet" href="./CSS/bootstrap.min.css?t=2023091301">
    <style type="text/css">
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        html, table {
            font-size: 15px;
        }

        * {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        th {
            border: 1px solid #dee2e6;
            text-align: center;
            font-weight: bold;
        }

        td {
            border: 1px solid #dee2e6;
        }

        .v-1 {
            background: linear-gradient(89.95deg, #6666FF 11.5%, #38c0ff 100.01%);
            color: white;
            background-color: #6666FF;
        }

        .v0 {
            color: white;
            background-color: rgb(23, 198, 83);
        }

        .v1 {
            background: linear-gradient(to right, #4B4B4B 5.77%, #1A1510 100%);
            color: #F9D9A8;
            background-color: #4B4B4B;
        }

        .v3 {
            background: linear-gradient(to right, #FFEBC1 21.65%, #FFE5B7 79.13%);
            color: #944800;
            border-top-right-radius: 0.05rem;
            background-color: #944800;
        }



        #tbMain {
            position: relative;
        }

        .card-body {
            position: relative;
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }

        /* 表格美化 */
        #tbMain {
            border-collapse: collapse;
            width: 100%;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #tbMain th {
            padding: 12px 8px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            border: 1px solid #dee2e6;
            position: relative;
        }

        #tbMain th:first-child {
            background: #f8f9fa;
            color: #495057;
            font-weight: 700;
            text-align: left;
            padding-left: 16px;
        }

        #tbMain td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-size: 14px;
        }

        #tbMain td:first-child {
            background: #f8f9fa;
            color: #495057;
            font-weight: 500;
            text-align: left;
            padding-left: 16px;
        }

        /* 按钮样式优化 */
        #tbMain a {
            text-decoration: none;
            transition: all 0.3s ease;
        }

        #tbMain a:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        /* 版本特定的颜色主题 - 保持边框，优化背景 */
        /* 免费版 - 绿色 */
        #tbMain th.v0 {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-color: #1e7e34;
        }

        #tbMain a.v0:hover {
            background: linear-gradient(135deg, #218838, #1ea085) !important;
        }

        /* 个人版 - 蓝色 */
        #tbMain th.v-1 {
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
            border-color: #0056b3;
        }

        #tbMain a.v-1:hover {
            background: linear-gradient(135deg, #0056b3, #520dc2) !important;
        }

        /* 专业版 - 深灰金色 */
        #tbMain th.v-2 {
            background: linear-gradient(135deg, #495057, #343a40);
            color: #ffc107;
            border-color: #343a40;
        }

        #tbMain a.v-2:hover {
            background: linear-gradient(135deg, #383d41, #23272b) !important;
            color: #ffc107 !important;
        }

        /* 旗舰版 - 金色 */
        #tbMain th.v-3 {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #495057;
            border-color: #e0a800;
        }

        #tbMain a.v-3:hover {
            background: linear-gradient(135deg, #e0a800, #ea6100) !important;
            color: #495057 !important;
        }

        /* 高亮效果优化 */
        .column-highlight {
            border-radius: 6px;
            transition: all 0.2s ease;
            border: 2px solid transparent;
            pointer-events: none;
            position: absolute;
            z-index: 1;
        }

        /* 表格容器 - 支持横向滚动 */
        .table-container {
            overflow-x: auto;
            margin: 0 -20px;
            padding: 0 20px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .card-body {
                max-width: 100%;
                padding: 15px;
            }

            .table-container {
                margin: 0 -15px;
                padding: 0 15px;
            }
        }

        @media (max-width: 768px) {
            .card-body {
                padding: 10px;
            }

            .table-container {
                margin: 0 -10px;
                padding: 0 10px;
            }

            #tbMain {
                font-size: 12px;
                min-width: 600px; /* 确保表格不会过度压缩 */
            }

            #tbMain th, #tbMain td {
                padding: 8px 4px;
                white-space: nowrap;
            }

            #tbMain th:first-child, #tbMain td:first-child {
                position: sticky;
                left: 0;
                background: #f8f9fa;
                z-index: 2;
                min-width: 80px;
            }
        }

        /* 表格行悬停效果 */
        #tbMain tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.03);
        }

        #tbMain tbody tr:hover td {
            background-color: rgba(0, 123, 255, 0.03);
        }

        #tbMain tbody tr:hover td:first-child {
            background-color: rgba(248, 249, 250, 0.9);
        }

        /* 功能图标样式 */
        #tbMain td:not(:first-child) {
            font-size: 15px;
            font-weight: 500;
        }

        /* 符号样式 */
        .symbol-check {
            color: #28a745 !important;
            font-weight: bold;
        }

        .symbol-cross {
            color: #dc3545 !important;
            font-weight: bold;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <header>
        <h1 style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">VIP会员权益详情</h1>
    </header>
    <main>
    <div class="card-body">
        <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333; margin-bottom: 8px; font-size: 24px;">VIP会员权益对比</h2>
            <p style="color: #666; font-size: 14px; margin: 0;">选择适合您的版本，享受更强大的OCR识别功能</p>
        </div>
        <div class="table-container">
            <table class="table table-striped" id="tbMain" aria-label="VIP会员特权对比表">
                <caption style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">会员版本功能比较</caption>
                <thead>
                    <tr>
                        <th style="min-width: 90px" scope="col">权益</th>
                        <%
                            var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                            lstUserTypes.Insert(0, UserTypeHelper.GetUserType(UserTypeEnum.体验版));
                            foreach (var item in lstUserTypes)
                            {%>
                        <th class="v<%=item.Type.GetHashCode()%>" scope="col"><%=item.Type.ToString().Replace("体验","免费") %>
                        </th>
                        <%
                            } %>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>区别</td>
                        <%if (lstUserTypes.Any(p => p.Type == UserTypeEnum.体验版))
                            {  %>
                        <td>基础功能，永久免费</td>
                        <%} %>
                        <td>个人专属，轻松识别</td>
                        <td>专业高效，精确稳定</td>
                        <td>旗舰体验，最佳选择</td>
                    </tr>
                    <tr id="toUpdate">
                        <td></td>
                        <%
                            for (int i = 0; i < lstUserTypes.Count; i++)
                            {
                                var item = lstUserTypes[i];
                                var url = item.Type.GetHashCode() == 0 ? Account.Web.CommonRequest.GetDownLoadUrl(Request) : "Upgrade.aspx?type=" + item.Type.GetHashCode();
                        %>
                        <td>
                            <a class="v<%=item.Type.GetHashCode()%>" style="display: inline-block; width: 100%; height: 40px; border-radius: 20px; font-size: 18px; font-weight: 500; text-align: center; line-height: 40px; cursor: pointer;"
                                href="<%=url %>"><%=item.Type.GetHashCode() == 0?"免费下载":"升级到"+item.Type.ToString() %></a>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>截图</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>✔</td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>贴图</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>✔</td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>截图识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>✔</td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>离线识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportLocalOcr)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>图片识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportImageFile)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>区域识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportVertical)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>竖排识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportVertical)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>划词翻译</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportTranslate)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>图片翻译</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportTranslate)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>公式识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportMath)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>表格识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportTable)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>文档识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportDocFile)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>文档翻译</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportDocFile)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>批量识别</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportBatch)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>自选通道</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportPassage)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>多结果</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSetOtherResult)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>多设备</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td><b><%=item.MaxLoginCount>1?item.MaxLoginCount.ToString():"✘" %></b></td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>识别频率</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td><b><%=(item.PerTimeSpan>0? (item.PerTimeSpan / 1000).ToString("F0")+"秒/"+item.PerTimeSpanExecCount+"次":"-").Replace("/1次","/次") %></b></td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td><b>每日限额</b></td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td><b><%=item.LimitPerDayCount+"次"%></b></td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>专属客服</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.MaxLoginCount > 1)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            } %>
                    </tr>
                    <tr>
                        <td>需求定制</td>
                        <%foreach (var item in lstUserTypes)
                            {%>
                        <td>
                            <%if (item.IsSupportTranslate)
                                {
                            %>
                            ✔
                            <%}
                                else
                                { %>
                            ✘
                            <%}%>
                        </td>
                        <%
                            }
                        %>
                    </tr>
                </tbody>
            </table>
        </div>
        </div>
    </main>

    <script type="text/javascript">
        // 页面加载好后运行初始化
        (window.addEventListener ? window.addEventListener('DOMContentLoaded', init, false) :
         window.attachEvent ? window.attachEvent('onload', init) : window.onload = init);

        function init() {
            var urlParams = location.search.substr(1).split('&').reduce(function(o,p){
                var pair = p.split('=');
                if (pair[0]) {
                    try {
                        o[pair[0]] = decodeURIComponent(pair[1] || '');
                    } catch(e) {
                        o[pair[0]] = pair[1] || '';
                    }
                }
                return o;
            }, {});

            // 设置更新按钮显示状态 - 只在非iframe环境下显示
            var toUpdateEl = document.getElementById("toUpdate");
            if (toUpdateEl) toUpdateEl.style.display = self === top ? "" : "none";

            // 初始化高亮列
            var colIndex = 2; // 默认高亮第二列

            // 根据类型参数高亮相应列
            var type = urlParams.type ? parseInt(urlParams.type) : -2;
            if (type > -2) {
                var ths = document.getElementById('tbMain').getElementsByTagName('th');
                for (var i = 0; i < ths.length; i++) {
                    if (ths[i].className.indexOf('v' + type) !== -1 && ths[i].style.display != 'none') {
                        colIndex = i;
                        break;
                    }
                }
            }

            // 初始化列高亮
            highlightCol(colIndex);

            // 为所有单元格添加鼠标事件
            var cells = document.getElementById('tbMain').getElementsByTagName("td");
            var heads = document.getElementById('tbMain').getElementsByTagName("th");
            addHoverEvents(cells);
            addHoverEvents(heads);

            // 美化符号样式
            beautifySymbols();
        }

        // 为单元格添加鼠标事件
        function addHoverEvents(cells) {
            for (var i = 0; i < cells.length; i++) {
                if (window.addEventListener) {
                    cells[i].addEventListener('mouseenter', hoverHandler, false);
                } else if (cells[i].attachEvent) {
                    cells[i].attachEvent('onmouseenter', function() {
                        var idx = window.event.srcElement.cellIndex;
                        if (idx > 0) highlightCol(idx);
                    });
                }
            }
        }

        // 处理鼠标悬停
        function hoverHandler() {
            var idx = this.cellIndex;
            if (idx > 0) highlightCol(idx);
        }

        // 高亮列
        function highlightCol(idx) {
            var hlDiv = document.getElementById('col-highlight') || createHighlightDiv();
            var table = document.getElementById('tbMain');
            var cell = table.rows[0].cells[idx];

            // 设置元素尺寸和位置（相对于表格）
            hlDiv.style.left = (cell.offsetLeft + 2) + 'px';
            hlDiv.style.top = '2px';
            hlDiv.style.width = (cell.offsetWidth - 4) + 'px';
            hlDiv.style.height = (table.offsetHeight - 4) + 'px';

            // 根据列索引设置高亮颜色（跳过第一列"权益"）
            var highlightColor = 'rgba(0, 123, 255, 0.6)'; // 默认蓝色
            var columnIndex = idx - 1; // 减去第一列"权益"列

            // 调试：输出列信息
            console.log('Column ' + idx + ' (version column ' + columnIndex + ') className:', cell.className);

            // 根据列位置确定颜色
            if (columnIndex === 0) {
                // 第一个版本列 - 免费版 - 绿色
                highlightColor = 'rgba(40, 167, 69, 0.7)';
            } else if (columnIndex === 1) {
                // 第二个版本列 - 个人版 - 蓝色
                highlightColor = 'rgba(0, 123, 255, 0.7)';
            } else if (columnIndex === 2) {
                // 第三个版本列 - 专业版 - 深灰
                highlightColor = 'rgba(73, 80, 87, 0.7)';
            } else if (columnIndex === 3) {
                // 第四个版本列 - 旗舰版 - 金色
                highlightColor = 'rgba(255, 193, 7, 0.7)';
            } else {
                // 其他版本列 - 使用循环颜色
                var colors = [
                    'rgba(40, 167, 69, 0.7)',   // 绿色
                    'rgba(0, 123, 255, 0.7)',   // 蓝色
                    'rgba(73, 80, 87, 0.7)',    // 深灰
                    'rgba(255, 193, 7, 0.7)',   // 金色
                    'rgba(220, 53, 69, 0.7)',   // 红色
                    'rgba(102, 16, 242, 0.7)'   // 紫色
                ];
                var colorIndex = columnIndex % colors.length;
                highlightColor = colors[colorIndex];
            }

            hlDiv.style.boxShadow = '0 0 12px 4px ' + highlightColor;
            hlDiv.style.borderColor = highlightColor;
            hlDiv.style.opacity = '1';
        }

        // 创建高亮元素
        function createHighlightDiv() {
            var div = document.createElement('div');
            div.id = 'col-highlight';
            div.className = 'column-highlight';
            var table = document.getElementById('tbMain');
            table.appendChild(div);
            return div;
        }

        // 美化符号样式
        function beautifySymbols() {
            var cells = document.getElementById('tbMain').getElementsByTagName('td');
            for (var i = 0; i < cells.length; i++) {
                var cell = cells[i];
                var text = cell.textContent || cell.innerText;
                if (text) {
                    text = text.trim();
                    if (text === '✔') {
                        cell.className += ' symbol-check';
                    } else if (text === '✘' || text === '✗' || text === '×') {
                        cell.className += ' symbol-cross';
                    }
                }
            }
        }
    </script>
</asp:Content>
