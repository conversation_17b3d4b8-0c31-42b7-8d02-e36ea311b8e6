﻿using Qiniu.Http;
using Qiniu.Storage;
using Qiniu.Util;
using System;
using System.Text;

namespace Account.Web
{
    public class QiNiuUpload
    {
        static string AccessKey = "5koh4QE5sLZZiU8zDS4z4d6vbkfeF3UkxPo8CboA";
        static string SecretKey = "hReEgGg5jHV0P0Rc5AzsZofaMuGvJRWtOZRv6Vbu";
        static string Bucket = "ocr-cdn";

        public static void UploadFile(string content, string fileName)
        {
            Mac mac = new Mac(AccessKey, SecretKey);
            // 设置上传策略
            PutPolicy putPolicy = new PutPolicy
            {
                // 设置要上传的目标空间
                Scope = Bucket + ":" + fileName
            };
            // 上传策略的过期时间(单位:秒)
            putPolicy.SetExpires(3600);

            // 生成上传token
            string token = Auth.CreateUploadToken(mac, putPolicy.ToJsonString());
            Config config = new Config
            {
                UseHttps = true,
                UseCdnDomains = true,
                ChunkSize = ChunkUnit.U512K
            };

            // 表单上传
            FormUploader target = new FormUploader(config);
            HttpResult result = target.UploadData(Encoding.UTF8.GetBytes(content), fileName, token, null).Result;
            Console.WriteLine("form upload result: " + result.ToString());
        }
    }
}
