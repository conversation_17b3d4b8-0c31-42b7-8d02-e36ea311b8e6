﻿using CommonLib;
using Microsoft.AspNet.SignalR;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Account.Web
{
    public class OcrHub : Hub
    {
        /// <summary>
        /// 客户端连接处理
        /// </summary>
        public override async Task OnConnected()
        {
            //LogHelper.Log.Warn("客户端连接成功：" + Context.ConnectionId);
            await base.OnConnected().ConfigureAwait(false);
        }

        /// <summary>
        /// 客户端断开处理
        /// </summary>
        public override async Task OnDisconnected(bool stopCalled)
        {
            //LogHelper.Log.Warn("客户端" + (stopCalled ? "主动" : "被动") + "断开：" + Context.ConnectionId);
            await base.OnDisconnected(stopCalled).ConfigureAwait(false);
        }

        /// <summary>
        /// 重连处理
        /// </summary>
        public override async Task OnReconnected()
        {
            //LogHelper.Log.Warn("客户端重连成功：" + Context.ConnectionId);
            await base.OnReconnected().ConfigureAwait(false);
        }

        private static readonly JsonSerializerSettings s_jsonSettings = new JsonSerializerSettings
        {
            MissingMemberHandling = MissingMemberHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore,
            ObjectCreationHandling = ObjectCreationHandling.Reuse
        };

        // 处理客户端命令
        public async Task DoProcess(byte[] bytes)
        {
            try
            {
                var strTmp = DecompressGzipToString(bytes);
                if (string.IsNullOrEmpty(strTmp))
                    return;

                var entity = JsonConvert.DeserializeObject<SignalRRequestEntity>(strTmp, s_jsonSettings);
                strTmp = null;

                if (entity == null || string.IsNullOrEmpty(entity.ServerId))
                    return;

                if (Equals(entity.OpType, "Heartbeat"))
                {
                    //LogHelper.Log.Warn(string.Format("ClientId:{2},ConnectionId:{3}\n{0}【{1}】\n{4}"
                    //    , entity.Server
                    //    , new DateTime(entity.Version).ToString("MM-dd HH:mm")
                    //    , entity.ClientId
                    //    , Context.ConnectionId
                    //    , entity.Content
                    //    ));
                    Clients.Caller.heartbeatResponse("success");
                }
                else
                    await ProcessByType(entity).ConfigureAwait(false);

                entity = null;
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("DoProcess失败！", oe);
            }
        }

        //private async Task ProcessByType(string msgId, string type, long version, string strServer, int timeOut, string content)
        private async Task ProcessByType(SignalRRequestEntity entity)
        {
            try
            {
                // 确认消息已接收
                await Clients.Caller.messageSent(entity.OpType, true).ConfigureAwait(false);

                var clientId = Context.ConnectionId;
                _ = Task.Run(async () =>
                {
                    try
                    {
                        //await Clients.Client(clientId)?.receiveMessage("Report", type + "Start");

                        var result = string.Empty;
                        switch (entity.OpType)
                        {
                            case "waitocr":
                                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(entity.TimeOut)))
                                {
                                    result = await OcrProcessHelper.WaitOcr(entity.Id, entity.Version, entity.Server, entity.TimeOut, OcrProcessHelper.ProcessByWSS, cts).ConfigureAwait(false);
                                }
                                break;
                            case "ocrresult":
                                OcrProcessHelper.SaveOcrResult(entity.Id, entity.Version, entity.Server, OcrProcessHelper.ProcessByWSS, entity.Content as string);
                                break;
                            case "waitfilestate":
                                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(entity.TimeOut)))
                                {
                                    result = await OcrProcessHelper.WaitFile(entity.Id, entity.Version, entity.Server, entity.TimeOut, OcrProcessHelper.ProcessByWSS, cts).ConfigureAwait(false);
                                }
                                break;
                            case "filestateresult":
                                OcrProcessHelper.SaveFileResult(entity.Id, entity.Version, entity.Server, OcrProcessHelper.ProcessByWSS, entity.Content as string);
                                break;
                            case "reportbeginprocess":
                                var content = entity.Content as string;
                                if (!string.IsNullOrEmpty(content))
                                {
                                    var dicQuery = ParseQueryString(content);
                                    var strId = dicQuery["id"];
                                    var dtAdd = BoxUtil.GetInt64FromObject(dicQuery["start"]);
                                    var dtReceived = BoxUtil.GetInt64FromObject(dicQuery["receive"]);
                                    var dtServerPush = BoxUtil.GetInt64FromObject(dicQuery["push"]);
                                    var dtOcrStartTransfer = BoxUtil.GetInt64FromObject(dicQuery["current"]);
                                    OcrProcessHelper.ReportToProcess(entity.Id, strId, entity.Server, dtReceived, dtServerPush, dtOcrStartTransfer, dtAdd, entity.Version, OcrProcessHelper.ProcessByWSS);

                                    dicQuery = null;
                                }
                                break;
                            default:
                                break;
                        }

                        //回传消息
                        if (!string.IsNullOrEmpty(result))
                            await Clients.Client(clientId)?.receiveMessage(entity.OpType, result).ConfigureAwait(false);

                        //await Clients.Client(clientId)?.receiveMessage("Report", type + "End");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Log.Error($"ProcessByType 异步处理异常: {entity.OpType}, {ex.Message}", ex);
                    }
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Warn(string.Format("ProcessCommandAsync {0} Error：{1}\n{2}", Context.ConnectionId, ex.Message, ex.StackTrace));
            }
        }

        private Dictionary<string, string> ParseQueryString(string extQuery)
        {
            var dicResult = new Dictionary<string, string>();
            if (string.IsNullOrEmpty(extQuery))
            {
                return dicResult;
            }
            try
            {
                var lines = extQuery.Split(new string[] { "&" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (var item in lines)
                {
                    var items = HttpUtility.UrlDecode(item).Split(new string[] { "=" }, StringSplitOptions.RemoveEmptyEntries);
                    if (items.Length != 2)
                    {
                        continue;
                    }
                    dicResult.Add(items[0], items[1]);
                    items = null;
                }
                lines = null;
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("ParseQueryString Error", oe);
            }
            finally
            {
                extQuery = null;
            }
            return dicResult;
        }

        private static string DecompressGzipToString(byte[] compressedData)
        {
            if (compressedData == null || compressedData.Length == 0)
            {
                return string.Empty;
            }
            var result = string.Empty;
            try
            {
                // 检查是否为GZIP格式（0x1F 0x8B是GZIP头部标识）
                if (compressedData.Length >= 2 && compressedData[0] == 0x1F && compressedData[1] == 0x8B)
                {
                    using (var memoryStream = new MemoryStream(compressedData))
                    using (var zipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                    using (var resultStream = new MemoryStream())
                    {
                        zipStream.CopyTo(resultStream);
                        result = Encoding.UTF8.GetString(resultStream.GetBuffer(), 0, (int)resultStream.Length);
                    }
                }
                else
                {
                    // 直接作为UTF-8字符串解码
                    result = Encoding.UTF8.GetString(compressedData);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("DecompressGzipToString Error", oe);
                try
                {
                    result = Encoding.UTF8.GetString(compressedData);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("DecompressGzipToString UTF8.GetString Error", ex);
                }
            }
            finally
            {
                compressedData = null;
            }
            return result;
        }
    }
}