CodeMirror.extendMode("css",{commentStart:"/*",commentEnd:"*/",newlineAfterToken:function(e,t){return/^[;{}]$/.test(t)}}),CodeMirror.extendMode("javascript",{commentStart:"/*",commentEnd:"*/",newlineAfterToken:function(e,t,n,o){return this.jsonMode?/^[\[,{]$/.test(t)||/^}/.test(n):(";"!=t||!o.lexical||")"!=o.lexical.type)&&/^[;{}]$/.test(t)&&!/^;/.test(n)}}),CodeMirror.extendMode("xml",{commentStart:"\x3c!--",commentEnd:"--\x3e",newlineAfterToken:function(e,t,n){return"tag"==e&&/>$/.test(t)||/^</.test(n)}}),CodeMirror.defineExtension("commentRange",function(e,t,n){var o=this,r=CodeMirror.innerMode(o.getMode(),o.getTokenAt(t).state).mode;o.operation(function(){if(e)o.replaceRange(r.commentEnd,n),o.replaceRange(r.commentStart,t),t.line==n.line&&t.ch==n.ch&&o.setCursor(t.line,t.ch+r.commentStart.length);else{var i=o.getRange(t,n),a=i.indexOf(r.commentStart),s=i.lastIndexOf(r.commentEnd);a>-1&&s>-1&&s>a&&(i=i.substr(0,a)+i.substring(a+r.commentStart.length,s)+i.substr(s+r.commentEnd.length)),o.replaceRange(i,t,n)}})}),CodeMirror.defineExtension("autoIndentRange",function(e,t){var n=this;this.operation(function(){for(var o=e.line;o<=t.line;o++)n.indentLine(o,"smart")})}),CodeMirror.defineExtension("autoFormatRange",function(e,t){var n=this,o=n.getMode(),r=n.getRange(e,t).split("\n"),i=CodeMirror.copyState(o,n.getTokenAt(e).state),a=n.getOption("tabSize"),s="",c=0,d=0==e.ch;function m(){s+="\n",d=!0,++c}for(var l=0;l<r.length;++l){for(var f=new CodeMirror.StringStream(r[l],a);!f.eol();){var g=CodeMirror.innerMode(o,i),u=o.token(f,i),M=f.current();f.start=f.pos,d&&!/\S/.test(M)||(s+=M,d=!1),!d&&g.mode.newlineAfterToken&&g.mode.newlineAfterToken(u,M,f.string.slice(f.pos)||r[l+1]||"",g.state)&&m()}!f.pos&&o.blankLine&&o.blankLine(i),d||m()}n.operation(function(){n.replaceRange(s,e,t);for(var o=e.line+1,r=e.line+c;o<=r;++o)n.indentLine(o,"smart");n.setSelection(e,n.getCursor(!1))})});