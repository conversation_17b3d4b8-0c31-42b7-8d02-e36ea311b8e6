﻿{"ja":"<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ja\"><head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ja/Detail.aspx\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/Detail.aspx\" hreflang=\"x-default\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hans/Detail.aspx\" hreflang=\"zh\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Detail.aspx\" hreflang=\"zh-tw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Detail.aspx\" hreflang=\"zh-yue\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Detail.aspx\" hreflang=\"en\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Detail.aspx\" hreflang=\"es\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Detail.aspx\" hreflang=\"hi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Detail.aspx\" hreflang=\"ar\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Detail.aspx\" hreflang=\"pt\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Detail.aspx\" hreflang=\"bn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Detail.aspx\" hreflang=\"ru\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Detail.aspx\" hreflang=\"ja\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Detail.aspx\" hreflang=\"pa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Detail.aspx\" hreflang=\"de\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Detail.aspx\" hreflang=\"fr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Detail.aspx\" hreflang=\"mr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Detail.aspx\" hreflang=\"te\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Detail.aspx\" hreflang=\"vi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Detail.aspx\" hreflang=\"ko\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Detail.aspx\" hreflang=\"ta\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Detail.aspx\" hreflang=\"ur\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Detail.aspx\" hreflang=\"tr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Detail.aspx\" hreflang=\"it\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Detail.aspx\" hreflang=\"gu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Detail.aspx\" hreflang=\"pl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Detail.aspx\" hreflang=\"uk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Detail.aspx\" hreflang=\"ms\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Detail.aspx\" hreflang=\"id\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Detail.aspx\" hreflang=\"ml\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Detail.aspx\" hreflang=\"kn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Detail.aspx\" hreflang=\"fa\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Detail.aspx\" hreflang=\"nl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Detail.aspx\" hreflang=\"th\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Detail.aspx\" hreflang=\"sw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Detail.aspx\" hreflang=\"ro\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Detail.aspx\" hreflang=\"my\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Detail.aspx\" hreflang=\"or\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Detail.aspx\" hreflang=\"he\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Detail.aspx\" hreflang=\"am\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Detail.aspx\" hreflang=\"fil\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Detail.aspx\" hreflang=\"sv\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Detail.aspx\" hreflang=\"el\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Detail.aspx\" hreflang=\"cs\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Detail.aspx\" hreflang=\"hu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Detail.aspx\" hreflang=\"be\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Detail.aspx\" hreflang=\"si\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Detail.aspx\" hreflang=\"ne\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Detail.aspx\" hreflang=\"km\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Detail.aspx\" hreflang=\"sk\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Detail.aspx\" hreflang=\"bg\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Detail.aspx\" hreflang=\"fr-ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Detail.aspx\" hreflang=\"ha\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Detail.aspx\" hreflang=\"yo\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Detail.aspx\" hreflang=\"ig\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Detail.aspx\" hreflang=\"ku\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Detail.aspx\" hreflang=\"rw\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Detail.aspx\" hreflang=\"ca\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Detail.aspx\" hreflang=\"da\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Detail.aspx\" hreflang=\"fi\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Detail.aspx\" hreflang=\"nb\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Detail.aspx\" hreflang=\"hr\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Detail.aspx\" hreflang=\"sr-cyrl\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Detail.aspx\" hreflang=\"sr-latn\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Detail.aspx\" hreflang=\"sq\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Detail.aspx\" hreflang=\"so\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Detail.aspx\" hreflang=\"zu\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Detail.aspx\" hreflang=\"ka\" />\n\n\n\n<meta charset=\"UTF-8\"><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\"><meta name=\"renderer\" content=\"webkit\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\"><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\"><link rel=\"Shortcut Icon\" href=\"/favicon.ico\" type=\"image/x-icon\"><link rel=\"bookmark\" href=\"/favicon.ico\" type=\"image/x-icon\"><meta name=\"robots\" content=\"all\"><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\"><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\"><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\">\n    <script type=\"text/javascript\">\n        var isWebP = (function () {\n            try {\n                return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0;\n            } catch (e) {\n                return false;\n            }\n        })();\n        document.addEventListener('DOMContentLoaded', function() {\n            var container = document.getElementById('user-login-container');\n            if (container) {\n                var url = container.getAttribute('data-url');\n                fetch(url)\n                    .then(response => response.text())\n                    .then(html => {\n                        container.innerHTML = html;\n                        // 为所有动态加载的按钮添加适当的样式\n                        container.querySelectorAll('a').forEach(function(link) {\n                            link.style.display = 'inline-block';\n                            link.style.marginLeft = '10px';\n                        });\n                    });\n            }\n        });\n    </script>\n    \n    <script type=\"application/ld+json\">{\"@context\":\"http://schema.org\",\"@type\":\"SoftwareApplication\",\"name\":\"OCR Assistant\",\"description\":\"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.\",\"category\":\"Productivity\",\"applicationCategory\":\"Business\",\"image\":\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif\",\"screenshot\":[\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png\",\"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png\"],\"aggregateRating\":{\"@type\":\"AggregateRating\",\"worstRating\":\"1\",\"bestRating\":\"5\",\"ratingValue\":\"4.8\",\"ratingCount\":\"524055\"},\"offers\":{\"@type\":\"Offer\",\"price\":0,\"priceCurrency\":\"USD\",\"category\":\"free\"},\"operatingSystem\": \"Windows\"}</script>\n<link rel=\"stylesheet\" href=\"../site/css/site.css\" type=\"text/css\">\n    <script src=\"site/js/site.js\" type=\"text/javascript\"></script>\n<title>\nOCRアシスタント製品紹介 - OCRテキスト認識アシスタント(OCRアシスタント)は、無料のテキスト認識(OCR)ツールで、作業効率の向上に取り組んでいます。\n</title></head>\n<body>\n    <div class=\"home-container root\">\n\n        <header class=\"v4_header_pc\">\n            <div class=\"header_pc_left\">\n                <a href=\"Default.aspx\" title=\"ホーム | AIインテリジェントテキスト認識\" class=\"pc-logo-wrap ml-4 mr-5\">\n                    <picture>\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" class=\"pc-logo\" style=\"height: 100%\" alt=\"\" aria-hidden=\"true\">\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"\" aria-hidden=\"true\">\n                    </picture>\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字認識アシスタント</span>\n                </a>\n                <ul class=\"top-nav\">\n                    <li>\n                        <a class=\"color-default\" href=\"Default.aspx\">家</a>&nbsp;\n                    </li>\n                    <li>\n                        <a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\">機能</a>&nbsp;\n                        <div class=\"triangle\"></div>\n                    </li>\n                    <li>\n                        <a class=\"color-default active\" data-sub=\"experience-sub-nav\" href=\"javascript:;\">オンライン体験</a>&nbsp;\n                        <div class=\"triangle active\"></div>\n                    </li>\n                    <li>\n                        <a class=\"color-default\" data-sub=\"tool-sub-nav\" href=\"javascript:;\">オンラインツール</a>&nbsp;\n                        <div class=\"triangle\"></div>\n                    </li>\n                    <li>\n                        <a class=\"color-default\" data-sub=\"cloud-sub-nav\" href=\"javascript:;\">我々について</a>&nbsp;\n                        <div class=\"triangle\"></div>\n                    </li>\n                </ul>\n                <div class=\"product-con\">\n                    <!-- 产品服务 -->\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0; opacity: 0; transform: translate(0px, -100%);\">\n                            <div class=\"prdocu-sub-left\">\n                                <div class=\"mr-auto\">\n                                    <picture>\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\">\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"\" aria-hidden=\"true\">\n                                    </picture>\n                                    <div class=\"mt-4\">\n                                        <h3 class=\"h6\">OCR文字認識アシスタント</h3>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">効率を向上させ、コストを削減し、価値を創造する</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">インテリジェントな認識、高速処理、正確な出力</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テキストから表まで、数式から翻訳まで</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">すべてのワードプロセッシングをとても簡単に</p>\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">今すぐ試す  <span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"bg-white prdocu-sub-right\">\n                                <div>\n                                    <h2 class=\"text-base-color h6\">AIインテリジェントテキスト認識</h2>\n                                    <div class=\"mt-4 d-flex flex-wrap\">\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">ユニバーサル識別</h3>\n                    <span class=\"color-gray fn14\">多言語印刷テキスト認識、契約書、請求書、ドキュメントスキャンに最適、精度98%+</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">手書き認識</h3>\n                    <span class=\"color-gray fn14\">中国語と英語の手書きコンテンツを認識し、ノート、テスト用紙、医療記録などのシナリオに適用し、90%+ の精度で</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">カード識別</h3>\n                    <span class=\"color-gray fn14\">22種類の請求書情報、サポート請求書、旅程表、タクシーチケット、その他の迅速な入力を自動的に抽出します</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">フォーミュラ認識</h3>\n                    <span class=\"color-gray fn14\">数式とテスト用紙の内容を特定し、教育指導、インテリジェント採点、問題バンクの構築をサポートします</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">フォーム認識</h3>\n                    <span class=\"color-gray fn14\">罫線/フレームなしのテーブルデータを抽出し、結合されたセルを自動的に処理し、ワンクリックでExcelにエクスポートします</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">単語の翻訳</h3>\n                    <span class=\"color-gray fn14\">100+言語をサポートしており、ドキュメントの読み取りや学習シナリオに適しています</span>\n                </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">画像翻訳</h3>\n        <span class=\"color-gray fn14\">画像内のテキストコンテンツをワンクリックで翻訳し、元の形式を保持し、海外の画像素材や国際交流に適しています</span>\n    </div>\n                                        </a>\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">ドキュメント翻訳</h3>\n        <span class=\"color-gray fn14\">Word/PDF/PPT/Excelドキュメントを翻訳し、元のレイアウトを保持し、ビジネス文書や学術資料に適しています</span>\n    </div>\n                                        </a>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"\">\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 100%; opacity: 1; transform: translate(0px, 0px);\">\n                            <div class=\"prdocu-sub-left\">\n                                <div class=\"mr-auto\">\n                                    <picture>\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\">\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"\" aria-hidden=\"true\">\n                                    </picture>\n                                    <div class=\"mt-4\">\n                                        <h3 class=\"h6\">OCR文字認識アシスタント</h3>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テキスト、表、数式、ドキュメント、翻訳</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">すべてのワープロのニーズを3つのステップで完了します</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">→アプリを識別する→スクリーンショット</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">生産性が300%向上</p>\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">今すぐ試す  <span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"bg-white prdocu-sub-right\">\n                                <div>\n                                    <h2 class=\"text-base-color h6\">オンラインで無料で試す</h2>\n                                    <div class=\"mt-4 d-flex flex-wrap\">\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">フル機能</h3>\n                    <span class=\"color-gray fn14\">すべてのOCRスマート機能を1か所で体験し、ニーズに最適なソリューションをすばやく見つけることができます</span>\n                </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">ユニバーサルテキスト認識</h3>\n                    <span class=\"color-gray fn14\">多言語の高精度テキストインテリジェント抽出、印刷とマルチシーンの複雑な画像認識をサポート</span>\n                </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">一般的なフォーム認識</h3>\n                    <span class=\"color-gray fn14\">テーブル画像をExcelファイルにインテリジェントに変換し、複雑なテーブル構造を自動的に処理し、セルをマージします</span>\n                </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">PDFからWordへ</h3>\n                    <span class=\"color-gray fn14\">PDFドキュメントはWord形式にすばやく変換されるため、元のレイアウトとグラフィックレイアウトが完全に保持されます</span>\n                </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">PDFからMarkdownへ</h3>\n                    <span class=\"color-gray fn14\">PDFドキュメントはインテリジェントにMD形式に変換され、コードブロックとテキスト構造は自動的に最適化されます</span>\n                </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">PDFから画像へ</h3>\n        <span class=\"color-gray fn14\">PDFドキュメントは高解像度のJPG画像に変換され、バッチ処理とカスタム解像度のエクスポートがサポートされています</span>\n    </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">WordからPDFへ</h3>\n        <span class=\"color-gray fn14\">WordドキュメントをPDFにワンクリックで変換し、元の形式を完全に保持し、アーカイブや正式なドキュメント共有に適しています</span>\n    </div>\n                                        </a>\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">Wordから画像へ</h3>\n        <span class=\"color-gray fn14\">Word文書はJPG画像に変換でき、複数ページ処理をサポートしているため、ソーシャルメディアでの共有や表示に便利です</span>\n    </div>\n                                        </a>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n                    <section id=\"tool-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\n                            <div class=\"prdocu-sub-left\">\n                                <div class=\"mr-auto\">\n                                    <picture>\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\">\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"\" aria-hidden=\"true\">\n                                    </picture>\n                                    <div class=\"mt-4\">\n                                        <h3 class=\"h6\">OCR文字認識アシスタント</h3>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">生活をシンプルにし、エクスペリエンスを向上させ、リリースタイムを短縮</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テクノロジーで生活への負担を軽減</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">写真を撮って識別→楽しむ→</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">毎日2時間の自由時間を追加で取得</p>\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">今すぐ試す  <span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"bg-white prdocu-sub-right\">\n                                <div>\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">オンラインツール</h2>\n                                    <div class=\"mt-4 d-flex flex-wrap\">\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">JSON の書式設定</h3>\n                    <span class=\"color-gray fn14\">JSONコード構造をインテリジェントに美化し、圧縮と拡張をサポートし、開発とデバッグを容易にします</span>\n                </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">テキスト比較の結合</h3>\n                    <span class=\"color-gray fn14\">テキストの違いを強調表示し、行ごとの比較をサポートし、スマートマージを行います</span>\n                </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">テキストエンコーディング変換</h3>\n                    <span class=\"color-gray fn14\">Base64/URL/Unicodeおよびその他のエンコード形式をサポートして相互に変換します</span>\n                </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">正規表現</h3>\n                    <span class=\"color-gray fn14\">正規表現のマッチング効果をリアルタイムで検証し、一般的なパターンの組み込みライブラリを用意</span>\n                </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">単語数組版統計</h3>\n                    <span class=\"color-gray fn14\">文字数、語彙数、段落数をインテリジェントにカウントし、テキストレイアウトを自動的に最適化します</span>\n                </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">タイムスタンプの変換</h3>\n        <span class=\"color-gray fn14\">時刻と Unix のタイムスタンプは、さまざまな形式とタイム ゾーン設定との間で変換されます</span>\n    </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">スポイト</h3>\n        <span class=\"color-gray fn14\">Webページ要素の色を正確に抽出し、HEX / RGB / HSL形式をサポートします</span>\n    </div>\n                                        </a>\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">オンライン計算機</h3>\n        <span class=\"color-gray fn14\">標準コンピューティング、科学計算、およびベース変換機能を統合して、複数のシナリオのコンピューティング要件を満たします</span>\n    </div>\n                                        </a>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n                    <section id=\"cloud-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\n                            <div class=\"prdocu-sub-left\">\n                                <div class=\"mr-auto\">\n                                    <picture>\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\">\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"\" aria-hidden=\"true\">\n                                    </picture>\n                                    <div class=\"mt-4\">\n                                        <h3 class=\"h6\">OCR文字認識アシスタント</h3>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">効率的なオフィス、インテリジェントな処理、高速出力</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">3つのステップでワークフローを加速</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">集録→処理→多重化</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">就業日が1時間に短縮されました</p>\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">今すぐ試す  <span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\n                                    </div>\n                                </div>\n                            </div>\n                            <div class=\"bg-white prdocu-sub-right\">\n                                <div>\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">我々について</h2>\n                                    <div class=\"mt-4 d-flex flex-wrap\">\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">サービスステータス</h3>\n                    <span class=\"color-gray fn14\">グローバル識別ノードの実行ステータスをリアルタイムで監視し、システムのパフォーマンスと可用性データを表示します</span>\n                </div>\n                                        </a>\n                                        <a href=\"Agreemeut.aspx\" target=\"_blank\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">ユーザー同意書</h3>\n                    <span class=\"color-gray fn14\">ユーザーの権利と利益、およびプラットフォームの健全な発展を保護するための製品の利用規約とサービス仕様について詳しく学びましょう</span>\n                </div>\n                                        </a>\n                                        <a href=\"privacy.html\" target=\"_blank\" class=\"pro-item color-default\">\n                                            <div class=\"letter-wrap\">\n                                                <h3 class=\"h6\">プライバシー契約</h3>\n                    <span class=\"color-gray fn14\">ユーザーデータ保護の慣行とプライバシーポリシーを確認して、お客様の情報をどのように保護しているかを理解してください</span>\n                </div>\n                                        </a>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </section>\n                </div>\n            </div>\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\n                <div id=\"translate\"></div>\n                <div id=\"user-login-container\" data-url=\"code.ashx?op=userinfo\" style=\"white-space: nowrap;padding-right:20px;\"><a class=\"pc_register\" style=\"display: inline-block; box-sizing: border-box; min-width: 80px; height: 33px; line-height: 33px; font-size: 14px; border-radius: 4px; margin: 0px 10px; text-align: center; background-color: rgb(13, 110, 253); color: rgb(255, 255, 255); border: none; font-weight: normal; text-decoration: none; padding: 0px 15px;\" href=\"User.aspx\">登录</a></div>\n            </div>\n        </header>\n        <header class=\"v4_header_mob\">\n            <div class=\"container\">\n                <div class=\"row no-gutters justify-content-between align-items-center\">\n                    <a href=\"Default.aspx\" title=\"ホーム | AIインテリジェントテキスト認識\" class=\"mob-logo-wrap\">\n                        <picture>\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"\" aria-hidden=\"true\">\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"\" aria-hidden=\"true\">\n                        </picture>\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字認識アシスタント</span>\n                    </a>\n                    <div class=\"right-menu\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\n                        <nav-products id=\"nav_products\">\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\n                                <label id=\"mdi-submail-nav-btn\">\n                                    <span class=\"mdi-nav-products-1\">\n                                        <span class=\"nav-products-icon\"></span>\n                                    </span>\n                                    <span class=\"mdi-nav-products-2\">\n                                        <span class=\"nav-products-icon\"></span>\n                                    </span>\n                                    <span class=\"mdi-nav-products-3\">\n                                        <span class=\"nav-products-icon\"></span>\n                                    </span>\n                                </label>\n                            </nav-button>\n                        </nav-products>\n                    </div>\n                </div>\n            </div>\n            <div class=\"mob-nav-content\">\n                <div class=\"sidebar-fix\">\n                    <div class=\"container\">\n                        <div class=\"row\">\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\n                                <div class=\"mob-nav-item active\" data=\"0\">\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>家</span></a></div>\n                                </div>\n                                <div class=\"mob-nav-item\" data=\"1\">\n                                    <div class=\"nav-header\"><a href=\"#\" class=\"nav-header-letter px-3\"><span>機能</span></a></div>\n                                </div>\n                                <div class=\"mob-nav-item\">\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span style=\"border-bottom: 1px solid #b2bbd0\">プレミアムメンバーシップ</span></a></div>\n                                </div>\n                                <div class=\"mob-nav-item\">\n                                    <div class=\"nav-header\"><a href=\"Status.aspx\" class=\"nav-header-letter px-3\"><span style=\"border-bottom: 1px solid #b2bbd0\">サービスステータス</span></a></div>\n                                </div>\n                                <div class=\"mob-nav-item\" data=\"2\">\n                                    <div class=\"nav-header\"><a href=\"#\" class=\"nav-header-letter px-3\"><span>我々について</span></a></div>\n                                </div>\n                            </div>\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\n                                <div class=\"sub-nav\">\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">効率的な生産性向上ツール</h2>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">インテリジェントな認識、高速処理、正確な出力</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">3秒で全ページのドキュメントを認識する</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ 認識精度</p>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">遅延のない多言語リアルタイム処理</p>\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">サインアップして7日間の無料Proメンバーシップを取得し、よりエキサイティングな体験を無料で体験してください!<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\n                                    </div>\n                                </div>\n                                <div class=\"sub-nav\">\n                                    <div class=\"sub-nav-item py-3 px-4\">\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">AIインテリジェント識別のためのワンストップサービスプラットフォーム</h2>\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI業界のエリート、さまざまなタイプの100 +高精度チャンネルをまとめる</p>\n                                    </div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">ユニバーサル識別</a></div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">フォーム認識</a></div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">フォーミュラ認識</a></div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">ドキュメント認識</a></div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">グラフィック翻訳</a></div>\n                                </div>\n                                <div class=\"sub-nav\">\n                                    <div class=\"sub-nav-item py-3 px-4\">\n                                        <h2 class=\"h4\" style=\"margin: inherit;\">OCRテキスト認識アシスタントについて</h2>\n                                    </div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Agreemeut.aspx\" target=\"_blank\">ユーザー同意書</a></div>\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"privacy.html\" target=\"_blank\">プライバシー契約</a></div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </header>\n        <form method=\"post\" action=\"./Detail.aspx\" id=\"aspnetForm\">\n<div class=\"aspNetHidden\">\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"6FSVgo3RrBbqJtvSQ24pNrqICbPKm5srzwnFFhkkdkfHN/uCFUaY6z+3lmdId2S8YYwE253w9ovRZUT9HsgYxrxLyms5eKda9GcTSp8naHw=\">\n</div>\n\n<div class=\"aspNetHidden\">\n\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"05E0F5E4\">\n</div>\n            <div>\n                \n    <iframe id=\"frm\" onload=\"this.height=this.contentWindow.document.body.scrollHeight+50\" width=\"100%\" height=\"3576\" style=\"margin-top: 60px; height: 3526px;\" frameborder=\"no\" border=\"0\" marginwidth=\"0\" marginheight=\"0\" scrolling=\"yes\" allowtransparency=\"yes\" src=\"/ja/Product.aspx\"></iframe>\n    <script type=\"text/javascript\">\n        function getCurrentLanguage() {\n            var pathParts = window.location.pathname.split('/');\n            if (pathParts.length > 1 && pathParts[1] && pathParts[1].length > 0) {\n                var possibleLang = pathParts[1];\n                if (possibleLang.length >= 2 && possibleLang.indexOf('.') === -1) {\n                    return possibleLang;\n                }\n            }\n            return \"zh-Hans\";\n        }\n        document.getElementById('frm').src = \"/\" + getCurrentLanguage() + \"/Product.aspx\";\n        var iFrames = document.getElementsByTagName('iframe');\n        function iResize() {\n            for (var i = 0, j = iFrames.length; i < j; i++) {\n                var bHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.body ? iFrames[i].contentWindow.document.body.scrollHeight : 0;\n                var dHeight = iFrames[i].contentWindow && iFrames[i].contentWindow.document && iFrames[i].contentWindow.document.documentElement ? iFrames[i].contentWindow.document.documentElement.scrollHeight : 0;\n                var cHeight = iFrames[i].document && iFrames[i].document.documentElement ? iFrames[i].document.documentElement.scrollHeight : 0;\n                var dHeight = window.innerHeight - 100;\n                iFrames[i].style.height = Math.max(Math.max(Math.max(bHeight, dHeight), cHeight), dHeight) + 'px';\n            }\n        }\n        window.setInterval(\"iResize()\", 200);\n    </script>\n\n            </div>\n        </form>\n\n        <div class=\"fixed-icon\">\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\n            </div>\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&amp;uin=365833440&amp;site=qq&amp;menu=yes\" target=\"_blank\">\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"QQカスタマーサービス\">\n                <div class=\"wx-text\">\nQQカスタマーサービス(365833440)\n                 </div>\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\n            </a>\n\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&amp;jump_from=webapi&amp;qr=1\" target=\"_blank\">\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"QQグループ\">\n                <div class=\"wx-text\">\nQQグループ (100029010)\n                 </div>\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\n            </a>\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"電子メール\">\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\n                    <div style=\"font-size: 15px\">\nEメール:<EMAIL>\n                     </div>\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\">\n                    <span style=\"font-size: 13px; color: #c1c1c1\">ご意見・ご感想をいただき、誠にありがとうございます!</span>\n                </div>\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\n            </a>\n        </div>\n\n        <style>\n            .content {\n                width: 100%;\n                padding-right: 0 !important;\n                padding-left: 0 !important;\n                margin-right: auto;\n                margin-left: auto;\n            }\n            \n            /* SEO优化：控制标题标签的字体大小 */\n            h3.h6 {\n                font-size: 1rem !important; /* 与h6标签相同大小 */\n                font-weight: bold !important;\n                line-height: 1.2 !important;\n                margin-bottom: .5rem;\n                font-family: inherit;\n            }\n            \n        </style>\n        <footer class=\"page-footer\">\n            <div class=\"container\">\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\nOCRテキスト認識アシスタント©️ 2025 無断複写・転載を禁じます。 全著作権所有 |&nbsp;&nbsp;&nbsp;\n                         <a href=\"privacy.html\" target=\"_blank\" style=\"text-decoration: underline;\">プライバシー契約</a>&nbsp;&nbsp;&nbsp;\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">鄂 ICP番号 2021012692</a>\n                    </div>\n                </div>\n            </div>\n        </footer>\n        <style>\n            #translate > .translateSelectLanguage {\n                right: 2rem;\n                font-size: 1rem;\n                width: 150px;\n                padding: 0.3rem;\n                margin-right: 20px;\n                border: 1px solid #C9C9C9;\n                background-color: #fff;\n                color: #555;\n            }\n        </style>\n    </div>\n\n\n<script type=\"text/javascript\">isNeedTrans=false;</script><script src=\"/static/js/translate.js\"></script></body></html>"}