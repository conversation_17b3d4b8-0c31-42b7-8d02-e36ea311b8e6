using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// 语言服务 - 负责多语言处理的核心功能
    /// 包括语言定义、获取、翻译和缓存管理
    /// </summary>
    public static class LanguageService
    {
        // 常量
        public const string DefaultLanguage = "zh-Hans"; // 与CommonTranslate.StrDefaultLang保持一致
        public const string HostUrl = "https://ocr.oldfish.cn/";
        public const string UserTranslationKey = "TransCache";
        public const string WebTranslationKey = "WebTrans";

        /// <summary>
        /// 从除URL路径外其他地方获取当前语言
        /// </summary>
        /// <remarks>
        /// 根据优先级顺序返回标准语言代码：URL路径 > 查询参数 > 浏览器语言
        /// </remarks>
        public static string GetCurrentLanguage(HttpRequest request, bool allowAuto = false)
        {
            var debugInfo = HttpContext.Current.Items["LangDebugInfo"] as System.Text.StringBuilder;
            debugInfo?.AppendLine("LanguageService.GetCurrentLanguage 开始执行。<br/>");

            string standardCode;

            // 1：从查询参数获取
            string queryLang = request.GetValue("lang");
            if (!string.IsNullOrEmpty(queryLang))
            {
                debugInfo?.AppendLine($"&nbsp;&nbsp;- 发现URL参数 'lang'，值为: '{queryLang}'<br/>");
                if (TryGetStandardLanguageCode(queryLang, out standardCode))
                {
                    debugInfo?.AppendLine($"&nbsp;&nbsp;- 成功从URL参数 'lang' 中获取语言: {queryLang} -> {standardCode}<br/>");
                    debugInfo?.AppendLine("LanguageService.GetCurrentLanguage 执行完毕。<br/><br/>");
                    return standardCode;
                }
            }

            // 2：从浏览器设置获取
            if (allowAuto && request?.UserLanguages?.Length > 0)
            {
                debugInfo?.AppendLine($"&nbsp;&nbsp;- 浏览器发送的 Accept-Language: [{string.Join(", ", request.UserLanguages)}]<br/>");
                // 从浏览器语言列表中寻找第一个有效语言
                foreach (var userLang in request.UserLanguages)
                {
                    if (string.IsNullOrEmpty(userLang))
                        continue;

                    string browserLang = userLang.Split(';')[0].Trim();
                    if (TryGetStandardLanguageCode(browserLang, out standardCode))
                    {
                        debugInfo?.AppendLine($"&nbsp;&nbsp;- 成功从浏览器语言列表中匹配到: {browserLang} -> {standardCode}<br/>");
                        debugInfo?.AppendLine("LanguageService.GetCurrentLanguage 执行完毕。<br/><br/>");
                        return standardCode;
                    }
                }
            }

            // 3：默认语言
            debugInfo?.AppendLine($"&nbsp;&nbsp;- 未能从URL参数或浏览器设置中找到匹配语言，返回默认语言: {DefaultLanguage}<br/>");
            debugInfo?.AppendLine("LanguageService.GetCurrentLanguage 执行完毕。<br/><br/>");
            return DefaultLanguage;
        }

        /// <summary>
        /// 检查是否需要重定向到带语言代码的URL
        /// </summary>
        /// <returns>如果需要重定向，返回重定向的URL；否则返回null</returns>
        public static string CheckLanguageRedirect(HttpContext context, out string currentLang)
        {
            currentLang = context.Items["lang"]?.ToString();
            var oldLang = GetCurrentLanguage(context.Request, true);
            var currentLanguagePath = context.Items["oriLanuagePath"]?.ToString() ?? string.Empty;

            if (!Equals(currentLang, oldLang)
                || (!string.IsNullOrEmpty(currentLanguagePath) && !string.Equals(currentLanguagePath, currentLang, StringComparison.OrdinalIgnoreCase)))
            {
                //// 兼容默认语言无语言路径
                //if (string.IsNullOrEmpty(currentLang) && string.Equals(oldLang, CommonTranslate.StrDefaultLang, StringComparison.OrdinalIgnoreCase))
                //{
                //    currentLang = currentLang ?? oldLang;
                //}
                //else
                {
                    currentLang = currentLang ?? oldLang;
                    return UrlService.BuildRedirectUrl(context, context.Request.Path, currentLang);
                }
            }
            return null;
        }

        /// <summary>
        /// 尝试将语言代码转换为标准格式
        /// </summary>
        /// <param name="langCode">输入的语言代码</param>
        /// <param name="standardCode">输出的标准语言代码</param>
        /// <returns>如果找到匹配返回true，否则返回false</returns>
        public static bool TryGetStandardLanguageCode(string langCode, out string standardCode)
        {
            var debugInfo = HttpContext.Current.Items["LangDebugInfo"] as System.Text.StringBuilder;
            debugInfo?.AppendLine($"&nbsp;&nbsp;&nbsp;&nbsp;-- TryGetStandardLanguageCode 尝试匹配输入值: '{langCode}'<br/>");

            // 输入为空，无法转换
            if (string.IsNullOrEmpty(langCode))
            {
                standardCode = DefaultLanguage;
                debugInfo?.AppendLine($"&nbsp;&nbsp;&nbsp;&nbsp;-- 结果: 失败，输入为空。<br/>");
                return false;
            }

            // 因为字典是 OrdinalIgnoreCase 的，所以 ContainsKey 本身就是不区分大小写的
            if (CommonTranslate.DicJsLanguage.ContainsKey(langCode))
            {
                // 为了获取字典中key的原始大小写（例如 "zh-Hans" 而不是 "zh-hans"），我们还需要一次查找
                standardCode = CommonTranslate.DicJsLanguage.Keys.First(k => k.Equals(langCode, StringComparison.OrdinalIgnoreCase));
                debugInfo?.AppendLine($"&nbsp;&nbsp;&nbsp;&nbsp;-- 结果: 成功 (作为字典Key匹配), 输出: '{standardCode}'<br/>");
                return true;
            }

            // 如果语言代码是value中的一个值
            foreach (var entry in CommonTranslate.DicJsLanguage)
            {
                if (entry.Value.Any(v => v.Equals(langCode, StringComparison.OrdinalIgnoreCase)))
                {
                    standardCode = entry.Key;
                    debugInfo?.AppendLine($"&nbsp;&nbsp;&nbsp;&nbsp;-- 结果: 成功 (作为字典Value匹配), 输出: '{standardCode}'<br/>");
                    return true;
                }
            }

            // 如果没有匹配
            standardCode = DefaultLanguage;
            debugInfo?.AppendLine($"&nbsp;&nbsp;&nbsp;&nbsp;-- 结果: 失败，无匹配项。<br/>");
            return false;
        }

        /// <summary>
        /// 获取页面翻译
        /// </summary>
        public static string GetPageTranslation(string pageKey, string physicalPath, string lang = null)
        {
            if (!string.IsNullOrEmpty(pageKey) && !string.IsNullOrEmpty(physicalPath) &&
                !string.IsNullOrEmpty(lang) && File.Exists(physicalPath))
            {
                try
                {
                    var key = string.Format("{0}_{1}",
                        pageKey.Replace(":", "-").Replace(".", "_").ToUpper().Trim(),
                        File.GetLastWriteTime(physicalPath).ToString("yyyy-MM-dd HHmmss"));

                    var dicTrans = CodeProcessHelper.PageTransCache.Get(key);
                    if (dicTrans != null && dicTrans.ContainsKey(lang))
                    {
                        return dicTrans[lang];
                    }
                }
                catch { }
            }
            return string.Empty;
        }

        /// <summary>
        /// 设置页面翻译
        /// </summary>
        public static void SetPageTranslation(string language, string localPath, string physicalPath, string translation)
        {
            if (string.IsNullOrEmpty(physicalPath) || !File.Exists(physicalPath))
                return;

            var strKey = string.Format("{0}_{1}",
                localPath.Replace("/", "-").Replace(":", "-").Replace(".", "_").ToUpper().Trim(),
                File.GetLastWriteTime(physicalPath).ToString("yyyy-MM-dd HHmmss"));

            var dicAll = CodeProcessHelper.PageTransCache.Get(strKey, true);
            if (!dicAll.ContainsKey(language))
            {
                // 替换所有hreflang和canonical标签
                string pattern = @"(<link\s+rel\s*=\s*[""'](?:canonical|alternate)[""'][^>]*>)+";
                if (Regex.IsMatch(translation, pattern, RegexOptions.IgnoreCase))
                {
                    var strNewLanguageLink = GenerateLanguageLinks(localPath, language);
                    // 找到标签并替换
                    translation = Regex.Replace(translation, pattern, strNewLanguageLink, RegexOptions.IgnoreCase);
                }

                // 修正HTML lang属性
                string langPattern = @"<html[^>]*lang\s*=\s*[""'][^""']*[""']";
                if (Regex.IsMatch(translation, langPattern, RegexOptions.IgnoreCase))
                {
                    translation = Regex.Replace(
                        translation,
                        langPattern,
                        $"<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"{language}\"",
                        RegexOptions.IgnoreCase
                    );
                }
                dicAll[language] = translation;
                CodeProcessHelper.PageTransCache.Set(strKey, dicAll);
            }
        }

        /// <summary>
        /// 生成多语言链接标签
        /// </summary>
        public static string GenerateLanguageLinks(string path, string currentLanguage)
        {
            StringBuilder sb = new StringBuilder();

            // 当前页面的URL
            string currentUrl = HostUrl + (string.IsNullOrEmpty(currentLanguage) ? "" : currentLanguage + "/") + path.TrimStart('/');

            // 添加canonical标签
            sb.AppendFormat("<link rel=\"canonical\" href=\"{0}\" />", currentUrl);

            // 默认语言链接
            sb.AppendFormat("<link rel=\"alternate\" href=\"{0}{1}/{2}\" hreflang=\"x-default\" />",
                HostUrl, CommonTranslate.StrDefaultLang, path);

            // 各语言版本链接
            foreach (var lang in CommonTranslate.DicJsLanguage.Keys)
            {
                // 获取标准化的语言代码
                string hreflangValue = ConvertToStandardLanguageCode(lang);

                // 路径格式URL
                sb.AppendFormat("<link rel=\"alternate\" href=\"{0}{1}/{2}\" hreflang=\"{3}\" />",
                    HostUrl, lang, path, hreflangValue);
            }

            return sb.ToString();
        }

        /// <summary>
        /// 生成多语言链接标签
        /// </summary>
        public static string GenerateSiteMapLanguageLinks(string path)
        {
            StringBuilder sb = new StringBuilder();

            // 当前页面的URL
            string currentUrl = HostUrl + (CommonTranslate.StrDefaultLang + "/") + path.TrimStart('/');

            sb.AppendLine(string.Format("<xhtml:link rel=\"alternate\" hreflang=\"x-default\" href=\"{0}\" />", currentUrl));

            foreach (var lang in CommonTranslate.DicJsLanguage.Keys)
            {
                // 获取标准化的语言代码
                string hreflangValue = ConvertToStandardLanguageCode(lang);

                // 路径格式URL
                sb.AppendLine(string.Format("<xhtml:link rel=\"alternate\" hreflang=\"{3}\" href=\"{0}{1}/{2}\" />",
                    HostUrl, lang, path, hreflangValue));
            }

            return sb.ToString();
        }

        private static readonly Dictionary<string, string> LanguageCodeMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "zh-Hans", "zh" },
            { "zh-Hant", "zh-tw" },
            { "yue", "zh-yue" },
        };

        // 将服务ID转换为标准语言代码
        private static string ConvertToStandardLanguageCode(string serviceId)
        {
            if (LanguageCodeMap.TryGetValue(serviceId, out var standardCode))
                return standardCode;
            return serviceId.ToLower();
        }

        /// <summary>
        /// 从查询参数获取语言
        /// </summary>
        public static string GetLanguageFromQueryString(HttpRequest request)
        {
            var reqLang = request.GetValue("lang");
            if (string.IsNullOrEmpty(reqLang) || reqLang.Contains("="))
                return null;

            // 防止出现lang=fr-FR&lang='这种情况
            var langs = reqLang.Split(new string[] { ",", " ", ";", "'" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var item in langs)
            {
                var lang = FindBestLanguageMatch(item);

                if (!string.IsNullOrEmpty(lang))
                    return lang;
            }

            // 如果带有请求头，但是没有匹配到语言，则取第一个
            // 部分正常请求，由于DicJsLanguage不全，也会出现这种情况
            // 垃圾请求还是获取不到，比如lang=abcd&lang='
            if (langs.Length > 0)
            {
                return langs[0];
            }

            return null;
        }

        /// <summary>
        /// 从浏览器头获取语言
        /// </summary>
        public static string GetLanguageFromBrowserHeader(HttpRequest request)
        {
            var arrLang = request.Headers["Accept-Language"]?.Split(new string[] { ",", " ", ";", "'" }, StringSplitOptions.RemoveEmptyEntries);
            if (arrLang != null && arrLang.Length > 0)
            {
                foreach (var strAccLang in arrLang)
                {
                    if (strAccLang.StartsWith("q="))
                        continue;

                    var lang = FindBestLanguageMatch(strAccLang);

                    if (!string.IsNullOrEmpty(lang))
                        return lang;
                }
            }

            return null;
        }

        /// <summary>
        /// 尝试查找最精确的语言匹配
        /// </summary>
        public static string FindBestLanguageMatch(string requestedLanguage)
        {
            if (string.IsNullOrEmpty(requestedLanguage))
                return null;

            requestedLanguage = requestedLanguage.ToLowerInvariant();

            // 1. 精确匹配: 完全一致的语言代码 (例如 "zh-CN" 与 "zh-CN")
            string exactMatch = TryFindLanguageMatch(requestedLanguage, MatchType.Exact);
            if (exactMatch != null)
                return exactMatch;

            // 2. 主要语言匹配: 匹配主要语言部分 (例如 "zh-CN" 应匹配 "zh-Hans")
            string primaryLanguage = requestedLanguage.Split('-')[0];
            string primaryMatch = TryFindLanguageMatch(primaryLanguage, MatchType.Primary);
            if (primaryMatch != null)
                return primaryMatch;

            // 3. 语言家族匹配: 相似语言区域 (例如 "zh-HK" 可能匹配到 "zh-Hant")
            string familyMatch = TryFindLanguageMatch(requestedLanguage, MatchType.Family);
            if (familyMatch != null)
                return familyMatch;

            // 4. 回退匹配: 尝试通用的语言代码变体 (例如各种中文变体尝试匹配到任何中文版本)
            if (primaryLanguage != requestedLanguage)
            {
                string fallbackMatch = TryFindLanguageMatch(primaryLanguage, MatchType.Fallback);
                if (fallbackMatch != null)
                    return fallbackMatch;
            }

            return null;
        }

        private enum MatchType { Exact, Primary, Family, Fallback }

        private static string TryFindLanguageMatch(string langCode, MatchType matchType)
        {
            // 特殊情况处理器 - 定义语言之间的关系
            var languageFamilies = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
            {
                // 中文语言家族
                { "zh", new List<string> { "zh-Hans", "zh-Hant", "zh-CN", "zh-SG", "zh-TW", "zh-HK" } },
                // 其他语言家族可以类似定义
                { "en", new List<string> { "en-US", "en-GB", "en-CA", "en-AU" } },
                { "fr", new List<string> { "fr-FR", "fr-CA", "fr-BE", "fr-CH" } },
                { "es", new List<string> { "es-ES", "es-MX", "es-AR", "es-CO" } },
                { "pt", new List<string> { "pt-PT", "pt-BR" } }
            };

            // 定义特殊映射关系
            var specialMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { "zh-CN", "zh-Hans" },
                { "zh-SG", "zh-Hans" },
                { "zh-TW", "zh-Hant" },
                { "zh-HK", "zh-Hant" },
                { "zh-MO", "zh-Hant" }
            };

            switch (matchType)
            {
                case MatchType.Exact:
                    // 精确匹配
                    foreach (var entry in CommonTranslate.DicJsLanguage)
                    {
                        if (string.Equals(entry.Key, langCode, StringComparison.OrdinalIgnoreCase) ||
                            entry.Value.Any(v => string.Equals(v, langCode, StringComparison.OrdinalIgnoreCase)))
                            return entry.Key;
                    }
                    break;

                case MatchType.Primary:
                    // 主要语言匹配
                    foreach (var entry in CommonTranslate.DicJsLanguage)
                    {
                        if (entry.Key.StartsWith(langCode + "-", StringComparison.OrdinalIgnoreCase) ||
                            entry.Value.Any(v => v.StartsWith(langCode + "-", StringComparison.OrdinalIgnoreCase)))
                            return entry.Key;
                    }
                    break;

                case MatchType.Family:
                    // 语言家族匹配
                    if (languageFamilies.TryGetValue(langCode.Split('-')[0], out var familyMembers))
                    {
                        // 检查特殊映射
                        if (specialMappings.TryGetValue(langCode, out var mappedCode))
                        {
                            foreach (var entry in CommonTranslate.DicJsLanguage)
                            {
                                if (string.Equals(entry.Key, mappedCode, StringComparison.OrdinalIgnoreCase))
                                    return entry.Key;
                            }
                        }

                        // 在同一语言家族中查找支持的语言
                        foreach (var member in familyMembers)
                        {
                            foreach (var entry in CommonTranslate.DicJsLanguage)
                            {
                                if (string.Equals(entry.Key, member, StringComparison.OrdinalIgnoreCase))
                                    return entry.Key;
                            }
                        }
                    }
                    break;

                case MatchType.Fallback:
                    // 回退匹配 - 同为一种主要语言的任何变体
                    foreach (var entry in CommonTranslate.DicJsLanguage)
                    {
                        if (entry.Key.Split('-')[0].Equals(langCode, StringComparison.OrdinalIgnoreCase))
                            return entry.Key;
                    }
                    break;
            }

            return null;
        }
    }
}
