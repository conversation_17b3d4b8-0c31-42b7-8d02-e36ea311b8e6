﻿2025-07-31 00:15:31,225 [10] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 00:15:31
2025-07-31 00:40:15,635 [31] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 00:40:15
2025-07-31 00:53:43,834 [17] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 00:53:43
2025-07-31 01:01:35,348 [29] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 01:01:35
2025-07-31 09:19:44,167 [46] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 09:19:44
2025-07-31 09:36:10,981 [63] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 09:36:10
2025-07-31 10:55:31,006 [81] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 10:55:31
2025-07-31 11:27:33,655 [26] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 11:27:33
2025-07-31 11:40:43,240 [41] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Detail.aspx(48): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:40:43,327 [41] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:max-age=0
HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/Detail.aspx
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: max-age=0
Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/Detail.aspx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Detail.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Detail.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->58056|REQUEST_METHOD->GET|SCRIPT_NAME->/Detail.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Detail.aspx|HTTP_CACHE_CONTROL->max-age=0|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/Detail.aspx|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Detail.aspx
Header:Cache-Control->max-age=0|Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/Detail.aspx|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans

2025-07-31 11:40:43,329 [41] ERROR CommonLog 
 - IIS异常:/Detail.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Detail.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/Detail.aspx
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-07-31 11:41:01,059 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Detail.aspx(114): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:41:01,061 [43] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:max-age=0
HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/Detail.aspx
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: max-age=0
Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/Detail.aspx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Detail.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Detail.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->58056|REQUEST_METHOD->GET|SCRIPT_NAME->/Detail.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Detail.aspx|HTTP_CACHE_CONTROL->max-age=0|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/Detail.aspx|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Detail.aspx
Header:Cache-Control->max-age=0|Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/Detail.aspx|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans

2025-07-31 11:41:01,062 [43] ERROR CommonLog 
 - IIS异常:/Detail.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Detail.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/Detail.aspx
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-07-31 11:41:18,094 [78] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Detail.aspx(114): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:41:18,095 [78] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:max-age=0
HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/Detail.aspx
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: max-age=0
Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/Detail.aspx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Detail.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Detail.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->58056|REQUEST_METHOD->GET|SCRIPT_NAME->/Detail.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Detail.aspx|HTTP_CACHE_CONTROL->max-age=0|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/Detail.aspx|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Detail.aspx
Header:Cache-Control->max-age=0|Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/Detail.aspx|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans

2025-07-31 11:41:18,096 [78] ERROR CommonLog 
 - IIS异常:/Detail.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Detail.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/Detail.aspx
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-07-31 11:41:18,741 [27] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Detail.aspx(114): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.PostProcessFoundBuildResult(BuildResult result, Boolean keyFromVPP, VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetBuildResultFromCacheInternal(String cacheKey, Boolean keyFromVPP, VirtualPath virtualPath, Int64 hashCode, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultFromCacheInternal(VirtualPath virtualPath, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:41:18,744 [27] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:max-age=0
HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/Detail.aspx
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: max-age=0
Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/Detail.aspx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Detail.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Detail.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->58056|REQUEST_METHOD->GET|SCRIPT_NAME->/Detail.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Detail.aspx|HTTP_CACHE_CONTROL->max-age=0|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/Detail.aspx|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Detail.aspx
Header:Cache-Control->max-age=0|Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/Detail.aspx|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans

2025-07-31 11:41:18,745 [27] ERROR CommonLog 
 - IIS异常:/Detail.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Detail.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/Detail.aspx
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-07-31 11:42:34,839 [79] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。 ---> System.Web.HttpException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。
   在 System.Web.UI.FileLevelPageControlBuilder.AppendSubBuilder(ControlBuilder subBuilder)
   在 System.Web.UI.TemplateParser.MaybeTerminateControl(String tagName, Match match)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:42:35,579 [42] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。 ---> System.Web.HttpException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。
   在 System.Web.UI.FileLevelPageControlBuilder.AppendSubBuilder(ControlBuilder subBuilder)
   在 System.Web.UI.TemplateParser.MaybeTerminateControl(String tagName, Match match)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:42:39,406 [70] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。 ---> System.Web.HttpException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。
   在 System.Web.UI.FileLevelPageControlBuilder.AppendSubBuilder(ControlBuilder subBuilder)
   在 System.Web.UI.TemplateParser.MaybeTerminateControl(String tagName, Match match)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:43:03,076 [15] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Detail.aspx(114): error CS0103: 当前上下文中不存在名称“strDownUrl”
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 11:43:03,078 [15] ERROR CommonLog 
 - IP地址为空！
ServerVariables:ALL_HTTP->HTTP_CACHE_CONTROL:max-age=0
HTTP_CONNECTION:keep-alive
HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE:zh-CN,zh;q=0.9
HTTP_COOKIE:lang=zh-Hans
HTTP_HOST:localhost:19225
HTTP_REFERER:http://localhost:19225/zh-Hans/Detail.aspx
HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
HTTP_SEC_CH_UA:"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
HTTP_SEC_CH_UA_MOBILE:?0
HTTP_SEC_CH_UA_PLATFORM:"Windows"
HTTP_UPGRADE_INSECURE_REQUESTS:1
HTTP_SEC_FETCH_SITE:same-origin
HTTP_SEC_FETCH_MODE:navigate
HTTP_SEC_FETCH_USER:?1
HTTP_SEC_FETCH_DEST:document
|ALL_RAW->Cache-Control: max-age=0
Connection: keep-alive
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding: gzip, deflate, br, zstd
Accept-Language: zh-CN,zh;q=0.9
Cookie: lang=zh-Hans
Host: localhost:19225
Referer: http://localhost:19225/zh-Hans/Detail.aspx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
Upgrade-Insecure-Requests: 1
Sec-Fetch-Site: same-origin
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
|APPL_MD_PATH->/LM/W3SVC/3/ROOT|APPL_PHYSICAL_PATH->D:\Code\Code\CodeManager\CodeManager\|AUTH_TYPE->|AUTH_USER->|AUTH_PASSWORD->|LOGON_USER->|REMOTE_USER->|CERT_COOKIE->|CERT_FLAGS->|CERT_ISSUER->|CERT_KEYSIZE->|CERT_SECRETKEYSIZE->|CERT_SERIALNUMBER->|CERT_SERVER_ISSUER->|CERT_SERVER_SUBJECT->|CERT_SUBJECT->|CONTENT_LENGTH->0|CONTENT_TYPE->|GATEWAY_INTERFACE->CGI/1.1|HTTPS->off|HTTPS_KEYSIZE->|HTTPS_SECRETKEYSIZE->|HTTPS_SERVER_ISSUER->|HTTPS_SERVER_SUBJECT->|INSTANCE_ID->3|INSTANCE_META_PATH->/LM/W3SVC/3|LOCAL_ADDR->::1|PATH_INFO->/Detail.aspx|PATH_TRANSLATED->D:\Code\Code\CodeManager\CodeManager\Detail.aspx|QUERY_STRING->|REMOTE_ADDR->::1|REMOTE_HOST->::1|REMOTE_PORT->58056|REQUEST_METHOD->GET|SCRIPT_NAME->/Detail.aspx|SERVER_NAME->localhost|SERVER_PORT->19225|SERVER_PORT_SECURE->0|SERVER_PROTOCOL->HTTP/1.1|SERVER_SOFTWARE->Microsoft-IIS/10.0|URL->/Detail.aspx|HTTP_CACHE_CONTROL->max-age=0|HTTP_CONNECTION->keep-alive|HTTP_ACCEPT->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|HTTP_ACCEPT_ENCODING->gzip, deflate, br, zstd|HTTP_ACCEPT_LANGUAGE->zh-CN,zh;q=0.9|HTTP_COOKIE->lang=zh-Hans|HTTP_HOST->localhost:19225|HTTP_REFERER->http://localhost:19225/zh-Hans/Detail.aspx|HTTP_USER_AGENT->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|HTTP_SEC_CH_UA->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|HTTP_SEC_CH_UA_MOBILE->?0|HTTP_SEC_CH_UA_PLATFORM->"Windows"|HTTP_UPGRADE_INSECURE_REQUESTS->1|HTTP_SEC_FETCH_SITE->same-origin|HTTP_SEC_FETCH_MODE->navigate|HTTP_SEC_FETCH_USER->?1|HTTP_SEC_FETCH_DEST->document
GET:http://localhost:19225/Detail.aspx
Header:Cache-Control->max-age=0|Connection->keep-alive|Accept->text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7|Accept-Encoding->gzip, deflate, br, zstd|Accept-Language->zh-CN,zh;q=0.9|Cookie->lang=zh-Hans|Host->localhost:19225|Referer->http://localhost:19225/zh-Hans/Detail.aspx|User-Agent->Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36|sec-ch-ua->"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"|sec-ch-ua-mobile->?0|sec-ch-ua-platform->"Windows"|Upgrade-Insecure-Requests->1|Sec-Fetch-Site->same-origin|Sec-Fetch-Mode->navigate|Sec-Fetch-User->?1|Sec-Fetch-Dest->document
Cookies:lang->zh-Hans

2025-07-31 11:43:03,078 [15] ERROR CommonLog 
 - IIS异常:/Detail.aspx被拦截！原因：ServiceException
IP: 
URL: http://localhost:19225/Detail.aspx
Method: GET
Referer: http://localhost:19225/zh-Hans/Detail.aspx
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

2025-07-31 11:47:05,069 [55] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 11:47:05
2025-07-31 12:55:04,877 [27] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 12:55:04
2025-07-31 17:15:06,816 [38] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 17:15:06
2025-07-31 18:07:47,748 [44] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 18:07:47
2025-07-31 19:02:26,556 [45] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 19:02:26
2025-07-31 19:12:07,847 [15] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 19:12:07
2025-07-31 19:23:41,733 [51] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 19:23:41
2025-07-31 20:04:39,444 [22] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 20:04:39
2025-07-31 20:43:06,679 [85] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 20:43:06
2025-07-31 21:54:04,335 [58] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 21:54:04
2025-07-31 22:37:48,561 [84] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 22:37:48
2025-07-31 22:56:29,418 [19] ERROR Application_Error 
 -  URL:http://localhost:19225/Status.aspx
System.Web.HttpCompileException (0x80004005): d:\Code\Code\CodeManager\CodeManager\Status.aspx(477): error CS1061: “CommonLib.ServerStateInfo”不包含“Name”的定义，并且找不到可接受类型为“CommonLib.ServerStateInfo”的第一个参数的扩展方法“Name”(是否缺少 using 指令或程序集引用?)
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-31 23:17:24,988 [103] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 23:17:24
2025-07-31 23:49:37,792 [70] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-31 23:49:37
2025-07-31 23:58:49,872 [92] ERROR Application_Error 
 -  URL:http://localhost:19225/Status.aspx
System.IO.IOException: 文件“D:\Code\Code\CodeManager\CodeManager\Status.aspx”正由另一进程使用，因此该进程无法访问此文件。
   在 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   在 System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   在 System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share)
   在 System.Web.Hosting.MapPathBasedVirtualFile.Open()
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
