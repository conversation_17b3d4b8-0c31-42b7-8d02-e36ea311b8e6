﻿<%@ Page Title="在线字数统计工具 | 文本排版字符计算器 | 多语言支持" Language="C#" MasterPageFile="~/tool/Tool.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link rel="stylesheet" href="static/css/index-b078aaba.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <script type="text/javascript" src="static/js/vue.js"></script>
    <script src="static/js/require.js"></script>
    <meta name="keywords" content="字数统计,汉字统计,自动文本排版,字符计算器,文本分析工具,字数计算器,文章统计分析,单词统计,语言分析,在线文档分析" />
    <meta name="description" content="免费在线字数统计工具，快速统计汉字、英文单词、数字、字符数量和段落信息。一键自动格式化和排版功能，为Word和其他文本编辑提供实时字数分析。">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="panel panel-default" style="margin-bottom: 0px;">
        <div class="panel-heading">
            <h3 class="panel-title">在线字数统计排版工具</h3>
        </div>
    </div>
    <div class="content">
        <div class="panel panel-default panel-beforef px-4">
            <div class="panel-body px-0">
                <div class="row">
                    <div class="col-md-9 mb-2">
                        <div class="mb-3"></div>
                        <div class="col-md-12">
                            <textarea class="form-control" rows="20" id="contenttext" placeholder="请在这里输入需要去重的文本,一行一个">
/*   欢迎使用在线字数统计排版工具        */
/*   适用于word一键排版和统计字数        */
        </textarea>
                        </div>
                    </div>
                    <div class="col-md-3 mt-3">
                        <table class="table table-bordered table-hover">
                            <tbody>
                                <tr>
                                    <td style="width: 100px">总字数</td>
                                    <td>
                                        <span id="id_total">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td>总行数</td>
                                    <td>
                                        <span id="id_part">0</span>(行)</td>
                                </tr>
                                <tr>
                                    <td class="">中文字数</td>
                                    <td class="">
                                        <span id="id_c_total">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">中文标点</td>
                                    <td class="">
                                        <span id="id_c_punctuation">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">字母个数</td>
                                    <td class="">
                                        <span id="id_e_total">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">单词个数</td>
                                    <td class="">
                                        <span id="id_e_words">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">英文标点</td>
                                    <td class="">
                                        <span id="id_e_punctuation">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">数字个数</td>
                                    <td class="">
                                        <span id="id_n_total">0</span>(个)</td>
                                </tr>
                                <tr>
                                    <td class="">数字组</td>
                                    <td class="">
                                        <span id="id_n_words">0</span>(个)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row" style="margin-top: 20px">
                    <div class="col-md-12 text-center">
                        <button class="btn btn-info" onclick="count()"><i class="fa fa-refresh"></i>统计字数</button>
                        <button type="button" class="btn btn-info" onclick="format()">自动排版</button>
                        <button class="btn btn-info" onclick="noSpace()">去行尾空格</button>
                        <button class="btn btn-info" onclick="noEmptyLines()">删除空行</button>
                        <button type="submit" class="btn btn-danger" id="copy" data-clipboard-target="#contenttext" style="margin: 5px;">复制文本</button>
                        <button type="submit" class="btn btn-danger" id="btn2" style="margin: 5px;">导出文本</button>
                        <button type="submit" class="btn btn-danger" id="btn5" style="margin: 5px;">清空文本</button>
                    </div>
                </div>
                <a onfocus="this.blur();" download="转换完成.txt" id="createInvote" class="ipt-todo hide">导出结果</a>
            </div>
        </div>
    </div>
    <script src="static/js/jquery-3.3.1.min.js"></script>
    <script src="static/js/clipboard.min.js"></script>
    <script type="text/javascript">
        function Trim(str) {
            return str.replace(/(^\s*)|(\s*$)/g, "");
        }

        //判断是否为空函数
        function isEmpty(obj) {
            if (typeof obj == "undefined" || obj == null || obj == "") {
                return true;
            } else {
                return false;
            }
        }


        $("#btn5").bind("click",
            function () {
                document.getElementById('contenttext').value = '';
                return false
            });


        $("#btn2").bind("click",
            function () {
                var daochu = document.getElementById('contenttext').value;
                if (isEmpty(daochu)) {
                    alert("没有内容,无法导出");
                    return false
                }
                daochu = daochu.replace(/\n/g, "\r\n");
                var isIE = (navigator.userAgent.indexOf('MSIE') >= 0);
                if (isIE) {
                    var winSave = window.open();
                    winSave.document.open("text", "utf-8");
                    winSave.document.write(daochu);
                    winSave.document.execCommand("SaveAs", true, "export.txt");
                    winSave.close();
                } else {
                    var mimeType = 'text/plain';
                    $('#createInvote').attr('href', 'data:' + mimeType + ';charset=utf-8,' + encodeURIComponent(daochu));
                    document.getElementById('createInvote').click();
                }
                return false
            });

        //之前的复制插件过时了,需要flash,更换成这个
        var clipboard = new ClipboardJS('#copy');
        clipboard.on('success', function (e) {
            if (e.text != "") {
                alert("复制成功！");
            } else {
                alert("复制内容为空！");
            }
        });

        clipboard.on('error', function (e) {
            alert("复制失败！");
        });

        var contentObj = $('#contenttext');

        contentObj.bind('input propertychange change',
            function () {
                count();
            });

        // 一键统计
        function count() {
            var content = contentObj.val().replace(/\r\n/g, "\n"); // 完整的内容
            var str = content.replace(/\n/g, ''); // 纯粹字符
            var Chinese_characters = content.match(/[\u4e00-\u9fa5]/g) || []; // 中文字符
            var phrase = content.match(/\b\w+\b/g) || []; // 数字+字母
            var group_number = content.match(/\b\d+\b/g) || []; // 数字
            var letter = str.match(/[A-Za-z]/g) || []; // 英文字母
            var number = str.match(/[0-9]/g) || []; // 数字
            // 英文标点
            var half_punctuation = str.match(
                /[|\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g) || [];

            // 中文字符总数
            var Chinese_all = 0;
            for (var i = 0; i < str.length; i++) {
                var c = str.charAt(i);
                if (c.match(/[^\x00-\xff]/)) Chinese_all++;
            }

            // 计算段落数
            var part = 0;
            var s_ma = content.split("\n");
            for (var i = 0; i < s_ma.length; i++) {
                if (s_ma[i].length > 0) part++;
            }

            // 字符总数
            $('#id_total').html(str.length);

            // 汉字数  
            $('#id_c_total').html(Chinese_characters.length);

            // 中文标点
            $('#id_c_punctuation').html(Chinese_all - Chinese_characters.length);

            // 英文字数
            $('#id_e_total').html(letter.length);

            // 英文标点
            $('#id_e_punctuation').html(half_punctuation.length);

            // 英文单词
            $('#id_e_words').html(phrase.length - group_number.length);

            // 数字单词
            $('#id_n_words').html(group_number.length);

            // 数字字符
            $('#id_n_total').html(number.length);

            // 行数
            $('#id_part').html(part);
        }

        // 清除行尾空格
        function noSpace() {
            var str = contentObj.val().replace(/\r\n/g, "\n").replace(/\n/g, "[mk~换行]");
            var m = str.split("[mk~换行]");
            var ma = [];
            var len = m.length;
            for (var i = 0; i < len; i++) {
                ma.push(m[i].replace(/(\s*$)/g, ""));
            }
            contentObj.val(ma.join("\r\n"));
            count(); // 重新统计字数
        }

        // 一键排版
        function format() {
            var str = contentObj.val().replace(/[\r\n]+/g, "[mk~换行]").replace(/[\n\n]+/g, "[mk~换行]").replace(/[\n]+/g,
                "[mk~换行]");
            var m = str.split("[mk~换行]");
            var ma = [];
            var len = m.length;
            for (var i = 0; i < len; i++) {
                ma.push('　　' + m[i].replace(/(^\s*)|(\s*$)/g, "")); // 缩进，去行尾空格
            }
            contentObj.val(ma.join("\r\n\r\n")); // 段落换行
            count(); // 重新统计字数
        }

        // 删除空行
        function noEmptyLines() {
            var str = contentObj.val().replace(/[\r\n]+/g, "[mk~换行]").replace(/[\n\n]+/g, "[mk~换行]").replace(/[\n]+/g,
                "[mk~换行]");
            var m = str.split("[mk~换行]");
            contentObj.val(m.join("\r\n")); // 段落换行
            count(); // 重新统计字数
        }

        // 半角转全角
        function format4() {
            var body = document.getElementById("thebody").value;
            for (var ii = 0; 100 > ii; ii++) {
                body = body.replace("　", ""); //去除全角空格
                body = body.replace(",", "，"); //替换英文标点 
                body = body.replace("......", "……");
                body = body.replace("。。。。。。", "……");
                body = body.replace("?", "？");
                body = body.replace(".", "。");
                body = body.replace(";", "；");
                body = body.replace(":", "：");
                body = body.replace("!", "！");
                body = body.replace("(", "（");
                body = body.replace(")", "）");
                body = body.replace("----", "——");
                body = body.replace("--", "——");
                body = body.replace("[", "［");
                body = body.replace("]", "］");
            }
            document.getElementById("thebody").value = body;
        }
    </script>
</asp:Content>
