﻿using System;
using System.IO;
using System.Data;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;

namespace ToolCommon
{
    public class DataFormatter
    {
        /* WebService 压缩 方法 整合
       * 方法一：webmethod直接返回byte[]
       * 方法二ICSharpCode 算法 压缩
       * 方法三DataSetSurrogate返回 DataSetSurrogate 对象序列化并压缩后的字节数组
       * 整合：肖百刚 
       * 代码来自互联网
       *  For details, see the WebService web site:  http://www.cnblogs.com/xiaobaigang/
      /*--------------------------------------------------------------------------*/

        private DataFormatter() { }
        /**/
        /// <summary>
        /// Serialize the Data of dataSet to binary format
        /// </summary>
        /// <param name="dsOriginal"></param>
        /// <returns></returns>
        static public byte[] GetBinaryFormatData(DataSet dsOriginal)
        {
            byte[] binaryDataResult = null;
            MemoryStream memStream = new MemoryStream();
            IFormatter brFormatter = new BinaryFormatter();
            dsOriginal.RemotingFormat = SerializationFormat.Binary;

            brFormatter.Serialize(memStream, dsOriginal);
            binaryDataResult = memStream.ToArray();
            memStream.Close();
            memStream.Dispose();
            return binaryDataResult;
        }
        /**/
        /// <summary>
        /// Retrieve dataSet from data of binary format
        /// </summary>
        /// <param name="binaryData"></param>
        /// <returns></returns>
        static public DataSet RetrieveDataSet(byte[] binaryData)
        {
            DataSet dataSetResult = null;
            MemoryStream memStream = new MemoryStream(binaryData);
            IFormatter brFormatter = new BinaryFormatter();

            object obj = brFormatter.Deserialize(memStream);
            dataSetResult = (DataSet)obj;
            return dataSetResult;
        }
    }
}

