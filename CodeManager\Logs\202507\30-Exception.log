﻿2025-07-30 22:19:48,739 [45] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-30 22:19:48
2025-07-30 22:43:16,035 [51] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:43:28,917 [33] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:44:49,472 [22] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:44:50,716 [63] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:45:31,980 [15] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:45:32,677 [10] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:45:33,034 [8] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:46:39,390 [15] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:46:40,044 [61] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:46:40,375 [31] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:46:40,582 [60] ERROR Application_Error 
 -  URL:http://localhost:19225/product.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:50:39,546 [35] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:51:00,499 [43] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:51:01,170 [38] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:51:01,438 [36] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 22:51:01,649 [33] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 23:05:48,368 [63] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 23:08:09,946 [33] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 23:08:10,974 [13] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): 查找 </asp:Content> 标记时遇到意外的文件结尾。
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateControlParser.HandlePostParse()
   在 System.Web.UI.PageParser.HandlePostParse()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 23:18:58,523 [44] ERROR Application_Error 
 -  URL:http://localhost:19225/Detail.aspx
System.Web.HttpParseException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。 ---> System.Web.HttpParseException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。 ---> System.Web.HttpException (0x80004005): Content 控件只能直接位于包含 Content 控件的内容页中。
   在 System.Web.UI.FileLevelPageControlBuilder.AppendLiteralString(String text)
   在 System.Web.UI.TemplateParser.ProcessLiteral()
   在 System.Web.UI.TemplateParser.ProcessException(Exception ex)
   在 System.Web.UI.TemplateParser.ProcessLiteral()
   在 System.Web.UI.TemplateParser.ProcessCodeBlock(Match match, CodeBlockType blockType, String text)
   在 System.Web.UI.TemplateParser.ParseStringInternal(String text, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseString(String text, VirtualPath virtualPath, Encoding fileEncoding)
   在 System.Web.UI.TemplateParser.ParseFile(String physicalPath, VirtualPath virtualPath)
   在 System.Web.UI.TemplateParser.ParseInternal()
   在 System.Web.UI.TemplateParser.Parse()
   在 System.Web.Compilation.BaseTemplateBuildProvider.get_CodeCompilerType()
   在 System.Web.Compilation.BuildProvider.GetCompilerTypeFromBuildProvider(BuildProvider buildProvider)
   在 System.Web.Compilation.BuildProvidersCompiler.ProcessBuildProviders()
   在 System.Web.Compilation.BuildProvidersCompiler.PerformBuild()
   在 System.Web.Compilation.BuildManager.CompileWebFile(VirtualPath virtualPath)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultInternal(VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVPathBuildResultWithNoAssert(HttpContext context, VirtualPath virtualPath, Boolean noBuild, Boolean allowCrossApp, Boolean allowBuildInPrecompile, Boolean throwIfNotFound, Boolean ensureIsUpToDate)
   在 System.Web.Compilation.BuildManager.GetVirtualPathObjectFactory(VirtualPath virtualPath, HttpContext context, Boolean allowCrossApp, Boolean throwIfNotFound)
   在 System.Web.Compilation.BuildManager.CreateInstanceFromVirtualPath(VirtualPath virtualPath, Type requiredBaseType, HttpContext context, Boolean allowCrossApp)
   在 System.Web.UI.PageHandlerFactory.GetHandlerHelper(HttpContext context, String requestType, VirtualPath virtualPath, String physicalPath)
   在 System.Web.HttpApplication.MaterializeHandlerExecutionStep.System.Web.HttpApplication.IExecutionStep.Execute()
   在 System.Web.HttpApplication.ExecuteStepImpl(IExecutionStep step)
   在 System.Web.HttpApplication.ExecuteStep(IExecutionStep step, Boolean& completedSynchronously)
2025-07-30 23:44:34,534 [49] ERROR Application_End 
 - 【POD挂了】 时间:2025-07-30 23:44:34
