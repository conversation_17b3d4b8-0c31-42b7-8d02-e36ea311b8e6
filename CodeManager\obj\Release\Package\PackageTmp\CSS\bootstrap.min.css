*, ::after, ::before {
	box-sizing: border-box
}

html {
	font-family: sans-serif;
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	-ms-overflow-style: scrollbar;
	-webkit-tap-highlight-color: transparent
}

@-ms-viewport {
	width: device-width
}

body {
	margin: 0;
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	text-align: left;
	background-color: #fff
}

[tabindex="-1"]:focus {
	outline: 0 !important
}

h4 {
	margin-top: 0;
	margin-bottom: .5rem
}

p {
	margin-top: 0;
	margin-bottom: 1rem
}

b {
	font-weight: bolder
}

a {
	color: #007bff;
	text-decoration: none;
	background-color: transparent;
	-webkit-text-decoration-skip: objects
}

	a:hover {
		color: #0056b3;
		text-decoration: underline
	}

	a:not([href]):not([tabindex]) {
		color: inherit;
		text-decoration: none
	}

		a:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {
			color: inherit;
			text-decoration: none
		}

		a:not([href]):not([tabindex]):focus {
			outline: 0
		}

table {
	border-collapse: collapse
}

th {
	text-align: inherit
}

label {
	display: inline-block;
	margin-bottom: .5rem
}

button {
	border-radius: 0
}

	button:focus {
		outline: 1px dotted;
		outline: 5px auto -webkit-focus-ring-color
	}

button, input, select {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	line-height: inherit
}

button, input {
	overflow: visible
}

button, select {
	text-transform: none
}

[type=submit], button, html [type=button] {
	-webkit-appearance: button
}

	[type=button]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {
		padding: 0;
		border-style: none
	}

input[type=checkbox] {
	box-sizing: border-box;
	padding: 0
}

::-webkit-file-upload-button {
	font: inherit;
	-webkit-appearance: button
}

h4 {
	margin-bottom: .5rem;
	font-family: inherit;
	font-weight: 500;
	line-height: 1.2;
	color: inherit
}

h4 {
	font-size: 1.5rem
}

.table {
	width: 100%;
	margin-bottom: 1rem;
	background-color: transparent
}

	.table td, .table th {
		padding: .75rem;
		vertical-align: top;
		border-top: 1px solid #dee2e6
	}

	.table thead th {
		vertical-align: bottom;
		border-bottom: 2px solid #dee2e6
	}

.table-striped tbody tr:nth-of-type(odd) {
	background-color: rgba(0,0,0,.05)
}

.form-control {
	display: block;
	width: 100%;
	height: calc(2.25rem + 2px);
	padding: .375rem .75rem;
	font-size: 1rem;
	line-height: 1.5;
	color: #495057;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #ced4da;
	border-radius: .25rem;
	transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

@media screen and (prefers-reduced-motion:reduce) {
	.form-control {
		transition: none
	}
}

.form-control::-ms-expand {
	background-color: transparent;
	border: 0
}

.form-control:focus {
	color: #495057;
	background-color: #fff;
	border-color: #80bdff;
	outline: 0;
	box-shadow: 0 0 0 .2rem rgba(0,123,255,.25)
}

.form-control::-webkit-input-placeholder {
	color: #6c757d;
	opacity: 1
}

.form-control::-moz-placeholder {
	color: #6c757d;
	opacity: 1
}

.form-control:-ms-input-placeholder {
	color: #6c757d;
	opacity: 1
}

.form-control::-ms-input-placeholder {
	color: #6c757d;
	opacity: 1
}

.form-control:disabled {
	background-color: #e9ecef;
	opacity: 1
}

.form-group {
	margin-bottom: 1rem
}

.btn {
	display: inline-block;
	font-weight: 400;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	border: 1px solid transparent;
	padding: .375rem .75rem;
	font-size: 1rem;
	line-height: 1.5;
	border-radius: .25rem;
	transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

@media screen and (prefers-reduced-motion:reduce) {
	.btn {
		transition: none
	}
}

.btn:focus, .btn:hover {
	text-decoration: none
}

.btn:focus {
	outline: 0;
	box-shadow: 0 0 0 .2rem rgba(0,123,255,.25)
}

.btn:disabled {
	opacity: .65
}

.btn:not(:disabled):not(.disabled) {
	cursor: pointer
}

.btn-primary {
	color: #fff;
	background-color: #007bff;
	border-color: #007bff
}

	.btn-primary:hover {
		color: #fff;
		background-color: #0069d9;
		border-color: #0062cc
	}

	.btn-primary:focus {
		box-shadow: 0 0 0 .2rem rgba(0,123,255,.5)
	}

	.btn-primary:disabled {
		color: #fff;
		background-color: #007bff;
		border-color: #007bff
	}

	.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active {
		color: #fff;
		background-color: #0062cc;
		border-color: #005cbf
	}

		.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus {
			box-shadow: 0 0 0 .2rem rgba(0,123,255,.5)
		}

.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #545b62;
	border-color: #4e555b
}

	.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(108,117,125,.5)
	}

.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #1e7e34;
	border-color: #1c7430
}

	.btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(40,167,69,.5)
	}

.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #117a8b;
	border-color: #10707f
}

	.btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(23,162,184,.5)
	}

.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active {
	color: #212529;
	background-color: #d39e00;
	border-color: #c69500
}

	.btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(255,193,7,.5)
	}

.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #bd2130;
	border-color: #b21f2d
}

	.btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(220,53,69,.5)
	}

.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active {
	color: #212529;
	background-color: #dae0e5;
	border-color: #d3d9df
}

	.btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(248,249,250,.5)
	}

.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #1d2124;
	border-color: #171a1d
}

	.btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(52,58,64,.5)
	}

.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #007bff;
	border-color: #007bff
}

	.btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(0,123,255,.5)
	}

.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #6c757d;
	border-color: #6c757d
}

	.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(108,117,125,.5)
	}

.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #28a745;
	border-color: #28a745
}

	.btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(40,167,69,.5)
	}

.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #17a2b8;
	border-color: #17a2b8
}

	.btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(23,162,184,.5)
	}

.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active {
	color: #212529;
	background-color: #ffc107;
	border-color: #ffc107
}

	.btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(255,193,7,.5)
	}

.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #dc3545;
	border-color: #dc3545
}

	.btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(220,53,69,.5)
	}

.btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active {
	color: #212529;
	background-color: #f8f9fa;
	border-color: #f8f9fa
}

	.btn-outline-light:not(:disabled):not(.disabled).active:focus, .btn-outline-light:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(248,249,250,.5)
	}

.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active {
	color: #fff;
	background-color: #343a40;
	border-color: #343a40
}

	.btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus {
		box-shadow: 0 0 0 .2rem rgba(52,58,64,.5)
	}

.btn-lg {
	padding: .5rem 1rem;
	font-size: 1.25rem;
	line-height: 1.5;
	border-radius: .3rem
}

.btn-block {
	display: block;
	width: 100%
}

.navbar-toggler:not(:disabled):not(.disabled) {
	cursor: pointer
}

.card-body {
	-ms-flex: 1 1 auto;
	flex: 1 1 auto;
}

.page-link:not(:disabled):not(.disabled) {
	cursor: pointer
}

.alert {
	position: relative;
	padding: .75rem 1.25rem;
	margin-bottom: 1rem;
	border: 1px solid transparent;
	border-radius: .25rem
}

.close:not(:disabled):not(.disabled) {
	cursor: pointer
}

	.close:not(:disabled):not(.disabled):focus, .close:not(:disabled):not(.disabled):hover {
		color: #000;
		text-decoration: none;
		opacity: .75
	}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
}

@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
}

@supports ((position:-webkit-sticky) or (position:sticky)) {
}

.text-center {
	text-align: center !important
}

.text-uppercase {
	text-transform: uppercase !important
}

.btn {
	display: inline-block;
	padding: 6px 12px;
	margin-bottom: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px
}

	.btn:active:focus, .btn:focus {
		outline: 5px auto -webkit-focus-ring-color;
		outline-offset: -2px
	}

	.btn:focus, .btn:hover {
		color: #333;
		text-decoration: none
	}

	.btn:active {
		background-image: none;
		outline: 0;
		-webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
		box-shadow: inset 0 3px 5px rgba(0,0,0,.125)
	}

.btn-default {
	color: #333;
	background-color: #fff;
	border-color: #ccc
}

	.btn-default:focus {
		color: #333;
		background-color: #e6e6e6;
		border-color: #8c8c8c
	}

	.btn-default:hover {
		color: #333;
		background-color: #e6e6e6;
		border-color: #adadad
	}

	.btn-default:active {
		color: #333;
		background-color: #e6e6e6;
		border-color: #adadad
	}

		.btn-default:active:focus, .btn-default:active:hover {
			color: #333;
			background-color: #d4d4d4;
			border-color: #8c8c8c
		}

	.btn-default:active {
		background-image: none
	}

.btn-primary {
	color: #fff;
	background-color: #337ab7;
	border-color: #2e6da4
}

	.btn-primary:focus {
		color: #fff;
		background-color: #286090;
		border-color: #122b40
	}

	.btn-primary:hover {
		color: #fff;
		background-color: #286090;
		border-color: #204d74
	}

	.btn-primary:active {
		color: #fff;
		background-color: #286090;
		border-color: #204d74
	}

		.btn-primary:active:focus, .btn-primary:active:hover {
			color: #fff;
			background-color: #204d74;
			border-color: #122b40
		}

	.btn-primary:active {
		background-image: none
	}

.btn-lg {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 1.3333333;
	border-radius: 6px
}

.btn-block {
	display: block;
	width: 100%
}

@media print {
	*, ::after, ::before {
		text-shadow: none !important;
		box-shadow: none !important
	}

	a:not(.btn) {
		text-decoration: underline
	}

	thead {
		display: table-header-group
	}

	tr {
		page-break-inside: avoid
	}

	p {
		orphans: 3;
		widows: 3
	}

	@page {
		size: a3
	}

	body {
		min-width: 992px !important
	}

	.table {
		border-collapse: collapse !important
	}

		.table td, .table th {
			background-color: #fff !important
		}
}
