﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.IO;

namespace GoogleAPI
{
    public class GoogleRecTest
    {

        public static string GetRecImg()
        {
            string result = "";
            string base64 = "";
            for (int j = 0; j < 10; j++)
            {
                for (int i = 11; i < 15; i++)
                {
                    var byt = File.ReadAllBytes(@"D:\助手\Image\0108\Old\" + i + ".jpg");//10584306645.jpg");
                    if (byt != null && byt.Length > 0)
                        base64 = Convert.ToBase64String(byt);

                    result += GetContext(base64);
                }

            }
            return result;
        }

        static string strSpilt = "\"description\": \"";

        public static string GetContext(string strBase64)
        {
            string result = "";

            var collect = new System.Collections.Specialized.NameValueCollection();

            collect.Add(":authority", "content-vision.googleapis.com");
            collect.Add(":method", "POST");
            collect.Add(":path", "/v1/images:annotate?key=AIzaSyD-a9IF8KKYgoC3cpgS-Al7hLQDbugrDcw&alt=json");
            collect.Add(":scheme", "https");
            collect.Add("x-client-data", "CJa2yQEIprbJAQjBtskBCImSygEI/ZXKAQ==");
            collect.Add("x-clientdetails", "appVersion=5.0%20(Windows%20NT%206.1%3B%20WOW64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F49.0.2623.87%20Safari%2F537.36&platform=Win32&userAgent=Mozilla%2F5.0%20(Windows%20NT%206.1%3B%20WOW64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F49.0.2623.87%20Safari%2F537.36");
            collect.Add("x-goog-encode-response-if-executable", "base64");
            collect.Add("x-javascript-user-agent", "google-api-javascript-client/1.1.0-beta");
            collect.Add("x-origin", "https://developers.google.com");
            collect.Add("x-referer", "https://developers.google.com");
            //collect.Add("Origin", "https://developers.google.com");

            string strCookie = "";
            string strPost = "{\"requests\":[{\"image\":{\"content\":\"" + strBase64.Replace("%2B", "+")
                            + "\"},\"features\":[{\"type\": \"TEXT_DETECTION\",\"maxResults\": 1}]}]}";
            string strTmp = WebClientExt.GetHtml("https://content-vision.googleapis.com/v1/images:annotate?key=AIzaSyD-a9IF8KKYgoC3cpgS-Al7hLQDbugrDcw&alt=json"
                , ref strCookie, "", strPost, "https://content-vision.googleapis.com/static/proxy.html?jsh=m%3B%2F_%2Fscs%2Fapps-static%2F_%2Fjs%2Fk%3Doz.gapi.zh_CN.91UXq1B85pQ.O%2Fm%3D__features__%2Fam%3DEQ%2Frt%3Dj%2Fd%3D1%2Frs%3DAGLTcCPL_91ipZ085iMmZZMHh8wOSlM57A"
                , CommonHelper.NMaxTimeOut, false, collect);

            if (!string.IsNullOrEmpty(strTmp))
            {
                if (strTmp.Contains(strSpilt))
                {
                    result = strTmp.Substring(strTmp.IndexOf(strSpilt) + strSpilt.Length);
                    result = result.Substring(0, result.IndexOf("\""));
                    if (result.IndexOf("\\n") > 0)
                    {
                        if (result.IndexOf("\\n") != result.LastIndexOf("\\n"))
                        {
                            var newStr = result.Substring(0, result.IndexOf("\\n"));
                            //BaiDuCode._Log.Info("Old:" + result + Environment.NewLine + "New:" + newStr);
                            if (!string.IsNullOrEmpty(newStr))
                                result = newStr;
                        }
                    }
                }
            }
            return result.Replace("\\n", "").Trim();
        }
    }
}