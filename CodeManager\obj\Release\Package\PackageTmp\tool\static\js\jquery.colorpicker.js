!function(e){var r=new Array("00","33","66","99","CC","FF"),t=new Array("FF0000","00FF00","0000FF","FFFF00","00FFFF","FF00FF");e.fn.colorpicker=function(o){var c=jQuery.extend({},jQuery.fn.colorpicker.defaults,o);return function(){e("body").append('<div id="colorpanel" style="position: absolute; display: none;"></div>');var o="",c="";for(i=0;i<2;i++)for(j=0;j<6;j++)for(o+="<tr height=12>",o+='<td width=11 rel="#000000" style="background-color:#000000">',c=0==i?r[j]+r[j]+r[j]:t[j],o=o+'<td width=11 rel="#'+c+'" style="background-color:#'+c+'">',o+='<td width=11 rel="#000000" style="background-color:#000000">',k=0;k<3;k++)for(l=0;l<6;l++)c=r[k+3*i]+r[l]+r[j],o=o+'<td width=11 rel="#'+c+'" style="background-color:#'+c+'">';o='<table width=232 border="0" cellspacing="0" cellpadding="0" style="border:1px solid #000;"><tr height=30><td colspan=21 bgcolor=#cccccc><input type="text" id="DisColor" size="6" disabled style="border:solid 1px #000000;background-color:#ffff00"><input type="text" id="HexColor" size="7" style="border:inset 1px;font-family:Arial;padding:2px 16px" value="#000000"><a href="javascript:void(0);" id="_cclose">关闭</a> | <a href="javascript:void(0);" id="_creset">清除</a></td></table><table id="CT" border="1" cellspacing="0" cellpadding="0" style="border-collapse: collapse" bordercolor="000000"  style="cursor:pointer;">'+o+"</table>",e("#colorpanel").html(o),e("#_cclose").on("click",function(){return e("#colorpanel").hide(),!1}).css({"font-size":"12px","padding-left":"20px"})}(),this.each(function(){var r=e(this);r.bind(c.event,function(){var t=e(this).offset().top,o=e(this).height(),l=e(this).offset().left;e("#colorpanel").css({top:t+o+5,left:l,width:260}).show();var a=c.target?e(c.target):r;null==a.data("color")&&a.data("color",a.css("color")),null==a.data("value")&&a.data("value",a.val()),e("#_creset").bind("click",function(){a.css("color",a.data("color")).val(a.data("value")),e("#colorpanel").hide(),c.reset(r)}),e("#CT tr td").unbind("click").mouseover(function(){var r=e(this).css("background-color");e("#DisColor").css("background",r),e("#HexColor").val(e(this).attr("rel"))}).click(function(){var t=e(this).attr("rel");t=c.ishex?t:function(e){var r;e&&e.constructor==Array&&3==e.length&&(e=e);(r=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(e))&&(e=[parseInt(r[1]),parseInt(r[2]),parseInt(r[3])]);(r=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(e))&&(e=[2.55*parseFloat(r[1]),2.55*parseFloat(r[2]),2.55*parseFloat(r[3])]);(r=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(e))&&(e=[parseInt(r[1],16),parseInt(r[2],16),parseInt(r[3],16)]);(r=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(e))&&(e=[parseInt(r[1]+r[1],16),parseInt(r[2]+r[2],16),parseInt(r[3]+r[3],16)]);return"rgb("+e[0]+","+e[1]+","+e[2]+")"}(t),c.fillcolor&&a.val(t),a.css("color",t),e("#colorpanel").hide(),e("#_creset").unbind("click"),c.success(r,t)})})})},jQuery.fn.colorpicker.defaults={ishex:!0,fillcolor:!1,target:null,event:"click",success:function(){},reset:function(){}}}(jQuery);