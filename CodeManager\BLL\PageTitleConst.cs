﻿using System.Web.UI;

public static class PageTitleConst
{

    public const string Default_Ext = " - OCR文字识别助手(OCR助手),免费文字识别(OCR)工具,致力于提升您的工作效率！";
    public const string Default_Ext_Short = " - OCR文字识别助手(OCR助手)";

    public const string Default = "OCR文字识别助手(OCR助手)官网";
    //private static List<string> lstShortTitle = new List<string>() { "server.aspx", "desc.aspx" };

    public static void SetExtTitle(this Page Page)
    {
        if (string.IsNullOrEmpty(Page.Header.Title))
        {
            //LogHelper.Log.Error("Url:" + Page.Request.Url.PathAndQuery);
            Page.Header.Title = Default;
        }
        //Page.Header.Title += lstShortTitle.Any(p => Page.Request.Url.AbsolutePath.ToLower().Contains(p)) ? Default_Ext_Short : Default_Ext;
        Page.Header.Title += Default_Ext;
    }
}