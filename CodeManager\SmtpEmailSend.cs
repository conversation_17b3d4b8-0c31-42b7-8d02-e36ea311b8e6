﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Text;

namespace Account.Web
{
    public class SmtpEmailSend
    {
        private static SmtpClient SmtpClient = null;
        private static MailAddress MailAddress_from = null;
        private static MailAddress MailAddress_to = null;
        private static MailMessage MailMessage_Mai = new MailMessage();
        private static string nickName = string.Empty;
        public static bool SendEmail(string sendMailServer, string sendMailAddress, string sendMailPassword, List<string> mailAddressTo, string title, string content, out string msg)
        {
            return SmtpEmailSend._SendEmail(mailAddressTo, title, content, sendMailServer, 25, sendMailAddress, sendMailPassword, SmtpEmailSend.nickName, null, out msg);
        }
        public static bool _SendEmail(List<string> strMailAddressTo, string title, string content, string smtpAddress, int smtpPort, string sendEmailFrom, string sendEmailFromPwd, string showNickName, string filePath, out string msg)
        {
            bool flag = false;
            msg = string.Empty;
            try
            {
                SmtpEmailSend.setSmtpClient(smtpAddress, smtpPort);
            }
            catch (Exception)
            {
                msg = "请确定SMTP服务名是否正确！";
                bool result = flag;
                return result;
            }
            try
            {
                SmtpEmailSend.setAddressform(sendEmailFrom, sendEmailFromPwd, showNickName);
            }
            catch (Exception)
            {
                msg = "请确定发件邮箱地址和密码的正确性！";
                bool result = flag;
                return result;
            }
            SmtpEmailSend.MailMessage_Mai.To.Clear();
            foreach (string current in strMailAddressTo)
            {
                SmtpEmailSend.MailAddress_to = new MailAddress(current);
                SmtpEmailSend.MailMessage_Mai.To.Add(SmtpEmailSend.MailAddress_to);
            }
            SmtpEmailSend.MailMessage_Mai.From = SmtpEmailSend.MailAddress_from;
            SmtpEmailSend.MailMessage_Mai.Subject = title;
            SmtpEmailSend.MailMessage_Mai.SubjectEncoding = Encoding.UTF8;
            SmtpEmailSend.MailMessage_Mai.Body = content;
            SmtpEmailSend.MailMessage_Mai.BodyEncoding = Encoding.UTF8;
            SmtpEmailSend.MailMessage_Mai.IsBodyHtml = false;
            SmtpEmailSend.MailMessage_Mai.Attachments.Clear();
            if (!string.IsNullOrEmpty(filePath))
            {
                SmtpEmailSend.MailMessage_Mai.Attachments.Add(new Attachment(filePath));
            }
            //SmtpEmailSend.SmtpClient.SendCompleted += new SendCompletedEventHandler(callback.Invoke);
            SmtpEmailSend.SmtpClient.SendAsync(SmtpEmailSend.MailMessage_Mai, "000000000");
            flag = true;
            return flag;
        }
        public static void setSmtpClient(string ServerHost, int Port)
        {
            SmtpEmailSend.SmtpClient = new SmtpClient();
            SmtpEmailSend.SmtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
            SmtpEmailSend.SmtpClient.Host = ServerHost;
            SmtpEmailSend.SmtpClient.Port = Port;
            SmtpEmailSend.SmtpClient.Timeout = 0;
        }
        public static void setAddressform(string strMailAddress, string strMailPwd, string strNickName)
        {
            new NetworkCredential(strMailAddress, strMailPwd);
            SmtpEmailSend.MailAddress_from = new MailAddress(strMailAddress, strNickName);
            SmtpEmailSend.SmtpClient.Credentials = new NetworkCredential(SmtpEmailSend.MailAddress_from.Address, strMailPwd);
        }
        public static bool Attachment_MaiInit(string path, out string msg)
        {
            bool result;
            try
            {
                msg = string.Empty;
                FileStream fileStream = new FileStream(path, FileMode.Open);
                string arg_15_0 = fileStream.Name;
                int num = (int)(fileStream.Length / 1024L / 1024L);
                fileStream.Close();
                if (num > 3)
                {
                    msg = "文件长度不能大于3M！你选择的文件大小为" + num.ToString() + "M";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            catch (IOException)
            {
                msg = "检测附件大小时发生异常";
                result = false;
            }
            return result;
        }
    }
}