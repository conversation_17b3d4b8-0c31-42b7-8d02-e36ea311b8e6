﻿using CommonLib;
using System;
using System.Collections.Specialized;
using System.Net;
using System.Reflection;
using System.Text;

namespace Account.Web
{
    public delegate byte[] GetImage(CNNWebClient myClient, string Url, ref string CookieStr, string ipAddress = "");
    public delegate string GetNoSyncHtml(string Url, string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2);
    public class WebClientExt
    {
        public static string GetHtml(string Url, string CookieStr, string ipAddress = "", string strPost = "", string Referer = "", int timeOut = 2, int nCount = 0)
        {
            string result = "";
            CNNWebClient myClient = new CNNWebClient();
            try
            {
                myClient.Timeout = timeOut;
                if (!string.IsNullOrEmpty(ipAddress))
                    myClient.StrIPAddress = ipAddress;
                if (!string.IsNullOrEmpty(strPost) && strPost.StartsWith("{"))
                    myClient.Headers.Add("Content-Type: application/json");
                else
                    myClient.Headers.Add("Content-Type: application/x-www-form-urlencoded");
                myClient.Headers.Add("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36");
                myClient.Headers.Add("Cache-Control: no-cache");
                myClient.Headers.Add("Pragma: no-cache");
                if (CookieStr != "")
                {
                    myClient.Headers.Add("Cookie: " + CookieStr);
                }
                myClient.Encoding = Encoding.UTF8;
                if (!string.IsNullOrEmpty(Referer))
                {
                    myClient.SetHeaderValue(myClient.Headers, "Referer", Referer);
                    myClient.SetHeaderValue(myClient.Headers, "Origin", Referer);
                }
                myClient.Headers.Add("sec-ch-ua", "\"Chromium\";v=\"110\", \"Not A(Brand\";v=\"24\", \"Google Chrome\";v=\"110\"");
                myClient.Headers.Add("sec-ch-ua-mobile", "?0");
                myClient.Headers.Add("sec-ch-ua-platform", "\"Windows\"");
                myClient.Headers.Add("Sec-Fetch-Site", "cross-site");
                myClient.Headers.Add("Sec-Fetch-Mode", "cors");
                myClient.Headers.Add("Sec-Fetch-Dest", "empty");
                if (string.IsNullOrEmpty(strPost))
                    result = myClient.DownloadString(new Uri(Url, true));
                else
                    result = myClient.UploadString(new Uri(Url, true), strPost);
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Set-Cookie"]))
                {
                    //JSESSIONID=TM566SC1-55FYNTOYH1A6L00BEW1G3-SAYYTW7I-VV85; Path=/; HttpOnly_session0=eNrz4IovSYovyc9OzYvn8k9OM3ROzXFyy%2BcNdg0O9vT3i%2Ff1d3H1MYiqzkyxUgrxNTUzC3Y21DU1dYv0C%2FGP9DB0NPMxMHByDTd0N9YNdoyMDAk399QNC7MwVdJJLrEyNDEyBwIzC3NjYyOdxGQ0gdwKK4PaKACsXCPB; Path=/; HttpOnly
                    CookieStr += myClient.ResponseHeaders["Set-Cookie"].Replace("Path=/;", " ").Replace("HttpOnly", "").Replace(",", "").Trim();
                }
                if (!string.IsNullOrEmpty(myClient.ResponseHeaders["Location"]))
                {
                    nCount++;
                    if (nCount < 3)
                        return GetHtml(myClient.ResponseHeaders["Location"], CookieStr, ipAddress, "", Referer, timeOut, nCount);
                }
                if (Url.Contains("baidu") && !string.IsNullOrEmpty(myClient.ResponseHeaders["Date"]))//header.Url.Contains("12306") ||
                {
                    try
                    {
                        result = myClient.ResponseHeaders["Date"];
                    }
                    catch { }
                }
            }
            catch (Exception oe)
            {
                //log4net.LogManager.GetLogger("Order").Error(oe);
                if (oe is WebException exception)
                {
                    var response = exception.Response as HttpWebResponse;
                    try
                    {
                        if (response != null)
                        {
                            switch (response.StatusCode)
                            {
                                case HttpStatusCode.NotFound: //404
                                    ServerInfo.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                    result = " ";
                                    break;
                            }
                        }
                        else if (!string.IsNullOrEmpty(exception.Message))
                        {
                            if (exception.Message.Contains("(404)"))
                            {
                                ServerInfo.ReportError(myClient.StrHost, myClient.StrIpAddress);
                                result = " ";
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        try
                        {
                            response?.Close();
                        }
                        catch { }
                    }
                }
                else if (!string.IsNullOrEmpty(oe.Message))
                {
                    if (oe.Message.Contains("(404)"))
                    {
                        ServerInfo.ReportError(myClient.StrHost, myClient.StrIpAddress);
                        result = " ";
                    }
                }
            }
            finally
            {
                try
                {
                    if (myClient.IsBusy)
                        myClient.CancelAsync();
                }
                catch { }
                try
                {
                    myClient.Dispose();
                }
                catch { }
                try
                {
                    myClient = null;
                }
                catch { }
            }
            return result;
        }
    }

    ///// <summary>
    ///// 过期时回调委托
    ///// </summary>
    ///// <param name="userdata"></param>
    //public delegate void TimeoutCaller(object userdata);

    public class CNNWebClient : WebClient
    {
        //private Calculagraph _timer;
        private int _timeOut = 3;
        private string strIPAddress = "";

        public string StrIPAddress
        {
            get { return strIPAddress; }
            set { strIPAddress = value; }
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public int Timeout
        {
            get
            {
                return _timeOut;
            }
            set
            {
                if (value <= 0)
                    _timeOut = 10;
                _timeOut = value;
            }
        }

        public string StrHost { get; set; } = "";

        public string StrIpAddress { get; set; } = "";

        /// <summary>
        /// 重写GetWebRequest,添加WebRequest对象超时时间
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        protected override WebRequest GetWebRequest(Uri address)
        {
            if (ServerTime.DateTime.Second % 2 == 0)
            {
                //System.Threading.Thread.Sleep(1);
                System.GC.Collect();
            }

            StrHost = address.Host;

            HttpWebRequest request;

            var selfHost = ServerInfo.IsSelfHost(StrHost);
            if (selfHost != 0)
            {
                address = ServerInfo.SetAddress(address, selfHost);
                StrIpAddress = address.Host;
            }
            else if (!string.IsNullOrEmpty(StrIPAddress))
            {
                address = new Uri(address.AbsoluteUri.Replace(address.Host, StrIPAddress), true);
            }
            try
            {
                request = (HttpWebRequest)base.GetWebRequest(address);
            }
            catch (Exception oe)
            {
                request = (HttpWebRequest)WebRequest.Create(address);
                Console.WriteLine(oe.Message);
            }

            if (selfHost != 0)
            {
                ServerInfo.SetUserAgent(request);
                request.Host = StrHost;
            }
            else
            {
                request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36";
            }

            request.ProtocolVersion = HttpVersion.Version10;
            //是否使用 Nagle 不使用 提高效率 
            request.ServicePoint.UseNagleAlgorithm = false;
            //最大连接数 
            request.ServicePoint.ConnectionLimit = int.MaxValue;
            //数据是否缓冲 false 提高效率  
            request.AllowWriteStreamBuffering = true;
            //request.ServicePoint.Expect100Continue = false;
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            request.AllowAutoRedirect = false;

            request.KeepAlive = true;
            request.IfModifiedSince = new DateTime(1970, 1, 1);
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //request.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);

            //HttpHelper.SetHeaderValue(request.Headers, "If-Modified-Since", "Wed, 31 Dec 1969 16:00:00 GMT");
            //request.Headers.Add("If-None-Match", DateTime.Now.Ticks.ToString());
            //}
            if (request.Proxy != null)
            {
                request.Proxy = null;
            }
            request.Proxy = GlobalProxySelection.GetEmptyWebProxy();
            //HttpHelper.SetHeaderValue(request.Headers, "Connection", "Close");
            return request;
        }

        public void SetHeaderValue(WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                BindingFlags.Instance | BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }
    }
}
