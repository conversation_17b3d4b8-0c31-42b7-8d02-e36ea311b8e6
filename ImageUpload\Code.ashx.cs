﻿using ImageLib;
using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading;
using System.Web;

namespace ImageUpload
{
    /// <summary>
    ///     Code 的摘要说明
    /// </summary>
    public class Code1 : IHttpHandler
    {

        public void ProcessRequest(HttpContext context)
        {
            if (context.Request.Params.Count <= 0 || !context.Request.Params.AllKeys.Contains("op"))
            {
                return;
            }
            context.Response.ContentType = "text/plain";
            var action = context.Request.Params["op"];
            var result = "";
            switch (action)
            {
                case "siteinfo":
                    #region SiteInfo

                    //result += CustomImageHelper.ReportToday();
                    result += Environment.NewLine + "服务器名称：" + context.Server.MachineName;
                    //服务器名称  
                    result += Environment.NewLine + "服务器IP地址：" + context.Request.ServerVariables["LOCAL_ADDR"];
                    //服务器IP地址  
                    result += Environment.NewLine + "HTTP访问端口：" + context.Request.ServerVariables["SERVER_PORT"];
                    //HTTP访问端口"
                    result += Environment.NewLine + ".NET版本：" + ".NET CLR" + Environment.Version.Major + "." +
                              Environment.Version.Minor + "." + Environment.Version.Build + "." + Environment.Version.Revision;
                    //.NET解释引擎版本  
                    result += Environment.NewLine + "服务器操作系统版本：" + Environment.OSVersion;
                    //服务器操作系统版本  
                    result += Environment.NewLine + "服务器IIS版本：" + context.Request.ServerVariables["SERVER_SOFTWARE"];
                    //服务器IIS版本  
                    result += Environment.NewLine + "服务器域名：" + context.Request.ServerVariables["SERVER_NAME"];
                    //服务器域名  
                    result += Environment.NewLine + "虚拟目录的绝对路径：" + context.Request.ServerVariables["APPL_RHYSICAL_PATH"];
                    //虚拟目录的绝对路径  
                    result += Environment.NewLine + "执行文件的绝对路径：" + context.Request.ServerVariables["PATH_TRANSLATED"];
                    ////执行文件的绝对路径  
                    //result += Environment.NewLine + "虚拟目录Session总数：" + context.Session.Contents.Count.ToString();
                    ////虚拟目录Session总数  
                    //result += Environment.NewLine + "虚拟目录Application总数：" + context.Application.Contents.Count.ToString();
                    //虚拟目录Application总数  
                    result += Environment.NewLine + "域名主机：" + context.Request.ServerVariables["HTTP_HOST"];
                    //域名主机  
                    result += Environment.NewLine + "服务器区域语言：" + context.Request.ServerVariables["HTTP_ACCEPT_LANGUAGE"];
                    //服务器区域语言  
                    result += Environment.NewLine + "用户信息：" + context.Request.ServerVariables["HTTP_USER_AGENT"];
                    result += Environment.NewLine + "CPU个数：" + Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
                    //CPU个数  
                    result += Environment.NewLine + "CPU类型：" + Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                    //CPU类型  
                    result += Environment.NewLine + "请求来源地址：" + context.Request.Headers["X-Real-IP"];

                    #endregion
                    break;
                #region 对外识别接口
                case "imgUpload":
                    if (context.Request.Files.Count > 0)
                    {
                        try
                        {
                            var file = context.Request.Files[0];
                            using (var binaryReader = new BinaryReader(file.InputStream))
                            {
                                var contentLength = file.ContentLength / 1024;//KB
                                //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                var byts = binaryReader.ReadBytes(file.ContentLength);
                                result = ImageHelper.GetUploadResult(byts);
                            }
                        }
                        catch (Exception oe)
                        {
                        }
                    }
                    break;
                case "imgCrop":
                    if (context.Request.Files.Count > 0)
                    {
                        try
                        {
                            var file = context.Request.Files[0];
                            using (var binaryReader = new BinaryReader(file.InputStream))
                            {
                                var contentLength = file.ContentLength / 1024;//KB
                                //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                var byts = binaryReader.ReadBytes(file.ContentLength);
                                result = ImageHelper.GetProcessResult(byts);
                            }
                        }
                        catch (Exception oe)
                        {
                        }
                    }
                    break;
                #endregion
            }
            if (string.IsNullOrEmpty(result))
            {
                result = "no";
            }
            context.Response.Write(result ?? "");
            context.Response.End();
        }

        private string GetGzipStream(Stream stream, int retryTime = 0)
        {
            var result = string.Empty;
            try
            {
                using (GZipStream gzipStream = new GZipStream(stream, CompressionMode.Decompress))
                {
                    using (MemoryStream decompressedStream = new MemoryStream())
                    {
                        gzipStream.CopyTo(decompressedStream);
                        result = Encoding.UTF8.GetString(decompressedStream.ToArray());
                    }
                }
            }
            catch (Exception oe)
            {
                if (retryTime < 5)
                {
                    retryTime++;
                    Thread.Sleep(100);
                    return GetGzipStream(stream, retryTime);
                }
            }
            return result;
        }

        public bool IsReusable => false;
    }

}